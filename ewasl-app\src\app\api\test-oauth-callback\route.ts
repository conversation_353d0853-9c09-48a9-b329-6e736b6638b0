import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { FacebookOAuthService } from '@/lib/oauth/facebook';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  const testResults = {
    timestamp: new Date().toISOString(),
    steps: [] as any[],
    success: false,
    error: null as any,
    summary: ''
  };

  try {
    console.log('🧪 STARTING OAUTH CALLBACK DIAGNOSTIC TEST...');
    
    // STEP 1: Test Database Connection
    testResults.steps.push({
      step: 1,
      name: 'Database Connection Test',
      status: 'testing',
      details: 'Testing Supabase service client connection'
    });

    try {
      const { data: testData, error: dbError } = await supabase
        .from('social_accounts')
        .select('count')
        .limit(1);
      
      if (dbError) {
        testResults.steps[0].status = 'failed';
        testResults.steps[0].error = dbError.message;
        testResults.steps[0].details = `Database connection failed: ${dbError.message}`;
      } else {
        testResults.steps[0].status = 'passed';
        testResults.steps[0].details = 'Database connection successful';
      }
    } catch (dbTestError) {
      testResults.steps[0].status = 'failed';
      testResults.steps[0].error = dbTestError.message;
      testResults.steps[0].details = `Database connection exception: ${dbTestError.message}`;
    }

    // STEP 2: Test Facebook API Access
    testResults.steps.push({
      step: 2,
      name: 'Facebook API Access Test',
      status: 'testing',
      details: 'Testing Facebook debug_token API access'
    });

    try {
      const testToken = 'test_token_123'; // This will fail but we can check the API response
      const debugResponse = await fetch(
        `https://graph.facebook.com/debug_token?input_token=${testToken}&access_token=${process.env.FACEBOOK_APP_ID}|${process.env.FACEBOOK_APP_SECRET}`
      );
      
      const debugData = await debugResponse.json();
      
      if (debugResponse.ok || debugData.error?.code === 190) { // 190 = Invalid token (expected)
        testResults.steps[1].status = 'passed';
        testResults.steps[1].details = 'Facebook API accessible (expected invalid token error)';
        testResults.steps[1].response = debugData;
      } else {
        testResults.steps[1].status = 'failed';
        testResults.steps[1].error = debugData.error?.message || 'Unknown API error';
        testResults.steps[1].details = `Facebook API error: ${debugData.error?.message}`;
      }
    } catch (apiTestError) {
      testResults.steps[1].status = 'failed';
      testResults.steps[1].error = apiTestError.message;
      testResults.steps[1].details = `Facebook API exception: ${apiTestError.message}`;
    }

    // STEP 3: Test OAuth Service Initialization
    testResults.steps.push({
      step: 3,
      name: 'OAuth Service Initialization',
      status: 'testing',
      details: 'Testing FacebookOAuthService initialization'
    });

    try {
      const facebookService = new FacebookOAuthService();
      testResults.steps[2].status = 'passed';
      testResults.steps[2].details = 'FacebookOAuthService initialized successfully';
    } catch (serviceError) {
      testResults.steps[2].status = 'failed';
      testResults.steps[2].error = serviceError.message;
      testResults.steps[2].details = `OAuth service initialization failed: ${serviceError.message}`;
    }

    // STEP 4: Test Environment Variables
    testResults.steps.push({
      step: 4,
      name: 'Environment Variables Check',
      status: 'testing',
      details: 'Checking required environment variables'
    });

    const requiredEnvVars = [
      'FACEBOOK_APP_ID',
      'FACEBOOK_APP_SECRET',
      'NEXT_PUBLIC_SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY'
    ];

    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    if (missingEnvVars.length === 0) {
      testResults.steps[3].status = 'passed';
      testResults.steps[3].details = 'All required environment variables present';
      testResults.steps[3].envVars = {
        FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID ? 'present' : 'missing',
        FACEBOOK_APP_SECRET: process.env.FACEBOOK_APP_SECRET ? 'present' : 'missing',
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'present' : 'missing',
        SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'present' : 'missing'
      };
    } else {
      testResults.steps[3].status = 'failed';
      testResults.steps[3].error = `Missing environment variables: ${missingEnvVars.join(', ')}`;
      testResults.steps[3].details = `Missing required environment variables`;
      testResults.steps[3].missingVars = missingEnvVars;
    }

    // STEP 5: Test OAuth States Table
    testResults.steps.push({
      step: 5,
      name: 'OAuth States Table Test',
      status: 'testing',
      details: 'Testing oauth_states table access'
    });

    try {
      const { data: statesData, error: statesError } = await supabase
        .from('oauth_states')
        .select('*')
        .limit(5);
      
      if (statesError) {
        testResults.steps[4].status = 'failed';
        testResults.steps[4].error = statesError.message;
        testResults.steps[4].details = `OAuth states table error: ${statesError.message}`;
      } else {
        testResults.steps[4].status = 'passed';
        testResults.steps[4].details = `OAuth states table accessible (${statesData?.length || 0} records)`;
        testResults.steps[4].recordCount = statesData?.length || 0;
      }
    } catch (statesTestError) {
      testResults.steps[4].status = 'failed';
      testResults.steps[4].error = statesTestError.message;
      testResults.steps[4].details = `OAuth states table exception: ${statesTestError.message}`;
    }

    // STEP 6: Test OAuth Logs Table
    testResults.steps.push({
      step: 6,
      name: 'OAuth Logs Table Test',
      status: 'testing',
      details: 'Testing oauth_logs table access and recent entries'
    });

    try {
      const { data: logsData, error: logsError } = await supabase
        .from('oauth_logs')
        .select('*')
        .eq('platform', 'facebook')
        .order('created_at', { ascending: false })
        .limit(10);
      
      if (logsError) {
        testResults.steps[5].status = 'failed';
        testResults.steps[5].error = logsError.message;
        testResults.steps[5].details = `OAuth logs table error: ${logsError.message}`;
      } else {
        testResults.steps[5].status = 'passed';
        testResults.steps[5].details = `OAuth logs table accessible (${logsData?.length || 0} Facebook logs)`;
        testResults.steps[5].recentLogs = logsData?.slice(0, 3) || [];
        testResults.steps[5].recordCount = logsData?.length || 0;
      }
    } catch (logsTestError) {
      testResults.steps[5].status = 'failed';
      testResults.steps[5].error = logsTestError.message;
      testResults.steps[5].details = `OAuth logs table exception: ${logsTestError.message}`;
    }

    // Calculate overall success
    const passedSteps = testResults.steps.filter(step => step.status === 'passed').length;
    const totalSteps = testResults.steps.length;
    
    testResults.success = passedSteps === totalSteps;
    testResults.summary = `${passedSteps}/${totalSteps} diagnostic steps passed`;

    if (testResults.success) {
      testResults.summary += ' - OAuth callback infrastructure appears healthy';
    } else {
      const failedSteps = testResults.steps.filter(step => step.status === 'failed');
      testResults.summary += ` - Issues found in: ${failedSteps.map(s => s.name).join(', ')}`;
    }

    console.log('✅ OAuth callback diagnostic test completed');
    console.log('Summary:', testResults.summary);

    return NextResponse.json(testResults);

  } catch (error) {
    console.error('❌ OAuth callback diagnostic test failed:', error);
    testResults.error = error instanceof Error ? error.message : 'Unknown error';
    testResults.summary = `Diagnostic test failed: ${testResults.error}`;
    
    return NextResponse.json(testResults, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  return NextResponse.json({
    message: 'Use GET method for OAuth callback diagnostic test',
    usage: 'GET /api/test-oauth-callback'
  });
}
