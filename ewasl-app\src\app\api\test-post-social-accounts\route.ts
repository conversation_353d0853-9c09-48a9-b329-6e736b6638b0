import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Simple test endpoint to isolate the post_social_accounts insertion issue
export async function POST(request: NextRequest) {
  console.log('🧪 Testing post_social_accounts insertion...');

  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('✅ User authenticated:', user.id);

    // Step 1: Get the specific social account
    const { data: socialAccount, error: socialError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('id', '9b99c9ad-e11f-4f9a-8f19-51f5d696563c')
      .single();

    if (socialError || !socialAccount) {
      console.error('❌ Error fetching social account:', socialError);
      return NextResponse.json({
        success: false,
        error: 'Social account not found',
        details: socialError
      }, { status: 404 });
    }

    console.log('✅ Social account found:', {
      id: socialAccount.id,
      platform: socialAccount.platform,
      account_name: socialAccount.account_name
    });

    // Step 2: Create a test post
    const { data: testPost, error: postError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content: '🧪 Test post for debugging post_social_accounts insertion',
        media_urls: [],
        status: 'DRAFT',
        timezone: 'Asia/Riyadh',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (postError) {
      console.error('❌ Error creating test post:', postError);
      return NextResponse.json({
        success: false,
        error: 'Failed to create test post',
        details: postError
      }, { status: 500 });
    }

    console.log('✅ Test post created:', testPost.id);

    // Step 3: Test post_social_accounts insertion with explicit data
    const insertData = {
      post_id: testPost.id,
      social_account_id: socialAccount.id,
      platform: socialAccount.platform, // Explicitly set from social account
      status: 'draft',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('📝 Attempting to insert post_social_accounts with data:', insertData);

    const { data: postSocialAccount, error: psaError } = await supabase
      .from('post_social_accounts')
      .insert(insertData)
      .select()
      .single();

    if (psaError) {
      console.error('❌ CRITICAL: Failed to insert post_social_accounts:', psaError);
      
      // Clean up test post
      await supabase.from('posts').delete().eq('id', testPost.id);
      
      return NextResponse.json({
        success: false,
        error: 'Failed to insert post_social_accounts',
        details: psaError,
        attempted_data: insertData
      }, { status: 500 });
    }

    console.log('✅ post_social_accounts inserted successfully:', postSocialAccount);

    // Clean up test data
    await supabase.from('post_social_accounts').delete().eq('id', postSocialAccount.id);
    await supabase.from('posts').delete().eq('id', testPost.id);

    return NextResponse.json({
      success: true,
      message: 'post_social_accounts insertion test passed!',
      data: {
        social_account: {
          id: socialAccount.id,
          platform: socialAccount.platform,
          account_name: socialAccount.account_name
        },
        test_post: {
          id: testPost.id
        },
        inserted_data: insertData,
        result: postSocialAccount
      }
    });

  } catch (error) {
    console.error('❌ Unexpected error in test:', error);
    return NextResponse.json({
      success: false,
      error: 'Unexpected error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
