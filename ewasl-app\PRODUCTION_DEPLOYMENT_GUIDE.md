# 🚀 eWasl Social Media Platform - Production Deployment Guide

## 🎯 **Objective**
Deploy the eWasl application to Vercel production to resolve the 500 Internal Server Error caused by Facebook/Instagram API restrictions on localhost environments.

## 📋 **Prerequisites**
- ✅ Vercel account (free tier is sufficient)
- ✅ GitHub repository with the eWasl code
- ✅ Facebook Developer App (ID: ****************)
- ✅ Supabase project (nnxfzhxqzmriggulsudr)

## 🚀 **Deployment Steps**

### **Step 1: Prepare for Deployment**

1. **Install Vercel CLI** (if not already installed):
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**:
   ```bash
   vercel login
   ```

3. **Navigate to project directory**:
   ```bash
   cd ewasl-app
   ```

### **Step 2: Deploy to Vercel**

1. **Deploy the application**:
   ```bash
   vercel --prod
   ```

2. **Follow the prompts**:
   - Link to existing project or create new one
   - Choose project name (e.g., `ewasl-social-platform`)
   - Confirm deployment settings

3. **Note the deployment URL** (e.g., `https://ewasl-social-platform.vercel.app`)

### **Step 3: Configure Environment Variables**

Go to [Vercel Dashboard](https://vercel.com/dashboard) → Your Project → Settings → Environment Variables

Add the following variables:

#### **Application Configuration**
```
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-deployment-url.vercel.app
```

#### **Database Configuration**
```
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

#### **Supabase Configuration**
```
NEXT_PUBLIC_SUPABASE_URL=https://nnxfzhxqzmriggulsudr.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjA2ODc2OCwiZXhwIjoyMDY3NjQ0NzY4fQ.q4cHohdkLxlB41jp1k-5w4QLWX4R93ugtfEvLnqSJNM
```

#### **NextAuth Configuration**
```
NEXTAUTH_URL=https://your-deployment-url.vercel.app
NEXTAUTH_SECRET=your_secure_nextauth_secret_here_production_2024
```

#### **Facebook/Instagram OAuth**
```
FACEBOOK_APP_ID=****************
FACEBOOK_APP_SECRET=********************************
FACEBOOK_BUSINESS_ID=1479865455689755
INSTAGRAM_APP_ID=****************
INSTAGRAM_APP_SECRET=********************************
```

#### **OAuth Configuration**
```
OAUTH_REDIRECT_URL=https://your-deployment-url.vercel.app/api/auth/callback
```

#### **Security Configuration**
```
SECURITY_ENABLE_RATE_LIMIT=true
SECURITY_RATE_LIMIT_MAX=100
SECURITY_ALLOWED_ORIGINS=https://your-deployment-url.vercel.app
```

#### **Cron Configuration**
```
CRON_SECRET=your_secure_cron_secret_here_2024
```

### **Step 4: Update Facebook App Configuration**

1. Go to [Facebook Developer Console](https://developers.facebook.com/apps/****************/)

2. **Update OAuth Redirect URIs**:
   - Go to **Facebook Login** → **Settings**
   - Add: `https://your-deployment-url.vercel.app/api/auth/callback/facebook`
   - Add: `https://your-deployment-url.vercel.app/api/auth/callback`

3. **Update App Domains**:
   - Go to **App Settings** → **Basic**
   - Add: `your-deployment-url.vercel.app` (without https://)

4. **Update Instagram Basic Display**:
   - Go to **Instagram Basic Display** → **Settings**
   - Add OAuth Redirect URI: `https://your-deployment-url.vercel.app/api/auth/callback/instagram`

### **Step 5: Redeploy After Environment Variables**

After setting all environment variables:
```bash
vercel --prod
```

## 🧪 **Testing the Deployment**

### **1. Basic Functionality Test**
- Visit: `https://your-deployment-url.vercel.app`
- Verify the homepage loads correctly
- Test user authentication

### **2. Post Creation Test**
- Visit: `https://your-deployment-url.vercel.app/posts/new`
- Create a test post with Facebook/Instagram selected
- Set to "نشر فوري" (Immediate Publishing)
- Click "نشر الآن" (Publish Now)
- **Expected Result**: ✅ No 500 Internal Server Error

### **3. Debug Tools Test**
- Visit: `https://your-deployment-url.vercel.app/debug-post`
- Run the step-by-step debug test
- **Expected Result**: ✅ All steps should pass, including Facebook/Instagram API calls

### **4. Social Media Integration Test**
- Test Facebook OAuth connection
- Test Instagram OAuth connection
- Verify posts are actually published to social media platforms

## 🔍 **Expected Improvements**

### **Issues That Should Be Resolved**
- ✅ **500 Internal Server Error**: Fixed due to proper domain and HTTPS
- ✅ **Facebook/Instagram API Restrictions**: Resolved with production domain
- ✅ **OAuth Callback Issues**: Fixed with proper redirect URIs
- ✅ **HTTPS Requirements**: Met with Vercel's automatic HTTPS

### **Better Error Visibility**
- ✅ **Server-side Logs**: Available in Vercel Function logs
- ✅ **Real-time Monitoring**: Vercel Analytics and monitoring
- ✅ **Error Tracking**: Better error reporting in production

## 🚨 **Troubleshooting**

### **If Deployment Fails**
1. Check build logs in Vercel Dashboard
2. Verify all environment variables are set correctly
3. Ensure Facebook app configuration is updated
4. Check Supabase connection from production

### **If 500 Errors Persist**
1. Check Vercel Function logs
2. Use the debug endpoint: `/debug-post`
3. Verify Facebook/Instagram API credentials
4. Check database table existence

### **If OAuth Fails**
1. Verify Facebook app redirect URIs
2. Check NEXTAUTH_URL matches deployment URL
3. Ensure NEXTAUTH_SECRET is set
4. Test OAuth flow step by step

## 📊 **Monitoring and Maintenance**

### **Vercel Dashboard Monitoring**
- Function execution logs
- Performance metrics
- Error rates and types
- Deployment history

### **Regular Checks**
- Social media API token validity
- Database connection health
- Scheduled post processing
- User authentication flows

## 🎉 **Success Criteria**

The deployment is successful when:
- ✅ Application loads without errors
- ✅ User authentication works
- ✅ Post creation completes without 500 errors
- ✅ Facebook/Instagram publishing works
- ✅ OAuth flows complete successfully
- ✅ Debug tools show all green status

---

## 📞 **Support**

If you encounter issues:
1. Check Vercel Function logs first
2. Use the built-in debug tools (`/debug-post`)
3. Verify environment variables in Vercel Dashboard
4. Test individual API endpoints
5. Check Facebook Developer Console for API issues

**The production deployment should resolve the localhost-related 500 Internal Server Error and enable proper Facebook/Instagram API integration.**
