import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing Facebook API fix with form-encoded data...');
    
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { content = '🔧 Test post from eWasl - Facebook API fix verification' } = body;

    // Get user's Facebook account
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'FACEBOOK')
      .limit(1);

    if (accountsError || !accounts || accounts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No Facebook account found',
        details: accountsError
      }, { status: 400 });
    }

    const account = accounts[0];
    console.log('📤 Testing Facebook API fix with account:', {
      id: account.id,
      account_name: account.account_name,
      hasAccessToken: !!account.access_token,
      hasPageId: !!account.page_id,
      hasPageToken: !!account.page_access_token
    });

    // Determine posting target and token
    let targetId = 'me';
    let publishingToken = account.access_token;

    if (account.page_id && account.page_access_token) {
      targetId = account.page_id;
      publishingToken = account.page_access_token;
      console.log('📘 Publishing to Facebook Page:', account.page_id);
    } else {
      console.log('📘 Publishing to user feed (no page configured)');
    }

    // Test the fixed Facebook API call with form-encoded data
    console.log('🔧 Testing FIXED Facebook API call...');
    
    const postData = {
      message: content,
      access_token: publishingToken,
    };

    // Use form-encoded data (the fix)
    const formData = new URLSearchParams();
    Object.keys(postData).forEach(key => {
      formData.append(key, postData[key]);
    });

    console.log('📤 Making Facebook Graph API request with form data...');
    console.log('🎯 Target:', targetId);
    console.log('📊 Form data:', formData.toString().replace(/access_token=[^&]+/, 'access_token=[REDACTED]'));

    const response = await fetch(`https://graph.facebook.com/v19.0/${targetId}/feed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData,
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    const result = await response.json();
    console.log('📡 Response body:', result);

    if (!response.ok) {
      console.error('❌ Facebook API still failing:', result);
      return NextResponse.json({
        success: false,
        error: 'Facebook API test failed',
        details: result,
        facebook_error: result.error,
        fix_status: 'FAILED - API still returning error'
      }, { status: 400 });
    }

    console.log('✅ Facebook API fix SUCCESS! Post published:', result.id);

    return NextResponse.json({
      success: true,
      message: 'Facebook API fix verification successful!',
      post_id: result.id,
      post_url: `https://facebook.com/${result.id}`,
      platform_response: result,
      fix_status: 'SUCCESS - Form-encoded data fix worked!',
      account: {
        name: account.account_name,
        platform: account.platform,
        target: targetId,
        using_page_token: !!account.page_access_token
      }
    });

  } catch (error: any) {
    console.error('❌ Facebook API fix test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Facebook API fix test failed',
      details: {
        message: error.message,
        stack: error.stack
      },
      fix_status: 'ERROR - Exception occurred during test'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Facebook API Fix Test Endpoint',
    description: 'Use POST method to test the Facebook API fix with form-encoded data',
    usage: 'POST with optional { "content": "test message" } in body'
  });
}
