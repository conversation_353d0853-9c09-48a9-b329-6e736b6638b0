{"version": 3, "file": "index.cjs", "sources": ["../src/document.ts"], "sourcesContent": ["import { Node } from '@tiptap/core'\n\n/**\n * The default document node which represents the top level node of the editor.\n * @see https://tiptap.dev/api/nodes/document\n */\nexport const Document = Node.create({\n  name: 'doc',\n  topNode: true,\n  content: 'block+',\n})\n"], "names": ["Node"], "mappings": ";;;;;;AAEA;;;AAGG;AACU,MAAA,QAAQ,GAAGA,SAAI,CAAC,MAAM,CAAC;AAClC,IAAA,IAAI,EAAE,KAAK;AACX,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,OAAO,EAAE,QAAQ;AAClB,CAAA;;;;;"}