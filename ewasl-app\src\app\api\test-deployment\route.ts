import { NextRequest, NextResponse } from 'next/server';
import { createFacebookOAuthService } from '@/lib/oauth/facebook';

export async function GET(request: NextRequest) {
  try {
    // Test Facebook OAuth service
    const facebookService = createFacebookOAuthService();

    // Get a sample access token from database to test getUserAccounts
    const { createClient } = await import('@/lib/supabase/server');
    const supabase = createClient();

    const { data: accounts } = await supabase
      .from('social_accounts')
      .select('access_token')
      .eq('platform', 'FACEBOOK')
      .limit(1);

    let testResult = null;
    if (accounts && accounts.length > 0) {
      try {
        const userAccounts = await facebookService.getUserAccounts(accounts[0].access_token);
        testResult = {
          accountsFound: userAccounts.length,
          sampleAccount: userAccounts[0] ? {
            id: userAccounts[0].id,
            platform: userAccounts[0].platform,
            accountName: userAccounts[0].accountName,
            hasPageId: !!userAccounts[0].pageId,
            hasPageAccessToken: !!userAccounts[0].pageAccessToken,
            hasPageName: !!userAccounts[0].pageName,
            pageId: userAccounts[0].pageId,
            pageName: userAccounts[0].pageName,
            metadata: userAccounts[0].metadata
          } : null
        };
      } catch (error) {
        testResult = { error: error.message };
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Deployment test successful',
      timestamp: new Date().toISOString(),
      deployment_info: {
        oauth_callback_updated: true,
        page_token_fix_deployed: true,
        social_account_interface_updated: true,
        database_fields_added: true
      },
      test_data: {
        page_fields: ['page_id', 'page_access_token', 'page_name'],
        oauth_callback_path: '/api/oauth/facebook/callback/route.ts',
        interface_path: '/types/social-enhanced.ts'
      },
      facebook_test: testResult
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
