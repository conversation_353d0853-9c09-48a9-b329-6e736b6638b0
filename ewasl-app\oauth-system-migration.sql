-- OAuth System Migration for eWasl Platform
-- This migration ensures all required tables and functions exist for OAuth functionality

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Create oauth_states table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.oauth_states (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  platform TEXT NOT NULL,
  state_token TEXT NOT NULL UNIQUE,
  redirect_uri TEXT NOT NULL,
  code_verifier TEXT, -- For PKCE (Twitter OAuth 2.0)
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_oauth_states_token ON public.oauth_states(state_token);
CREATE INDEX IF NOT EXISTS idx_oauth_states_user_platform ON public.oauth_states(user_id, platform);
CREATE INDEX IF NOT EXISTS idx_oauth_states_expires ON public.oauth_states(expires_at);

-- 2. Create social_accounts table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.social_accounts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  platform TEXT NOT NULL CHECK (platform IN ('FACEBOOK', 'INSTAGRAM', 'TWITTER', 'LINKEDIN', 'TIKTOK')),
  account_id TEXT NOT NULL,
  account_name TEXT NOT NULL,
  account_handle TEXT,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  expires_at TIMESTAMPTZ,
  connection_status TEXT DEFAULT 'connected' CHECK (connection_status IN ('connected', 'expired', 'error', 'reconnecting', 'disconnected')),
  last_validated_at TIMESTAMPTZ,
  metadata JSONB DEFAULT '{}',
  profile_image_url TEXT,
  permissions JSONB DEFAULT '[]',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, platform, account_id)
);

-- Create indexes for social_accounts
CREATE INDEX IF NOT EXISTS idx_social_accounts_user_platform ON public.social_accounts(user_id, platform);
CREATE INDEX IF NOT EXISTS idx_social_accounts_status ON public.social_accounts(connection_status);
CREATE INDEX IF NOT EXISTS idx_social_accounts_expires ON public.social_accounts(expires_at);

-- 3. Create oauth_logs table if it doesn't exist (for debugging)
CREATE TABLE IF NOT EXISTS public.oauth_logs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  platform TEXT NOT NULL,
  action TEXT NOT NULL,
  status TEXT NOT NULL,
  details JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for oauth_logs
CREATE INDEX IF NOT EXISTS idx_oauth_logs_user_platform ON public.oauth_logs(user_id, platform);
CREATE INDEX IF NOT EXISTS idx_oauth_logs_created_at ON public.oauth_logs(created_at);

-- 4. Create audit_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.audit_logs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  resource_type TEXT,
  resource_ids TEXT[],
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for audit_logs
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_action ON public.audit_logs(user_id, action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);

-- 5. Enable Row Level Security
ALTER TABLE public.oauth_states ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.oauth_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS Policies
-- OAuth states policies
DROP POLICY IF EXISTS "Users can manage their oauth states" ON public.oauth_states;
CREATE POLICY "Users can manage their oauth states" ON public.oauth_states
  FOR ALL USING (auth.uid() = user_id);

-- Social accounts policies
DROP POLICY IF EXISTS "Users can manage their social accounts" ON public.social_accounts;
CREATE POLICY "Users can manage their social accounts" ON public.social_accounts
  FOR ALL USING (auth.uid() = user_id);

-- OAuth logs policies
DROP POLICY IF EXISTS "Users can view their oauth logs" ON public.oauth_logs;
CREATE POLICY "Users can view their oauth logs" ON public.oauth_logs
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Service role can insert oauth logs" ON public.oauth_logs;
CREATE POLICY "Service role can insert oauth logs" ON public.oauth_logs
  FOR INSERT WITH CHECK (true);

-- Audit logs policies
DROP POLICY IF EXISTS "Users can view their audit logs" ON public.audit_logs;
CREATE POLICY "Users can view their audit logs" ON public.audit_logs
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Service role can insert audit logs" ON public.audit_logs;
CREATE POLICY "Service role can insert audit logs" ON public.audit_logs
  FOR INSERT WITH CHECK (true);

-- 7. Create cleanup function for expired OAuth states
CREATE OR REPLACE FUNCTION cleanup_expired_oauth_states()
RETURNS void AS $$
BEGIN
  DELETE FROM public.oauth_states WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION cleanup_expired_oauth_states() TO authenticated;

-- 8. Create function to update social_accounts updated_at
CREATE OR REPLACE FUNCTION update_social_accounts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for social_accounts updated_at
DROP TRIGGER IF EXISTS trigger_update_social_accounts_updated_at ON public.social_accounts;
CREATE TRIGGER trigger_update_social_accounts_updated_at
  BEFORE UPDATE ON public.social_accounts
  FOR EACH ROW
  EXECUTE FUNCTION update_social_accounts_updated_at();

-- 9. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON public.oauth_states TO authenticated;
GRANT ALL ON public.social_accounts TO authenticated;
GRANT SELECT, INSERT ON public.oauth_logs TO authenticated;
GRANT SELECT, INSERT ON public.audit_logs TO authenticated;

-- Verify migration completion
SELECT 'OAuth system migration completed successfully' as status;
