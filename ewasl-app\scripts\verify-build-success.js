const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 eWasl Build Verification - Final Check');
console.log('=========================================');

let allChecks = 0;
let passedChecks = 0;

function runCheck(name, checkFunction) {
  allChecks++;
  console.log(`\n🔧 ${name}`);
  console.log('-'.repeat(name.length + 3));

  try {
    const result = checkFunction();
    if (result) {
      console.log('✅ PASSED');
      passedChecks++;
    } else {
      console.log('❌ FAILED');
    }
  } catch (error) {
    console.log(`❌ FAILED: ${error.message}`);
  }
}

// Check 1: TypeScript Compilation
runCheck('TypeScript Compilation', () => {
  try {
    execSync('npx tsc --noEmit', { cwd: path.join(__dirname, '..'), stdio: 'pipe' });
    return true;
  } catch (error) {
    console.log('TypeScript errors detected');
    return false;
  }
});

// Check 2: Next.js Build
runCheck('Next.js Production Build', () => {
  try {
    const output = execSync('npm run build', {
      cwd: path.join(__dirname, '..'),
      stdio: 'pipe',
      encoding: 'utf8'
    });

    // Check if build completed successfully
    if (output.includes('Route (app)') && !output.includes('Failed to compile')) {
      console.log('Build completed with optimized pages');
      return true;
    } else {
      console.log('Build output indicates errors');
      return false;
    }
  } catch (error) {
    console.log('Build process failed');
    return false;
  }
});

// Check 3: Required Files Exist
runCheck('Required Files Check', () => {
  const requiredFiles = [
    'package.json',
    'next.config.js',
    'tailwind.config.js',
    'tsconfig.json',
    'src/app/layout.tsx',
    'src/app/page.tsx',
    'src/lib/config/validation.ts',
    'src/lib/social/twitter.ts',
    'src/lib/social/facebook.ts',
    'src/lib/social/linkedin.ts',
  ];

  const missingFiles = [];

  for (const file of requiredFiles) {
    const filePath = path.join(__dirname, '..', file);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file);
    }
  }

  if (missingFiles.length === 0) {
    console.log(`All ${requiredFiles.length} required files present`);
    return true;
  } else {
    console.log(`Missing files: ${missingFiles.join(', ')}`);
    return false;
  }
});

// Check 4: Environment Configuration
runCheck('Environment Configuration', () => {
  const envPath = path.join(__dirname, '..', '.env.local');

  if (!fs.existsSync(envPath)) {
    console.log('No .env.local file found (expected for production)');
    return true; // This is OK for production
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
  ];

  const missingVars = requiredVars.filter(varName => !envContent.includes(varName));

  if (missingVars.length === 0) {
    console.log('All required environment variables configured');
    return true;
  } else {
    console.log(`Missing environment variables: ${missingVars.join(', ')}`);
    return false;
  }
});

// Check 5: API Routes Structure
runCheck('API Routes Structure', () => {
  const apiDir = path.join(__dirname, '..', 'src', 'app', 'api');
  const expectedRoutes = [
    'auth/register/route.ts',
    'auth/twitter/route.ts',
    'auth/twitter/callback/route.ts',
    'posts/publish/route.ts',
    'social/accounts/route.ts',
    'social/disconnect/route.ts',
    'stripe/create-checkout-session/route.ts',
    'stripe/manage-billing/route.ts',
    'stripe/webhook/route.ts',
  ];

  const missingRoutes = [];

  for (const route of expectedRoutes) {
    const routePath = path.join(apiDir, route);
    if (!fs.existsSync(routePath)) {
      missingRoutes.push(route);
    }
  }

  if (missingRoutes.length === 0) {
    console.log(`All ${expectedRoutes.length} API routes present`);
    return true;
  } else {
    console.log(`Missing API routes: ${missingRoutes.join(', ')}`);
    return false;
  }
});

// Check 6: Package Dependencies
runCheck('Package Dependencies', () => {
  const packagePath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

  const criticalDeps = [
    'next',
    'react',
    'typescript',
    '@supabase/supabase-js',
    'stripe',
    'twitter-api-v2',
    'tailwindcss',
  ];

  const missingDeps = criticalDeps.filter(dep =>
    !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
  );

  if (missingDeps.length === 0) {
    console.log(`All ${criticalDeps.length} critical dependencies installed`);
    return true;
  } else {
    console.log(`Missing dependencies: ${missingDeps.join(', ')}`);
    return false;
  }
});

// Final Summary
console.log('\n🎯 FINAL VERIFICATION RESULTS');
console.log('=============================');
console.log(`✅ Passed: ${passedChecks}/${allChecks} checks`);
console.log(`📊 Success Rate: ${Math.round((passedChecks / allChecks) * 100)}%`);

if (passedChecks === allChecks) {
  console.log('\n🎉 ALL CHECKS PASSED!');
  console.log('====================');
  console.log('✅ eWasl is ready for production deployment');
  console.log('✅ Build process is stable and error-free');
  console.log('✅ All required files and dependencies present');
  console.log('✅ API routes properly configured');
  console.log('✅ Environment setup validated');
  console.log('✅ TypeScript compilation successful');
  console.log('');
  console.log('🚀 DEPLOYMENT STATUS: READY');
  console.log('🌐 Production URL: https://app.ewasl.com');
  console.log('📊 Repository: https://github.com/TahaOsa/eWasl.com.git');
} else {
  console.log('\n⚠️  SOME CHECKS FAILED');
  console.log('======================');
  console.log('Please review the failed checks above and fix any issues.');
  console.log('The application may still work, but some features might be limited.');
}

console.log('\n📋 NEXT STEPS:');
console.log('==============');
console.log('1. Configure environment variables in DigitalOcean');
console.log('2. Set up custom SMTP provider in Supabase');
console.log('3. Configure social media app credentials');
console.log('4. Test all functionality with real accounts');
console.log('5. Monitor application performance and errors');
