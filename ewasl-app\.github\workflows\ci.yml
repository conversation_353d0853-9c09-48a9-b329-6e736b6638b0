name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Setup environment variables for build
      run: |
        echo "NEXT_PUBLIC_APP_URL=https://ewasl-social-scheduler-8672h.ondigitalocean.app" >> $GITHUB_ENV
        echo "NEXT_PUBLIC_SUPABASE_URL=https://ajpcbugydftdyhlbddpl.supabase.co" >> $GITHUB_ENV
        echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=dummy_key_for_build" >> $GITHUB_ENV
        echo "SUPABASE_SERVICE_ROLE_KEY=dummy_key_for_build" >> $GITHUB_ENV
        echo "NEXTAUTH_SECRET=dummy_secret_for_build" >> $GITHUB_ENV
        echo "NEXTAUTH_URL=https://ewasl-social-scheduler-8672h.ondigitalocean.app" >> $GITHUB_ENV
      
    - name: Lint
      run: npm run lint
      
    - name: Build
      run: npm run build
      
    # Add test step if you have tests
    # - name: Test
    #   run: npm test
