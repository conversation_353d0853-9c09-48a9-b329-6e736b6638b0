-- Migration: 004_business_accounts.sql
-- Purpose: Create tables for Facebook pages, LinkedIn organizations, and Instagram business accounts
-- Date: January 2025

-- Create facebook_pages table
CREATE TABLE IF NOT EXISTS facebook_pages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  page_id TEXT NOT NULL,
  page_name TEXT NOT NULL,
  page_access_token TEXT NOT NULL,
  instagram_business_account_id TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, page_id)
);

-- Create linkedin_organizations table
CREATE TABLE IF NOT EXISTS linkedin_organizations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id TEXT NOT NULL,
  organization_name TEXT NOT NULL,
  organization_urn TEXT,
  logo_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, organization_id)
);

-- Create instagram_business_accounts table
CREATE TABLE IF NOT EXISTS instagram_business_accounts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  instagram_account_id TEXT NOT NULL,
  username TEXT NOT NULL,
  name TEXT,
  profile_picture_url TEXT,
  followers_count INTEGER,
  facebook_page_id TEXT NOT NULL,
  page_access_token TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, instagram_account_id)
);

-- Add foreign key constraint to link Instagram accounts to Facebook pages
ALTER TABLE instagram_business_accounts 
ADD CONSTRAINT fk_instagram_facebook_page 
FOREIGN KEY (user_id, facebook_page_id) 
REFERENCES facebook_pages(user_id, page_id) 
ON DELETE CASCADE;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_facebook_pages_user_id ON facebook_pages(user_id);
CREATE INDEX IF NOT EXISTS idx_facebook_pages_page_id ON facebook_pages(page_id);
CREATE INDEX IF NOT EXISTS idx_facebook_pages_active ON facebook_pages(is_active);

CREATE INDEX IF NOT EXISTS idx_linkedin_organizations_user_id ON linkedin_organizations(user_id);
CREATE INDEX IF NOT EXISTS idx_linkedin_organizations_org_id ON linkedin_organizations(organization_id);
CREATE INDEX IF NOT EXISTS idx_linkedin_organizations_active ON linkedin_organizations(is_active);

CREATE INDEX IF NOT EXISTS idx_instagram_business_user_id ON instagram_business_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_instagram_business_account_id ON instagram_business_accounts(instagram_account_id);
CREATE INDEX IF NOT EXISTS idx_instagram_business_facebook_page ON instagram_business_accounts(facebook_page_id);
CREATE INDEX IF NOT EXISTS idx_instagram_business_active ON instagram_business_accounts(is_active);

-- Create triggers to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_facebook_pages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_facebook_pages_updated_at
  BEFORE UPDATE ON facebook_pages
  FOR EACH ROW
  EXECUTE FUNCTION update_facebook_pages_updated_at();

CREATE OR REPLACE FUNCTION update_linkedin_organizations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_linkedin_organizations_updated_at
  BEFORE UPDATE ON linkedin_organizations
  FOR EACH ROW
  EXECUTE FUNCTION update_linkedin_organizations_updated_at();

CREATE OR REPLACE FUNCTION update_instagram_business_accounts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_instagram_business_accounts_updated_at
  BEFORE UPDATE ON instagram_business_accounts
  FOR EACH ROW
  EXECUTE FUNCTION update_instagram_business_accounts_updated_at();

-- Add comments for documentation
COMMENT ON TABLE facebook_pages IS 'Stores Facebook business pages that users can post to';
COMMENT ON COLUMN facebook_pages.page_id IS 'Facebook Page ID from the Graph API';
COMMENT ON COLUMN facebook_pages.page_access_token IS 'Page-specific access token for posting';
COMMENT ON COLUMN facebook_pages.instagram_business_account_id IS 'Linked Instagram business account ID if available';

COMMENT ON TABLE linkedin_organizations IS 'Stores LinkedIn organizations that users can post to';
COMMENT ON COLUMN linkedin_organizations.organization_id IS 'LinkedIn organization ID';
COMMENT ON COLUMN linkedin_organizations.organization_urn IS 'LinkedIn URN for API calls';

COMMENT ON TABLE instagram_business_accounts IS 'Stores Instagram business accounts linked to Facebook pages';
COMMENT ON COLUMN instagram_business_accounts.instagram_account_id IS 'Instagram business account ID from Facebook Graph API';
COMMENT ON COLUMN instagram_business_accounts.facebook_page_id IS 'Associated Facebook page ID';
COMMENT ON COLUMN instagram_business_accounts.page_access_token IS 'Page access token for Instagram posting'; 