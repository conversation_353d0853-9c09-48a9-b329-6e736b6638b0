/**
 * Facebook OAuth Callback Handler
 * Handles the OAuth callback from Facebook and processes the authorization code
 */

import { NextRequest, NextResponse } from 'next/server'
import { createFacebookOAuthService } from '@/lib/oauth/facebook'
import { createClient } from '@/lib/supabase/server'
import { createClient as createServiceClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')
    const errorDescription = searchParams.get('error_description')

    // Handle OAuth errors
    if (error) {
      console.error('Facebook OAuth error:', error, errorDescription)
      return NextResponse.redirect(
        new URL(`/social?error=${encodeURIComponent(error)}&description=${encodeURIComponent(errorDescription || '')}`, request.url)
      )
    }

    if (!code || !state) {
      console.error('Missing code or state parameter')
      return NextResponse.redirect(
        new URL('/social?error=missing_parameters', request.url)
      )
    }

    // Get user session with proper cookie handling
    const cookieStore = cookies()
    const supabase = createClient(cookieStore)
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      console.error('User not authenticated:', userError)
      // Try to get user from state validation as fallback
      console.log('Attempting to validate state and get user from oauth_states table...')
    }

    // Validate state parameter - first try with user ID if available
    let stateRecord = null
    let actualUserId = user?.id

    if (user) {
      // User session is available, validate with user ID
      const { data, error: stateError } = await supabase
        .from('oauth_states')
        .select('*')
        .eq('state_token', state)
        .eq('user_id', user.id)
        .eq('platform', 'FACEBOOK')
        .single()

      if (!stateError && data) {
        stateRecord = data
      }
    }

    // If no state record found with user session, try without user ID constraint
    if (!stateRecord) {
      console.log('Trying to validate state without user constraint...')
      const { data, error: stateError } = await supabase
        .from('oauth_states')
        .select('*')
        .eq('state_token', state)
        .eq('platform', 'FACEBOOK')
        .single()

      if (stateError || !data) {
        console.error('Invalid or expired state:', stateError)
        return NextResponse.redirect(
          new URL('/social?error=invalid_state', request.url)
        )
      }

      stateRecord = data
      actualUserId = data.user_id
      console.log('Found state record for user:', actualUserId)
    }

    // Check if state is expired (10 minutes)
    const stateAge = Date.now() - new Date(stateRecord.created_at).getTime()
    if (stateAge > 10 * 60 * 1000) {
      console.error('State expired:', stateAge)
      return NextResponse.redirect(
        new URL('/social?error=state_expired', request.url)
      )
    }

    // Ensure we have a valid user ID
    if (!actualUserId) {
      console.error('No valid user ID found')
      return NextResponse.redirect(
        new URL('/auth/signin?error=authentication_required', request.url)
      )
    }

    // Exchange code for access token
    const facebookService = createFacebookOAuthService()
    const tokenData = await facebookService.exchangeCodeForToken(code)

    if (!tokenData.access_token) {
      console.error('Failed to get access token')
      return NextResponse.redirect(
        new URL('/social?error=token_exchange_failed', request.url)
      )
    }

    // CRITICAL FIX: Detect token type before calling getUserAccounts
    console.log('🔍 DETECTING FACEBOOK TOKEN TYPE...')

    let tokenType = 'unknown'
    let tokenMetadata = null

    try {
      // Use Facebook's debug_token API to determine token type
      const debugResponse = await fetch(
        `https://graph.facebook.com/debug_token?input_token=${tokenData.access_token}&access_token=${process.env.FACEBOOK_APP_ID}|${process.env.FACEBOOK_APP_SECRET}`
      )

      if (debugResponse.ok) {
        const debugData = await debugResponse.json()
        tokenType = debugData.data?.type || 'unknown'
        tokenMetadata = debugData.data

        console.log('✅ Token type detected:', {
          type: tokenType,
          userId: debugData.data?.user_id,
          profileId: debugData.data?.profile_id,
          scopes: debugData.data?.scopes,
          isValid: debugData.data?.is_valid
        })
      } else {
        console.error('❌ Failed to debug token:', await debugResponse.text())
      }
    } catch (debugError) {
      console.error('❌ Token debug error:', debugError)
    }

    // Get user's Facebook pages and Instagram accounts with enhanced token handling
    console.log('🔍 CALLING getUserAccounts WITH TOKEN TYPE:', tokenType)
    const accountsData = await facebookService.getUserAccounts(tokenData.access_token, tokenType, tokenMetadata)

    if (!accountsData || accountsData.length === 0) {
      console.error('❌ No Facebook pages or Instagram accounts found')
      console.error('Token type was:', tokenType)
      console.error('Token metadata:', tokenMetadata)

      // Enhanced error message based on token type
      const errorMessage = tokenType === 'PAGE'
        ? 'تم الاتصال بـ Facebook ولكن لم يتم العثور على صفحات. قد تحتاج إلى استخدام حساب مدير الصفحة.'
        : 'تم الاتصال بـ Facebook ولكن لم يتم العثور على حسابات.'

      return NextResponse.redirect(
        new URL(`/social?error=${encodeURIComponent(errorMessage)}`, request.url)
      )
    }

    console.log('✅ Found accounts:', accountsData.length)

    // Create service client for database operations
    const supabaseService = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Save accounts to database with enhanced logging
    console.log('💾 SAVING ACCOUNTS TO DATABASE...')
    let savedCount = 0
    const saveErrors = []

    for (const account of accountsData) {
      try {
        console.log(`💾 Processing account: ${account.accountName} (${account.platform})`)

        // Validate required fields before database insertion
        if (!account.id || !account.accountName || !account.accessToken) {
          const error = `Missing required fields for account: ${account.accountName}`
          console.error('❌', error)
          saveErrors.push(error)
          continue
        }

        // Prepare account data with enhanced metadata
        const accountData = {
          user_id: actualUserId,
          platform: account.platform,
          account_id: account.id,
          account_name: account.accountName,
          access_token: account.accessToken,
          refresh_token: account.refreshToken || null,
          expires_at: account.expiresAt ? new Date(account.expiresAt).toISOString() : null,
          token_expires_at: account.expiresAt ? new Date(account.expiresAt).toISOString() : null,
          last_validated_at: new Date().toISOString(),
          metadata: {
            ...account.metadata,
            token_type: tokenType,
            oauth_timestamp: new Date().toISOString(),
            facebook_user_id: tokenMetadata?.user_id,
            facebook_profile_id: tokenMetadata?.profile_id,
            token_scopes: tokenMetadata?.scopes
          },
          profile_image_url: account.profileImageUrl || null,
          permissions: account.permissions || [],
          is_active: true,
          last_sync_at: new Date().toISOString(),
          // Facebook page-specific fields (CRITICAL FIX for publishing)
          page_id: account.pageId || account.page_id || null,
          page_access_token: account.pageAccessToken || account.page_access_token || null,
          page_name: account.pageName || account.page_name || null,
          account_data: {
            id: account.id,
            name: account.accountName,
            platform: account.platform,
            access_token: account.accessToken ? account.accessToken.substring(0, 20) + '...' : null,
            refresh_token: account.refreshToken ? account.refreshToken.substring(0, 20) + '...' : null,
            expires_at: account.expiresAt,
            permissions: account.permissions || [],
            account_type: account.accountType || 'page',
            token_type: tokenType
          }
        }

        console.log('💾 Saving account data:', {
          user_id: accountData.user_id,
          platform: accountData.platform,
          account_name: accountData.account_name,
          account_id: accountData.account_id,
          page_id: accountData.page_id,
          page_access_token: accountData.page_access_token ? accountData.page_access_token.substring(0, 20) + '...' : null,
          page_name: accountData.page_name,
          token_type: tokenType,
          has_metadata: !!accountData.metadata
        })

        // Insert or update account in database using service client
        console.log(`💾 Attempting database upsert for account: ${account.id}`)

        const { data: insertedData, error: dbError } = await supabaseService
          .from('social_accounts')
          .upsert(accountData, {
            onConflict: 'user_id,platform,account_id'
          })
          .select()

        if (dbError) {
          const errorMsg = `Database error saving account ${account.id}: ${dbError.message}`
          console.error('❌', errorMsg)
          console.error('Database error details:', dbError)
          saveErrors.push(errorMsg)
        } else {
          console.log(`✅ Successfully saved account: ${account.accountName} (${account.platform})`)
          console.log('Database insert result:', insertedData)
          savedCount++
        }

      } catch (accountError) {
        const errorMsg = `Error processing account ${account.id}: ${accountError.message}`
        console.error('❌', errorMsg)
        console.error('Account processing error:', accountError)
        saveErrors.push(errorMsg)
      }
    }

    console.log(`💾 DATABASE SAVE COMPLETE: ${savedCount}/${accountsData.length} accounts saved`)
    if (saveErrors.length > 0) {
      console.error('❌ Save errors encountered:', saveErrors)
    }

    // Clean up the used state
    await supabase
      .from('oauth_states')
      .delete()
      .eq('state_token', state)

    // Log the OAuth completion with comprehensive details
    console.log('📝 LOGGING OAUTH COMPLETION...')

    try {
      await supabase
        .from('oauth_logs')
        .insert({
          user_id: actualUserId,
          platform: 'facebook',
          action: 'callback_completed',
          success: savedCount > 0,
          details: {
            accounts_found: accountsData.length,
            accounts_saved: savedCount,
            save_errors: saveErrors,
            token_type: tokenType,
            token_metadata: tokenMetadata,
            state: state,
            timestamp: new Date().toISOString()
          }
        })

      console.log('✅ OAuth completion logged successfully')
    } catch (logError) {
      console.error('❌ Failed to log OAuth completion:', logError)
    }

    console.log(`🎉 FACEBOOK OAUTH COMPLETED: Saved ${savedCount}/${accountsData.length} accounts`)

    // Enhanced redirect with Arabic success/error messages
    if (savedCount > 0) {
      const successMessage = `تم ربط ${savedCount} حساب Facebook بنجاح!`
      console.log('✅ Redirecting with success message:', successMessage)
      return NextResponse.redirect(
        new URL(`/social?success=${encodeURIComponent(successMessage)}`, request.url)
      )
    } else {
      const errorMessage = saveErrors.length > 0
        ? `تم الاتصال بـ Facebook ولكن حدثت أخطاء في حفظ الحسابات: ${saveErrors.join(', ')}`
        : 'تم الاتصال بـ Facebook ولكن لم يتم العثور على حسابات.'

      console.log('❌ Redirecting with error message:', errorMessage)
      return NextResponse.redirect(
        new URL(`/social?error=${encodeURIComponent(errorMessage)}`, request.url)
      )
    }

  } catch (error) {
    console.error('Facebook OAuth callback error:', error)
    
    // Log the error
    try {
      const cookieStore = cookies()
      const supabase = createClient(cookieStore)
      const { data: { user } } = await supabase.auth.getUser()

      // Use actualUserId if available, otherwise try to get from user session
      const logUserId = actualUserId || user?.id

      if (logUserId) {
        await supabase
          .from('oauth_logs')
          .insert({
            user_id: logUserId,
            platform: 'facebook',
            action: 'callback_error',
            success: false,
            details: {
              error: error instanceof Error ? error.message : 'Unknown error',
              stack: error instanceof Error ? error.stack : undefined
            }
          })
      }
    } catch (logError) {
      console.error('Failed to log OAuth error:', logError)
    }
    
    return NextResponse.redirect(
      new URL('/social?error=callback_failed', request.url)
    )
  }
}
