import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Simple test endpoint to isolate the post creation issue
export async function POST(request: NextRequest) {
  console.log('🧪 Simple post creation test started...');
  
  try {
    // Parse request body
    const body = await request.json();
    console.log('📝 Request body:', body);

    // Create Supabase client
    const supabase = createClient();
    console.log('✅ Supabase client created');

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.error('❌ Authentication failed:', userError);
      return NextResponse.json({
        success: false,
        error: 'Authentication failed',
        details: userError?.message
      }, { status: 401 });
    }

    console.log('✅ User authenticated:', user.id);

    // Check if publishing_results table exists
    console.log('📊 Checking if publishing_results table exists...');
    const { data: tableCheck, error: tableCheckError } = await supabase
      .from('publishing_results')
      .select('id')
      .limit(1);
    
    const publishingTableExists = !tableCheckError;
    console.log('📊 Publishing results table exists:', publishingTableExists);

    // Check if scheduled_posts_queue table exists
    console.log('📊 Checking if scheduled_posts_queue table exists...');
    const { data: queueCheck, error: queueCheckError } = await supabase
      .from('scheduled_posts_queue')
      .select('id')
      .limit(1);
    
    const queueTableExists = !queueCheckError;
    console.log('📊 Scheduled posts queue table exists:', queueTableExists);

    // Try to create a simple post
    console.log('📝 Creating simple post...');
    const { data: post, error: createError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content: body.content || 'Test post from simple API',
        status: 'DRAFT',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ Failed to create post:', createError);
      return NextResponse.json({
        success: false,
        error: 'Failed to create post',
        details: createError.message
      }, { status: 500 });
    }

    console.log('✅ Post created successfully:', post.id);

    return NextResponse.json({
      success: true,
      message: 'Simple post created successfully',
      data: {
        post_id: post.id,
        user_id: user.id,
        publishingTableExists,
        queueTableExists,
        content: post.content,
        status: post.status
      }
    });

  } catch (error: any) {
    console.error('💥 Simple post creation error:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
