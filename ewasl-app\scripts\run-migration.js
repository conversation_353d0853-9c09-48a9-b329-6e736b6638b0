const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Use hardcoded values for now since we know them
const supabaseUrl = 'https://ajpcbugydftdyhlbddpl.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqcGNidWd5ZGZ0ZHlobGJkZHBsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODAzMzgwNiwiZXhwIjoyMDYzNjA5ODA2fQ.oEzL8RGuwDyLOupfw2TQLbpViDujaFnTGfDl6sBMFkc';

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function runMigration() {
  try {
    console.log('🚀 Running eWasl database migration...');

    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250523214109_initial_schema.sql');
    const schema = fs.readFileSync(migrationPath, 'utf8');

    console.log('📄 Migration file loaded successfully');

    // Split the schema into individual statements
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📝 Found ${statements.length} SQL statements to execute`);

    // Execute each statement individually
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';';
      console.log(`\n⚡ Executing statement ${i + 1}/${statements.length}:`);
      console.log(statement.substring(0, 100) + '...');

      try {
        // Use the raw SQL execution method
        const { data, error } = await supabase.rpc('exec_sql', { sql: statement });

        if (error) {
          console.error(`❌ Error in statement ${i + 1}:`, error.message);
          // Continue with next statement for non-critical errors
          if (error.message.includes('already exists') || error.message.includes('IF NOT EXISTS')) {
            console.log('⚠️  Skipping - object already exists');
            continue;
          }
          throw error;
        }

        console.log(`✅ Statement ${i + 1} executed successfully`);

      } catch (statementError) {
        console.error(`❌ Failed to execute statement ${i + 1}:`, statementError.message);

        // Try alternative approach for this statement
        console.log('🔄 Trying alternative execution method...');

        // For some statements, we might need to handle them differently
        if (statement.includes('CREATE TYPE') && statementError.message.includes('already exists')) {
          console.log('⚠️  Type already exists, continuing...');
          continue;
        }

        if (statement.includes('CREATE TABLE') && statementError.message.includes('already exists')) {
          console.log('⚠️  Table already exists, continuing...');
          continue;
        }

        if (statement.includes('CREATE POLICY') && statementError.message.includes('already exists')) {
          console.log('⚠️  Policy already exists, continuing...');
          continue;
        }

        // If it's a critical error, stop execution
        if (!statementError.message.includes('already exists')) {
          throw statementError;
        }
      }

      // Small delay between statements
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log('✅ All database tables, types, and policies have been created');

    // Verify the migration by checking if tables exist
    console.log('\n🔍 Verifying migration...');

    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');

    if (tablesError) {
      console.log('⚠️  Could not verify tables (this is normal)');
    } else {
      console.log('📊 Created tables:', tables?.map(t => t.table_name).join(', '));
    }

    console.log('\n🚀 Database is ready for eWasl Social Scheduler!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

runMigration();
