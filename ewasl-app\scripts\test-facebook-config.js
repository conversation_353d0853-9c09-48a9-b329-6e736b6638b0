#!/usr/bin/env node

/**
 * Test Facebook App Configuration
 * Verifies Facebook App settings and API connectivity
 */

require('dotenv').config({ path: '.env.local' });

console.log('📘 Testing Facebook App Configuration...\n');

const FACEBOOK_APP_ID = process.env.FACEBOOK_APP_ID;
const FACEBOOK_APP_SECRET = process.env.FACEBOOK_APP_SECRET;
const APP_URL = process.env.NEXT_PUBLIC_APP_URL;

// Test 1: Environment Variables
console.log('📋 Step 1: Checking Environment Variables...');
if (!FACEBOOK_APP_ID) {
  console.log('❌ FACEBOOK_APP_ID is missing');
  process.exit(1);
}
if (!FACEBOOK_APP_SECRET) {
  console.log('❌ FACEBOOK_APP_SECRET is missing');
  process.exit(1);
}
if (!APP_URL) {
  console.log('❌ NEXT_PUBLIC_APP_URL is missing');
  process.exit(1);
}

console.log(`✅ Facebook App ID: ${FACEBOOK_APP_ID}`);
console.log(`✅ Facebook App Secret: ${'*'.repeat(FACEBOOK_APP_SECRET.length)}`);
console.log(`✅ App URL: ${APP_URL}`);

// Test 2: Generate OAuth URL
console.log('\n📋 Step 2: Generating OAuth URL...');
const redirectUri = `${APP_URL}/api/facebook/callback`;
const scopes = [
  'pages_show_list',
  'pages_manage_posts',
  'pages_read_engagement',
  'business_management',
  'email',
  'public_profile'
];

const oauthUrl = `https://www.facebook.com/v20.0/dialog/oauth?` +
  `client_id=${FACEBOOK_APP_ID}&` +
  `redirect_uri=${encodeURIComponent(redirectUri)}&` +
  `scope=${scopes.join(',')}&` +
  `response_type=code&` +
  `state=test_${Date.now()}`;

console.log('✅ OAuth URL generated:');
console.log(`   ${oauthUrl}`);

// Test 3: Test App Access Token
console.log('\n📋 Step 3: Testing App Access Token...');
const testAppToken = async () => {
  try {
    const appAccessToken = `${FACEBOOK_APP_ID}|${FACEBOOK_APP_SECRET}`;
    const response = await fetch(`https://graph.facebook.com/v20.0/oauth/access_token_info?access_token=${appAccessToken}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ App access token is valid');
      console.log(`   App ID: ${data.app_id}`);
      console.log(`   Type: ${data.type}`);
    } else {
      const error = await response.text();
      console.log('❌ App access token test failed:', error);
    }
  } catch (error) {
    console.log('❌ Error testing app access token:', error.message);
  }
};

// Test 4: Test Graph API Connectivity
console.log('\n📋 Step 4: Testing Graph API Connectivity...');
const testGraphAPI = async () => {
  try {
    const response = await fetch('https://graph.facebook.com/v20.0/');
    if (response.ok) {
      console.log('✅ Graph API is accessible');
    } else {
      console.log('❌ Graph API connectivity failed');
    }
  } catch (error) {
    console.log('❌ Error connecting to Graph API:', error.message);
  }
};

// Run tests
(async () => {
  await testAppToken();
  await testGraphAPI();
  
  console.log('\n🎯 Configuration Summary:');
  console.log('📋 Required Facebook App Settings:');
  console.log('   1. Valid OAuth Redirect URIs:');
  console.log(`      - ${redirectUri}`);
  console.log('   2. App Domains: localhost');
  console.log(`   3. Site URL: ${APP_URL}`);
  console.log('\n💡 Next Steps:');
  console.log('   1. Update Facebook App settings with the above URLs');
  console.log('   2. Start HTTPS development server');
  console.log('   3. Test OAuth flow manually');
  
  console.log('\n🔗 Quick Test OAuth URL:');
  console.log(oauthUrl);
})();
