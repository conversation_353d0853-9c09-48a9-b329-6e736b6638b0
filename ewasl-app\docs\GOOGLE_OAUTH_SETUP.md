# Google OAuth Setup Guide

This guide explains how to configure Google OAuth for the eWasl application.

## Prerequisites

- Google Cloud Console account
- Supabase project access
- Admin access to the application

## Step 1: Google Cloud Console Setup

### 1.1 Create/Access Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select or create a project for your eWasl application
3. Enable the Google+ API (if not already enabled)

### 1.2 Configure OAuth Consent Screen

1. Navigate to **APIs & Services** > **OAuth consent screen**
2. Choose **External** user type (unless using Google Workspace)
3. Fill in the required information:
   - **App name**: eWasl Social Media Scheduler
   - **User support email**: <EMAIL>
   - **Developer contact information**: <EMAIL>
4. Add scopes:
   - `email`
   - `profile`
   - `openid`
5. Add test users if in testing mode

### 1.3 Create OAuth 2.0 Credentials

1. Navigate to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Choose **Web application**
4. Configure:
   - **Name**: eWasl OAuth Client
   - **Authorized JavaScript origins**:
     - `http://localhost:3000` (development)
     - `https://app.ewasl.com` (production)
   - **Authorized redirect URIs**:
     - `http://localhost:3000/auth/callback` (development)
     - `https://app.ewasl.com/auth/callback` (production)
     - `https://ajpcbugydftdyhlbddpl.supabase.co/auth/v1/callback` (Supabase)

5. Save and copy the **Client ID** and **Client Secret**

## Step 2: Supabase Configuration

### 2.1 Enable Google Provider

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Navigate to **Authentication** > **Providers**
3. Find **Google** and enable it
4. Enter the credentials:
   - **Client ID**: from Google Cloud Console
   - **Client Secret**: from Google Cloud Console
5. Save the configuration

### 2.2 Configure Redirect URLs

1. In Supabase, go to **Authentication** > **URL Configuration**
2. Add redirect URLs:
   - `http://localhost:3000/auth/callback`
   - `https://app.ewasl.com/auth/callback`

## Step 3: Application Configuration

### 3.1 Environment Variables

Add these to your `.env.local` file:

```bash
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here

# Alternative naming (for compatibility)
AUTH_GOOGLE_ID=your-google-client-id-here
AUTH_GOOGLE_SECRET=your-google-client-secret-here
```

### 3.2 Production Environment

For DigitalOcean deployment, add the same variables to your app's environment:

```bash
# Using DigitalOcean CLI
doctl apps update <app-id> --spec app-platform-spec.yaml

# Or via DigitalOcean dashboard:
# Apps > Your App > Settings > Environment Variables
```

## Step 4: Testing

### 4.1 Local Testing

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Navigate to `http://localhost:3000/auth/signin`

3. Click the Google signin button

4. Verify the OAuth flow works correctly

### 4.2 Production Testing

1. Deploy your changes to production
2. Test the Google signin at `https://app.ewasl.com/auth/signin`
3. Verify user creation in Supabase dashboard

## Troubleshooting

### Common Issues

#### "Error 400: redirect_uri_mismatch"
- **Cause**: Redirect URI not configured correctly
- **Fix**: Add the exact redirect URI to Google Cloud Console

#### "Access blocked: This app's request is invalid"
- **Cause**: OAuth consent screen not properly configured
- **Fix**: Complete all required fields in OAuth consent screen

#### "Google OAuth غير مُفعل"
- **Cause**: Google provider not enabled in Supabase
- **Fix**: Enable Google provider in Supabase Authentication settings

#### "Provider not enabled" in Supabase
- **Cause**: Missing or incorrect client credentials
- **Fix**: Verify Client ID and Secret in Supabase match Google Cloud Console

### Debug Steps

1. **Check environment variables**:
   ```bash
   npm run debug:env
   ```

2. **Test Supabase configuration**:
   ```bash
   curl "https://ajpcbugydftdyhlbddpl.supabase.co/auth/v1/settings"
   ```

3. **Check application logs**:
   - Browser developer console
   - Server logs (`npm run dev`)
   - Supabase Auth logs

### Support

If you continue to experience issues:

1. Check the [Supabase Auth documentation](https://supabase.com/docs/guides/auth/social-login/auth-google)
2. Review Google Cloud Console audit logs
3. Contact the development team with:
   - Error messages
   - Browser console logs
   - Steps to reproduce

## Security Notes

- Never commit client secrets to version control
- Use different OAuth clients for development and production
- Regularly rotate client secrets
- Monitor OAuth usage in Google Cloud Console
- Review Supabase Auth logs for suspicious activity 