import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { createRateLimit, rateLimitConfigs } from '@/lib/rate-limit';
import { z } from 'zod';

export interface ApiHandlerConfig {
  requireAuth?: boolean;
  requireAdmin?: boolean;
  rateLimit?: keyof typeof rateLimitConfigs;
  validation?: {
    body?: z.ZodSchema;
    query?: z.ZodSchema;
  };
}

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string;
    email: string;
    role: string;
  };
  validatedData?: {
    body?: any;
    query?: any;
  };
}

/**
 * Secure API middleware that handles authentication, authorization, rate limiting, and validation
 */
export function createApiHandler(
  config: ApiHandlerConfig,
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>
) {
  return async function(request: NextRequest): Promise<NextResponse> {
    try {
      // Apply rate limiting if configured
      if (config.rateLimit) {
        const rateLimitConfig = rateLimitConfigs[config.rateLimit];
        const rateLimit = createRateLimit(rateLimitConfig);
        
        const rateLimitResult = await rateLimit(request, async (req) => {
          // Continue with the rest of the middleware
          return NextResponse.next();
        });
        
        // If rate limit exceeded, return the rate limit response
        if (rateLimitResult.status === 429) {
          return rateLimitResult;
        }
      }

      // Create authenticated request object
      const authenticatedRequest = request as AuthenticatedRequest;

      // Handle authentication if required
      if (config.requireAuth || config.requireAdmin) {
        const session = await getServerSession(authOptions);
        
        if (!session?.user) {
          return NextResponse.json(
            { error: 'Authentication required' },
            { status: 401 }
          );
        }

        // Check admin role if required
        if (config.requireAdmin && session.user.role !== 'ADMIN') {
          return NextResponse.json(
            { error: 'Admin access required' },
            { status: 403 }
          );
        }

        // Add user to request
        authenticatedRequest.user = {
          id: session.user.id,
          email: session.user.email,
          role: session.user.role,
        };
      }

      // Handle input validation
      if (config.validation) {
        const validatedData: any = {};

        // Validate request body
        if (config.validation.body) {
          try {
            const body = await request.json();
            validatedData.body = config.validation.body.parse(body);
          } catch (error) {
            if (error instanceof z.ZodError) {
              return NextResponse.json(
                {
                  error: 'Invalid request body',
                  details: error.errors.map(err => ({
                    field: err.path.join('.'),
                    message: err.message,
                  })),
                },
                { status: 400 }
              );
            }
            return NextResponse.json(
              { error: 'Invalid JSON in request body' },
              { status: 400 }
            );
          }
        }

        // Validate query parameters
        if (config.validation.query) {
          try {
            const url = new URL(request.url);
            const queryParams = Object.fromEntries(url.searchParams.entries());
            validatedData.query = config.validation.query.parse(queryParams);
          } catch (error) {
            if (error instanceof z.ZodError) {
              return NextResponse.json(
                {
                  error: 'Invalid query parameters',
                  details: error.errors.map(err => ({
                    field: err.path.join('.'),
                    message: err.message,
                  })),
                },
                { status: 400 }
              );
            }
          }
        }

        authenticatedRequest.validatedData = validatedData;
      }

      // Call the actual handler
      return await handler(authenticatedRequest);

    } catch (error) {
      console.error('API middleware error:', error);
      
      // Don't expose internal errors in production
      if (process.env.NODE_ENV === 'production') {
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
      
      return NextResponse.json(
        { 
          error: 'Internal server error',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Utility function for handling API errors consistently
 */
export function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error);

  if (error instanceof z.ZodError) {
    return NextResponse.json(
      {
        error: 'Validation failed',
        details: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
        })),
      },
      { status: 400 }
    );
  }

  if (error instanceof Error) {
    // Handle specific error types
    if (error.message.includes('Unauthorized')) {
      return NextResponse.json(
        { error: 'Unauthorized access' },
        { status: 401 }
      );
    }

    if (error.message.includes('Forbidden')) {
      return NextResponse.json(
        { error: 'Access forbidden' },
        { status: 403 }
      );
    }

    if (error.message.includes('Not found')) {
      return NextResponse.json(
        { error: 'Resource not found' },
        { status: 404 }
      );
    }
  }

  // Default error response
  return NextResponse.json(
    { 
      error: process.env.NODE_ENV === 'production' 
        ? 'Internal server error' 
        : error instanceof Error ? error.message : 'Unknown error'
    },
    { status: 500 }
  );
}

/**
 * Utility function to create standardized success responses
 */
export function createSuccessResponse(data: any, status: number = 200): NextResponse {
  return NextResponse.json(
    {
      success: true,
      data,
      timestamp: new Date().toISOString(),
    },
    { status }
  );
}

/**
 * Utility function to create standardized error responses
 */
export function createErrorResponse(
  message: string, 
  status: number = 400, 
  details?: any
): NextResponse {
  return NextResponse.json(
    {
      success: false,
      error: message,
      details,
      timestamp: new Date().toISOString(),
    },
    { status }
  );
}

/**
 * Predefined middleware configurations for common use cases
 */
export const middlewareConfigs = {
  // Public endpoints (no auth required)
  public: {
    requireAuth: false,
    rateLimit: 'general' as const,
  },
  
  // Protected endpoints (auth required)
  protected: {
    requireAuth: true,
    rateLimit: 'api' as const,
  },
  
  // Admin endpoints (admin role required)
  admin: {
    requireAuth: true,
    requireAdmin: true,
    rateLimit: 'api' as const,
  },
  
  // Authentication endpoints (strict rate limiting)
  auth: {
    requireAuth: false,
    rateLimit: 'auth' as const,
  },
  
  // Sensitive operations (very strict rate limiting)
  sensitive: {
    requireAuth: true,
    rateLimit: 'sensitive' as const,
  },
};
