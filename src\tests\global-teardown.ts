/**
 * Global Test Teardown for eWasl OAuth Integration Tests
 * Cleans up test environment and generates reports
 */

import { FullConfig } from '@playwright/test';
import { createServiceRoleClient } from '@/lib/supabase/service-role';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting eWasl OAuth Integration Test Teardown...');

  // Clean up test data
  try {
    console.log('🗑️ Cleaning up test data...');
    await cleanupTestData();
    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.error('❌ Test data cleanup failed:', error);
  }

  // Generate test summary report
  try {
    console.log('📊 Generating test summary report...');
    await generateTestSummaryReport();
    console.log('✅ Test summary report generated');
  } catch (error) {
    console.error('❌ Test summary report generation failed:', error);
  }

  // Archive screenshots and videos
  try {
    console.log('📁 Archiving test artifacts...');
    await archiveTestArtifacts();
    console.log('✅ Test artifacts archived');
  } catch (error) {
    console.error('❌ Test artifacts archiving failed:', error);
  }

  console.log('✅ Global teardown completed successfully');
}

/**
 * Clean up test data from database
 */
async function cleanupTestData() {
  const supabase = createServiceRoleClient();
  const testUserEmail = process.env.TEST_USER_EMAIL!;

  // Get test user ID
  const { data: testUser } = await supabase
    .from('users')
    .select('id')
    .eq('email', testUserEmail)
    .single();

  if (!testUser) {
    console.log('No test user found, skipping cleanup');
    return;
  }

  console.log(`Cleaning up data for test user: ${testUser.id}`);

  // Clean up test posts
  const { error: postsError } = await supabase
    .from('posts')
    .delete()
    .eq('user_id', testUser.id)
    .or('content.like.%TEST%,content.like.%مرحباً بكم في منصة eWasl%');

  if (postsError) {
    console.warn('Error cleaning up test posts:', postsError);
  }

  // Clean up test scheduled posts
  const { error: scheduledError } = await supabase
    .from('scheduled_posts_queue')
    .delete()
    .eq('user_id', testUser.id);

  if (scheduledError) {
    console.warn('Error cleaning up scheduled posts:', scheduledError);
  }

  // Clean up test activities
  const { error: activitiesError } = await supabase
    .from('activities')
    .delete()
    .eq('user_id', testUser.id)
    .like('details', '%TEST%');

  if (activitiesError) {
    console.warn('Error cleaning up test activities:', activitiesError);
  }

  // Note: We don't clean up social accounts as they might be real OAuth connections
  console.log('✅ Database cleanup completed');
}

/**
 * Generate test summary report
 */
async function generateTestSummaryReport() {
  const testResultsDir = path.join(process.cwd(), 'test-results');
  const resultsFile = path.join(testResultsDir, 'results.json');
  
  if (!fs.existsSync(resultsFile)) {
    console.log('No test results file found, skipping report generation');
    return;
  }

  const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
  
  const summary = {
    timestamp: new Date().toISOString(),
    totalTests: results.suites?.reduce((total: number, suite: any) => 
      total + (suite.specs?.length || 0), 0) || 0,
    passedTests: 0,
    failedTests: 0,
    skippedTests: 0,
    duration: results.stats?.duration || 0,
    environment: {
      baseUrl: process.env.NEXT_PUBLIC_APP_URL,
      nodeVersion: process.version,
      platform: process.platform
    },
    testCategories: {
      oauthIntegration: 0,
      arabicRtl: 0,
      performance: 0,
      responsive: 0
    }
  };

  // Process test results
  results.suites?.forEach((suite: any) => {
    suite.specs?.forEach((spec: any) => {
      spec.tests?.forEach((test: any) => {
        switch (test.status) {
          case 'passed':
            summary.passedTests++;
            break;
          case 'failed':
            summary.failedTests++;
            break;
          case 'skipped':
            summary.skippedTests++;
            break;
        }

        // Categorize tests
        const title = test.title?.toLowerCase() || '';
        if (title.includes('oauth') || title.includes('facebook') || title.includes('instagram')) {
          summary.testCategories.oauthIntegration++;
        }
        if (title.includes('arabic') || title.includes('rtl')) {
          summary.testCategories.arabicRtl++;
        }
        if (title.includes('performance') || title.includes('load')) {
          summary.testCategories.performance++;
        }
        if (title.includes('mobile') || title.includes('responsive')) {
          summary.testCategories.responsive++;
        }
      });
    });
  });

  // Generate HTML report
  const htmlReport = generateHtmlReport(summary, results);
  
  // Save reports
  fs.writeFileSync(
    path.join(testResultsDir, 'summary.json'), 
    JSON.stringify(summary, null, 2)
  );
  
  fs.writeFileSync(
    path.join(testResultsDir, 'summary.html'), 
    htmlReport
  );

  console.log(`📊 Test Summary:
    Total Tests: ${summary.totalTests}
    Passed: ${summary.passedTests}
    Failed: ${summary.failedTests}
    Skipped: ${summary.skippedTests}
    Duration: ${(summary.duration / 1000).toFixed(2)}s
    Success Rate: ${((summary.passedTests / summary.totalTests) * 100).toFixed(1)}%`);
}

/**
 * Generate HTML report
 */
function generateHtmlReport(summary: any, results: any): string {
  return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير اختبارات eWasl - OAuth Integration</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .passed { border-left: 4px solid #28a745; }
        .failed { border-left: 4px solid #dc3545; }
        .skipped { border-left: 4px solid #ffc107; }
        .total { border-left: 4px solid #007bff; }
        .categories { margin: 20px 0; }
        .category { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 4px; }
        .timestamp { color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 تقرير اختبارات منصة eWasl</h1>
        <p>اختبارات تكامل OAuth ودعم اللغة العربية</p>
        <p class="timestamp">تم إنشاؤه في: ${new Date(summary.timestamp).toLocaleString('ar-SA')}</p>
    </div>

    <div class="stats">
        <div class="stat-card total">
            <h3>${summary.totalTests}</h3>
            <p>إجمالي الاختبارات</p>
        </div>
        <div class="stat-card passed">
            <h3>${summary.passedTests}</h3>
            <p>اختبارات ناجحة</p>
        </div>
        <div class="stat-card failed">
            <h3>${summary.failedTests}</h3>
            <p>اختبارات فاشلة</p>
        </div>
        <div class="stat-card skipped">
            <h3>${summary.skippedTests}</h3>
            <p>اختبارات متجاهلة</p>
        </div>
    </div>

    <div class="categories">
        <h2>فئات الاختبارات</h2>
        <div class="category">
            <strong>تكامل OAuth:</strong> ${summary.testCategories.oauthIntegration} اختبار
        </div>
        <div class="category">
            <strong>دعم العربية RTL:</strong> ${summary.testCategories.arabicRtl} اختبار
        </div>
        <div class="category">
            <strong>الأداء:</strong> ${summary.testCategories.performance} اختبار
        </div>
        <div class="category">
            <strong>التصميم المتجاوب:</strong> ${summary.testCategories.responsive} اختبار
        </div>
    </div>

    <div class="environment">
        <h2>بيئة الاختبار</h2>
        <p><strong>الرابط الأساسي:</strong> ${summary.environment.baseUrl}</p>
        <p><strong>إصدار Node.js:</strong> ${summary.environment.nodeVersion}</p>
        <p><strong>المنصة:</strong> ${summary.environment.platform}</p>
        <p><strong>مدة التشغيل:</strong> ${(summary.duration / 1000).toFixed(2)} ثانية</p>
    </div>
</body>
</html>`;
}

/**
 * Archive test artifacts
 */
async function archiveTestArtifacts() {
  const testResultsDir = path.join(process.cwd(), 'test-results');
  const archiveDir = path.join(testResultsDir, 'archive', new Date().toISOString().split('T')[0]);

  if (!fs.existsSync(archiveDir)) {
    fs.mkdirSync(archiveDir, { recursive: true });
  }

  // Move screenshots and videos to archive
  const files = fs.readdirSync(testResultsDir);
  
  for (const file of files) {
    if (file.endsWith('.png') || file.endsWith('.webm') || file.endsWith('.mp4')) {
      const sourcePath = path.join(testResultsDir, file);
      const destPath = path.join(archiveDir, file);
      
      try {
        fs.renameSync(sourcePath, destPath);
      } catch (error) {
        console.warn(`Failed to archive ${file}:`, error);
      }
    }
  }

  console.log(`📁 Test artifacts archived to: ${archiveDir}`);
}

export default globalTeardown;
