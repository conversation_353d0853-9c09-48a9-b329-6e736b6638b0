# Supabase Connection Pooling Configuration

This document describes the connection pooling implementation for the eWasl application, designed to optimize database performance and scalability.

## Overview

The connection pooling system provides:
- **Supavisor Integration**: Support for Supabase's native connection pooler
- **Custom Pool Management**: Configurable connection pool settings
- **Performance Monitoring**: Real-time metrics and health checks
- **Production Optimization**: Optimized for Vercel and serverless environments

## Environment Variables

### Core Configuration

```bash
# Enable Supabase connection pooling
SUPABASE_ENABLE_POOLING=true

# Supavisor pooling port (default: 6543)
SUPABASE_POOLING_PORT=6543

# Connection pool size
SUPABASE_POOL_MAX=20          # Maximum connections
SUPABASE_POOL_MIN=2           # Minimum connections

# Timeout configuration
SUPABASE_CONNECTION_TIMEOUT=5000    # Connection timeout (ms)
SUPABASE_IDLE_TIMEOUT=30000         # Idle timeout (ms)
SUPABASE_MAX_LIFETIME=3600          # Max connection lifetime (seconds)

# Query timeouts
SUPABASE_STATEMENT_TIMEOUT=30s      # Statement timeout
SUPABASE_LOCK_TIMEOUT=10s           # Lock timeout
SUPABASE_IDLE_TRANSACTION_TIMEOUT=60s # Idle transaction timeout

# Retry configuration
SUPABASE_RETRY_ATTEMPTS=3           # Number of retry attempts
SUPABASE_RETRY_DELAY=1000           # Retry delay (ms)
SUPABASE_RETRY_BACKOFF=true         # Enable exponential backoff

# Monitoring
SUPABASE_ENABLE_METRICS=true        # Enable metrics collection
SUPABASE_METRICS_INTERVAL=60000     # Metrics collection interval (ms)
```

### Production Recommendations

For **Vercel Production**:
```bash
SUPABASE_ENABLE_POOLING=true
SUPABASE_POOL_MAX=20
SUPABASE_POOL_MIN=2
SUPABASE_CONNECTION_TIMEOUT=5000
SUPABASE_IDLE_TIMEOUT=30000
SUPABASE_ENABLE_METRICS=true
```

For **High Traffic** (>1000 concurrent users):
```bash
SUPABASE_POOL_MAX=50
SUPABASE_POOL_MIN=5
SUPABASE_CONNECTION_TIMEOUT=3000
SUPABASE_IDLE_TIMEOUT=20000
```

For **Development**:
```bash
SUPABASE_ENABLE_POOLING=false
SUPABASE_POOL_MAX=5
SUPABASE_POOL_MIN=1
SUPABASE_ENABLE_METRICS=false
```

## Implementation Details

### Connection Pool Manager

The `ConnectionPoolManager` class handles:
- Pool configuration and lifecycle
- Supavisor integration
- Metrics collection
- Health monitoring

### Client Configuration

All Supabase clients automatically use connection pooling:

```typescript
// Server-side client with pooling
const supabase = createClient();

// Service role client with pooling
const supabaseAdmin = createServiceRoleClient();
```

### Health Monitoring

Access connection pool health at:
```
GET /api/health/connection-pool
```

Response includes:
- Pool status and metrics
- Database connectivity
- Query performance
- Configuration details

## Supavisor Integration

### What is Supavisor?

Supavisor is Supabase's native connection pooler that:
- Reduces connection overhead
- Improves scalability
- Handles connection lifecycle
- Provides transaction-level pooling

### Enabling Supavisor

1. **In Supabase Dashboard**:
   - Go to Settings → Database
   - Enable "Connection Pooling"
   - Note the pooling port (usually 6543)

2. **In Application**:
   ```bash
   SUPABASE_ENABLE_POOLING=true
   SUPABASE_POOLING_PORT=6543
   ```

3. **Connection String**:
   - Standard: `postgresql://postgres:[password]@[project].supabase.co:5432/postgres`
   - Pooled: `postgresql://postgres:[password]@[project].pooler.supabase.com:6543/postgres`

## Performance Benefits

### Before Connection Pooling
- Each request creates new database connection
- High connection overhead
- Limited concurrent connections
- Potential connection exhaustion

### After Connection Pooling
- Reused connections across requests
- Reduced connection overhead
- Better resource utilization
- Improved scalability

### Expected Improvements
- **Response Time**: 20-40% faster database queries
- **Throughput**: 2-3x more concurrent requests
- **Resource Usage**: 50% less connection overhead
- **Stability**: Better handling of traffic spikes

## Monitoring and Debugging

### Health Check Endpoint

```bash
curl https://your-app.vercel.app/api/health/connection-pool
```

### Key Metrics

- `totalConnections`: Total pool connections
- `activeConnections`: Currently active connections
- `idleConnections`: Available idle connections
- `waitingClients`: Queued connection requests
- `averageQueryTime`: Average query response time
- `errorRate`: Connection error percentage

### Troubleshooting

**High Error Rate**:
- Check database connectivity
- Verify Supabase credentials
- Review connection limits

**Slow Queries**:
- Monitor `averageQueryTime`
- Check database performance
- Review query optimization

**Connection Exhaustion**:
- Increase `SUPABASE_POOL_MAX`
- Reduce `SUPABASE_IDLE_TIMEOUT`
- Check for connection leaks

## Best Practices

### Configuration
1. Start with conservative pool sizes
2. Monitor metrics and adjust gradually
3. Use shorter timeouts for serverless
4. Enable metrics in production

### Development
1. Use smaller pool sizes locally
2. Test with realistic connection loads
3. Monitor health endpoints
4. Profile query performance

### Production
1. Enable Supavisor for best performance
2. Set appropriate pool limits
3. Monitor connection metrics
4. Implement proper error handling

## Migration Guide

### From No Pooling

1. Add environment variables:
   ```bash
   SUPABASE_ENABLE_POOLING=true
   SUPABASE_POOL_MAX=20
   SUPABASE_POOL_MIN=2
   ```

2. Deploy and monitor health endpoint

3. Gradually increase pool size if needed

### Testing

1. Load test with connection pooling enabled
2. Monitor `/api/health/connection-pool`
3. Compare performance metrics
4. Adjust configuration as needed

## Support

For issues with connection pooling:
1. Check health endpoint for diagnostics
2. Review application logs
3. Monitor Supabase dashboard
4. Adjust pool configuration

The connection pooling system is designed to be self-managing and should improve performance automatically once properly configured.
