import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Test authentication only
export async function GET(request: NextRequest) {
  console.log('🧪 Testing authentication...');
  
  try {
    console.log('🔐 Attempting to get authenticated user...');
    const { user, supabase } = await getAuthenticatedUser(request);
    
    console.log('✅ Authentication successful');
    console.log('👤 User ID:', user.id);
    console.log('📧 User email:', user.email);
    
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        created_at: user.created_at
      }
    });

  } catch (error: any) {
    console.error('❌ Authentication test failed:', error);
    console.error('❌ Error message:', error.message);
    console.error('❌ Error stack:', error.stack);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 401 });
  }
}
