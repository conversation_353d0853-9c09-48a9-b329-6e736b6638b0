/**
 * Facebook OAuth Authorization Endpoint
 * Consistent OAuth endpoint for Facebook authentication
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getAuthenticatedUser } from '@/lib/auth/api-auth'
import crypto from 'crypto'

export async function GET(request: NextRequest) {
  console.log('🔥 Facebook OAuth endpoint called - START');
  try {
    // Get authenticated user from session
    const { user, supabase } = await getAuthenticatedUser(request);

    if (!user?.id) {
      return NextResponse.json(
        { error: 'المستخدم غير مصرح له' }, // User not authenticated in Arabic
        { status: 401 }
      )
    }

    const userId = user.id;

    // Define redirect URI for consistent OAuth flow
    // Prioritize production URLs over temporary Vercel URLs
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL ||
                   process.env.NEXTAUTH_URL ||
                   'https://app.ewasl.com';

    const redirectUri = `${baseUrl}/api/oauth/facebook/callback`;

    console.log('🔍 OAuth Debug Info:', {
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      VERCEL_URL: process.env.VERCEL_URL,
      baseUrl,
      redirectUri
    });

    // Verify Facebook app configuration
    const clientId = process.env.FACEBOOK_APP_ID;
    if (!clientId) {
      return NextResponse.json(
        { error: 'معرف تطبيق فيسبوك غير مكون' }, // Facebook App ID not configured in Arabic
        { status: 500 }
      )
    }

    // Generate OAuth state using direct database operations
    let state: string;
    try {
      // Generate secure random state
      state = crypto.randomBytes(32).toString('hex');

      // Store OAuth state directly in database
      const { error: stateError } = await supabase
        .from('oauth_states')
        .insert({
          user_id: userId,
          platform: 'FACEBOOK',
          state_token: state,
          redirect_uri: redirectUri,
          expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes
        })

      if (stateError) {
        console.error('Failed to create OAuth state:', stateError)
        return NextResponse.json(
          { error: 'فشل في إنشاء حالة OAuth' }, // Failed to create OAuth state in Arabic
          { status: 500 }
        )
      }

      console.log('Facebook OAuth state created successfully:', state)

    } catch (dbError) {
      console.error('Database operation failed:', dbError)
      return NextResponse.json(
        { error: 'فشل في عملية قاعدة البيانات' }, // Database operation failed in Arabic
        { status: 500 }
      )
    }

    // Facebook OAuth scopes (User OAuth - NOT Business OAuth)
    const scopes = [
      'pages_show_list',
      'pages_read_engagement',
      'pages_manage_posts',
      'pages_read_user_content',
      'instagram_basic',
      'instagram_content_publish'
      // REMOVED: 'business_management' - Triggers Business OAuth flow
    ].join(',');

    // Build Facebook OAuth URL directly
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      scope: scopes,
      response_type: 'code',
      state: state,
    });

    const authUrl = `https://www.facebook.com/v18.0/dialog/oauth?${params.toString()}`;

    // Return the authorization URL for client-side redirect
    return NextResponse.json({
      authUrl,
      state,
      platform: 'facebook'
    })

  } catch (error) {
    console.error('Facebook OAuth initiation error:', error)
    
    return NextResponse.json(
      { 
        error: 'فشل في بدء OAuth', // OAuth initiation failed in Arabic
        message: error instanceof Error ? error.message : 'خطأ غير معروف' // Unknown error in Arabic
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  // Redirect POST requests to GET for OAuth initiation
  return GET(request)
}
