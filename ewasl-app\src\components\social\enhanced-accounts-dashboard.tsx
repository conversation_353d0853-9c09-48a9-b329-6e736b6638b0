"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal,
  RefreshCw,
  Settings,
  Users,
  AlertTriangle,
  CheckCircle,
  Grid3X3,
  List,
  Download,
  Upload
} from 'lucide-react';
import { EnhancedAccountCard } from './enhanced-account-card';
import ConnectAccountButton from './connect-account-button';
import {
  EnhancedSocialAccount,
  SocialAccountsFilter,
  ConnectionStatus,
  SocialPlatform,
  BulkOperation
} from '@/types/social-enhanced';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface EnhancedAccountsDashboardProps {
  language: 'ar' | 'en';
}

const platformOptions: { value: SocialPlatform; label: { ar: string; en: string } }[] = [
  { value: 'FACEBOOK', label: { ar: 'فيسبوك', en: 'Facebook' } },
  { value: 'INSTAGRAM', label: { ar: 'إنستغرام', en: 'Instagram' } },
  { value: 'TWITTER', label: { ar: 'تويتر', en: 'Twitter' } },
  { value: 'LINKEDIN', label: { ar: 'لينكد إن', en: 'LinkedIn' } },
  { value: 'TIKTOK', label: { ar: 'تيك توك', en: 'TikTok' } },
  { value: 'YOUTUBE', label: { ar: 'يوتيوب', en: 'YouTube' } },
];

const statusOptions: { value: ConnectionStatus; label: { ar: string; en: string } }[] = [
  { value: 'connected', label: { ar: 'متصل', en: 'Connected' } },
  { value: 'expired', label: { ar: 'منتهي الصلاحية', en: 'Expired' } },
  { value: 'error', label: { ar: 'خطأ', en: 'Error' } },
  { value: 'disconnected', label: { ar: 'غير متصل', en: 'Disconnected' } },
];

export function EnhancedAccountsDashboard({ language }: EnhancedAccountsDashboardProps) {
  const [accounts, setAccounts] = useState<EnhancedSocialAccount[]>([]);
  const [selectedAccounts, setSelectedAccounts] = useState<string[]>([]);
  const [filter, setFilter] = useState<SocialAccountsFilter>({});
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoading, setIsLoading] = useState(true);
  const [bulkOperation, setBulkOperation] = useState<BulkOperation | null>(null);

  // Load accounts data
  useEffect(() => {
    loadAccounts();
  }, []);

  const loadAccounts = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/social/accounts/enhanced');
      const data = await response.json();

      if (response.ok) {
        setAccounts(data.accounts || []);
      } else {
        toast.error(language === 'ar' ? 'فشل في تحميل الحسابات' : 'Failed to load accounts');
      }
    } catch (error) {
      console.error('Error loading accounts:', error);
      toast.error(language === 'ar' ? 'خطأ في تحميل الحسابات' : 'Error loading accounts');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter accounts based on current filters
  const filteredAccounts = useMemo(() => {
    return accounts.filter(account => {
      if (filter.platform && account.platform !== filter.platform) return false;
      if (filter.status && account.connectionStatus !== filter.status) return false;
      if (filter.search) {
        const searchLower = filter.search.toLowerCase();
        return (
          account.accountName.toLowerCase().includes(searchLower) ||
          account.platform.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  }, [accounts, filter]);

  // Handle account selection
  const handleAccountSelect = (accountId: string, selected: boolean) => {
    setSelectedAccounts(prev => 
      selected 
        ? [...prev, accountId]
        : prev.filter(id => id !== accountId)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedAccounts(selected ? filteredAccounts.map(a => a.id) : []);
  };

  // Handle bulk operations
  const handleBulkOperation = async (operation: BulkOperation) => {
    if (selectedAccounts.length === 0) {
      toast.error(language === 'ar' ? 'يرجى اختيار حساب واحد على الأقل' : 'Please select at least one account');
      return;
    }

    setBulkOperation(operation);
    
    try {
      const response = await fetch('/api/social/accounts/bulk-operation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...operation,
          accountIds: selectedAccounts
        })
      });

      if (response.ok) {
        toast.success(language === 'ar' ? 'تم تنفيذ العملية بنجاح' : 'Operation completed successfully');
        loadAccounts();
        setSelectedAccounts([]);
      } else {
        toast.error(language === 'ar' ? 'فشل في تنفيذ العملية' : 'Operation failed');
      }
    } catch (error) {
      console.error('Bulk operation error:', error);
      toast.error(language === 'ar' ? 'خطأ في تنفيذ العملية' : 'Operation error');
    } finally {
      setBulkOperation(null);
    }
  };

  // Account actions
  const handleRefresh = () => {
    loadAccounts();
  };

  const handleRefreshAccount = async (accountId: string) => {
    try {
      const response = await fetch(`/api/social/accounts/${accountId}/refresh`, {
        method: 'POST'
      });
      
      if (response.ok) {
        toast.success(language === 'ar' ? 'تم تحديث الحساب' : 'Account refreshed');
        loadAccounts();
      } else {
        toast.error(language === 'ar' ? 'فشل في تحديث الحساب' : 'Failed to refresh account');
      }
    } catch (error) {
      console.error('Refresh error:', error);
      toast.error(language === 'ar' ? 'خطأ في التحديث' : 'Refresh error');
    }
  };

  const handleDisconnectAccount = async (accountId: string) => {
    try {
      const response = await fetch(`/api/social/accounts/${accountId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        toast.success(language === 'ar' ? 'تم قطع اتصال الحساب' : 'Account disconnected');
        loadAccounts();
      } else {
        toast.error(language === 'ar' ? 'فشل في قطع الاتصال' : 'Failed to disconnect');
      }
    } catch (error) {
      console.error('Disconnect error:', error);
      toast.error(language === 'ar' ? 'خطأ في قطع الاتصال' : 'Disconnect error');
    }
  };

  const handleAccountSettings = (accountId: string) => {
    // Navigate to account settings
    window.location.href = `/social/accounts/${accountId}/settings`;
  };

  // Statistics
  const stats = useMemo(() => {
    const total = accounts.length;
    const connected = accounts.filter(a => a.connectionStatus === 'connected').length;
    const expired = accounts.filter(a => a.connectionStatus === 'expired').length;
    const errors = accounts.filter(a => a.connectionStatus === 'error').length;

    return { total, connected, expired, errors };
  }, [accounts]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">
            {language === 'ar' ? 'إدارة الحسابات الاجتماعية' : 'Social Accounts Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إدارة وتنظيم حساباتك على منصات التواصل الاجتماعي' 
              : 'Manage and organize your social media accounts'
            }
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button onClick={loadAccounts} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
          <ConnectAccountButton
            onAccountConnected={handleRefresh}
            className="h-8"
          />
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'إجمالي الحسابات' : 'Total Accounts'}
                </p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'متصل' : 'Connected'}
                </p>
                <p className="text-2xl font-bold text-green-600">{stats.connected}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'منتهي الصلاحية' : 'Expired'}
                </p>
                <p className="text-2xl font-bold text-yellow-600">{stats.expired}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <div>
                <p className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'أخطاء' : 'Errors'}
                </p>
                <p className="text-2xl font-bold text-red-600">{stats.errors}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في الحسابات...' : 'Search accounts...'}
                  value={filter.search || ''}
                  onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Platform Filter */}
            <Select
              value={filter.platforms?.[0] || 'all'}
              onValueChange={(value) => 
                setFilter(prev => ({ 
                  ...prev, 
                  platforms: value === 'all' ? undefined : [value as SocialPlatform]
                }))
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'المنصة' : 'Platform'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {language === 'ar' ? 'جميع المنصات' : 'All Platforms'}
                </SelectItem>
                {platformOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label[language]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Status Filter */}
            <Select
              value={filter.status?.[0] || 'all'}
              onValueChange={(value) => 
                setFilter(prev => ({ 
                  ...prev, 
                  status: value === 'all' ? undefined : [value as ConnectionStatus]
                }))
              }
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'الحالة' : 'Status'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {language === 'ar' ? 'جميع الحالات' : 'All Status'}
                </SelectItem>
                {statusOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label[language]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* View Mode Toggle */}
            <div className="flex items-center gap-1 border rounded-md p-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="h-8 w-8 p-0"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="h-8 w-8 p-0"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedAccounts.length > 0 && (
        <Card className="border-blue-200 bg-blue-50/50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Checkbox
                  checked={selectedAccounts.length === filteredAccounts.length}
                  onCheckedChange={handleSelectAll}
                />
                <span className="font-medium">
                  {selectedAccounts.length} {language === 'ar' ? 'حساب محدد' : 'accounts selected'}
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkOperation({ type: 'refresh', accountIds: selectedAccounts })}
                  disabled={!!bulkOperation}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'تحديث' : 'Refresh'}
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkOperation({ type: 'validate', accountIds: selectedAccounts })}
                  disabled={!!bulkOperation}
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'التحقق' : 'Validate'}
                </Button>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => handleBulkOperation({ type: 'disconnect', accountIds: selectedAccounts })}>
                      {language === 'ar' ? 'قطع الاتصال' : 'Disconnect'}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Download className="h-4 w-4 mr-2" />
                      {language === 'ar' ? 'تصدير' : 'Export'}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Accounts Grid/List */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredAccounts.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {language === 'ar' ? 'لا توجد حسابات' : 'No accounts found'}
            </h3>
            <p className="text-muted-foreground mb-4">
              {language === 'ar' 
                ? 'ابدأ بإضافة حساباتك على منصات التواصل الاجتماعي'
                : 'Start by adding your social media accounts'
              }
            </p>
            <ConnectAccountButton
              onAccountConnected={handleRefresh}
            />
          </CardContent>
        </Card>
      ) : (
        <div className={cn(
          viewMode === 'grid' 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
            : "space-y-4"
        )}>
          {filteredAccounts.map((account) => (
            <EnhancedAccountCard
              key={account.id}
              account={account}
              isSelected={selectedAccounts.includes(account.id)}
              onSelect={handleAccountSelect}
              onRefresh={handleRefreshAccount}
              onDisconnect={handleDisconnectAccount}
              onSettings={handleAccountSettings}
              language={language}
            />
          ))}
        </div>
      )}
    </div>
  );
}
