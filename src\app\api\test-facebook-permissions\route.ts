import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * Facebook Permissions Diagnostic Endpoint
 * Checks Facebook app permissions and scopes against required permissions
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Required permissions for eWasl functionality
    const requiredPermissions = [
      'pages_manage_posts',
      'pages_read_engagement',
      'pages_show_list',
      'publish_to_groups',
      'instagram_basic',
      'instagram_content_publish'
    ];

    // Get all connected Facebook accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('platform', 'facebook')
      .eq('is_active', true);

    if (accountsError) {
      return NextResponse.json({
        success: false,
        error: 'فشل في جلب الحسابات المتصلة',
        details: accountsError.message,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    if (!accounts || accounts.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'لا توجد حسابات فيسبوك متصلة للفحص',
        accounts: [],
        timestamp: new Date().toISOString()
      });
    }

    const results = [];
    let totalPermissionIssues = 0;

    for (const account of accounts) {
      const result = {
        account_id: account.id,
        platform_account_id: account.platform_account_id,
        account_name: account.account_name,
        permissions_status: 'unknown',
        granted_permissions: [],
        missing_permissions: [],
        permission_issues: [],
        can_publish_posts: false,
        can_manage_pages: false,
        can_access_instagram: false,
        error: null
      };

      try {
        // Check permissions using Facebook Graph API
        const permissionsResponse = await fetch(
          `https://graph.facebook.com/me/permissions?access_token=${account.access_token}`
        );

        if (!permissionsResponse.ok) {
          const errorData = await permissionsResponse.json();
          result.error = `فشل في جلب الصلاحيات: ${errorData.error?.message || 'خطأ غير معروف'}`;
          result.permissions_status = 'error';
          totalPermissionIssues++;
        } else {
          const permissionsData = await permissionsResponse.json();
          const grantedPermissions = permissionsData.data
            .filter((perm: any) => perm.status === 'granted')
            .map((perm: any) => perm.permission);

          result.granted_permissions = grantedPermissions;
          result.missing_permissions = requiredPermissions.filter(
            perm => !grantedPermissions.includes(perm)
          );

          // Check specific capabilities
          result.can_publish_posts = grantedPermissions.includes('pages_manage_posts');
          result.can_manage_pages = grantedPermissions.includes('pages_show_list');
          result.can_access_instagram = grantedPermissions.includes('instagram_basic');

          // Identify permission issues
          if (result.missing_permissions.length > 0) {
            result.permission_issues = result.missing_permissions.map(perm => {
              switch (perm) {
                case 'pages_manage_posts':
                  return 'لا يمكن نشر المنشورات على الصفحات';
                case 'pages_read_engagement':
                  return 'لا يمكن قراءة إحصائيات التفاعل';
                case 'pages_show_list':
                  return 'لا يمكن عرض قائمة الصفحات';
                case 'publish_to_groups':
                  return 'لا يمكن النشر في المجموعات';
                case 'instagram_basic':
                  return 'لا يمكن الوصول إلى إنستغرام';
                case 'instagram_content_publish':
                  return 'لا يمكن النشر على إنستغرام';
                default:
                  return `صلاحية مفقودة: ${perm}`;
              }
            });
            totalPermissionIssues++;
          }

          result.permissions_status = result.missing_permissions.length === 0 ? 'complete' : 'incomplete';
        }

        // Test Pages API access
        try {
          const pagesResponse = await fetch(
            `https://graph.facebook.com/me/accounts?access_token=${account.access_token}`
          );
          
          if (pagesResponse.ok) {
            const pagesData = await pagesResponse.json();
            result.can_manage_pages = pagesData.data && pagesData.data.length > 0;
          }
        } catch (pagesError) {
          // Pages API test failed, but don't fail the entire check
        }

      } catch (error) {
        result.error = `خطأ في فحص الصلاحيات: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`;
        result.permissions_status = 'error';
        totalPermissionIssues++;
      }

      results.push(result);
    }

    // Generate recommendations
    const recommendations = [];
    if (totalPermissionIssues > 0) {
      recommendations.push('يوجد مشاكل في صلاحيات بعض الحسابات');
      recommendations.push('يرجى إعادة ربط الحسابات المتأثرة مع منح جميع الصلاحيات المطلوبة');
      recommendations.push('تأكد من أن تطبيق فيسبوك لديه الصلاحيات المطلوبة في Facebook Developer Console');
      recommendations.push('راجع إعدادات التطبيق في https://developers.facebook.com/apps/');
    } else {
      recommendations.push('جميع الحسابات لديها الصلاحيات المطلوبة');
    }

    return NextResponse.json({
      success: true,
      message: 'تم فحص صلاحيات فيسبوك بنجاح',
      summary: {
        total_accounts: accounts.length,
        accounts_with_issues: totalPermissionIssues,
        healthy_accounts: accounts.length - totalPermissionIssues,
        required_permissions: requiredPermissions
      },
      accounts: results,
      recommendations,
      app_permissions_check: {
        app_id: process.env.FACEBOOK_APP_ID,
        app_secret_configured: !!process.env.FACEBOOK_APP_SECRET,
        business_id_configured: !!process.env.FACEBOOK_BUSINESS_ID
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Facebook permissions test error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم الداخلي أثناء فحص الصلاحيات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}