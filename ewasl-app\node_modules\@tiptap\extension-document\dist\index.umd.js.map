{"version": 3, "file": "index.umd.js", "sources": ["../src/document.ts"], "sourcesContent": ["import { Node } from '@tiptap/core'\n\n/**\n * The default document node which represents the top level node of the editor.\n * @see https://tiptap.dev/api/nodes/document\n */\nexport const Document = Node.create({\n  name: 'doc',\n  topNode: true,\n  content: 'block+',\n})\n"], "names": ["Node"], "mappings": ";;;;;;EAEA;;;EAGG;AACU,QAAA,QAAQ,GAAGA,SAAI,CAAC,MAAM,CAAC;EAClC,IAAA,IAAI,EAAE,KAAK;EACX,IAAA,OAAO,EAAE,IAAI;EACb,IAAA,OAAO,EAAE,QAAQ;EAClB,CAAA;;;;;;;;;;;"}