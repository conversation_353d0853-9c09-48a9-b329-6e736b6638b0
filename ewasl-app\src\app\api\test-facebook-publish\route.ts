import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get the Facebook account with page tokens
    const { data: accounts, error: accountError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('platform', 'FACEBOOK')
      .eq('connection_status', 'connected')
      .not('page_id', 'is', null)
      .not('page_access_token', 'is', null)
      .limit(1);

    if (accountError) {
      return NextResponse.json({
        success: false,
        error: 'Database error',
        details: accountError.message
      }, { status: 500 });
    }

    if (!accounts || accounts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No Facebook accounts with page tokens found',
        message: 'Please reconnect your Facebook account to populate page tokens'
      }, { status: 400 });
    }

    const account = accounts[0];
    
    // Test Facebook Graph API call
    const testContent = '🧪 اختبار نشر مباشر من API\n\nهذا اختبار للتحقق من أن رموز الصفحة تعمل بشكل صحيح.\n\n#اختبار #إي_وصل #فيسبوك';
    
    const facebookResponse = await fetch(`https://graph.facebook.com/v18.0/${account.page_id}/feed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: testContent,
        access_token: account.page_access_token
      })
    });

    const facebookData = await facebookResponse.json();

    if (!facebookResponse.ok) {
      return NextResponse.json({
        success: false,
        error: 'Facebook API error',
        facebook_error: facebookData,
        account_info: {
          account_id: account.account_id,
          account_name: account.account_name,
          page_id: account.page_id,
          page_name: account.page_name,
          has_page_token: !!account.page_access_token,
          page_token_length: account.page_access_token?.length || 0
        }
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: 'Facebook post published successfully!',
      facebook_response: facebookData,
      account_info: {
        account_id: account.account_id,
        account_name: account.account_name,
        page_id: account.page_id,
        page_name: account.page_name,
        has_page_token: !!account.page_access_token,
        page_token_length: account.page_access_token?.length || 0
      },
      post_url: `https://www.facebook.com/${facebookData.id}`
    });

  } catch (error) {
    console.error('Test Facebook publish error:', error);
    return NextResponse.json({
      success: false,
      error: 'Server error',
      details: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Use POST method to test Facebook publishing',
    endpoint: '/api/test-facebook-publish',
    method: 'POST',
    description: 'Tests direct Facebook Graph API publishing with page tokens'
  });
}
