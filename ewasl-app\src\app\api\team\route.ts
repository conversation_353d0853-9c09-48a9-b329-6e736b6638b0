import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Validation schemas
const inviteMemberSchema = z.object({
  email: z.string().email(),
  role: z.enum(['admin', 'editor', 'reviewer', 'viewer']),
  workspaceIds: z.array(z.string()).optional().default([]),
  message: z.string().optional()
});

const updateMemberSchema = z.object({
  memberId: z.string(),
  role: z.enum(['admin', 'editor', 'reviewer', 'viewer']).optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
  workspaceIds: z.array(z.string()).optional(),
  permissions: z.object({
    canCreatePosts: z.boolean().optional(),
    canEditPosts: z.boolean().optional(),
    canDeletePosts: z.boolean().optional(),
    canManageTeam: z.boolean().optional(),
    canViewAnalytics: z.boolean().optional(),
    canApproveContent: z.boolean().optional(),
    canManageWorkspaces: z.boolean().optional()
  }).optional()
});

const createWorkspaceSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i).default('#3b82f6'),
  isPrivate: z.boolean().default(false),
  memberIds: z.array(z.string()).optional().default([])
});

const createApprovalWorkflowSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  workspaceId: z.string().optional(),
  steps: z.array(z.object({
    name: z.string().min(1).max(100),
    approverIds: z.array(z.string()).min(1),
    requiredApprovals: z.number().min(1).default(1),
    autoApprove: z.boolean().default(false),
    timeoutHours: z.number().min(1).max(168).optional() // 1 hour to 1 week
  })).min(1).max(10)
});

// GET /api/team - Get team members and organization info
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    // Get user's membership in the organization
    const { data: membership, error: membershipError } = await supabase
      .from('organization_members')
      .select('role, status')
      .eq('organization_id', organizationId)
      .eq('user_id', user.id)
      .single();

    if (membershipError || !membership) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get all team members
    const { data: members, error: membersError } = await supabase
      .from('organization_members')
      .select(`
        id,
        user_id,
        role,
        status,
        permissions,
        joined_at,
        invited_by,
        users!inner(
          id,
          email,
          full_name,
          avatar_url
        )
      `)
      .eq('organization_id', organizationId);

    if (membersError) {
      return NextResponse.json(
        { error: 'Failed to fetch team members' },
        { status: 500 }
      );
    }

    // Get workspaces
    const { data: workspaces, error: workspacesError } = await supabase
      .from('workspaces')
      .select(`
        id,
        name,
        description,
        color,
        is_active,
        created_at,
        workspace_members(count)
      `)
      .eq('organization_id', organizationId);

    if (workspacesError) {
      return NextResponse.json(
        { error: 'Failed to fetch workspaces' },
        { status: 500 }
      );
    }

    // Get approval workflows
    const { data: approvalWorkflows, error: approvalError } = await supabase
      .from('approval_workflows')
      .select(`
        id,
        name,
        description,
        is_active,
        workspace_id,
        created_at,
        approval_steps(
          id,
          name,
          approver_ids,
          required_approvals,
          auto_approve,
          timeout_hours,
          step_order
        )
      `)
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false });

    if (approvalError) {
      return NextResponse.json(
        { error: 'Failed to fetch approval workflows' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        members: members?.map(member => ({
          id: member.id,
          userId: member.user_id,
          name: member.users.full_name,
          email: member.users.email,
          avatar: member.users.avatar_url,
          role: member.role,
          status: member.status,
          permissions: member.permissions,
          joinedAt: member.joined_at,
          invitedBy: member.invited_by
        })) || [],
        workspaces: workspaces?.map(workspace => ({
          id: workspace.id,
          name: workspace.name,
          description: workspace.description,
          color: workspace.color,
          isActive: workspace.is_active,
          memberCount: workspace.workspace_members?.[0]?.count || 0,
          createdAt: workspace.created_at
        })) || [],
        approvalWorkflows: approvalWorkflows?.map(workflow => ({
          id: workflow.id,
          name: workflow.name,
          description: workflow.description,
          isActive: workflow.is_active,
          workspaceId: workflow.workspace_id,
          createdAt: workflow.created_at,
          steps: workflow.approval_steps?.sort((a, b) => a.step_order - b.step_order).map(step => ({
            id: step.id,
            name: step.name,
            approverIds: step.approver_ids,
            requiredApprovals: step.required_approvals,
            autoApprove: step.auto_approve,
            timeoutHours: step.timeout_hours
          })) || []
        })) || [],
        currentUser: {
          id: user.id,
          role: membership.role,
          status: membership.status
        }
      }
    });

  } catch (error) {
    console.error('Team API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/team - Handle team operations
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, organizationId } = body;

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    // Check user permissions
    const { data: membership, error: membershipError } = await supabase
      .from('organization_members')
      .select('role, status')
      .eq('organization_id', organizationId)
      .eq('user_id', user.id)
      .single();

    if (membershipError || !membership) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    const canManageTeam = membership.role === 'owner' || membership.role === 'admin';
    const canManageWorkspaces = membership.role === 'owner' || membership.role === 'admin';

    switch (action) {
      case 'invite_member':
        return handleInviteMember(supabase, user, organizationId, body, canManageTeam);
      
      case 'update_member':
        return handleUpdateMember(supabase, user, organizationId, body, canManageTeam);
      
      case 'remove_member':
        return handleRemoveMember(supabase, user, organizationId, body, canManageTeam);
      
      case 'create_workspace':
        return handleCreateWorkspace(supabase, user, organizationId, body, canManageWorkspaces);
      
      case 'create_approval_workflow':
        return handleCreateApprovalWorkflow(supabase, user, organizationId, body, canManageWorkspaces);
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Team POST API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle member invitation
async function handleInviteMember(supabase: any, user: any, organizationId: string, body: any, canManageTeam: boolean) {
  if (!canManageTeam) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    );
  }

  const validation = inviteMemberSchema.safeParse(body);
  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid request data', details: validation.error.errors },
      { status: 400 }
    );
  }

  const { email, role, workspaceIds, message } = validation.data;

  try {
    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    // Create invitation
    const { data: invitation, error: inviteError } = await supabase
      .from('team_invitations')
      .insert({
        organization_id: organizationId,
        email,
        role,
        invited_by: user.id,
        message,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        status: 'pending'
      })
      .select()
      .single();

    if (inviteError) {
      return NextResponse.json(
        { error: 'Failed to create invitation' },
        { status: 500 }
      );
    }

    // TODO: Send invitation email

    return NextResponse.json({
      success: true,
      message: 'Invitation sent successfully',
      data: { invitationId: invitation.id }
    });

  } catch (error) {
    console.error('Invite member error:', error);
    return NextResponse.json(
      { error: 'Failed to invite member' },
      { status: 500 }
    );
  }
}

// Handle member update
async function handleUpdateMember(supabase: any, user: any, organizationId: string, body: any, canManageTeam: boolean) {
  if (!canManageTeam) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    );
  }

  const validation = updateMemberSchema.safeParse(body);
  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid request data', details: validation.error.errors },
      { status: 400 }
    );
  }

  const { memberId, role, status, permissions } = validation.data;

  try {
    const updateData: any = {};
    if (role) updateData.role = role;
    if (status) updateData.status = status;
    if (permissions) updateData.permissions = permissions;
    updateData.updated_at = new Date().toISOString();

    const { error: updateError } = await supabase
      .from('organization_members')
      .update(updateData)
      .eq('id', memberId)
      .eq('organization_id', organizationId);

    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to update member' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Member updated successfully'
    });

  } catch (error) {
    console.error('Update member error:', error);
    return NextResponse.json(
      { error: 'Failed to update member' },
      { status: 500 }
    );
  }
}

// Handle member removal
async function handleRemoveMember(supabase: any, user: any, organizationId: string, body: any, canManageTeam: boolean) {
  if (!canManageTeam) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    );
  }

  const { memberId } = body;

  if (!memberId) {
    return NextResponse.json(
      { error: 'Member ID is required' },
      { status: 400 }
    );
  }

  try {
    // Remove member from organization
    const { error: removeError } = await supabase
      .from('organization_members')
      .delete()
      .eq('id', memberId)
      .eq('organization_id', organizationId);

    if (removeError) {
      return NextResponse.json(
        { error: 'Failed to remove member' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Member removed successfully'
    });

  } catch (error) {
    console.error('Remove member error:', error);
    return NextResponse.json(
      { error: 'Failed to remove member' },
      { status: 500 }
    );
  }
}

// Handle workspace creation
async function handleCreateWorkspace(supabase: any, user: any, organizationId: string, body: any, canManageWorkspaces: boolean) {
  if (!canManageWorkspaces) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    );
  }

  const validation = createWorkspaceSchema.safeParse(body);
  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid request data', details: validation.error.errors },
      { status: 400 }
    );
  }

  const { name, description, color, isPrivate, memberIds } = validation.data;

  try {
    // Create workspace
    const { data: workspace, error: workspaceError } = await supabase
      .from('workspaces')
      .insert({
        organization_id: organizationId,
        name,
        description,
        color,
        is_private: isPrivate,
        created_by: user.id,
        is_active: true
      })
      .select()
      .single();

    if (workspaceError) {
      return NextResponse.json(
        { error: 'Failed to create workspace' },
        { status: 500 }
      );
    }

    // Add members to workspace
    if (memberIds.length > 0) {
      const memberInserts = memberIds.map(memberId => ({
        workspace_id: workspace.id,
        user_id: memberId,
        role: 'member'
      }));

      await supabase
        .from('workspace_members')
        .insert(memberInserts);
    }

    return NextResponse.json({
      success: true,
      message: 'Workspace created successfully',
      data: { workspaceId: workspace.id }
    });

  } catch (error) {
    console.error('Create workspace error:', error);
    return NextResponse.json(
      { error: 'Failed to create workspace' },
      { status: 500 }
    );
  }
}

// Handle approval workflow creation
async function handleCreateApprovalWorkflow(supabase: any, user: any, organizationId: string, body: any, canManageWorkspaces: boolean) {
  if (!canManageWorkspaces) {
    return NextResponse.json(
      { error: 'Insufficient permissions' },
      { status: 403 }
    );
  }

  const validation = createApprovalWorkflowSchema.safeParse(body);
  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid request data', details: validation.error.errors },
      { status: 400 }
    );
  }

  const { name, description, workspaceId, steps } = validation.data;

  try {
    // Create approval workflow
    const { data: workflow, error: workflowError } = await supabase
      .from('approval_workflows')
      .insert({
        organization_id: organizationId,
        workspace_id: workspaceId,
        name,
        description,
        created_by: user.id,
        is_active: true
      })
      .select()
      .single();

    if (workflowError) {
      return NextResponse.json(
        { error: 'Failed to create approval workflow' },
        { status: 500 }
      );
    }

    // Create approval steps
    const stepInserts = steps.map((step, index) => ({
      workflow_id: workflow.id,
      name: step.name,
      approver_ids: step.approverIds,
      required_approvals: step.requiredApprovals,
      auto_approve: step.autoApprove,
      timeout_hours: step.timeoutHours,
      step_order: index + 1
    }));

    const { error: stepsError } = await supabase
      .from('approval_steps')
      .insert(stepInserts);

    if (stepsError) {
      return NextResponse.json(
        { error: 'Failed to create approval steps' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Approval workflow created successfully',
      data: { workflowId: workflow.id }
    });

  } catch (error) {
    console.error('Create approval workflow error:', error);
    return NextResponse.json(
      { error: 'Failed to create approval workflow' },
      { status: 500 }
    );
  }
}
