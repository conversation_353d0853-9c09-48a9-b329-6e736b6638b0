-- Fix social_accounts table schema to support Facebook Pages and Instagram Business accounts
-- This addresses the missing fields causing the 500 error in publishing

-- Add missing columns to social_accounts table
ALTER TABLE social_accounts 
ADD COLUMN IF NOT EXISTS page_id TEXT,
ADD COLUMN IF NOT EXISTS page_access_token TEXT,
ADD COLUMN IF NOT EXISTS page_name TEXT,
ADD COLUMN IF NOT EXISTS instagram_business_account_id TEXT,
ADD COLUMN IF NOT EXISTS token_expires_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_validated_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS connection_status TEXT DEFAULT 'connected' CHECK (connection_status IN ('connected', 'expired', 'error', 'reconnecting', 'disconnected'));

-- Update existing records to have proper connection status
UPDATE social_accounts 
SET connection_status = 'connected' 
WHERE connection_status IS NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_social_accounts_user_platform ON social_accounts(user_id, platform);
CREATE INDEX IF NOT EXISTS idx_social_accounts_connection_status ON social_accounts(connection_status);
CREATE INDEX IF NOT EXISTS idx_social_accounts_page_id ON social_accounts(page_id) WHERE page_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_social_accounts_instagram_business ON social_accounts(instagram_business_account_id) WHERE instagram_business_account_id IS NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN social_accounts.page_id IS 'Facebook Page ID for business page posting';
COMMENT ON COLUMN social_accounts.page_access_token IS 'Page-specific access token for Facebook page posting';
COMMENT ON COLUMN social_accounts.page_name IS 'Display name of the Facebook page';
COMMENT ON COLUMN social_accounts.instagram_business_account_id IS 'Instagram Business Account ID linked to Facebook page';
COMMENT ON COLUMN social_accounts.connection_status IS 'Current status of the social media account connection';
COMMENT ON COLUMN social_accounts.last_validated_at IS 'Timestamp of last successful token validation';
