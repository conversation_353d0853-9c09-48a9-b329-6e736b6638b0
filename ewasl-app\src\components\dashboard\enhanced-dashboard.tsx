"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Eye,
  Heart,
  MessageCircle,
  Share,
  Users,
  Calendar,
  RefreshCw,
  Activity,
  BarChart3,
  Zap,
  Clock,
  Target,
  Globe
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface DashboardMetrics {
  overview: {
    totalPosts: number;
    totalEngagement: number;
    totalReach: number;
    totalImpressions: number;
    engagementRate: number;
    connectedAccounts: number;
    scheduledPosts: number;
    publishedToday: number;
  };
  trends: {
    postsGrowth: number;
    engagementGrowth: number;
    reachGrowth: number;
    impressionsGrowth: number;
  };
  platformBreakdown: Array<{
    platform: string;
    posts: number;
    engagement: number;
    reach: number;
    color: string;
  }>;
  engagementTrends: Array<{
    date: string;
    engagement: number;
    reach: number;
    impressions: number;
    posts: number;
  }>;
  topPerformingPosts: Array<{
    id: string;
    content: string;
    platform: string;
    engagement: number;
    reach: number;
    publishedAt: string;
  }>;
  realtimeActivity: Array<{
    id: string;
    type: 'post_published' | 'engagement_received' | 'account_connected' | 'post_scheduled';
    message: string;
    timestamp: string;
    platform?: string;
  }>;
}

interface EnhancedDashboardProps {
  className?: string;
  language?: 'ar' | 'en';
  refreshInterval?: number;
}

export function EnhancedDashboard({ 
  className, 
  language = 'ar',
  refreshInterval = 30000 // 30 seconds
}: EnhancedDashboardProps) {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedTab, setSelectedTab] = useState('overview');

  // Translations
  const t = {
    ar: {
      title: 'لوحة التحكم المحسنة',
      subtitle: 'نظرة شاملة على أداء وسائل التواصل الاجتماعي',
      overview: 'نظرة عامة',
      analytics: 'التحليلات',
      realtime: 'الوقت الفعلي',
      performance: 'الأداء',
      totalPosts: 'إجمالي المنشورات',
      totalEngagement: 'إجمالي التفاعل',
      totalReach: 'إجمالي الوصول',
      totalImpressions: 'إجمالي المشاهدات',
      engagementRate: 'معدل التفاعل',
      connectedAccounts: 'الحسابات المتصلة',
      scheduledPosts: 'المنشورات المجدولة',
      publishedToday: 'نُشر اليوم',
      platformBreakdown: 'توزيع المنصات',
      engagementTrends: 'اتجاهات التفاعل',
      topPosts: 'أفضل المنشورات',
      realtimeActivity: 'النشاط المباشر',
      refresh: 'تحديث',
      lastUpdated: 'آخر تحديث',
      live: 'مباشر',
      growth: 'النمو',
      thisWeek: 'هذا الأسبوع',
      thisMonth: 'هذا الشهر',
      last7Days: 'آخر 7 أيام',
      last30Days: 'آخر 30 يوم',
      last90Days: 'آخر 90 يوم'
    },
    en: {
      title: 'Enhanced Dashboard',
      subtitle: 'Comprehensive overview of your social media performance',
      overview: 'Overview',
      analytics: 'Analytics',
      realtime: 'Real-time',
      performance: 'Performance',
      totalPosts: 'Total Posts',
      totalEngagement: 'Total Engagement',
      totalReach: 'Total Reach',
      totalImpressions: 'Total Impressions',
      engagementRate: 'Engagement Rate',
      connectedAccounts: 'Connected Accounts',
      scheduledPosts: 'Scheduled Posts',
      publishedToday: 'Published Today',
      platformBreakdown: 'Platform Breakdown',
      engagementTrends: 'Engagement Trends',
      topPosts: 'Top Posts',
      realtimeActivity: 'Real-time Activity',
      refresh: 'Refresh',
      lastUpdated: 'Last Updated',
      live: 'Live',
      growth: 'Growth',
      thisWeek: 'This Week',
      thisMonth: 'This Month',
      last7Days: 'Last 7 Days',
      last30Days: 'Last 30 Days',
      last90Days: 'Last 90 Days'
    }
  };

  const text = t[language];

  // Fetch dashboard metrics
  const fetchMetrics = useCallback(async (showLoading = true) => {
    if (showLoading) setIsRefreshing(true);
    
    try {
      const response = await fetch(`/api/analytics/enhanced-dashboard?timeRange=${timeRange}`);
      
      if (response.ok) {
        const data = await response.json();
        setMetrics(data.metrics);
        setLastUpdated(new Date());
      } else {
        // Fallback to demo data
        setMetrics(generateDemoData());
      }
    } catch (error) {
      console.error('Failed to fetch dashboard metrics:', error);
      setMetrics(generateDemoData());
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [timeRange]);

  // Generate demo data for development
  const generateDemoData = (): DashboardMetrics => {
    const now = new Date();
    const days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    }).reverse();

    return {
      overview: {
        totalPosts: 156,
        totalEngagement: 12847,
        totalReach: 45623,
        totalImpressions: 78945,
        engagementRate: 8.2,
        connectedAccounts: 4,
        scheduledPosts: 12,
        publishedToday: 3
      },
      trends: {
        postsGrowth: 12.5,
        engagementGrowth: 18.3,
        reachGrowth: 15.7,
        impressionsGrowth: 22.1
      },
      platformBreakdown: [
        { platform: 'Instagram', posts: 45, engagement: 5234, reach: 18567, color: '#E4405F' },
        { platform: 'Facebook', posts: 38, engagement: 3892, reach: 15234, color: '#1877F2' },
        { platform: 'Twitter', posts: 42, engagement: 2456, reach: 8934, color: '#1DA1F2' },
        { platform: 'LinkedIn', posts: 31, engagement: 1265, reach: 2888, color: '#0A66C2' }
      ],
      engagementTrends: days.map((date, i) => ({
        date,
        engagement: Math.floor(Math.random() * 2000) + 1000,
        reach: Math.floor(Math.random() * 5000) + 3000,
        impressions: Math.floor(Math.random() * 8000) + 5000,
        posts: Math.floor(Math.random() * 5) + 1
      })),
      topPerformingPosts: [
        {
          id: '1',
          content: 'أفضل استراتيجيات التسويق الرقمي لعام 2024',
          platform: 'Instagram',
          engagement: 1234,
          reach: 5678,
          publishedAt: '2024-01-12T10:30:00Z'
        },
        {
          id: '2',
          content: 'نصائح لزيادة التفاعل على وسائل التواصل',
          platform: 'Facebook',
          engagement: 987,
          reach: 4321,
          publishedAt: '2024-01-11T14:15:00Z'
        }
      ],
      realtimeActivity: [
        {
          id: '1',
          type: 'post_published',
          message: language === 'ar' ? 'تم نشر منشور جديد على Instagram' : 'New post published on Instagram',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          platform: 'Instagram'
        },
        {
          id: '2',
          type: 'engagement_received',
          message: language === 'ar' ? 'تم تلقي 25 إعجاب جديد' : 'Received 25 new likes',
          timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString()
        }
      ]
    };
  };

  // Auto-refresh effect
  useEffect(() => {
    fetchMetrics();
    
    const interval = setInterval(() => {
      fetchMetrics(false); // Silent refresh
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [fetchMetrics, refreshInterval]);

  // Format numbers
  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  // Format percentage
  const formatPercentage = (num: number): string => `${num.toFixed(1)}%`;

  // Get trend icon and color
  const getTrendDisplay = (value: number) => {
    const isPositive = value > 0;
    return {
      icon: isPositive ? TrendingUp : TrendingDown,
      color: isPositive ? 'text-green-600' : 'text-red-600',
      bgColor: isPositive ? 'bg-green-50' : 'bg-red-50'
    };
  };

  if (isLoading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!metrics) return null;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        language === 'ar' ? "flex-row-reverse" : ""
      )}>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{text.title}</h1>
          <p className="text-gray-600 mt-1">{text.subtitle}</p>
        </div>

        <div className={cn(
          "flex items-center gap-4",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          {/* Time Range Selector */}
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">{text.last7Days}</SelectItem>
              <SelectItem value="30d">{text.last30Days}</SelectItem>
              <SelectItem value="90d">{text.last90Days}</SelectItem>
            </SelectContent>
          </Select>

          {/* Refresh Button */}
          <Button
            variant="outline"
            onClick={() => fetchMetrics()}
            disabled={isRefreshing}
            className={cn(
              "flex items-center gap-2",
              language === 'ar' ? "flex-row-reverse" : ""
            )}
          >
            <RefreshCw className={cn("w-4 h-4", isRefreshing && "animate-spin")} />
            {text.refresh}
          </Button>

          {/* Live Indicator */}
          <div className={cn(
            "flex items-center gap-2 text-sm text-gray-600",
            language === 'ar' ? "flex-row-reverse" : ""
          )}>
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>{text.live}</span>
          </div>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Posts */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className={cn(
              "flex items-center justify-between",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <div className={language === 'ar' ? "text-right" : ""}>
                <p className="text-sm text-gray-600">{text.totalPosts}</p>
                <p className="text-3xl font-bold text-gray-900">
                  {formatNumber(metrics.overview.totalPosts)}
                </p>
                <div className={cn(
                  "flex items-center gap-1 mt-2",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  {(() => {
                    const trend = getTrendDisplay(metrics.trends.postsGrowth);
                    const TrendIcon = trend.icon;
                    return (
                      <>
                        <TrendIcon className={cn("w-4 h-4", trend.color)} />
                        <span className={cn("text-sm", trend.color)}>
                          {formatPercentage(Math.abs(metrics.trends.postsGrowth))}
                        </span>
                      </>
                    );
                  })()}
                </div>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Engagement */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className={cn(
              "flex items-center justify-between",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <div className={language === 'ar' ? "text-right" : ""}>
                <p className="text-sm text-gray-600">{text.totalEngagement}</p>
                <p className="text-3xl font-bold text-gray-900">
                  {formatNumber(metrics.overview.totalEngagement)}
                </p>
                <div className={cn(
                  "flex items-center gap-1 mt-2",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  {(() => {
                    const trend = getTrendDisplay(metrics.trends.engagementGrowth);
                    const TrendIcon = trend.icon;
                    return (
                      <>
                        <TrendIcon className={cn("w-4 h-4", trend.color)} />
                        <span className={cn("text-sm", trend.color)}>
                          {formatPercentage(Math.abs(metrics.trends.engagementGrowth))}
                        </span>
                      </>
                    );
                  })()}
                </div>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Heart className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Reach */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className={cn(
              "flex items-center justify-between",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <div className={language === 'ar' ? "text-right" : ""}>
                <p className="text-sm text-gray-600">{text.totalReach}</p>
                <p className="text-3xl font-bold text-gray-900">
                  {formatNumber(metrics.overview.totalReach)}
                </p>
                <div className={cn(
                  "flex items-center gap-1 mt-2",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  {(() => {
                    const trend = getTrendDisplay(metrics.trends.reachGrowth);
                    const TrendIcon = trend.icon;
                    return (
                      <>
                        <TrendIcon className={cn("w-4 h-4", trend.color)} />
                        <span className={cn("text-sm", trend.color)}>
                          {formatPercentage(Math.abs(metrics.trends.reachGrowth))}
                        </span>
                      </>
                    );
                  })()}
                </div>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Engagement Rate */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className={cn(
              "flex items-center justify-between",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <div className={language === 'ar' ? "text-right" : ""}>
                <p className="text-sm text-gray-600">{text.engagementRate}</p>
                <p className="text-3xl font-bold text-gray-900">
                  {formatPercentage(metrics.overview.engagementRate)}
                </p>
                <div className={cn(
                  "flex items-center gap-1 mt-2",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <Badge variant="secondary" className="text-xs">
                    {text.thisWeek}
                  </Badge>
                </div>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Target className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">{text.overview}</TabsTrigger>
          <TabsTrigger value="analytics">{text.analytics}</TabsTrigger>
          <TabsTrigger value="realtime">{text.realtime}</TabsTrigger>
          <TabsTrigger value="performance">{text.performance}</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Engagement Trends Chart */}
            <Card>
              <CardHeader>
                <CardTitle className={language === 'ar' ? "text-right" : ""}>
                  {text.engagementTrends}
                </CardTitle>
                <CardDescription className={language === 'ar' ? "text-right" : ""}>
                  {language === 'ar' ? 'تطور التفاعل والوصول عبر الزمن' : 'Engagement and reach trends over time'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={metrics.engagementTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="engagement"
                      stackId="1"
                      stroke="#8884d8"
                      fill="#8884d8"
                      name={language === 'ar' ? 'التفاعل' : 'Engagement'}
                    />
                    <Area
                      type="monotone"
                      dataKey="reach"
                      stackId="2"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      name={language === 'ar' ? 'الوصول' : 'Reach'}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Platform Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className={language === 'ar' ? "text-right" : ""}>
                  {text.platformBreakdown}
                </CardTitle>
                <CardDescription className={language === 'ar' ? "text-right" : ""}>
                  {language === 'ar' ? 'توزيع المنشورات حسب المنصة' : 'Post distribution by platform'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={metrics.platformBreakdown}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ platform, posts }) => `${platform}: ${posts}`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="posts"
                    >
                      {metrics.platformBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Quick Stats Row */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className={cn(
                  "flex items-center gap-3",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Globe className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className={language === 'ar' ? "text-right" : ""}>
                    <p className="text-sm text-gray-600">{text.connectedAccounts}</p>
                    <p className="text-2xl font-bold">{metrics.overview.connectedAccounts}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className={cn(
                  "flex items-center gap-3",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <Clock className="w-5 h-5 text-yellow-600" />
                  </div>
                  <div className={language === 'ar' ? "text-right" : ""}>
                    <p className="text-sm text-gray-600">{text.scheduledPosts}</p>
                    <p className="text-2xl font-bold">{metrics.overview.scheduledPosts}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className={cn(
                  "flex items-center gap-3",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <Zap className="w-5 h-5 text-green-600" />
                  </div>
                  <div className={language === 'ar' ? "text-right" : ""}>
                    <p className="text-sm text-gray-600">{text.publishedToday}</p>
                    <p className="text-2xl font-bold">{metrics.overview.publishedToday}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className={cn(
                  "flex items-center gap-3",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Eye className="w-5 h-5 text-purple-600" />
                  </div>
                  <div className={language === 'ar' ? "text-right" : ""}>
                    <p className="text-sm text-gray-600">{text.totalImpressions}</p>
                    <p className="text-2xl font-bold">{formatNumber(metrics.overview.totalImpressions)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Real-time Tab */}
        <TabsContent value="realtime" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse text-right" : ""
              )}>
                <Activity className="w-5 h-5 text-green-500" />
                {text.realtimeActivity}
                <Badge variant="secondary" className="text-xs">
                  {text.live}
                </Badge>
              </CardTitle>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {language === 'ar' ? 'النشاط المباشر لحساباتك' : 'Live activity from your accounts'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {metrics.realtimeActivity.map((activity) => (
                  <div
                    key={activity.id}
                    className={cn(
                      "flex items-center gap-4 p-4 bg-gray-50 rounded-lg",
                      language === 'ar' ? "flex-row-reverse" : ""
                    )}
                  >
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <div className={cn("flex-1", language === 'ar' ? "text-right" : "")}>
                      <p className="font-medium text-gray-900">{activity.message}</p>
                      <p className="text-sm text-gray-500">
                        {new Date(activity.timestamp).toLocaleTimeString(
                          language === 'ar' ? 'ar-SA' : 'en-US'
                        )}
                      </p>
                    </div>
                    {activity.platform && (
                      <Badge variant="outline" className="text-xs">
                        {activity.platform}
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Performing Posts */}
            <Card>
              <CardHeader>
                <CardTitle className={language === 'ar' ? "text-right" : ""}>
                  {text.topPosts}
                </CardTitle>
                <CardDescription className={language === 'ar' ? "text-right" : ""}>
                  {language === 'ar' ? 'أفضل المنشورات أداءً' : 'Best performing posts'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics.topPerformingPosts.map((post) => (
                    <div
                      key={post.id}
                      className={cn(
                        "flex items-center gap-4 p-4 border rounded-lg",
                        language === 'ar' ? "flex-row-reverse" : ""
                      )}
                    >
                      <div className={cn("flex-1", language === 'ar' ? "text-right" : "")}>
                        <p className="font-medium text-gray-900 line-clamp-2">
                          {post.content}
                        </p>
                        <div className={cn(
                          "flex items-center gap-4 mt-2 text-sm text-gray-500",
                          language === 'ar' ? "flex-row-reverse" : ""
                        )}>
                          <span>{post.platform}</span>
                          <span>
                            {language === 'ar' ? 'التفاعل' : 'Engagement'}: {formatNumber(post.engagement)}
                          </span>
                          <span>
                            {language === 'ar' ? 'الوصول' : 'Reach'}: {formatNumber(post.reach)}
                          </span>
                        </div>
                      </div>
                      <Badge variant="secondary">
                        {formatNumber(post.engagement)}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Platform Performance */}
            <Card>
              <CardHeader>
                <CardTitle className={language === 'ar' ? "text-right" : ""}>
                  {language === 'ar' ? 'أداء المنصات' : 'Platform Performance'}
                </CardTitle>
                <CardDescription className={language === 'ar' ? "text-right" : ""}>
                  {language === 'ar' ? 'مقارنة أداء المنصات المختلفة' : 'Compare performance across platforms'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={metrics.platformBreakdown}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="platform" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar
                      dataKey="engagement"
                      fill="#8884d8"
                      name={language === 'ar' ? 'التفاعل' : 'Engagement'}
                    />
                    <Bar
                      dataKey="reach"
                      fill="#82ca9d"
                      name={language === 'ar' ? 'الوصول' : 'Reach'}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Last Updated Footer */}
      <div className={cn(
        "flex items-center justify-between text-sm text-gray-500 pt-4 border-t",
        language === 'ar' ? "flex-row-reverse" : ""
      )}>
        <span>
          {text.lastUpdated}: {lastUpdated.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
        </span>
        <div className={cn(
          "flex items-center gap-2",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>{language === 'ar' ? 'يتم التحديث كل 30 ثانية' : 'Updates every 30 seconds'}</span>
        </div>
      </div>
    </div>
  );
}
