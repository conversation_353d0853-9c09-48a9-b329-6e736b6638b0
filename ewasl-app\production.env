# Application Environment
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://ewasl-social-platform.vercel.app

# Database Configuration
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://nnxfzhxqzmriggulsudr.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjA2ODc2OCwiZXhwIjoyMDY3NjQ0NzY4fQ.q4cHohdkLxlB41jp1k-5w4QLWX4R93ugtfEvLnqSJNM

# NextAuth Configuration
NEXTAUTH_URL=https://ewasl-social-platform.vercel.app
NEXTAUTH_SECRET=ewasl_production_secret_2024_secure_key_for_nextauth_deployment

# Facebook/Instagram OAuth
FACEBOOK_APP_ID=1366325774493759
FACEBOOK_APP_SECRET=********************************
FACEBOOK_BUSINESS_ID=1479865455689755
INSTAGRAM_APP_ID=1366325774493759
INSTAGRAM_APP_SECRET=********************************

# OAuth Configuration
OAUTH_REDIRECT_URL=https://ewasl-social-platform.vercel.app/api/auth/callback

# Security Configuration
SECURITY_ENABLE_RATE_LIMIT=true
SECURITY_RATE_LIMIT_MAX=100
SECURITY_ALLOWED_ORIGINS=https://ewasl-social-platform.vercel.app

# Cron Configuration
CRON_SECRET=ewasl_cron_secret_2024_secure_production_deployment

# Twitter OAuth (placeholder)
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret

# LinkedIn OAuth (placeholder)
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret

# TikTok OAuth (placeholder)
TIKTOK_CLIENT_ID=your_tiktok_client_id
TIKTOK_CLIENT_SECRET=your_tiktok_client_secret

# Stripe Configuration (placeholder)
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# OpenRouter AI Configuration (placeholder)
OPENROUTER_API_KEY=your_openrouter_api_key

# Email Configuration (placeholder)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
