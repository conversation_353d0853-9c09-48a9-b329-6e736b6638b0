import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Create the missing publishing_results table
export async function POST(request: NextRequest) {
  console.log('🛠️ Creating missing publishing_results table...');
  
  try {
    const supabase = createClient();
    console.log('✅ Supabase client created');

    // Create the publishing_results table using raw SQL
    const createTableSQL = `
      -- Create the missing publishing_results table
      CREATE TABLE IF NOT EXISTS public.publishing_results (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          post_id UUID NOT NULL REFERENCES public.posts(id) ON DELETE CASCADE,
          social_account_id UUID NOT NULL REFERENCES public.social_accounts(id) ON DELETE CASCADE,
          platform VARCHAR(50) NOT NULL,
          platform_post_id VARCHAR(255),
          platform_url TEXT,
          status VARCHAR(50) NOT NULL DEFAULT 'pending',
          error_message TEXT,
          published_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          
          -- Indexes for better performance
          CONSTRAINT unique_post_social_account UNIQUE(post_id, social_account_id)
      );

      -- Create indexes
      CREATE INDEX IF NOT EXISTS idx_publishing_results_post_id ON public.publishing_results(post_id);
      CREATE INDEX IF NOT EXISTS idx_publishing_results_social_account_id ON public.publishing_results(social_account_id);
      CREATE INDEX IF NOT EXISTS idx_publishing_results_platform ON public.publishing_results(platform);
      CREATE INDEX IF NOT EXISTS idx_publishing_results_status ON public.publishing_results(status);

      -- Enable RLS
      ALTER TABLE public.publishing_results ENABLE ROW LEVEL SECURITY;
    `;

    console.log('🔧 Executing table creation SQL...');
    const { data, error } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (error) {
      console.error('❌ Table creation failed:', error);
      
      // Try alternative approach using individual queries
      console.log('🔄 Trying alternative approach...');
      
      const { error: createError } = await supabase
        .from('publishing_results')
        .select('*')
        .limit(1);
      
      if (createError && createError.message.includes('does not exist')) {
        return NextResponse.json({
          success: false,
          error: 'Cannot create table - insufficient privileges',
          message: 'You need to create the publishing_results table manually in Supabase Dashboard',
          sql: createTableSQL
        }, { status: 500 });
      }
    }

    console.log('✅ Table creation completed');

    // Create RLS policies
    const policiesSQL = `
      -- Create RLS policies
      CREATE POLICY IF NOT EXISTS "Users can view their own publishing results" ON public.publishing_results
          FOR SELECT USING (
              post_id IN (
                  SELECT id FROM public.posts WHERE user_id = auth.uid()
              )
          );

      CREATE POLICY IF NOT EXISTS "Users can insert their own publishing results" ON public.publishing_results
          FOR INSERT WITH CHECK (
              post_id IN (
                  SELECT id FROM public.posts WHERE user_id = auth.uid()
              )
          );

      CREATE POLICY IF NOT EXISTS "Users can update their own publishing results" ON public.publishing_results
          FOR UPDATE USING (
              post_id IN (
                  SELECT id FROM public.posts WHERE user_id = auth.uid()
              )
          );

      CREATE POLICY IF NOT EXISTS "Users can delete their own publishing results" ON public.publishing_results
          FOR DELETE USING (
              post_id IN (
                  SELECT id FROM public.posts WHERE user_id = auth.uid()
              )
          );
    `;

    console.log('🔐 Creating RLS policies...');
    const { error: policyError } = await supabase.rpc('exec_sql', { sql: policiesSQL });
    
    if (policyError) {
      console.log('⚠️ Policy creation failed (this is expected):', policyError.message);
    }

    // Test if table was created successfully
    const { data: testData, error: testError } = await supabase
      .from('publishing_results')
      .select('*')
      .limit(1);

    const tableExists = !testError;
    console.log('📊 Table creation result:', tableExists);

    return NextResponse.json({
      success: tableExists,
      tableExists,
      message: tableExists ? 'Table created successfully' : 'Table creation failed',
      sql: createTableSQL,
      error: testError?.message || null
    });

  } catch (error: any) {
    console.error('❌ Table creation error:', error);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
