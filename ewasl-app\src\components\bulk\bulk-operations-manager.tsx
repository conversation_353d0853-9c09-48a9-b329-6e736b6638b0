"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import {
  CheckSquare,
  Square,
  MoreHorizontal,
  Trash2,
  Edit,
  Copy,
  Calendar,
  Send,
  Download,
  Upload,
  Filter,
  Search,
  RefreshCw,
  Play,
  Pause,
  Stop,
  Clock,
  Users,
  Target,
  Zap,
  AlertCircle,
  CheckCircle,
  XCircle,
  Info,
  Loader2,
  FileText,
  Image,
  Video,
  Globe,
  BarChart3
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRTL } from '@/lib/i18n/arabic-rtl-optimizer';
import { toast } from 'sonner';

interface Post {
  id: string;
  content: string;
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  platforms: string[];
  mediaUrls?: string[];
  scheduledAt?: Date;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  author: {
    name: string;
    avatar?: string;
  };
  analytics?: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
    engagement: number;
  };
  tags: string[];
  priority: 'low' | 'medium' | 'high';
}

interface BulkOperation {
  id: string;
  type: 'publish' | 'schedule' | 'delete' | 'duplicate' | 'update_status' | 'update_platforms' | 'export';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  totalItems: number;
  processedItems: number;
  failedItems: number;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  results?: any;
}

interface BulkOperationsManagerProps {
  language?: 'ar' | 'en';
  className?: string;
  posts: Post[];
  onPostsUpdate?: (posts: Post[]) => void;
  onBulkOperation?: (operation: BulkOperation, selectedPosts: Post[]) => Promise<void>;
}

export function BulkOperationsManager({
  language = 'ar',
  className,
  posts,
  onPostsUpdate,
  onBulkOperation
}: BulkOperationsManagerProps) {
  const [selectedPosts, setSelectedPosts] = useState<Set<string>>(new Set());
  const [bulkOperations, setBulkOperations] = useState<BulkOperation[]>([]);
  const [activeOperation, setActiveOperation] = useState<BulkOperation | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [platformFilter, setPlatformFilter] = useState<string>('all');
  const [showBulkDialog, setShowBulkDialog] = useState(false);
  const [bulkOperationType, setBulkOperationType] = useState<string>('');
  const [bulkScheduleDate, setBulkScheduleDate] = useState('');
  const [bulkPlatforms, setBulkPlatforms] = useState<string[]>([]);

  const rtl = useRTL(language);

  // Translations
  const t = {
    ar: {
      title: 'إدارة العمليات المجمعة',
      subtitle: 'إدارة متقدمة للمنشورات المتعددة والعمليات المجمعة',
      selection: {
        selectAll: 'تحديد الكل',
        deselectAll: 'إلغاء تحديد الكل',
        selected: 'محدد',
        posts: 'منشورات'
      },
      operations: {
        publish: 'نشر',
        schedule: 'جدولة',
        delete: 'حذف',
        duplicate: 'نسخ',
        updateStatus: 'تحديث الحالة',
        updatePlatforms: 'تحديث المنصات',
        export: 'تصدير'
      },
      status: {
        all: 'الكل',
        draft: 'مسودة',
        scheduled: 'مجدول',
        published: 'منشور',
        failed: 'فشل'
      },
      platforms: {
        all: 'جميع المنصات',
        instagram: 'إنستغرام',
        facebook: 'فيسبوك',
        twitter: 'تويتر',
        linkedin: 'لينكد إن'
      },
      actions: {
        apply: 'تطبيق',
        cancel: 'إلغاء',
        search: 'بحث',
        filter: 'تصفية',
        refresh: 'تحديث',
        start: 'بدء',
        pause: 'إيقاف مؤقت',
        stop: 'إيقاف',
        retry: 'إعادة المحاولة'
      },
      messages: {
        noSelection: 'يرجى تحديد منشورات للعمل عليها',
        operationStarted: 'تم بدء العملية بنجاح',
        operationCompleted: 'تمت العملية بنجاح',
        operationFailed: 'فشلت العملية',
        confirmDelete: 'هل أنت متأكد من حذف المنشورات المحددة؟',
        confirmBulkAction: 'هل أنت متأكد من تطبيق هذه العملية على {count} منشور؟'
      }
    },
    en: {
      title: 'Bulk Operations Manager',
      subtitle: 'Advanced management for multiple posts and batch operations',
      selection: {
        selectAll: 'Select All',
        deselectAll: 'Deselect All',
        selected: 'selected',
        posts: 'posts'
      },
      operations: {
        publish: 'Publish',
        schedule: 'Schedule',
        delete: 'Delete',
        duplicate: 'Duplicate',
        updateStatus: 'Update Status',
        updatePlatforms: 'Update Platforms',
        export: 'Export'
      },
      status: {
        all: 'All',
        draft: 'Draft',
        scheduled: 'Scheduled',
        published: 'Published',
        failed: 'Failed'
      },
      platforms: {
        all: 'All Platforms',
        instagram: 'Instagram',
        facebook: 'Facebook',
        twitter: 'Twitter',
        linkedin: 'LinkedIn'
      },
      actions: {
        apply: 'Apply',
        cancel: 'Cancel',
        search: 'Search',
        filter: 'Filter',
        refresh: 'Refresh',
        start: 'Start',
        pause: 'Pause',
        stop: 'Stop',
        retry: 'Retry'
      },
      messages: {
        noSelection: 'Please select posts to work with',
        operationStarted: 'Operation started successfully',
        operationCompleted: 'Operation completed successfully',
        operationFailed: 'Operation failed',
        confirmDelete: 'Are you sure you want to delete the selected posts?',
        confirmBulkAction: 'Are you sure you want to apply this operation to {count} posts?'
      }
    }
  };

  const text = t[language];

  // Filter posts based on search and filters
  const filteredPosts = useMemo(() => {
    return posts.filter(post => {
      const matchesSearch = post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesStatus = statusFilter === 'all' || post.status === statusFilter;
      const matchesPlatform = platformFilter === 'all' || post.platforms.includes(platformFilter);
      
      return matchesSearch && matchesStatus && matchesPlatform;
    });
  }, [posts, searchTerm, statusFilter, platformFilter]);

  // Selection handlers
  const handleSelectAll = useCallback(() => {
    if (selectedPosts.size === filteredPosts.length) {
      setSelectedPosts(new Set());
    } else {
      setSelectedPosts(new Set(filteredPosts.map(post => post.id)));
    }
  }, [filteredPosts, selectedPosts.size]);

  const handleSelectPost = useCallback((postId: string) => {
    const newSelection = new Set(selectedPosts);
    if (newSelection.has(postId)) {
      newSelection.delete(postId);
    } else {
      newSelection.add(postId);
    }
    setSelectedPosts(newSelection);
  }, [selectedPosts]);

  // Bulk operation handlers
  const handleBulkOperation = useCallback(async (operationType: string) => {
    if (selectedPosts.size === 0) {
      toast.error(text.messages.noSelection);
      return;
    }

    const selectedPostsData = posts.filter(post => selectedPosts.has(post.id));
    
    const operation: BulkOperation = {
      id: `bulk-${Date.now()}`,
      type: operationType as any,
      status: 'pending',
      progress: 0,
      totalItems: selectedPosts.size,
      processedItems: 0,
      failedItems: 0,
      startedAt: new Date()
    };

    setBulkOperations(prev => [...prev, operation]);
    setActiveOperation(operation);

    try {
      if (onBulkOperation) {
        await onBulkOperation(operation, selectedPostsData);
      }
      
      // Simulate operation progress
      await simulateOperation(operation);
      
      toast.success(text.messages.operationCompleted);
    } catch (error) {
      console.error('Bulk operation failed:', error);
      toast.error(text.messages.operationFailed);
      
      setBulkOperations(prev => 
        prev.map(op => 
          op.id === operation.id 
            ? { ...op, status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' }
            : op
        )
      );
    } finally {
      setActiveOperation(null);
      setSelectedPosts(new Set());
    }
  }, [selectedPosts, posts, onBulkOperation, text.messages]);

  // Simulate operation progress
  const simulateOperation = async (operation: BulkOperation) => {
    const updateProgress = (progress: number, processedItems: number) => {
      setBulkOperations(prev => 
        prev.map(op => 
          op.id === operation.id 
            ? { ...op, progress, processedItems, status: 'running' }
            : op
        )
      );
    };

    for (let i = 0; i <= operation.totalItems; i++) {
      await new Promise(resolve => setTimeout(resolve, 200));
      const progress = (i / operation.totalItems) * 100;
      updateProgress(progress, i);
    }

    setBulkOperations(prev => 
      prev.map(op => 
        op.id === operation.id 
          ? { ...op, status: 'completed', completedAt: new Date(), progress: 100 }
          : op
      )
    );
  };

  // Get operation status icon
  const getOperationStatusIcon = (status: BulkOperation['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'running':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-gray-500" />;
      default:
        return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  // Get post type icon
  const getPostTypeIcon = (post: Post) => {
    if (post.mediaUrls && post.mediaUrls.length > 0) {
      const hasVideo = post.mediaUrls.some(url => url.includes('.mp4') || url.includes('.mov'));
      return hasVideo ? <Video className="w-4 h-4" /> : <Image className="w-4 h-4" />;
    }
    return <FileText className="w-4 h-4" />;
  };

  return (
    <div className={cn(
      "space-y-6",
      language === 'ar' ? "rtl" : "ltr",
      className
    )} dir={rtl.direction}>
      {/* Header */}
      <div className={rtl.cn(
        "flex items-center justify-between",
        rtl.flex()
      )}>
        <div className={rtl.textAlign()}>
          <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
            {text.title}
          </h1>
          <p className="text-gray-600 mt-1" style={{ fontFamily: rtl.getFontFamily('primary') }}>
            {text.subtitle}
          </p>
        </div>
        
        <div className={rtl.cn(
          "flex items-center gap-4",
          rtl.flex()
        )}>
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
            className={rtl.cn(
              "flex items-center gap-2",
              rtl.flex()
            )}
          >
            <RefreshCw className="w-4 h-4" />
            {text.actions.refresh}
          </Button>
        </div>
      </div>

      {/* Selection Summary */}
      {selectedPosts.size > 0 && (
        <Alert className="border-blue-200 bg-blue-50">
          <CheckSquare className="h-4 w-4 text-blue-600" />
          <AlertTitle className={rtl.cn(
            "text-blue-900",
            rtl.textAlign()
          )}>
            {selectedPosts.size} {text.selection.selected} {text.selection.posts}
          </AlertTitle>
          <AlertDescription className={rtl.cn(
            "text-blue-800",
            rtl.textAlign()
          )}>
            <div className={rtl.cn(
              "flex items-center gap-2 mt-2",
              rtl.flex()
            )}>
              <Button
                size="sm"
                onClick={() => setShowBulkDialog(true)}
                className={rtl.cn(
                  "flex items-center gap-2",
                  rtl.flex()
                )}
              >
                <Zap className="w-4 h-4" />
                {language === 'ar' ? 'تطبيق عملية مجمعة' : 'Apply Bulk Operation'}
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setSelectedPosts(new Set())}
              >
                {text.selection.deselectAll}
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className={rtl.cn(
            "flex items-center gap-2",
            rtl.flex(),
            rtl.textAlign()
          )}>
            <Filter className="w-5 h-5" />
            {text.actions.filter}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <Label className={rtl.textAlign()}>
                {text.actions.search}
              </Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في المنشورات...' : 'Search posts...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={cn("pl-10", rtl.textAlign())}
                />
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <Label className={rtl.textAlign()}>
                {language === 'ar' ? 'الحالة' : 'Status'}
              </Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(text.status).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Platform Filter */}
            <div>
              <Label className={rtl.textAlign()}>
                {language === 'ar' ? 'المنصة' : 'Platform'}
              </Label>
              <Select value={platformFilter} onValueChange={setPlatformFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(text.platforms).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Bulk Actions */}
            <div className={rtl.cn(
              "flex items-end gap-2",
              rtl.flex()
            )}>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                className={rtl.cn(
                  "flex items-center gap-2",
                  rtl.flex()
                )}
              >
                {selectedPosts.size === filteredPosts.length ? (
                  <>
                    <Square className="w-4 h-4" />
                    {text.selection.deselectAll}
                  </>
                ) : (
                  <>
                    <CheckSquare className="w-4 h-4" />
                    {text.selection.selectAll}
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Posts List with Selection */}
      <Card>
        <CardHeader>
          <CardTitle className={rtl.textAlign()}>
            {language === 'ar' ? 'المنشورات' : 'Posts'} ({filteredPosts.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {filteredPosts.map((post) => (
              <div
                key={post.id}
                className={cn(
                  "flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors",
                  selectedPosts.has(post.id) && "bg-blue-50 border-blue-200",
                  rtl.flex()
                )}
              >
                <Checkbox
                  checked={selectedPosts.has(post.id)}
                  onCheckedChange={() => handleSelectPost(post.id)}
                />

                <div className={rtl.cn(
                  "flex items-center gap-3 flex-1",
                  rtl.flex()
                )}>
                  {getPostTypeIcon(post)}

                  <div className={rtl.cn("flex-1", rtl.textAlign())}>
                    <p className="font-medium line-clamp-1">{post.content}</p>
                    <div className={rtl.cn(
                      "flex items-center gap-2 mt-1 text-sm text-gray-600",
                      rtl.flex()
                    )}>
                      <span>{post.author.name}</span>
                      <span>•</span>
                      <span>{rtl.formatRelativeTime(post.createdAt)}</span>
                      <span>•</span>
                      <Badge variant={
                        post.status === 'published' ? 'default' :
                        post.status === 'scheduled' ? 'secondary' :
                        post.status === 'failed' ? 'destructive' : 'outline'
                      }>
                        {text.status[post.status]}
                      </Badge>
                    </div>
                  </div>

                  <div className={rtl.cn(
                    "flex items-center gap-2",
                    rtl.flex()
                  )}>
                    {post.platforms.map((platform, index) => (
                      <span key={index} className="text-lg">
                        {platform === 'instagram' && '📷'}
                        {platform === 'facebook' && '👥'}
                        {platform === 'twitter' && '🐦'}
                        {platform === 'linkedin' && '💼'}
                      </span>
                    ))}
                  </div>

                  {post.analytics && (
                    <div className={rtl.cn(
                      "flex items-center gap-3 text-sm text-gray-600",
                      rtl.flex()
                    )}>
                      <span>👁 {rtl.formatCompactNumber(post.analytics.views)}</span>
                      <span>❤️ {rtl.formatCompactNumber(post.analytics.likes)}</span>
                      <span>💬 {rtl.formatCompactNumber(post.analytics.comments)}</span>
                    </div>
                  )}
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem>
                      <Edit className="w-4 h-4 mr-2" />
                      {language === 'ar' ? 'تعديل' : 'Edit'}
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Copy className="w-4 h-4 mr-2" />
                      {language === 'ar' ? 'نسخ' : 'Duplicate'}
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="w-4 h-4 mr-2" />
                      {language === 'ar' ? 'حذف' : 'Delete'}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ))}

            {filteredPosts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <BarChart3 className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>{language === 'ar' ? 'لا توجد منشورات' : 'No posts found'}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Active Operations */}
      {bulkOperations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className={rtl.textAlign()}>
              {language === 'ar' ? 'العمليات النشطة' : 'Active Operations'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {bulkOperations.slice(-5).map((operation) => (
                <div key={operation.id} className="p-4 border rounded-lg">
                  <div className={rtl.cn(
                    "flex items-center justify-between mb-2",
                    rtl.flex()
                  )}>
                    <div className={rtl.cn(
                      "flex items-center gap-2",
                      rtl.flex()
                    )}>
                      {getOperationStatusIcon(operation.status)}
                      <span className="font-medium">
                        {text.operations[operation.type as keyof typeof text.operations]}
                      </span>
                      <Badge variant="outline">
                        {operation.totalItems} {text.selection.posts}
                      </Badge>
                    </div>

                    <div className={rtl.cn(
                      "flex items-center gap-2 text-sm text-gray-600",
                      rtl.flex()
                    )}>
                      {operation.status === 'running' && (
                        <span>{operation.processedItems}/{operation.totalItems}</span>
                      )}
                      {operation.completedAt && (
                        <span>{rtl.formatRelativeTime(operation.completedAt)}</span>
                      )}
                    </div>
                  </div>

                  {operation.status === 'running' && (
                    <Progress value={operation.progress} className="mb-2" />
                  )}

                  {operation.error && (
                    <Alert className="mt-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{operation.error}</AlertDescription>
                    </Alert>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk Operation Dialog */}
      <Dialog open={showBulkDialog} onOpenChange={setShowBulkDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className={rtl.textAlign()}>
              {language === 'ar' ? 'عملية مجمعة' : 'Bulk Operation'}
            </DialogTitle>
            <DialogDescription className={rtl.textAlign()}>
              {language === 'ar'
                ? `تطبيق عملية على ${selectedPosts.size} منشور محدد`
                : `Apply operation to ${selectedPosts.size} selected posts`
              }
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Operation Type */}
            <div>
              <Label className={rtl.textAlign()}>
                {language === 'ar' ? 'نوع العملية' : 'Operation Type'}
              </Label>
              <Select value={bulkOperationType} onValueChange={setBulkOperationType}>
                <SelectTrigger>
                  <SelectValue placeholder={language === 'ar' ? 'اختر العملية' : 'Select operation'} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="publish">
                    <div className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                      <Send className="w-4 h-4" />
                      {text.operations.publish}
                    </div>
                  </SelectItem>
                  <SelectItem value="schedule">
                    <div className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                      <Calendar className="w-4 h-4" />
                      {text.operations.schedule}
                    </div>
                  </SelectItem>
                  <SelectItem value="duplicate">
                    <div className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                      <Copy className="w-4 h-4" />
                      {text.operations.duplicate}
                    </div>
                  </SelectItem>
                  <SelectItem value="updateStatus">
                    <div className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                      <Edit className="w-4 h-4" />
                      {text.operations.updateStatus}
                    </div>
                  </SelectItem>
                  <SelectItem value="updatePlatforms">
                    <div className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                      <Globe className="w-4 h-4" />
                      {text.operations.updatePlatforms}
                    </div>
                  </SelectItem>
                  <SelectItem value="export">
                    <div className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                      <Download className="w-4 h-4" />
                      {text.operations.export}
                    </div>
                  </SelectItem>
                  <SelectItem value="delete">
                    <div className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                      <Trash2 className="w-4 h-4" />
                      {text.operations.delete}
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Schedule Date (if scheduling) */}
            {bulkOperationType === 'schedule' && (
              <div>
                <Label className={rtl.textAlign()}>
                  {language === 'ar' ? 'تاريخ ووقت الجدولة' : 'Schedule Date & Time'}
                </Label>
                <Input
                  type="datetime-local"
                  value={bulkScheduleDate}
                  onChange={(e) => setBulkScheduleDate(e.target.value)}
                  className={rtl.textAlign()}
                />
              </div>
            )}

            {/* Platform Selection (if updating platforms) */}
            {bulkOperationType === 'updatePlatforms' && (
              <div>
                <Label className={rtl.textAlign()}>
                  {language === 'ar' ? 'المنصات الجديدة' : 'New Platforms'}
                </Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {['instagram', 'facebook', 'twitter', 'linkedin'].map((platform) => (
                    <div key={platform} className={rtl.cn(
                      "flex items-center space-x-2",
                      rtl.flex()
                    )}>
                      <Checkbox
                        id={platform}
                        checked={bulkPlatforms.includes(platform)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setBulkPlatforms([...bulkPlatforms, platform]);
                          } else {
                            setBulkPlatforms(bulkPlatforms.filter(p => p !== platform));
                          }
                        }}
                      />
                      <Label htmlFor={platform} className="text-sm">
                        {text.platforms[platform as keyof typeof text.platforms]}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Confirmation */}
            {bulkOperationType && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription className={rtl.textAlign()}>
                  {text.messages.confirmBulkAction.replace('{count}', selectedPosts.size.toString())}
                </AlertDescription>
              </Alert>
            )}

            <div className={rtl.cn(
              "flex justify-end gap-2",
              rtl.flex()
            )}>
              <Button variant="outline" onClick={() => setShowBulkDialog(false)}>
                {text.actions.cancel}
              </Button>
              <Button
                onClick={() => {
                  handleBulkOperation(bulkOperationType);
                  setShowBulkDialog(false);
                }}
                disabled={!bulkOperationType}
              >
                {text.actions.apply}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
