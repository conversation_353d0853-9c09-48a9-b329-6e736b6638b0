#!/usr/bin/env node

/**
 * Comprehensive Database Connection Test Script
 * Tests the new Supabase database connection with Prisma
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDatabaseConnection() {
  console.log('🔍 Testing eWasl Database Connection with Prisma...\n');
  
  try {
    // Test 1: Basic connection
    console.log('1. Testing basic database connection...');
    await prisma.$connect();
    console.log('   ✅ Database connection successful\n');

    // Test 2: Check if tables exist
    console.log('2. Checking database tables...');
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `;
    console.log(`   ✅ Found ${tables.length} tables:`);
    tables.forEach(table => console.log(`      - ${table.table_name}`));
    console.log('');

    // Test 3: Test user operations
    console.log('3. Testing user operations...');
    
    // Create a test user
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User',
        role: 'USER'
      }
    });
    console.log(`   ✅ Created test user: ${testUser.email} (ID: ${testUser.id})`);

    // Read the user back
    const foundUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    console.log(`   ✅ Retrieved user: ${foundUser.name}`);

    // Test 4: Test social account operations
    console.log('\n4. Testing social account operations...');
    
    const socialAccount = await prisma.socialAccount.create({
      data: {
        userId: testUser.id,
        platform: 'TWITTER',
        accountId: 'test_twitter_123',
        accountName: '@testuser',
        accessToken: 'test_token_123'
      }
    });
    console.log(`   ✅ Created social account: ${socialAccount.platform} - ${socialAccount.accountName}`);

    // Test 5: Test post operations
    console.log('\n5. Testing post operations...');
    
    const testPost = await prisma.post.create({
      data: {
        userId: testUser.id,
        content: 'This is a test post for eWasl database integration! 🚀',
        status: 'DRAFT'
      }
    });
    console.log(`   ✅ Created test post: "${testPost.content.substring(0, 50)}..."`);

    // Test 6: Test activity logging
    console.log('\n6. Testing activity logging...');
    
    const activity = await prisma.activity.create({
      data: {
        userId: testUser.id,
        postId: testPost.id,
        action: 'POST_SCHEDULED',
        details: 'Test post scheduled for publishing'
      }
    });
    console.log(`   ✅ Created activity log: ${activity.action}`);

    // Test 7: Test relationships
    console.log('\n7. Testing database relationships...');
    
    const userWithRelations = await prisma.user.findUnique({
      where: { id: testUser.id },
      include: {
        posts: true,
        socialAccounts: true,
        activities: true
      }
    });
    
    console.log(`   ✅ User has ${userWithRelations.posts.length} posts`);
    console.log(`   ✅ User has ${userWithRelations.socialAccounts.length} social accounts`);
    console.log(`   ✅ User has ${userWithRelations.activities.length} activities`);

    // Cleanup test data
    console.log('\n8. Cleaning up test data...');
    await prisma.activity.delete({ where: { id: activity.id } });
    await prisma.post.delete({ where: { id: testPost.id } });
    await prisma.socialAccount.delete({ where: { id: socialAccount.id } });
    await prisma.user.delete({ where: { id: testUser.id } });
    console.log('   ✅ Test data cleaned up');

    console.log('\n🎉 All database tests passed successfully!');
    console.log('\n📊 Database Integration Summary:');
    console.log('   ✅ Connection: Working');
    console.log('   ✅ Tables: All created');
    console.log('   ✅ CRUD Operations: Working');
    console.log('   ✅ Relationships: Working');
    console.log('   ✅ Prisma Client: Working');
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.error('\n🔧 Troubleshooting steps:');
    console.error('   1. Check DATABASE_URL in .env.local');
    console.error('   2. Verify database password is correct');
    console.error('   3. Ensure Supabase project is active');
    console.error('   4. Check network connectivity');
    console.error('   5. Run: npx prisma generate');
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDatabaseConnection();
