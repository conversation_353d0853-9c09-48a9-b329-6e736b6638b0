/**
 * eWasl Vercel Deployment Validation Test
 * Tests the production deployment to ensure all components work correctly
 */

const { chromium } = require('playwright');

async function testVercelDeployment() {
  console.log('🚀 Starting eWasl Vercel Deployment Validation...\n');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  });
  
  const page = await context.newPage();
  
  // Test configuration
  const baseUrl = 'https://app.ewasl.com'; // Update with actual Vercel URL
  const testResults = {
    passed: 0,
    failed: 0,
    tests: []
  };
  
  function logTest(name, status, details = '') {
    const emoji = status === 'PASS' ? '✅' : '❌';
    console.log(`${emoji} ${name}: ${status}`);
    if (details) console.log(`   ${details}`);
    
    testResults.tests.push({ name, status, details });
    if (status === 'PASS') testResults.passed++;
    else testResults.failed++;
  }
  
  try {
    // Test 1: Homepage loads
    console.log('\n📋 Testing Core Application...');
    try {
      await page.goto(baseUrl, { waitUntil: 'networkidle' });
      await page.waitForTimeout(2000);
      
      const title = await page.title();
      if (title && !title.includes('404')) {
        logTest('Homepage Load', 'PASS', `Title: ${title}`);
      } else {
        logTest('Homepage Load', 'FAIL', 'Page not found or invalid title');
      }
    } catch (error) {
      logTest('Homepage Load', 'FAIL', error.message);
    }
    
    // Test 2: Dashboard access
    console.log('\n🏠 Testing Dashboard...');
    try {
      await page.goto(`${baseUrl}/dashboard`, { waitUntil: 'networkidle' });
      await page.waitForTimeout(3000);
      
      // Check if dashboard loads (might redirect to auth)
      const currentUrl = page.url();
      if (currentUrl.includes('/dashboard') || currentUrl.includes('/auth')) {
        logTest('Dashboard Access', 'PASS', 'Dashboard accessible or auth redirect working');
        
        // Test sidebar if on dashboard
        if (currentUrl.includes('/dashboard')) {
          const sidebar = await page.locator('[data-testid="sidebar"], nav, .sidebar').first();
          if (await sidebar.isVisible()) {
            logTest('Dashboard Sidebar', 'PASS', 'Sidebar is visible');
          } else {
            logTest('Dashboard Sidebar', 'FAIL', 'Sidebar not found');
          }
        }
      } else {
        logTest('Dashboard Access', 'FAIL', `Unexpected redirect to: ${currentUrl}`);
      }
    } catch (error) {
      logTest('Dashboard Access', 'FAIL', error.message);
    }
    
    // Test 3: API Health Check
    console.log('\n🔧 Testing API Endpoints...');
    try {
      const response = await page.request.get(`${baseUrl}/api/health`);
      if (response.ok()) {
        const data = await response.json();
        logTest('API Health Check', 'PASS', `Status: ${response.status()}`);
      } else {
        logTest('API Health Check', 'FAIL', `HTTP ${response.status()}`);
      }
    } catch (error) {
      logTest('API Health Check', 'FAIL', error.message);
    }
    
    // Test 4: Posts API
    try {
      const response = await page.request.get(`${baseUrl}/api/posts`);
      if (response.status() === 401 || response.status() === 200) {
        logTest('Posts API', 'PASS', 'API responding (auth required or success)');
      } else {
        logTest('Posts API', 'FAIL', `HTTP ${response.status()}`);
      }
    } catch (error) {
      logTest('Posts API', 'FAIL', error.message);
    }
    
    // Test 5: Social Accounts API
    try {
      const response = await page.request.get(`${baseUrl}/api/social/accounts`);
      if (response.status() === 401 || response.status() === 200) {
        logTest('Social Accounts API', 'PASS', 'API responding (auth required or success)');
      } else {
        logTest('Social Accounts API', 'FAIL', `HTTP ${response.status()}`);
      }
    } catch (error) {
      logTest('Social Accounts API', 'FAIL', error.message);
    }
    
    // Test 6: Static Assets
    console.log('\n📁 Testing Static Assets...');
    try {
      await page.goto(`${baseUrl}/posts`, { waitUntil: 'networkidle' });
      await page.waitForTimeout(2000);
      
      // Check for console errors
      const logs = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          logs.push(msg.text());
        }
      });
      
      await page.waitForTimeout(3000);
      
      if (logs.length === 0) {
        logTest('Console Errors', 'PASS', 'No console errors detected');
      } else {
        logTest('Console Errors', 'FAIL', `${logs.length} errors: ${logs.slice(0, 2).join(', ')}`);
      }
    } catch (error) {
      logTest('Static Assets', 'FAIL', error.message);
    }
    
    // Test 7: Page Navigation
    console.log('\n🧭 Testing Navigation...');
    const pages = ['/posts', '/analytics', '/schedule', '/social', '/settings'];
    
    for (const pagePath of pages) {
      try {
        await page.goto(`${baseUrl}${pagePath}`, { waitUntil: 'networkidle' });
        await page.waitForTimeout(1000);
        
        const currentUrl = page.url();
        if (currentUrl.includes(pagePath) || currentUrl.includes('/auth')) {
          logTest(`Navigation to ${pagePath}`, 'PASS', 'Page loads or auth redirect');
        } else {
          logTest(`Navigation to ${pagePath}`, 'FAIL', `Unexpected redirect: ${currentUrl}`);
        }
      } catch (error) {
        logTest(`Navigation to ${pagePath}`, 'FAIL', error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Critical test failure:', error);
  } finally {
    await browser.close();
  }
  
  // Test Summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 DEPLOYMENT VALIDATION SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Tests Passed: ${testResults.passed}`);
  console.log(`❌ Tests Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);
  
  if (testResults.failed === 0) {
    console.log('\n🎉 ALL TESTS PASSED! Deployment is ready for production.');
  } else {
    console.log('\n⚠️  Some tests failed. Review the issues above before going live.');
  }
  
  console.log('\n📋 Detailed Results:');
  testResults.tests.forEach(test => {
    const emoji = test.status === 'PASS' ? '✅' : '❌';
    console.log(`${emoji} ${test.name}: ${test.status}`);
    if (test.details) console.log(`   └─ ${test.details}`);
  });
  
  return testResults;
}

// Run the test
if (require.main === module) {
  testVercelDeployment().catch(console.error);
}

module.exports = { testVercelDeployment };
