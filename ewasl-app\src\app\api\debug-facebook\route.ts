import { NextRequest, NextResponse } from 'next/server';
import { createFacebookOAuthService } from '@/lib/oauth/facebook';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 FACEBOOK API DIAGNOSTIC TEST STARTING...');

    // First, check environment variables
    const envCheck = {
      FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID ? 'SET' : 'MISSING',
      FACEBOOK_APP_SECRET: process.env.FACEBOOK_APP_SECRET ? 'SET' : 'MISSING',
      FACEBOOK_BUSINESS_ID: process.env.FACEBOOK_BUSINESS_ID ? 'SET' : 'MISSING',
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'NOT_SET'
    };

    console.log('🔍 Environment Variables Check:', envCheck);

    // If FACEBOOK_APP_SECRET is missing, return early with diagnostic info
    if (!process.env.FACEBOOK_APP_SECRET) {
      return NextResponse.json({
        error: 'FACEBOOK_APP_SECRET environment variable is missing',
        environmentVariables: envCheck,
        solution: 'Add FACEBOOK_APP_SECRET to Vercel environment variables',
        facebookAppId: process.env.FACEBOOK_APP_ID,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    // Test Facebook OAuth service
    let facebookService;
    try {
      facebookService = createFacebookOAuthService();
    } catch (serviceError) {
      return NextResponse.json({
        error: 'Failed to create Facebook OAuth service',
        environmentVariables: envCheck,
        serviceError: serviceError instanceof Error ? serviceError.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    // Get authenticated user and access token from database
    const { createClient } = await import('@/lib/supabase/server');
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({
        error: 'User not authenticated',
        details: userError?.message
      }, { status: 401 });
    }

    console.log('🔍 Current user:', user.id);

    // Get user's social accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'FACEBOOK');

    console.log('🔍 Database query result:', { accounts, accountsError });

    if (accountsError) {
      return NextResponse.json({
        error: 'Database query failed',
        details: accountsError.message
      }, { status: 500 });
    }

    if (!accounts || accounts.length === 0) {
      console.log('🔍 No Facebook accounts found in database');
      
      // Check oauth_logs for recent OAuth attempts
      const { data: oauthLogs } = await supabase
        .from('oauth_logs')
        .select('*')
        .eq('user_id', user.id)
        .eq('platform', 'facebook')
        .order('created_at', { ascending: false })
        .limit(5);

      return NextResponse.json({
        message: 'No Facebook accounts found in database',
        userId: user.id,
        recentOAuthLogs: oauthLogs || [],
        timestamp: new Date().toISOString()
      });
    }

    console.log('🔍 Found Facebook account:', accounts[0]);

    // Test direct Facebook Graph API call
    const accessToken = accounts[0].access_token;
    console.log('🔍 Testing Facebook Graph API with access token...');

    // Test appsecret_proof generation
    const crypto = require('crypto');
    const appSecret = process.env.FACEBOOK_APP_SECRET!;
    const appSecretProof = crypto
      .createHmac('sha256', appSecret)
      .update(accessToken)
      .digest('hex');

    console.log('🔍 Generated appsecret_proof:', appSecretProof.substring(0, 10) + '...');

    // Test /me endpoint first (with appsecret_proof)
    const meResponse = await fetch(`https://graph.facebook.com/v18.0/me?access_token=${accessToken}&appsecret_proof=${appSecretProof}`);
    const meData = await meResponse.json();
    console.log('🔍 Facebook /me response:', meData);

    // Test /me/accounts endpoint (with appsecret_proof)
    const accountsResponse = await fetch(`https://graph.facebook.com/v18.0/me/accounts?fields=id,name,access_token,category,tasks,instagram_business_account,fan_count,about,website,is_verified,picture.type(large)&access_token=${accessToken}&appsecret_proof=${appSecretProof}`);
    const accountsData = await accountsResponse.json();
    console.log('🔍 Facebook /me/accounts response:', accountsData);

    // Test getUserAccounts method
    let userAccounts = [];
    let getUserAccountsError = null;
    try {
      userAccounts = await facebookService.getUserAccounts(accessToken);
      console.log('🔍 getUserAccounts result:', userAccounts);
    } catch (error) {
      getUserAccountsError = error instanceof Error ? error.message : 'Unknown error';
      console.error('🔍 getUserAccounts error:', error);
    }

    // DEEP INVESTIGATION: Test different Facebook API endpoints to understand token type
    console.log('🔍 DEEP INVESTIGATION: Testing token type and permissions...');

    // Test 1: Check if this is a User token by calling /me with user fields
    const userTestResponse = await fetch(`https://graph.facebook.com/v18.0/me?fields=id,name,email,accounts&access_token=${accessToken}&appsecret_proof=${appSecretProof}`);
    const userTestData = await userTestResponse.json();
    console.log('🔍 User token test (/me with accounts field):', userTestData);

    // Test 2: Check token info to determine token type
    const tokenInfoResponse = await fetch(`https://graph.facebook.com/v18.0/me?fields=id,name,category,about&access_token=${accessToken}&appsecret_proof=${appSecretProof}`);
    const tokenInfoData = await tokenInfoResponse.json();
    console.log('🔍 Token info test:', tokenInfoData);

    // Test 3: Try to get token debug info (requires app access token)
    const appAccessToken = `${process.env.FACEBOOK_APP_ID}|${process.env.FACEBOOK_APP_SECRET}`;
    const debugTokenResponse = await fetch(`https://graph.facebook.com/v18.0/debug_token?input_token=${accessToken}&access_token=${appAccessToken}`);
    const debugTokenData = await debugTokenResponse.json();
    console.log('🔍 Debug token info:', debugTokenData);

    // Test 4: Check permissions granted to this token
    const permissionsResponse = await fetch(`https://graph.facebook.com/v18.0/me/permissions?access_token=${accessToken}&appsecret_proof=${appSecretProof}`);
    const permissionsData = await permissionsResponse.json();
    console.log('🔍 Token permissions:', permissionsData);

    return NextResponse.json({
      message: 'Facebook API diagnostic test completed',
      userId: user.id,
      environmentVariables: envCheck,
      databaseAccount: {
        id: accounts[0].id,
        platform: accounts[0].platform,
        accountName: accounts[0].account_name,
        isActive: accounts[0].is_active,
        lastValidated: accounts[0].last_validated_at,
        expiresAt: accounts[0].expires_at
      },
      facebookApiTests: {
        meEndpoint: {
          success: meResponse.ok,
          data: meData
        },
        accountsEndpoint: {
          success: accountsResponse.ok,
          data: accountsData
        }
      },
      getUserAccountsTest: {
        success: !getUserAccountsError,
        error: getUserAccountsError,
        accountsFound: userAccounts.length,
        accounts: userAccounts.map(acc => ({
          id: acc.id,
          platform: acc.platform,
          name: acc.accountName,
          status: acc.connectionStatus
        }))
      },
      appSecretProofTest: {
        generated: appSecretProof ? 'SUCCESS' : 'FAILED',
        length: appSecretProof ? appSecretProof.length : 0
      },
      deepInvestigation: {
        userTokenTest: {
          success: userTestResponse.ok,
          data: userTestData
        },
        tokenInfoTest: {
          success: tokenInfoResponse.ok,
          data: tokenInfoData
        },
        debugTokenTest: {
          success: debugTokenResponse.ok,
          data: debugTokenData
        },
        permissionsTest: {
          success: permissionsResponse.ok,
          data: permissionsData
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('🔍 Facebook diagnostic error:', error);
    return NextResponse.json({
      error: 'Test failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
