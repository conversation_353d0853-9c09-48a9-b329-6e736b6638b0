#!/usr/bin/env node

/**
 * Create Supabase Auth User Script
 * Creates a test user through Supabase Auth system
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

async function createAuthUser() {
  console.log('🔧 Creating Supabase Auth user for eWasl application...\n');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('   Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
    return;
  }
  
  try {
    // Create Supabase client with service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Test user credentials
    const testUser = {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Test User'
    };
    
    console.log('👤 Test User Credentials:');
    console.log(`   Email: ${testUser.email}`);
    console.log(`   Password: ${testUser.password}`);
    console.log(`   Name: ${testUser.name}`);
    console.log('');
    
    // First, let's clean up any existing user
    console.log('1. Cleaning up existing test user...');
    
    // Delete from users table
    const { error: deleteUserError } = await supabase
      .from('users')
      .delete()
      .eq('email', testUser.email);
    
    if (deleteUserError && deleteUserError.code !== 'PGRST116') { // PGRST116 = no rows found
      console.log('   ⚠️ Could not delete existing user from database:', deleteUserError.message);
    } else {
      console.log('   ✅ Cleaned up existing database user');
    }
    
    // Try to delete from auth.users (this might fail if user doesn't exist)
    try {
      const { data: authUsers } = await supabase.auth.admin.listUsers();
      const existingAuthUser = authUsers.users.find(u => u.email === testUser.email);
      
      if (existingAuthUser) {
        const { error: deleteAuthError } = await supabase.auth.admin.deleteUser(existingAuthUser.id);
        if (deleteAuthError) {
          console.log('   ⚠️ Could not delete existing auth user:', deleteAuthError.message);
        } else {
          console.log('   ✅ Cleaned up existing auth user');
        }
      } else {
        console.log('   ✅ No existing auth user found');
      }
    } catch (error) {
      console.log('   ⚠️ Could not check existing auth users:', error.message);
    }
    
    // Create user through Supabase Auth
    console.log('\n2. Creating user through Supabase Auth...');
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: testUser.email,
      password: testUser.password,
      email_confirm: true, // Auto-confirm email for testing
      user_metadata: {
        name: testUser.name
      }
    });
    
    if (authError) {
      console.error('   ❌ Failed to create auth user:', authError.message);
      return;
    }
    
    console.log('   ✅ Auth user created successfully!');
    console.log(`   Auth User ID: ${authData.user.id}`);
    console.log(`   Email: ${authData.user.email}`);
    console.log(`   Email Confirmed: ${authData.user.email_confirmed_at ? 'Yes' : 'No'}`);
    
    // Create corresponding user in our users table
    console.log('\n3. Creating user record in database...');
    const { data: dbUser, error: dbError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id, // Use the same ID from auth
        email: testUser.email,
        name: testUser.name,
        role: 'USER',
        email_verified: authData.user.email_confirmed_at
      })
      .select()
      .single();
    
    if (dbError) {
      console.error('   ❌ Failed to create database user:', dbError.message);
      // Try to clean up the auth user
      await supabase.auth.admin.deleteUser(authData.user.id);
      return;
    }
    
    console.log('   ✅ Database user created successfully!');
    console.log(`   Database User ID: ${dbUser.id}`);
    console.log(`   Name: ${dbUser.name}`);
    console.log(`   Role: ${dbUser.role}`);
    
    // Create a sample social account for testing
    console.log('\n4. Creating sample social media account...');
    const { data: socialAccount, error: socialError } = await supabase
      .from('social_accounts')
      .insert({
        user_id: dbUser.id,
        platform: 'TWITTER',
        account_id: 'test_twitter_account',
        account_name: '@testuser_ewasl',
        access_token: 'sample_access_token_for_testing'
      })
      .select()
      .single();
    
    if (socialError) {
      console.log('   ⚠️ Could not create sample social account:', socialError.message);
    } else {
      console.log('   ✅ Sample Twitter account created');
      console.log(`   Platform: ${socialAccount.platform}`);
      console.log(`   Account: ${socialAccount.account_name}`);
    }
    
    // Create a sample post for testing
    console.log('\n5. Creating sample post...');
    const { data: samplePost, error: postError } = await supabase
      .from('posts')
      .insert({
        user_id: dbUser.id,
        content: 'مرحباً بكم في eWasl! 🚀 هذه منشور تجريبي لاختبار منصة إدارة وسائل التواصل الاجتماعي. #eWasl #SocialMedia #Testing',
        status: 'DRAFT'
      })
      .select()
      .single();
    
    if (postError) {
      console.log('   ⚠️ Could not create sample post:', postError.message);
    } else {
      console.log('   ✅ Sample post created');
      console.log(`   Content: "${samplePost.content.substring(0, 50)}..."`);
      console.log(`   Status: ${samplePost.status}`);
    }
    
    // Log activity
    console.log('\n6. Logging user registration activity...');
    const { error: activityError } = await supabase
      .from('activities')
      .insert({
        user_id: dbUser.id,
        action: 'REGISTER',
        details: 'Test user account created through Supabase Auth for local testing'
      });
    
    if (activityError) {
      console.log('   ⚠️ Could not log activity:', activityError.message);
    } else {
      console.log('   ✅ Registration activity logged');
    }
    
    console.log('\n🎉 Supabase Auth user setup complete!');
    console.log('\n📋 Login Instructions:');
    console.log('   1. Open your browser to: http://localhost:3000');
    console.log('   2. Click "Sign In" or go to: http://localhost:3000/auth/signin');
    console.log(`   3. Use email: ${testUser.email}`);
    console.log(`   4. Use password: ${testUser.password}`);
    console.log('\n✨ You should now be able to login successfully and access the dashboard!');
    console.log('\n🔧 Technical Details:');
    console.log(`   - Auth User ID: ${authData.user.id}`);
    console.log(`   - Database User ID: ${dbUser.id}`);
    console.log(`   - Email Confirmed: ${authData.user.email_confirmed_at ? 'Yes' : 'No'}`);
    console.log(`   - Created: ${new Date().toISOString()}`);
    
  } catch (error) {
    console.error('❌ Failed to create Supabase Auth user:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Make sure the development server is running');
    console.error('   2. Check that all environment variables are set');
    console.error('   3. Verify Supabase connection is working');
    console.error('   4. Check that service role key has admin permissions');
  }
}

// Run the script
createAuthUser();
