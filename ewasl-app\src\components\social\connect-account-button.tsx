'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  Facebook, 
  Instagram, 
  Twitter, 
  Linkedin,
  Loader2
} from 'lucide-react'
import { SocialPlatform } from '@/types/social-enhanced'
import { useToast } from '@/hooks/use-toast'

interface ConnectAccountButtonProps {
  onAccountConnected?: () => void
  disabled?: boolean
  className?: string
}

interface PlatformConfig {
  platform: SocialPlatform
  name: string
  icon: React.ComponentType<{ className?: string }>
  color: string
  available: boolean
  comingSoon?: boolean
}

const PLATFORMS: PlatformConfig[] = [
  {
    platform: 'FACEBOOK',
    name: 'Facebook',
    icon: Facebook,
    color: '#1877F2',
    available: true
  },
  {
    platform: 'INSTAGRAM',
    name: 'Instagram',
    icon: Instagram,
    color: '#E4405F',
    available: true
  },
  {
    platform: 'TWITTER',
    name: 'Twitter',
    icon: Twitter,
    color: '#1DA1F2',
    available: false,
    comingSoon: true
  },
  {
    platform: 'LINKEDIN',
    name: 'LinkedIn',
    icon: Linkedin,
    color: '#0A66C2',
    available: false,
    comingSoon: true
  },
  {
    platform: 'TIKTOK',
    name: 'TikTok',
    icon: ({ className }) => (
      <div className={`${className} bg-black text-white rounded-sm flex items-center justify-center text-xs font-bold`}>
        TT
      </div>
    ),
    color: '#000000',
    available: false,
    comingSoon: true
  }
]

export function ConnectAccountButton({
  onAccountConnected,
  disabled = false,
  className = ''
}: ConnectAccountButtonProps) {
  const [isConnecting, setIsConnecting] = useState<SocialPlatform | null>(null)
  const { toast } = useToast()

  const handleConnect = async (platform: SocialPlatform) => {
    if (!PLATFORMS.find(p => p.platform === platform)?.available) {
      toast({
        title: 'قريباً',
        description: `ربط ${PLATFORMS.find(p => p.platform === platform)?.name} سيكون متاحاً قريباً`,
        variant: 'default'
      })
      return
    }

    setIsConnecting(platform)

    try {
      let connectUrl = ''
      
      switch (platform) {
        case 'FACEBOOK':
          connectUrl = '/api/oauth/facebook/auth'
          break
        case 'INSTAGRAM':
          connectUrl = '/api/oauth/instagram/auth'
          break
        case 'TWITTER':
          connectUrl = '/api/auth/connect/twitter'
          break
        case 'LINKEDIN':
          connectUrl = '/api/auth/connect/linkedin'
          break
        case 'TIKTOK':
          connectUrl = '/api/auth/connect/tiktok'
          break
        default:
          throw new Error(`Unsupported platform: ${platform}`)
      }

      // Show loading toast
      toast({
        title: 'جاري الاتصال...',
        description: `جاري توجيهك إلى ${PLATFORMS.find(p => p.platform === platform)?.name}`,
      })

      // Handle different OAuth endpoint types
      if (platform === 'FACEBOOK' || platform === 'INSTAGRAM') {
        // New OAuth endpoints return JSON with authUrl
        const response = await fetch(connectUrl)
        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || 'فشل في بدء OAuth')
        }

        if (data.authUrl) {
          window.location.href = data.authUrl
        } else {
          throw new Error('لم يتم الحصول على رابط OAuth')
        }
      } else {
        // Legacy endpoints redirect directly
        window.location.href = connectUrl
      }

    } catch (error) {
      console.error('Connection error:', error)
      toast({
        title: 'خطأ في الاتصال',
        description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        variant: 'destructive'
      })
      setIsConnecting(null)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          disabled={disabled || isConnecting !== null}
          className={`gap-2 ${className}`}
        >
          {isConnecting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              جاري الاتصال...
            </>
          ) : (
            <>
              <Plus className="h-4 w-4" />
              ربط حساب جديد
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {PLATFORMS.map((platform) => {
          const Icon = platform.icon
          const isCurrentlyConnecting = isConnecting === platform.platform
          
          return (
            <DropdownMenuItem
              key={platform.platform}
              onClick={() => handleConnect(platform.platform)}
              disabled={!platform.available || isCurrentlyConnecting}
              className="flex items-center gap-3 cursor-pointer"
            >
              <div 
                className="w-5 h-5 rounded flex items-center justify-center"
                style={{ backgroundColor: platform.color }}
              >
                <Icon className="h-3 w-3 text-white" />
              </div>
              
              <span className="flex-1">{platform.name}</span>
              
              {platform.comingSoon && (
                <Badge variant="secondary" className="text-xs">
                  قريباً
                </Badge>
              )}
              
              {isCurrentlyConnecting && (
                <Loader2 className="h-3 w-3 animate-spin" />
              )}
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default ConnectAccountButton
