/**
 * Performance monitoring utilities for eWasl application
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface WebVitalsMetric {
  name: 'CLS' | 'FID' | 'FCP' | 'LCP' | 'TTFB';
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private isInitialized = false;

  constructor() {
    if (typeof window !== 'undefined') {
      this.initialize();
    }
  }

  private initialize() {
    if (this.isInitialized) return;

    // Monitor Core Web Vitals
    this.initializeWebVitals();
    
    // Monitor resource loading
    this.initializeResourceMonitoring();
    
    // Monitor navigation timing
    this.initializeNavigationMonitoring();
    
    // Monitor long tasks
    this.initializeLongTaskMonitoring();
    
    // Monitor memory usage
    this.initializeMemoryMonitoring();

    this.isInitialized = true;
  }

  private initializeWebVitals() {
    // Import web-vitals dynamically to avoid SSR issues
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(this.onWebVital.bind(this));
      getFID(this.onWebVital.bind(this));
      getFCP(this.onWebVital.bind(this));
      getLCP(this.onWebVital.bind(this));
      getTTFB(this.onWebVital.bind(this));
    }).catch(console.error);
  }

  private onWebVital(metric: WebVitalsMetric) {
    this.recordMetric({
      name: `web-vital-${metric.name.toLowerCase()}`,
      value: metric.value,
      timestamp: Date.now(),
      metadata: {
        rating: metric.rating,
        delta: metric.delta,
        id: metric.id,
      },
    });

    // Send to analytics
    this.sendToAnalytics('web-vital', metric);
  }

  private initializeResourceMonitoring() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            this.recordResourceMetric(entry as PerformanceResourceTiming);
          }
        }
      });

      observer.observe({ entryTypes: ['resource'] });
      this.observers.push(observer);
    }
  }

  private recordResourceMetric(entry: PerformanceResourceTiming) {
    const duration = entry.responseEnd - entry.startTime;
    const size = entry.transferSize || 0;

    this.recordMetric({
      name: 'resource-load',
      value: duration,
      timestamp: Date.now(),
      metadata: {
        url: entry.name,
        type: this.getResourceType(entry.name),
        size,
        cached: entry.transferSize === 0 && entry.decodedBodySize > 0,
      },
    });

    // Alert on slow resources
    if (duration > 3000) { // 3 seconds
      this.recordMetric({
        name: 'slow-resource',
        value: duration,
        timestamp: Date.now(),
        metadata: {
          url: entry.name,
          type: this.getResourceType(entry.name),
        },
      });
    }
  }

  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) return 'image';
    if (url.includes('/api/')) return 'api';
    return 'other';
  }

  private initializeNavigationMonitoring() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            this.recordNavigationMetric(entry as PerformanceNavigationTiming);
          }
        }
      });

      observer.observe({ entryTypes: ['navigation'] });
      this.observers.push(observer);
    }
  }

  private recordNavigationMetric(entry: PerformanceNavigationTiming) {
    const metrics = {
      'dns-lookup': entry.domainLookupEnd - entry.domainLookupStart,
      'tcp-connect': entry.connectEnd - entry.connectStart,
      'ssl-handshake': entry.connectEnd - entry.secureConnectionStart,
      'ttfb': entry.responseStart - entry.requestStart,
      'dom-content-loaded': entry.domContentLoadedEventEnd - entry.navigationStart,
      'load-complete': entry.loadEventEnd - entry.navigationStart,
    };

    Object.entries(metrics).forEach(([name, value]) => {
      if (value > 0) {
        this.recordMetric({
          name: `navigation-${name}`,
          value,
          timestamp: Date.now(),
        });
      }
    });
  }

  private initializeLongTaskMonitoring() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'longtask') {
            this.recordMetric({
              name: 'long-task',
              value: entry.duration,
              timestamp: Date.now(),
              metadata: {
                startTime: entry.startTime,
              },
            });
          }
        }
      });

      try {
        observer.observe({ entryTypes: ['longtask'] });
        this.observers.push(observer);
      } catch (error) {
        // Long task API not supported
        console.warn('Long task monitoring not supported');
      }
    }
  }

  private initializeMemoryMonitoring() {
    // Monitor memory usage periodically
    setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        this.recordMetric({
          name: 'memory-usage',
          value: memory.usedJSHeapSize,
          timestamp: Date.now(),
          metadata: {
            totalJSHeapSize: memory.totalJSHeapSize,
            jsHeapSizeLimit: memory.jsHeapSizeLimit,
            usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
          },
        });
      }
    }, 30000); // Every 30 seconds
  }

  public recordMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);
    
    // Keep only last 1000 metrics to prevent memory leaks
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Send critical metrics immediately
    if (this.isCriticalMetric(metric)) {
      this.sendToAnalytics('performance-critical', metric);
    }
  }

  private isCriticalMetric(metric: PerformanceMetric): boolean {
    const criticalThresholds = {
      'web-vital-lcp': 4000, // 4 seconds
      'web-vital-fid': 300,  // 300ms
      'web-vital-cls': 0.25, // 0.25
      'long-task': 100,      // 100ms
      'slow-resource': 3000, // 3 seconds
    };

    return metric.name in criticalThresholds && 
           metric.value > criticalThresholds[metric.name as keyof typeof criticalThresholds];
  }

  private sendToAnalytics(event: string, data: any) {
    // Send to analytics service
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
      // Example: Google Analytics 4
      if ('gtag' in window) {
        (window as any).gtag('event', event, {
          custom_parameter_1: data.name || data.id,
          custom_parameter_2: data.value,
          custom_parameter_3: data.rating || 'unknown',
        });
      }

      // Send to custom analytics endpoint
      fetch('/api/analytics/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event,
          data,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        }),
      }).catch(console.error);
    }
  }

  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  public getMetricsByName(name: string): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.name === name);
  }

  public getAverageMetric(name: string): number {
    const metrics = this.getMetricsByName(name);
    if (metrics.length === 0) return 0;
    
    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / metrics.length;
  }

  public generateReport(): string {
    const report = {
      timestamp: new Date().toISOString(),
      totalMetrics: this.metrics.length,
      webVitals: {
        lcp: this.getAverageMetric('web-vital-lcp'),
        fid: this.getAverageMetric('web-vital-fid'),
        cls: this.getAverageMetric('web-vital-cls'),
        fcp: this.getAverageMetric('web-vital-fcp'),
        ttfb: this.getAverageMetric('web-vital-ttfb'),
      },
      performance: {
        averageResourceLoad: this.getAverageMetric('resource-load'),
        longTasks: this.getMetricsByName('long-task').length,
        slowResources: this.getMetricsByName('slow-resource').length,
      },
    };

    return JSON.stringify(report, null, 2);
  }

  public cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics = [];
    this.isInitialized = false;
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for performance monitoring
export function usePerformanceMonitor() {
  return {
    recordMetric: performanceMonitor.recordMetric.bind(performanceMonitor),
    getMetrics: performanceMonitor.getMetrics.bind(performanceMonitor),
    getReport: performanceMonitor.generateReport.bind(performanceMonitor),
  };
}

export default performanceMonitor;
