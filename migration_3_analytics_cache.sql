-- Migration 3: Analytics Cache Table
-- Run this in Supabase SQL Editor

-- Create Analytics Cache Table
CREATE TABLE IF NOT EXISTS public.analytics_cache (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  platform TEXT NOT NULL,
  account_id TEXT NOT NULL,
  account_name TEXT,
  metrics JSONB NOT NULL DEFAULT '{}',
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique combination of user, platform, and account
  UNIQUE(user_id, platform, account_id)
);

-- Create indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_analytics_cache_user_id ON public.analytics_cache(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_cache_platform ON public.analytics_cache(platform);
CREATE INDEX IF NOT EXISTS idx_analytics_cache_last_updated ON public.analytics_cache(last_updated DESC);
CREATE INDEX IF NOT EXISTS idx_analytics_cache_user_platform ON public.analytics_cache(user_id, platform);

-- Enable Row Level Security
ALTER TABLE public.analytics_cache ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies
CREATE POLICY IF NOT EXISTS "Users can view their own analytics cache" ON public.analytics_cache
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own analytics cache" ON public.analytics_cache
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own analytics cache" ON public.analytics_cache
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own analytics cache" ON public.analytics_cache
  FOR DELETE USING (auth.uid() = user_id);

-- Create Post Analytics Table for detailed post-level analytics
CREATE TABLE IF NOT EXISTS public.post_analytics (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  platform TEXT NOT NULL,
  post_id TEXT NOT NULL,
  content TEXT,
  published_at TIMESTAMPTZ,
  metrics JSONB NOT NULL DEFAULT '{}',
  engagement JSONB NOT NULL DEFAULT '{}',
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique combination of user, platform, and post
  UNIQUE(user_id, platform, post_id)
);

-- Create indexes for post analytics
CREATE INDEX IF NOT EXISTS idx_post_analytics_user_id ON public.post_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_post_analytics_platform ON public.post_analytics(platform);
CREATE INDEX IF NOT EXISTS idx_post_analytics_published_at ON public.post_analytics(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_post_analytics_user_platform ON public.post_analytics(user_id, platform);

-- Enable RLS for post analytics
ALTER TABLE public.post_analytics ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies for post analytics
CREATE POLICY IF NOT EXISTS "Users can view their own post analytics" ON public.post_analytics
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own post analytics" ON public.post_analytics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own post analytics" ON public.post_analytics
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own post analytics" ON public.post_analytics
  FOR DELETE USING (auth.uid() = user_id);

-- Create function to cleanup old analytics cache (older than 7 days)
CREATE OR REPLACE FUNCTION cleanup_old_analytics_cache()
RETURNS void AS $$
BEGIN
  DELETE FROM public.analytics_cache 
  WHERE last_updated < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION cleanup_old_analytics_cache() TO authenticated;

-- Verify table creation
SELECT 'Analytics cache tables created successfully' as status;
