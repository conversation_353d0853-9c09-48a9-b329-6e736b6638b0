#!/usr/bin/env node

console.log('🔍 Testing Account Visibility with Different User IDs\n');

// Test accounts endpoint with different scenarios
async function testAccountVisibility() {
  const testCases = [
    {
      name: 'Demo User ID',
      userId: '3ddaeb03-2d95-4fff-abad-2a2c7dd25037',
      description: 'Should show LinkedIn account "burak ozan"'
    },
    {
      name: 'Empty User ID',
      userId: '',
      description: 'Should fallback to demo user and show LinkedIn account'
    },
    {
      name: 'Undefined User ID',
      userId: 'undefined',
      description: 'Should fallback to demo user and show LinkedIn account'
    },
    {
      name: 'No User ID Parameter',
      userId: null,
      description: 'Should fallback to demo user and show LinkedIn account'
    }
  ];

  for (const testCase of testCases) {
    console.log(`📱 Testing: ${testCase.name}`);
    console.log(`   Expected: ${testCase.description}`);
    
    try {
      const url = testCase.userId === null 
        ? 'https://app.ewasl.com/api/social/accounts'
        : `https://app.ewasl.com/api/social/accounts?userId=${testCase.userId}`;
        
      const response = await fetch(url);
      const data = await response.json();
      
      if (response.ok && data.success) {
        const linkedinAccounts = data.accounts?.filter(acc => acc.platform === 'LINKEDIN') || [];
        
        console.log(`   ✅ Status: ${response.status} (Success)`);
        console.log(`   📊 Total accounts: ${data.accounts?.length || 0}`);
        console.log(`   🔗 LinkedIn accounts: ${linkedinAccounts.length}`);
        
        if (linkedinAccounts.length > 0) {
          linkedinAccounts.forEach(acc => {
            console.log(`      - ${acc.account_name} (${acc.account_id})`);
            console.log(`      - User ID: ${acc.user_id}`);
          });
        }
        
        console.log(`   🎯 Effective User ID: ${data.debug?.userId || 'unknown'}`);
      } else {
        console.log(`   ❌ Error: ${response.status} - ${data.error || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.log(`   💥 Network Error: ${error.message}`);
    }
    
    console.log(''); // Empty line
  }
}

// Run test
async function runTest() {
  console.log('🚀 Starting account visibility test...\n');
  
  await testAccountVisibility();
  
  console.log('📊 TEST SUMMARY:');
  console.log('If all tests show the LinkedIn account "burak ozan", then the fix is working!');
  console.log('The account should be visible regardless of which user ID is used.');
  console.log('\n🏁 Test complete!');
}

runTest(); 