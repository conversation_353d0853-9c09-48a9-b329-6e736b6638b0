/**
 * Instagram OAuth Authorization Endpoint
 * Instagram Business API OAuth through Facebook Graph API
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getAuthenticatedUser } from '@/lib/auth/api-auth'
import crypto from 'crypto'

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user from session
    const { user, supabase } = await getAuthenticatedUser(request);

    if (!user?.id) {
      return NextResponse.json(
        { error: 'المستخدم غير مصرح له' }, // User not authenticated in Arabic
        { status: 401 }
      )
    }

    const userId = user.id;

    // Define redirect URI for Instagram OAuth (uses Facebook Graph API)
    // Prioritize production URLs over temporary Vercel URLs
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL ||
                   process.env.NEXTAUTH_URL ||
                   'https://app.ewasl.com';

    const redirectUri = `${baseUrl}/api/oauth/instagram/callback`;

    console.log('🔍 Instagram OAuth Debug Info:', {
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      VERCEL_URL: process.env.VERCEL_URL,
      baseUrl,
      redirectUri
    });

    // Instagram Business API requires Facebook App ID
    const clientId = process.env.FACEBOOK_APP_ID;
    if (!clientId) {
      return NextResponse.json(
        { error: 'معرف تطبيق فيسبوك غير مكون' }, // Facebook App ID not configured in Arabic
        { status: 500 }
      )
    }

    // Generate OAuth state using direct database operations
    const state = crypto.randomUUID().replace(/-/g, '');
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    try {
      const { error: insertError } = await supabase
        .from('oauth_states')
        .insert({
          state_token: state,
          user_id: userId,
          platform: 'INSTAGRAM',
          expires_at: expiresAt.toISOString(),
          created_at: new Date().toISOString()
        });

      if (insertError) {
        console.error('Failed to store OAuth state:', insertError);
        return NextResponse.json(
          { error: 'فشل في تخزين حالة OAuth' }, // Failed to store OAuth state in Arabic
          { status: 500 }
        )
      }

    } catch (dbError) {
      console.error('Database operation failed:', dbError)
      return NextResponse.json(
        { error: 'فشل في عملية قاعدة البيانات' }, // Database operation failed in Arabic
        { status: 500 }
      )
    }

    // Instagram Business API scopes through Facebook Graph API
    // Fixed: Removed invalid scopes instagram_graph_user_profile, instagram_graph_user_media
    const scopes = [
      'instagram_basic',
      'instagram_content_publish',
      'pages_show_list',
      'pages_read_engagement',
      'business_management'
    ].join(',');

    // Build Instagram OAuth URL (uses Facebook OAuth with Instagram scopes)
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      scope: scopes,
      response_type: 'code',
      state: `${state}_instagram`,
    });

    const authUrl = `https://www.facebook.com/v18.0/dialog/oauth?${params.toString()}`;
    
    console.log('🚀 Instagram OAuth URL:', authUrl);
    console.log('📋 Scopes:', scopes);

    // Redirect to Instagram OAuth
    return NextResponse.redirect(authUrl);

  } catch (error) {
    console.error('❌ Instagram OAuth initiation error:', error);
    return NextResponse.json(
      { error: 'خطأ في بدء OAuth لانستغرام' }, // Error starting Instagram OAuth in Arabic
      { status: 500 }
    )
  }
}

// Handle POST requests (for programmatic OAuth initiation)
export async function POST(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);

    if (!user?.id) {
      return NextResponse.json(
        { error: 'المستخدم غير مصرح له' },
        { status: 401 }
      )
    }

    // Same logic as GET but return JSON response instead of redirect
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';
    const redirectUri = `${baseUrl}/api/oauth/instagram/callback`;
    const clientId = process.env.FACEBOOK_APP_ID;

    if (!clientId) {
      return NextResponse.json(
        { error: 'معرف تطبيق فيسبوك غير مكون' },
        { status: 500 }
      )
    }

    const state = crypto.randomUUID().replace(/-/g, '');
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

    await supabase
      .from('oauth_states')
      .insert({
        state_token: state,
        user_id: user.id,
        platform: 'INSTAGRAM',
        expires_at: expiresAt.toISOString(),
        created_at: new Date().toISOString()
      });

    // Fixed: Removed invalid scopes instagram_graph_user_profile, instagram_graph_user_media
    const scopes = [
      'instagram_basic',
      'instagram_content_publish',
      'pages_show_list',
      'pages_read_engagement',
      'business_management'
    ].join(',');

    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      scope: scopes,
      response_type: 'code',
      state: `${state}_instagram`,
    });

    const authUrl = `https://www.facebook.com/v18.0/dialog/oauth?${params.toString()}`;

    return NextResponse.json({
      success: true,
      authUrl,
      state,
      scopes: scopes.split(',')
    });

  } catch (error) {
    console.error('❌ Instagram OAuth POST error:', error);
    return NextResponse.json(
      { error: 'خطأ في بدء OAuth لانستغرام' },
      { status: 500 }
    )
  }
}