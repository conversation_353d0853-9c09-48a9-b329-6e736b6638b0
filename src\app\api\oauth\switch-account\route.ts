import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

/**
 * Account Switching API
 * Handles switching between multiple accounts on the same platform
 */

export async function POST(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    const body = await request.json();
    const { platform, fromAccountId, toAccountId, userId = user.id } = body;

    if (!platform || !fromAccountId || !toAccountId) {
      return NextResponse.json(
        { error: 'Platform, fromAccountId, and toAccountId are required' },
        { status: 400 }
      );
    }

    console.log(`🔄 Switching ${platform} account from ${fromAccountId} to ${toAccountId} for user ${userId}`);

    // Validate both accounts exist and belong to user
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', userId)
      .eq('platform', platform.toUpperCase())
      .in('id', [fromAccountId, toAccountId])
      .eq('is_active', true);

    if (accountsError || !accounts || accounts.length !== 2) {
      return NextResponse.json(
        { error: 'لم يتم العثور على أحد الحسابات أو كلاهما' },
        { status: 404 }
      );
    }

    const fromAccount = accounts.find(acc => acc.id === fromAccountId);
    const toAccount = accounts.find(acc => acc.id === toAccountId);

    if (!fromAccount || !toAccount) {
      return NextResponse.json(
        { error: 'معرفات الحسابات غير صحيحة' },
        { status: 400 }
      );
    }

    // Update user preferences to set new default account
    await updateUserPreferences(supabase, userId, platform, toAccountId);

    // Transfer scheduled posts (optional)
    const transferScheduled = body.transferScheduled !== false;
    if (transferScheduled) {
      await transferScheduledPosts(supabase, userId, fromAccountId, toAccountId);
    }

    // Log account switch activity
    await supabase
      .from('activities')
      .insert({
        user_id: userId,
        action: 'ACCOUNT_SWITCHED',
        details: `Switched ${platform} account from ${fromAccount.account_name} to ${toAccount.account_name}`,
        metadata: {
          platform,
          from_account_id: fromAccountId,
          to_account_id: toAccountId,
          from_account_name: fromAccount.account_name,
          to_account_name: toAccount.account_name,
          transferred_scheduled: transferScheduled
        },
        created_at: new Date().toISOString()
      });

    console.log(`✅ Successfully switched ${platform} account for user ${userId}`);

    return NextResponse.json({
      success: true,
      message: 'تم تبديل الحساب بنجاح',
      platform,
      fromAccount: {
        id: fromAccount.id,
        name: fromAccount.account_name
      },
      toAccount: {
        id: toAccount.id,
        name: toAccount.account_name
      },
      transferredScheduled: transferScheduled,
      switchedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error switching account:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في تبديل الحساب',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET - Get available accounts for switching
 */
export async function GET(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform');
    const currentAccountId = searchParams.get('currentAccountId');

    if (!platform) {
      return NextResponse.json(
        { error: 'Platform is required' },
        { status: 400 }
      );
    }

    // Get all active accounts for this platform
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', platform.toUpperCase())
      .eq('is_active', true)
      .order('account_name');

    if (accountsError) {
      throw new Error(`Database error: ${accountsError.message}`);
    }

    // Get current user preferences
    const { data: preferences } = await supabase
      .from('user_business_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    const currentDefault = getCurrentDefaultAccount(preferences, platform);

    // Process accounts for display
    const processedAccounts = accounts.map(account => ({
      id: account.id,
      name: account.account_name,
      handle: account.account_handle,
      platform: account.platform,
      connectionStatus: account.connection_status,
      profileImageUrl: account.profile_image_url,
      isDefault: account.id === currentDefault,
      isCurrent: account.id === currentAccountId,
      lastValidatedAt: account.last_validated_at,
      metadata: account.metadata
    }));

    return NextResponse.json({
      success: true,
      platform,
      accounts: processedAccounts,
      totalAccounts: processedAccounts.length,
      currentDefault,
      canSwitch: processedAccounts.length > 1
    });

  } catch (error) {
    console.error('❌ Error getting switch options:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في الحصول على خيارات التبديل',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT - Set default account for platform
 */
export async function PUT(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    const body = await request.json();
    const { platform, accountId, userId = user.id } = body;

    if (!platform || !accountId) {
      return NextResponse.json(
        { error: 'Platform and accountId are required' },
        { status: 400 }
      );
    }

    // Validate account exists and belongs to user
    const { data: account, error: accountError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('user_id', userId)
      .eq('platform', platform.toUpperCase())
      .eq('is_active', true)
      .single();

    if (accountError || !account) {
      return NextResponse.json(
        { error: 'لم يتم العثور على الحساب' },
        { status: 404 }
      );
    }

    // Update user preferences
    await updateUserPreferences(supabase, userId, platform, accountId);

    // Log preference change
    await supabase
      .from('activities')
      .insert({
        user_id: userId,
        action: 'DEFAULT_ACCOUNT_CHANGED',
        details: `Set ${account.account_name} as default ${platform} account`,
        metadata: {
          platform,
          account_id: accountId,
          account_name: account.account_name
        },
        created_at: new Date().toISOString()
      });

    return NextResponse.json({
      success: true,
      message: 'تم تعيين الحساب الافتراضي بنجاح',
      platform,
      account: {
        id: account.id,
        name: account.account_name
      },
      updatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error setting default account:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في تعيين الحساب الافتراضي',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Update user preferences for default account
 */
async function updateUserPreferences(supabase: any, userId: string, platform: string, accountId: string) {
  const preferenceField = getPreferenceField(platform);
  
  if (!preferenceField) {
    console.warn(`No preference field for platform: ${platform}`);
    return;
  }

  await supabase
    .from('user_business_preferences')
    .upsert({
      user_id: userId,
      [preferenceField]: accountId,
      updated_at: new Date().toISOString()
    }, {
      onConflict: 'user_id'
    });
}

/**
 * Transfer scheduled posts from one account to another
 */
async function transferScheduledPosts(supabase: any, userId: string, fromAccountId: string, toAccountId: string) {
  try {
    const { error } = await supabase
      .from('scheduled_posts_queue')
      .update({
        social_account_id: toAccountId,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('social_account_id', fromAccountId)
      .eq('status', 'PENDING');

    if (error) {
      console.error('Error transferring scheduled posts:', error);
    } else {
      console.log(`✅ Transferred scheduled posts from ${fromAccountId} to ${toAccountId}`);
    }
  } catch (error) {
    console.error('Error in transferScheduledPosts:', error);
  }
}

/**
 * Get preference field name for platform
 */
function getPreferenceField(platform: string): string | null {
  const fields: Record<string, string> = {
    'FACEBOOK': 'default_facebook_page_id',
    'LINKEDIN': 'default_linkedin_org_id',
    'INSTAGRAM': 'default_instagram_account_id'
  };
  
  return fields[platform.toUpperCase()] || null;
}

/**
 * Get current default account from preferences
 */
function getCurrentDefaultAccount(preferences: any, platform: string): string | null {
  if (!preferences) return null;
  
  const field = getPreferenceField(platform);
  return field ? preferences[field] : null;
}
