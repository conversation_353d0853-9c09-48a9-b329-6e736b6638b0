export type SocialPlatform = 
  | 'FACEBOOK' 
  | 'INSTAGRAM' 
  | 'TWITTER' 
  | 'LINKEDIN' 
  | 'TIKTOK' 
  | 'YOUTUBE' 
  | 'SNAPCHAT'
  | 'PINTEREST';

export type ConnectionStatus = 
  | 'connected' 
  | 'expired' 
  | 'error' 
  | 'reconnecting' 
  | 'disconnected';

export type AccountPermission = 
  | 'read' 
  | 'write' 
  | 'manage' 
  | 'analytics' 
  | 'admin';

export interface RateLimitInfo {
  current: number;
  limit: number;
  resetTime: Date;
  percentage: number;
}

export interface AccountMetrics {
  followerCount?: number;
  followingCount?: number;
  postsCount?: number;
  engagementRate?: number;
  lastPostDate?: Date;
}

export interface EnhancedSocialAccount {
  id: string;
  userId: string;
  organizationId?: string;
  platform: SocialPlatform;
  accountId: string;
  accountName: string;
  accountHandle?: string;
  profileImageUrl?: string;
  accessToken: string;
  refreshToken?: string;
  tokenExpiresAt?: Date;
  permissions: AccountPermission[];
  connectionStatus: ConnectionStatus;
  lastValidatedAt: Date;
  metadata: Record<string, any>;
  rateLimits: Record<string, RateLimitInfo>;
  metrics?: AccountMetrics;
  groups: AccountGroup[];
  createdAt: Date;
  updatedAt: Date;
  // Facebook-specific fields for page token storage
  pageId?: string;
  pageAccessToken?: string;
  pageName?: string;
}

// Type alias for backward compatibility
export type SocialAccount = EnhancedSocialAccount;

export interface AccountGroup {
  id: string;
  organizationId: string;
  name: string;
  description?: string;
  color: string;
  accountCount: number;
  createdBy: string;
  createdAt: Date;
}

export interface BulkOperation {
  type: 'connect' | 'disconnect' | 'refresh' | 'validate' | 'update_group';
  accountIds: string[];
  parameters?: Record<string, any>;
}

export interface ConnectionHealth {
  accountId: string;
  platform: SocialPlatform;
  status: ConnectionStatus;
  lastChecked: Date;
  errorMessage?: string;
  nextCheckAt: Date;
}

export interface PlatformConfig {
  platform: SocialPlatform;
  name: string;
  icon: string;
  color: string;
  isEnabled: boolean;
  oauthUrl?: string;
  features: {
    posting: boolean;
    scheduling: boolean;
    analytics: boolean;
    stories: boolean;
    directMessages: boolean;
  };
  rateLimits: {
    posts: { limit: number; window: string };
    api: { limit: number; window: string };
  };
}

export interface SocialAccountsFilter {
  platforms?: SocialPlatform[];
  status?: ConnectionStatus[];
  groups?: string[];
  search?: string;
  sortBy?: 'name' | 'platform' | 'lastActivity' | 'followers';
  sortOrder?: 'asc' | 'desc';
}

export interface SocialAccountsState {
  accounts: EnhancedSocialAccount[];
  groups: AccountGroup[];
  selectedAccounts: string[];
  filter: SocialAccountsFilter;
  isLoading: boolean;
  error?: string;
  bulkOperation?: BulkOperation;
}
