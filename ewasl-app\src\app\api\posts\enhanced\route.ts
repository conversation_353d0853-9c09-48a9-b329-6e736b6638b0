import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'

// Enhanced post creation schema
const createPostSchema = z.object({
  content: z.string().min(1, 'المحتوى مطلوب'),
  media_urls: z.array(z.string().url()).optional().default([]),
  status: z.enum(['DRAFT', 'SCHEDULED', 'PUBLISHED']),
  scheduled_at: z.string().datetime().optional(),
  social_account_ids: z.array(z.string()).min(1, 'يجب اختيار حساب واحد على الأقل'),
  timezone: z.string().default('Asia/Riyadh'),
  character_limits: z.record(z.number()).optional(),
  platform_settings: z.record(z.any()).optional().default({}),
})

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validation = createPostSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'بيانات غير صحيحة',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const {
      content,
      media_urls,
      status,
      scheduled_at,
      social_account_ids,
      timezone,
      character_limits,
      platform_settings
    } = validation.data

    // Validate social accounts belong to user
    const { data: userAccounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('id, platform, account_name')
      .eq('user_id', user.id)
      .in('id', social_account_ids)

    if (accountsError || !userAccounts || userAccounts.length !== social_account_ids.length) {
      return NextResponse.json(
        { error: 'بعض الحسابات المحددة غير صحيحة' },
        { status: 400 }
      )
    }

    // Extract hashtags and mentions from content
    const extractHashtags = (text: string): string[] => {
      const hashtagRegex = /#[\u0600-\u06FF\w]+/g
      return text.match(hashtagRegex) || []
    }

    const extractMentions = (text: string): string[] => {
      const mentionRegex = /@[\u0600-\u06FF\w]+/g
      return text.match(mentionRegex) || []
    }

    const extractPlainText = (html: string): string => {
      // Simple HTML tag removal - in production, use a proper HTML parser
      return html.replace(/<[^>]*>/g, '')
    }

    const plainText = extractPlainText(content)
    const hashtags = extractHashtags(plainText)
    const mentions = extractMentions(plainText)

    // Calculate character counts per platform
    const characterCounts: Record<string, number> = {}
    userAccounts.forEach(account => {
      characterCounts[account.platform] = plainText.length
    })

    // Create the main post record
    const { data: post, error: postError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content,
        content_html: content, // Store the rich HTML content
        media_urls,
        hashtags,
        mentions,
        status,
        scheduled_at: scheduled_at ? new Date(scheduled_at).toISOString() : null,
        platform_settings: platform_settings || {},
        character_counts: characterCounts,
        timezone,
        auto_publish: status === 'PUBLISHED',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (postError) {
      console.error('Error creating post:', postError)
      return NextResponse.json(
        { error: 'فشل في إنشاء المنشور' },
        { status: 500 }
      )
    }

    // Create post-social account associations
    const associations = social_account_ids.map(accountId => {
      const account = userAccounts.find(acc => acc.id === accountId)
      return {
        post_id: post.id,
        social_account_id: accountId,
        platform_content: content, // Could be customized per platform
        platform_media_urls: media_urls,
        status: status,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    })

    const { error: associationsError } = await supabase
      .from('post_social_accounts')
      .insert(associations)

    if (associationsError) {
      console.error('Error creating post associations:', associationsError)
      // Don't fail the request, but log the error
    }

    // If scheduled, add to queue
    if (status === 'SCHEDULED' && scheduled_at) {
      const { error: queueError } = await supabase
        .from('scheduled_posts_queue')
        .insert({
          post_id: post.id,
          scheduled_for: new Date(scheduled_at).toISOString(),
          timezone,
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (queueError) {
        console.error('Error adding to queue:', queueError)
        // Don't fail the request, but log the error
      }
    }

    // If immediate publishing, trigger publishing process
    if (status === 'PUBLISHED') {
      // In a real implementation, this would trigger a background job
      // For now, we'll just mark it as ready for publishing
      console.log('Post ready for immediate publishing:', post.id)
    }

    return NextResponse.json({
      success: true,
      post: {
        ...post,
        social_accounts: userAccounts,
        associations_created: associations.length
      },
      message: status === 'DRAFT' 
        ? 'تم حفظ المنشور كمسودة'
        : status === 'SCHEDULED'
        ? 'تم جدولة المنشور بنجاح'
        : 'تم إنشاء المنشور للنشر الفوري'
    })

  } catch (error) {
    console.error('Enhanced post creation error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء المنشور' },
      { status: 500 }
    )
  }
}

// Get enhanced posts list
export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const platform = searchParams.get('platform')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build query
    let query = supabase
      .from('posts')
      .select(`
        *,
        post_social_accounts (
          id,
          social_account_id,
          platform_content,
          status,
          platform_post_id,
          platform_url,
          published_at,
          error_message,
          social_accounts (
            id,
            platform,
            account_name,
            metadata
          )
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (status) {
      query = query.eq('status', status)
    }

    const { data: posts, error: postsError } = await query

    if (postsError) {
      console.error('Error fetching posts:', postsError)
      return NextResponse.json(
        { error: 'فشل في جلب المنشورات' },
        { status: 500 }
      )
    }

    // Transform posts data
    const enhancedPosts = (posts || []).map(post => ({
      ...post,
      platforms: post.post_social_accounts?.map((psa: any) => psa.social_accounts?.platform).filter(Boolean) || [],
      social_accounts: post.post_social_accounts?.map((psa: any) => psa.social_accounts).filter(Boolean) || [],
      publishing_status: post.post_social_accounts?.reduce((acc: any, psa: any) => {
        if (psa.social_accounts?.platform) {
          acc[psa.social_accounts.platform] = {
            status: psa.status,
            platform_post_id: psa.platform_post_id,
            platform_url: psa.platform_url,
            published_at: psa.published_at,
            error_message: psa.error_message
          }
        }
        return acc
      }, {}) || {}
    }))

    // Get summary statistics
    const { data: summary } = await supabase
      .from('posts')
      .select('status')
      .eq('user_id', user.id)

    const stats = {
      total: summary?.length || 0,
      draft: summary?.filter(p => p.status === 'DRAFT').length || 0,
      scheduled: summary?.filter(p => p.status === 'SCHEDULED').length || 0,
      published: summary?.filter(p => p.status === 'PUBLISHED').length || 0,
      failed: summary?.filter(p => p.status === 'FAILED').length || 0
    }

    return NextResponse.json({
      success: true,
      posts: enhancedPosts,
      pagination: {
        limit,
        offset,
        total: stats.total,
        hasMore: offset + limit < stats.total
      },
      stats
    })

  } catch (error) {
    console.error('Enhanced posts fetch error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب المنشورات' },
      { status: 500 }
    )
  }
}
