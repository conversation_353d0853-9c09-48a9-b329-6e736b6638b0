const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ajpcbugydftdyhlbddpl.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqcGNidWd5ZGZ0ZHlobGJkZHBsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODAzMzgwNiwiZXhwIjoyMDYzNjA5ODA2fQ.oEzL8RGuwDyLOupfw2TQLbpViDujaFnTGfDl6sBMFkc';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Email validation function (simplified version)
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!emailRegex.test(email)) {
    return false;
  }

  // Check for test/development patterns
  const testPatterns = [
    /test.*@/,
    /demo.*@/,
    /fake.*@/,
    /example.*@/,
    /@test\./,
    /@example\./,
    /@localhost/,
    /@.*\.test$/,
    /@.*\.local$/
  ];

  for (const pattern of testPatterns) {
    if (pattern.test(email.toLowerCase())) {
      return false;
    }
  }

  // Check for disposable domains
  const disposableDomains = [
    '10minutemail.com',
    'tempmail.org',
    'guerrillamail.com',
    'mailinator.com',
    'yopmail.com'
  ];

  const domain = email.split('@')[1]?.toLowerCase();
  if (disposableDomains.includes(domain)) {
    return false;
  }

  return true;
}

async function cleanupInvalidEmails() {
  console.log('🧹 eWasl Email Cleanup - Preventing Bounce Backs');
  console.log('================================================');
  
  try {
    // Get all users from the database
    const { data: users, error: fetchError } = await supabase
      .from('users')
      .select('id, email, created_at');

    if (fetchError) {
      console.error('❌ Error fetching users:', fetchError.message);
      return;
    }

    if (!users || users.length === 0) {
      console.log('✅ No users found in database');
      return;
    }

    console.log(`📊 Found ${users.length} users to check`);
    
    let validEmails = 0;
    let invalidEmails = 0;
    let cleanedEmails = [];

    for (const user of users) {
      if (!user.email) {
        console.log(`⚠️  User ${user.id}: No email address`);
        continue;
      }

      if (isValidEmail(user.email)) {
        validEmails++;
        console.log(`✅ Valid: ${user.email}`);
      } else {
        invalidEmails++;
        cleanedEmails.push(user);
        console.log(`❌ Invalid: ${user.email} (User ID: ${user.id})`);
      }
    }

    console.log('\n📈 Cleanup Summary:');
    console.log('==================');
    console.log(`✅ Valid emails: ${validEmails}`);
    console.log(`❌ Invalid emails: ${invalidEmails}`);
    console.log(`📊 Total checked: ${users.length}`);

    if (invalidEmails > 0) {
      console.log('\n🚨 INVALID EMAILS FOUND:');
      console.log('========================');
      
      for (const user of cleanedEmails) {
        console.log(`- ${user.email} (ID: ${user.id}, Created: ${user.created_at})`);
      }

      console.log('\n💡 RECOMMENDED ACTIONS:');
      console.log('======================');
      console.log('1. Contact users with invalid emails to update their addresses');
      console.log('2. Consider removing test/demo accounts');
      console.log('3. Implement email verification for existing users');
      console.log('4. Set up custom SMTP provider (SendGrid/Mailgun)');
      
      // Optionally, you can uncomment the following to automatically clean up test emails
      /*
      console.log('\n🗑️  Cleaning up test emails...');
      for (const user of cleanedEmails) {
        if (user.email.includes('test') || user.email.includes('demo') || user.email.includes('fake')) {
          const { error: deleteError } = await supabase
            .from('users')
            .delete()
            .eq('id', user.id);
          
          if (deleteError) {
            console.log(`❌ Failed to delete user ${user.id}: ${deleteError.message}`);
          } else {
            console.log(`✅ Deleted test user: ${user.email}`);
          }
        }
      }
      */
    } else {
      console.log('\n🎉 ALL EMAILS ARE VALID!');
      console.log('========================');
      console.log('✅ No cleanup needed');
      console.log('✅ Email deliverability should be good');
    }

    console.log('\n🔧 NEXT STEPS TO PREVENT FUTURE BOUNCES:');
    console.log('========================================');
    console.log('1. ✅ Enhanced email validation implemented');
    console.log('2. 🔄 Set up custom SMTP provider in Supabase');
    console.log('3. 📧 Configure SendGrid SMTP settings');
    console.log('4. 🧪 Use valid test emails during development');
    console.log('5. ✉️  Implement email verification flow');

  } catch (error) {
    console.error('❌ Error during cleanup:', error.message);
  }
}

// Run the cleanup
cleanupInvalidEmails().catch(console.error);
