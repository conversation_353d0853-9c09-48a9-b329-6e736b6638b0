/**
 * Background Job for Automatic Token Refresh
 * Runs periodically to refresh expiring OAuth tokens
 */

import { TokenRefreshService } from '@/lib/oauth/token-refresh-service';
import { createServiceRoleClient } from '@/lib/supabase/service-role';

export interface TokenRefreshJobResult {
  success: boolean;
  processed: number;
  successful: number;
  failed: number;
  errors: string[];
  duration: number;
  timestamp: string;
}

export class TokenRefreshJob {
  private tokenService = new TokenRefreshService();
  private supabase = createServiceRoleClient();

  /**
   * Run the token refresh job
   */
  async run(): Promise<TokenRefreshJobResult> {
    const startTime = Date.now();
    console.log('🔄 Starting automatic token refresh job...');

    try {
      // Log job start
      await this.logJobExecution('started');

      // Run token refresh
      const result = await this.tokenService.refreshAllExpiredTokens();

      // Send notifications for accounts needing reconnection
      if (result.needReconnection.length > 0) {
        await this.notifyUsersForReconnection(result.needReconnection);
      }

      // Log job completion
      await this.logJobExecution('completed', {
        processed: result.processed,
        successful: result.successful,
        failed: result.failed,
        needReconnection: result.needReconnection.length
      });

      const duration = Date.now() - startTime;
      
      console.log(`✅ Token refresh job completed in ${duration}ms`);
      console.log(`📊 Results: ${result.successful}/${result.processed} successful`);

      return {
        success: true,
        processed: result.processed,
        successful: result.successful,
        failed: result.failed,
        errors: [],
        duration,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error('❌ Token refresh job failed:', error);
      
      // Log job failure
      await this.logJobExecution('failed', { error: errorMessage });

      return {
        success: false,
        processed: 0,
        successful: 0,
        failed: 0,
        errors: [errorMessage],
        duration,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Check if job should run based on schedule
   */
  shouldRun(): boolean {
    // Run every 6 hours (configurable)
    const intervalHours = parseInt(process.env.TOKEN_REFRESH_INTERVAL_HOURS || '6');
    const intervalMs = intervalHours * 60 * 60 * 1000;
    
    // Check last run time from environment or database
    const lastRunKey = 'LAST_TOKEN_REFRESH_RUN';
    const lastRun = process.env[lastRunKey];
    
    if (!lastRun) {
      return true; // First run
    }

    const lastRunTime = new Date(lastRun).getTime();
    const now = Date.now();
    
    return (now - lastRunTime) >= intervalMs;
  }

  /**
   * Log job execution to database
   */
  private async logJobExecution(status: 'started' | 'completed' | 'failed', details?: any) {
    try {
      await this.supabase
        .from('job_logs')
        .insert({
          job_name: 'token_refresh',
          status,
          details: details || {},
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.warn('Failed to log job execution:', error);
    }
  }

  /**
   * Notify users that their accounts need reconnection
   */
  private async notifyUsersForReconnection(needReconnection: string[]) {
    try {
      console.log(`📧 Notifying ${needReconnection.length} users for account reconnection`);

      for (const userPlatform of needReconnection) {
        const [platform, userId] = userPlatform.split(':');
        
        // Get user details
        const { data: user, error: userError } = await this.supabase
          .from('users')
          .select('email, name')
          .eq('id', userId)
          .single();

        if (userError || !user) {
          console.warn(`User not found for notification: ${userId}`);
          continue;
        }

        // Create notification record
        await this.supabase
          .from('notifications')
          .insert({
            user_id: userId,
            type: 'account_reconnection_needed',
            title: 'إعادة ربط الحساب مطلوبة',
            message: `يحتاج حسابك على ${this.getPlatformNameArabic(platform)} إلى إعادة ربط. يرجى تسجيل الدخول وإعادة ربط الحساب.`,
            data: {
              platform,
              action_url: '/social'
            },
            created_at: new Date().toISOString()
          });

        // TODO: Send email notification
        // await this.sendReconnectionEmail(user.email, user.name, platform);
      }

    } catch (error) {
      console.error('Error sending reconnection notifications:', error);
    }
  }

  /**
   * Get platform name in Arabic
   */
  private getPlatformNameArabic(platform: string): string {
    const platformNames: Record<string, string> = {
      'FACEBOOK': 'فيسبوك',
      'INSTAGRAM': 'إنستغرام',
      'TWITTER': 'تويتر',
      'X': 'إكس',
      'LINKEDIN': 'لينكد إن',
      'TIKTOK': 'تيك توك'
    };

    return platformNames[platform.toUpperCase()] || platform;
  }

  /**
   * Get job status and statistics
   */
  async getJobStatus(): Promise<{
    lastRun?: string;
    nextRun?: string;
    recentRuns: any[];
    isEnabled: boolean;
  }> {
    try {
      // Get recent job logs
      const { data: recentRuns, error } = await this.supabase
        .from('job_logs')
        .select('*')
        .eq('job_name', 'token_refresh')
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Error fetching job logs:', error);
      }

      const lastRun = recentRuns?.[0]?.created_at;
      const intervalHours = parseInt(process.env.TOKEN_REFRESH_INTERVAL_HOURS || '6');
      const nextRun = lastRun ? 
        new Date(new Date(lastRun).getTime() + intervalHours * 60 * 60 * 1000).toISOString() : 
        undefined;

      return {
        lastRun,
        nextRun,
        recentRuns: recentRuns || [],
        isEnabled: process.env.TOKEN_REFRESH_ENABLED !== 'false'
      };

    } catch (error) {
      console.error('Error getting job status:', error);
      return {
        recentRuns: [],
        isEnabled: false
      };
    }
  }

  /**
   * Manual trigger for the job (for testing/admin use)
   */
  async trigger(): Promise<TokenRefreshJobResult> {
    console.log('🔧 Manually triggering token refresh job...');
    return await this.run();
  }
}

// Export singleton instance
export const tokenRefreshJob = new TokenRefreshJob();

/**
 * API helper to run token refresh job
 */
export async function runTokenRefreshJob(): Promise<TokenRefreshJobResult> {
  return await tokenRefreshJob.run();
}

/**
 * Check if token refresh job should run
 */
export function shouldRunTokenRefreshJob(): boolean {
  return tokenRefreshJob.shouldRun();
}

/**
 * Get token refresh job status
 */
export async function getTokenRefreshJobStatus() {
  return await tokenRefreshJob.getJobStatus();
}
