#!/usr/bin/env node

/**
 * Deploy Social Media Integration Fixes to Digital Ocean
 * This script deploys the updated app configuration with fixed credentials
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Deploying Social Media Integration Fixes to Digital Ocean');
console.log('===========================================================\n');

// Check if doctl is installed
function checkDoctl() {
  try {
    const version = execSync('doctl version', { encoding: 'utf8' });
    console.log('✅ doctl is installed:', version.trim());
    return true;
  } catch (error) {
    console.log('❌ doctl is not installed. Please install it first:');
    console.log('   • Download from: https://github.com/digitalocean/doctl/releases');
    console.log('   • Or install via package manager:');
    console.log('     - Windows: scoop install doctl');
    console.log('     - macOS: brew install doctl');
    console.log('     - Linux: snap install doctl');
    return false;
  }
}

// Check authentication
function checkAuth() {
  try {
    const account = execSync('doctl account get', { encoding: 'utf8' });
    console.log('✅ Digital Ocean authentication verified');
    return true;
  } catch (error) {
    console.log('❌ Not authenticated with Digital Ocean');
    console.log('   Please run: doctl auth init');
    console.log('   Then enter your API token');
    return false;
  }
}

// Get current apps
function getCurrentApps() {
  try {
    const apps = execSync('doctl apps list --format ID,Name,Status', { encoding: 'utf8' });
    console.log('📋 Current Digital Ocean Apps:');
    console.log(apps);
    return apps;
  } catch (error) {
    console.log('❌ Failed to get current apps:', error.message);
    return null;
  }
}

// Deploy updated app configuration
function deployUpdatedApp() {
  const appConfigPath = path.join(__dirname, '..', '.do', 'app-updated.yaml');
  
  if (!fs.existsSync(appConfigPath)) {
    console.log('❌ Updated app configuration not found at:', appConfigPath);
    return false;
  }

  try {
    console.log('🔄 Deploying updated app configuration...');
    console.log('   Config file:', appConfigPath);
    
    // Deploy the updated configuration
    const deployResult = execSync(`doctl apps create --spec "${appConfigPath}"`, { 
      encoding: 'utf8',
      stdio: 'inherit' 
    });
    
    console.log('✅ App deployment initiated successfully!');
    console.log('📊 Deploy result:', deployResult);
    
    return true;
  } catch (error) {
    console.log('❌ Deployment failed:', error.message);
    
    // If app already exists, try updating instead
    console.log('🔄 App might already exist, trying update instead...');
    
    try {
      // List apps to get the ID
      const appsOutput = execSync('doctl apps list --format ID,Name --no-header', { encoding: 'utf8' });
      const lines = appsOutput.trim().split('\n');
      
      let appId = null;
      for (const line of lines) {
        if (line.includes('ewasl-social-scheduler')) {
          appId = line.split(/\s+/)[0];
          break;
        }
      }
      
      if (appId) {
        console.log(`🔄 Updating existing app (ID: ${appId})...`);
        const updateResult = execSync(`doctl apps update ${appId} --spec "${appConfigPath}"`, { 
          encoding: 'utf8',
          stdio: 'inherit' 
        });
        
        console.log('✅ App updated successfully!');
        return true;
      } else {
        console.log('❌ Could not find existing app to update');
        return false;
      }
    } catch (updateError) {
      console.log('❌ Update also failed:', updateError.message);
      return false;
    }
  }
}

// Main deployment function
async function main() {
  try {
    // Pre-flight checks
    console.log('🔍 Pre-flight checks...');
    
    if (!checkDoctl()) {
      process.exit(1);
    }
    
    if (!checkAuth()) {
      process.exit(1);
    }
    
    // Show current state
    getCurrentApps();
    
    // Deploy updated configuration
    console.log('\n🚀 Starting deployment...');
    const deploySuccess = deployUpdatedApp();
    
    if (!deploySuccess) {
      console.log('❌ Deployment failed. Please check the errors above.');
      process.exit(1);
    }
    
    console.log('\n🎉 DEPLOYMENT INITIATED SUCCESSFULLY!');
    console.log('📊 Summary:');
    console.log('   • Social media credentials updated');
    console.log('   • Security enhancements deployed');
    console.log('   • OAuth flows ready for production');
    
    console.log('\n⏰ Next: Wait 5-10 minutes for deployment to complete');
    
  } catch (error) {
    console.error('\n💥 Deployment script error:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { 
  checkDoctl, 
  checkAuth, 
  deployUpdatedApp 
}; 