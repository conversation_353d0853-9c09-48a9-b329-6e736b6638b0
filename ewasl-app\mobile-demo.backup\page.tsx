"use client";

import React, { useState, useEffect } from 'react';
import { MobileOptimizedLayout } from '@/components/mobile/mobile-optimized-layout';
import { MobileDashboard } from '@/components/mobile/mobile-dashboard';
import { MobilePostCard } from '@/components/mobile/mobile-post-card';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Smartphone,
  Tablet,
  Monitor,
  TouchIcon as Touch,
  Zap,
  Eye,
  Globe,
  CheckCircle,
  Info,
  ArrowLeft,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';

interface MobileDemoPageProps {
  searchParams?: {
    lang?: string;
  };
}

export default function MobileDemoPage({ searchParams }: MobileDemoPageProps) {
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('mobile');
  const [selectedDemo, setSelectedDemo] = useState('dashboard');
  const router = useRouter();

  // Translations
  const t = {
    ar: {
      title: 'عرض التصميم المتجاوب للجوال',
      subtitle: 'تجربة محسنة للجوال مع تفاعلات سهلة اللمس',
      backToMain: 'العودة للرئيسية',
      switchLanguage: 'English',
      features: {
        title: 'ميزات التصميم المتجاوب',
        touchOptimized: {
          title: 'محسن للمس',
          description: 'أزرار وعناصر تفاعل كبيرة وسهلة اللمس'
        },
        responsive: {
          title: 'تصميم متجاوب',
          description: 'يتكيف تلقائياً مع جميع أحجام الشاشات'
        },
        fastLoading: {
          title: 'تحميل سريع',
          description: 'محسن للأداء على الشبكات البطيئة'
        },
        offline: {
          title: 'دعم عدم الاتصال',
          description: 'يعمل حتى بدون اتصال بالإنترنت'
        }
      },
      demos: {
        dashboard: 'لوحة التحكم',
        posts: 'المنشورات',
        layout: 'التخطيط'
      },
      deviceTypes: {
        mobile: 'جوال',
        tablet: 'تابلت',
        desktop: 'سطح المكتب'
      },
      stats: {
        title: 'إحصائيات الأداء',
        loadTime: 'وقت التحميل',
        touchTargets: 'أهداف اللمس',
        accessibility: 'إمكانية الوصول',
        performance: 'الأداء'
      }
    },
    en: {
      title: 'Mobile-First Responsive Design Demo',
      subtitle: 'Optimized mobile experience with touch-friendly interactions',
      backToMain: 'Back to Main',
      switchLanguage: 'العربية',
      features: {
        title: 'Responsive Design Features',
        touchOptimized: {
          title: 'Touch Optimized',
          description: 'Large, easy-to-tap buttons and interactive elements'
        },
        responsive: {
          title: 'Responsive Design',
          description: 'Automatically adapts to all screen sizes'
        },
        fastLoading: {
          title: 'Fast Loading',
          description: 'Optimized for performance on slow networks'
        },
        offline: {
          title: 'Offline Support',
          description: 'Works even without internet connection'
        }
      },
      demos: {
        dashboard: 'Dashboard',
        posts: 'Posts',
        layout: 'Layout'
      },
      deviceTypes: {
        mobile: 'Mobile',
        tablet: 'Tablet',
        desktop: 'Desktop'
      },
      stats: {
        title: 'Performance Stats',
        loadTime: 'Load Time',
        touchTargets: 'Touch Targets',
        accessibility: 'Accessibility',
        performance: 'Performance'
      }
    }
  };

  const text = t[language];

  useEffect(() => {
    // Set language from search params
    if (searchParams?.lang === 'en') {
      setLanguage('en');
    }

    // Detect device type
    const checkDeviceType = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setDeviceType('mobile');
      } else if (width < 1024) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    checkDeviceType();
    window.addEventListener('resize', checkDeviceType);
    return () => window.removeEventListener('resize', checkDeviceType);
  }, [searchParams]);

  const toggleLanguage = () => {
    const newLang = language === 'ar' ? 'en' : 'ar';
    setLanguage(newLang);
    
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('lang', newLang);
    window.history.replaceState({}, '', url.toString());
  };

  // Sample post data
  const samplePost = {
    id: '1',
    content: language === 'ar' 
      ? 'هذا مثال على منشور محسن للجوال مع تصميم متجاوب وتفاعلات سهلة اللمس. يتضمن المنشور صوراً ومقاطع فيديو وإحصائيات مفصلة.'
      : 'This is an example of a mobile-optimized post with responsive design and touch-friendly interactions. The post includes images, videos, and detailed analytics.',
    mediaUrls: [
      'https://via.placeholder.com/400x400/3b82f6/ffffff?text=Image+1',
      'https://via.placeholder.com/400x400/10b981/ffffff?text=Image+2',
      'https://via.placeholder.com/400x400/f59e0b/ffffff?text=Video',
      'https://via.placeholder.com/400x400/ef4444/ffffff?text=Image+3',
      'https://via.placeholder.com/400x400/8b5cf6/ffffff?text=Image+4'
    ],
    platforms: ['instagram', 'facebook', 'twitter'],
    status: 'published' as const,
    publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    analytics: {
      views: 12847,
      likes: 1234,
      comments: 89,
      shares: 156,
      engagement: 8.2
    },
    author: {
      name: language === 'ar' ? 'أحمد محمد' : 'Ahmed Mohammed',
      avatar: 'https://via.placeholder.com/40x40/3b82f6/ffffff?text=AM'
    }
  };

  // Device type icons
  const deviceIcons = {
    mobile: Smartphone,
    tablet: Tablet,
    desktop: Monitor
  };

  const DeviceIcon = deviceIcons[deviceType];

  return (
    <div className={cn(
      "min-h-screen bg-gray-50",
      language === 'ar' ? "rtl" : "ltr"
    )}>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className={cn(
          "flex items-center justify-between",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          <div className={language === 'ar' ? "text-right" : ""}>
            <div className={cn(
              "flex items-center gap-3 mb-2",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <h1 className="text-3xl font-bold text-gray-900">{text.title}</h1>
              <Badge variant="secondary" className="text-xs">
                <Sparkles className="w-3 h-3 mr-1" />
                {language === 'ar' ? 'جديد' : 'New'}
              </Badge>
            </div>
            <p className="text-xl text-gray-600">{text.subtitle}</p>
          </div>
          
          <div className={cn(
            "flex items-center gap-4",
            language === 'ar' ? "flex-row-reverse" : ""
          )}>
            <Button
              variant="outline"
              onClick={toggleLanguage}
              className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}
            >
              <Globe className="w-4 h-4" />
              {text.switchLanguage}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}
            >
              <ArrowLeft className="w-4 h-4" />
              {text.backToMain}
            </Button>
          </div>
        </div>

        {/* Device Type Indicator */}
        <Alert className="border-blue-200 bg-blue-50">
          <DeviceIcon className="h-4 w-4 text-blue-600" />
          <AlertTitle className={cn(
            "text-blue-900",
            language === 'ar' ? "text-right" : ""
          )}>
            {language === 'ar' ? 'نوع الجهاز الحالي' : 'Current Device Type'}: {text.deviceTypes[deviceType]}
          </AlertTitle>
          <AlertDescription className={cn(
            "text-blue-800",
            language === 'ar' ? "text-right" : ""
          )}>
            {language === 'ar' 
              ? 'التصميم يتكيف تلقائياً مع حجم شاشتك لتوفير أفضل تجربة مستخدم'
              : 'The design automatically adapts to your screen size for the best user experience'
            }
          </AlertDescription>
        </Alert>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Touch className="w-5 h-5 text-blue-600" />
                </div>
                <CardTitle className="text-lg">{text.features.touchOptimized.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.features.touchOptimized.description}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Smartphone className="w-5 h-5 text-green-600" />
                </div>
                <CardTitle className="text-lg">{text.features.responsive.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.features.responsive.description}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-orange-600" />
                </div>
                <CardTitle className="text-lg">{text.features.fastLoading.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.features.fastLoading.description}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-purple-600" />
                </div>
                <CardTitle className="text-lg">{text.features.offline.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.features.offline.description}
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* Performance Stats */}
        <Card>
          <CardHeader>
            <CardTitle className={language === 'ar' ? "text-right" : ""}>
              {text.stats.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className={cn("text-center", language === 'ar' ? "text-right" : "")}>
                <div className="text-2xl font-bold text-green-600">< 1s</div>
                <div className="text-sm text-gray-600">{text.stats.loadTime}</div>
              </div>
              <div className={cn("text-center", language === 'ar' ? "text-right" : "")}>
                <div className="text-2xl font-bold text-blue-600">44px+</div>
                <div className="text-sm text-gray-600">{text.stats.touchTargets}</div>
              </div>
              <div className={cn("text-center", language === 'ar' ? "text-right" : "")}>
                <div className="text-2xl font-bold text-purple-600">AAA</div>
                <div className="text-sm text-gray-600">{text.stats.accessibility}</div>
              </div>
              <div className={cn("text-center", language === 'ar' ? "text-right" : "")}>
                <div className="text-2xl font-bold text-orange-600">95+</div>
                <div className="text-sm text-gray-600">{text.stats.performance}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Demo Components */}
        <Tabs value={selectedDemo} onValueChange={setSelectedDemo} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="dashboard">{text.demos.dashboard}</TabsTrigger>
            <TabsTrigger value="posts">{text.demos.posts}</TabsTrigger>
            <TabsTrigger value="layout">{text.demos.layout}</TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className={language === 'ar' ? "text-right" : ""}>
                  {language === 'ar' ? 'لوحة التحكم المحسنة للجوال' : 'Mobile-Optimized Dashboard'}
                </CardTitle>
                <CardDescription className={language === 'ar' ? "text-right" : ""}>
                  {language === 'ar' 
                    ? 'لوحة تحكم متجاوبة مع تبويبات محسنة للجوال وعناصر تفاعل سهلة اللمس'
                    : 'Responsive dashboard with mobile-optimized tabs and touch-friendly interactions'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <MobileDashboard language={language} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="posts" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className={language === 'ar' ? "text-right" : ""}>
                  {language === 'ar' ? 'بطاقة منشور محسنة للجوال' : 'Mobile-Optimized Post Card'}
                </CardTitle>
                <CardDescription className={language === 'ar' ? "text-right" : ""}>
                  {language === 'ar' 
                    ? 'بطاقة منشور مع معاينة وسائط تفاعلية وإحصائيات مفصلة'
                    : 'Post card with interactive media preview and detailed analytics'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <MobilePostCard 
                  post={samplePost} 
                  language={language}
                  onEdit={(id) => console.log('Edit post:', id)}
                  onDelete={(id) => console.log('Delete post:', id)}
                  onDuplicate={(id) => console.log('Duplicate post:', id)}
                  onView={(id) => console.log('View post:', id)}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="layout" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className={language === 'ar' ? "text-right" : ""}>
                  {language === 'ar' ? 'تخطيط محسن للجوال' : 'Mobile-Optimized Layout'}
                </CardTitle>
                <CardDescription className={language === 'ar' ? "text-right" : ""}>
                  {language === 'ar' 
                    ? 'تخطيط متجاوب مع شريط جانبي قابل للطي وقائمة جوال'
                    : 'Responsive layout with collapsible sidebar and mobile menu'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Info className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                  <p className="text-gray-600">
                    {language === 'ar' 
                      ? 'التخطيط المحسن للجوال نشط حالياً في هذه الصفحة'
                      : 'Mobile-optimized layout is currently active on this page'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
