import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const FACEBOOK_APP_ID = '****************';

interface PermissionCheck {
  permission: string;
  required: boolean;
  granted: boolean;
  status: 'granted' | 'declined' | 'expired' | 'missing';
  description_ar: string;
}

interface AccountPermissionResult {
  account_id: string;
  account_name: string;
  user_id?: string;
  page_id?: string;
  permissions: PermissionCheck[];
  can_publish: boolean;
  missing_permissions: string[];
  recommendations: string[];
}

const REQUIRED_PERMISSIONS = [
  {
    permission: 'pages_manage_posts',
    required: true,
    description_ar: 'إدارة منشورات الصفحات - مطلوب للنشر على صفحات فيسبوك'
  },
  {
    permission: 'pages_read_engagement',
    required: true,
    description_ar: 'قراءة تفاعل الصفحات - مطلوب لقراءة إحصائيات المنشورات'
  },
  {
    permission: 'publish_to_groups',
    required: false,
    description_ar: 'النشر في المجموعات - اختياري للنشر في مجموعات فيسبوك'
  },
  {
    permission: 'pages_show_list',
    required: true,
    description_ar: 'عرض قائمة الصفحات - مطلوب لاختيار الصفحات المتاحة'
  },
  {
    permission: 'public_profile',
    required: true,
    description_ar: 'الملف الشخصي العام - مطلوب للوصول الأساسي'
  }
];

async function checkAccountPermissions(accessToken: string, accountId: string): Promise<{
  permissions: PermissionCheck[];
  user_id?: string;
  error?: string;
}> {
  try {
    // Get user permissions
    const permissionsResponse = await fetch(`https://graph.facebook.com/me/permissions?access_token=${accessToken}`);
    const permissionsData = await permissionsResponse.json();

    if (!permissionsResponse.ok) {
      return {
        permissions: [],
        error: permissionsData.error?.message || 'فشل في جلب الصلاحيات'
      };
    }

    // Get user info
    const userResponse = await fetch(`https://graph.facebook.com/me?access_token=${accessToken}`);
    const userData = await userResponse.json();

    const grantedPermissions = permissionsData.data || [];
    const permissionMap = new Map(grantedPermissions.map((p: any) => [p.permission, p.status]));

    const permissionChecks: PermissionCheck[] = REQUIRED_PERMISSIONS.map(reqPerm => {
      const status = permissionMap.get(reqPerm.permission) || 'missing';
      return {
        permission: reqPerm.permission,
        required: reqPerm.required,
        granted: status === 'granted',
        status: status as 'granted' | 'declined' | 'expired' | 'missing',
        description_ar: reqPerm.description_ar
      };
    });

    return {
      permissions: permissionChecks,
      user_id: userData.id
    };
  } catch (error) {
    return {
      permissions: [],
      error: `خطأ في فحص الصلاحيات: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
    };
  }
}

async function checkPagePermissions(accessToken: string, pageId: string): Promise<{
  can_manage: boolean;
  page_info?: any;
  error?: string;
}> {
  try {
    // Check if we can access the page
    const pageResponse = await fetch(`https://graph.facebook.com/${pageId}?fields=id,name,access_token&access_token=${accessToken}`);
    const pageData = await pageResponse.json();

    if (!pageResponse.ok) {
      return {
        can_manage: false,
        error: pageData.error?.message || 'لا يمكن الوصول للصفحة'
      };
    }

    return {
      can_manage: true,
      page_info: pageData
    };
  } catch (error) {
    return {
      can_manage: false,
      error: `خطأ في فحص صلاحيات الصفحة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
    };
  }
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    console.log('🔍 [Facebook Permissions Test] Starting Facebook permissions validation...');

    // Fetch all Facebook social accounts
    const { data: socialAccounts, error: fetchError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('platform', 'FACEBOOK')
      .eq('status', 'CONNECTED');

    if (fetchError) {
      console.error('❌ [Facebook Permissions Test] Database fetch error:', fetchError);
      return NextResponse.json({
        success: false,
        message: 'خطأ في قاعدة البيانات',
        error: fetchError.message,
        response_time_ms: Date.now() - startTime
      }, { status: 500 });
    }

    console.log(`📊 [Facebook Permissions Test] Found ${socialAccounts?.length || 0} Facebook accounts`);

    if (!socialAccounts || socialAccounts.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'لا توجد حسابات فيسبوك متصلة للفحص',
        accounts: [],
        app_info: {
          app_id: FACEBOOK_APP_ID,
          required_permissions: REQUIRED_PERMISSIONS.map(p => ({
            permission: p.permission,
            required: p.required,
            description: p.description_ar
          }))
        },
        response_time_ms: Date.now() - startTime
      });
    }

    // Check permissions for each account
    const permissionResults: AccountPermissionResult[] = [];

    for (const account of socialAccounts) {
      console.log(`🔍 [Facebook Permissions Test] Checking permissions for: ${account.account_name || account.account_id}`);
      
      const permissionCheck = await checkAccountPermissions(account.access_token, account.account_id);
      
      if (permissionCheck.error) {
        permissionResults.push({
          account_id: account.account_id,
          account_name: account.account_name || 'غير محدد',
          permissions: [],
          can_publish: false,
          missing_permissions: ['جميع الصلاحيات'],
          recommendations: [
            'إعادة ربط الحساب مطلوبة',
            permissionCheck.error
          ]
        });
        continue;
      }

      const missingRequired = permissionCheck.permissions
        .filter(p => p.required && !p.granted)
        .map(p => p.permission);

      const canPublish = missingRequired.length === 0;

      let recommendations: string[] = [];
      if (!canPublish) {
        recommendations.push('يجب الحصول على الصلاحيات المطلوبة للنشر');
        recommendations.push(`الصلاحيات المفقودة: ${missingRequired.join(', ')}`);
      }

      // Check page permissions if page_id exists
      if (account.page_id) {
        const pageCheck = await checkPagePermissions(account.access_token, account.page_id);
        if (!pageCheck.can_manage) {
          recommendations.push(`لا يمكن إدارة الصفحة: ${pageCheck.error}`);
        }
      }

      if (canPublish && recommendations.length === 0) {
        recommendations.push('جميع الصلاحيات متوفرة ✅');
      }

      permissionResults.push({
        account_id: account.account_id,
        account_name: account.account_name || 'غير محدد',
        user_id: permissionCheck.user_id,
        page_id: account.page_id,
        permissions: permissionCheck.permissions,
        can_publish: canPublish,
        missing_permissions: missingRequired,
        recommendations
      });

      console.log(`${canPublish ? '✅' : '❌'} [Facebook Permissions Test] Account ${account.account_name}: ${canPublish ? 'يمكن النشر' : 'صلاحيات مفقودة'}`);
    }

    // Summary statistics
    const publishableAccounts = permissionResults.filter(r => r.can_publish).length;
    const totalAccounts = permissionResults.length;

    const response = {
      success: true,
      message: `تم فحص صلاحيات ${totalAccounts} حساب فيسبوك`,
      app_info: {
        app_id: FACEBOOK_APP_ID,
        app_url: `https://developers.facebook.com/apps/${FACEBOOK_APP_ID}/dashboard/`,
        required_permissions: REQUIRED_PERMISSIONS.map(p => ({
          permission: p.permission,
          required: p.required,
          description: p.description_ar
        }))
      },
      summary: {
        total_accounts: totalAccounts,
        publishable_accounts: publishableAccounts,
        blocked_accounts: totalAccounts - publishableAccounts,
        success_rate: `${Math.round((publishableAccounts / totalAccounts) * 100)}%`
      },
      accounts: permissionResults,
      global_recommendations: publishableAccounts < totalAccounts ? [
        'قم بمراجعة إعدادات التطبيق في Facebook Developer Console',
        'تأكد من موافقة فيسبوك على جميع الصلاحيات المطلوبة',
        'أعد ربط الحسابات التي تفتقر للصلاحيات المطلوبة',
        `رابط إدارة التطبيق: https://developers.facebook.com/apps/${FACEBOOK_APP_ID}/app-review/`
      ] : [
        'جميع الحسابات لديها الصلاحيات المطلوبة ✅',
        'يمكن المتابعة مع اختبار النشر الفعلي'
      ],
      response_time_ms: Date.now() - startTime,
      timestamp: new Date().toISOString()
    };

    console.log(`✅ [Facebook Permissions Test] Completed: ${publishableAccounts}/${totalAccounts} accounts can publish`);
    
    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ [Facebook Permissions Test] Unexpected error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'خطأ في خادم اختبار صلاحيات فيسبوك',
      error: error instanceof Error ? error.message : 'خطأ غير معروف',
      response_time_ms: Date.now() - startTime,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
