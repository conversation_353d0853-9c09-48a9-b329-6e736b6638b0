/**
 * Error Codes and Arabic Messages for eWasl Platform
 * Provides standardized error handling with Arabic localization
 */

export interface ErrorCode {
  code: string;
  httpStatus: number;
  arabicMessage: string;
  englishMessage: string;
  troubleshooting?: string;
  category: 'authentication' | 'authorization' | 'validation' | 'database' | 'network' | 'publishing' | 'oauth' | 'system';
}

export const ERROR_CODES: Record<string, ErrorCode> = {
  // Authentication Errors (AUTH_*)
  AUTH_REQUIRED: {
    code: 'AUTH_REQUIRED',
    httpStatus: 401,
    arabicMessage: 'المصادقة مطلوبة للوصول إلى هذا المورد',
    englishMessage: 'Authentication required to access this resource',
    troubleshooting: 'يرجى تسجيل الدخول أولاً',
    category: 'authentication'
  },
  AUTH_INVALID_TOKEN: {
    code: 'AUTH_INVALID_TOKEN',
    httpStatus: 401,
    arabicMessage: 'الرمز المميز غير صحيح أو منتهي الصلاحية',
    englishMessage: 'Invalid or expired authentication token',
    troubleshooting: 'يرجى تسجيل الدخول مرة أخرى',
    category: 'authentication'
  },
  AUTH_SESSION_EXPIRED: {
    code: 'AUTH_SESSION_EXPIRED',
    httpStatus: 401,
    arabicMessage: 'انتهت صلاحية جلسة المستخدم',
    englishMessage: 'User session has expired',
    troubleshooting: 'يرجى تسجيل الدخول مرة أخرى',
    category: 'authentication'
  },

  // Authorization Errors (AUTHZ_*)
  AUTHZ_FORBIDDEN: {
    code: 'AUTHZ_FORBIDDEN',
    httpStatus: 403,
    arabicMessage: 'غير مصرح لك بالوصول إلى هذا المورد',
    englishMessage: 'You are not authorized to access this resource',
    troubleshooting: 'تحقق من صلاحياتك أو اتصل بالمسؤول',
    category: 'authorization'
  },
  AUTHZ_INSUFFICIENT_PERMISSIONS: {
    code: 'AUTHZ_INSUFFICIENT_PERMISSIONS',
    httpStatus: 403,
    arabicMessage: 'صلاحياتك غير كافية لتنفيذ هذا الإجراء',
    englishMessage: 'Insufficient permissions to perform this action',
    troubleshooting: 'اطلب صلاحيات إضافية من المسؤول',
    category: 'authorization'
  },

  // Validation Errors (VAL_*)
  VAL_INVALID_INPUT: {
    code: 'VAL_INVALID_INPUT',
    httpStatus: 400,
    arabicMessage: 'البيانات المدخلة غير صحيحة',
    englishMessage: 'Invalid input data provided',
    troubleshooting: 'تحقق من صحة البيانات المدخلة',
    category: 'validation'
  },
  VAL_MISSING_REQUIRED_FIELD: {
    code: 'VAL_MISSING_REQUIRED_FIELD',
    httpStatus: 400,
    arabicMessage: 'حقل مطلوب مفقود',
    englishMessage: 'Required field is missing',
    troubleshooting: 'تأكد من إدخال جميع الحقول المطلوبة',
    category: 'validation'
  },
  VAL_CONTENT_TOO_LONG: {
    code: 'VAL_CONTENT_TOO_LONG',
    httpStatus: 400,
    arabicMessage: 'المحتوى طويل جداً (الحد الأقصى 2800 حرف)',
    englishMessage: 'Content is too long (maximum 2800 characters)',
    troubleshooting: 'قم بتقصير المحتوى ليكون ضمن الحد المسموح',
    category: 'validation'
  },
  VAL_INVALID_MEDIA_FORMAT: {
    code: 'VAL_INVALID_MEDIA_FORMAT',
    httpStatus: 400,
    arabicMessage: 'تنسيق الوسائط غير مدعوم',
    englishMessage: 'Unsupported media format',
    troubleshooting: 'استخدم تنسيقات مدعومة: JPG, PNG, MP4',
    category: 'validation'
  },

  // Database Errors (DB_*)
  DB_CONNECTION_FAILED: {
    code: 'DB_CONNECTION_FAILED',
    httpStatus: 500,
    arabicMessage: 'فشل في الاتصال بقاعدة البيانات',
    englishMessage: 'Database connection failed',
    troubleshooting: 'يرجى المحاولة مرة أخرى لاحقاً',
    category: 'database'
  },
  DB_QUERY_FAILED: {
    code: 'DB_QUERY_FAILED',
    httpStatus: 500,
    arabicMessage: 'فشل في تنفيذ استعلام قاعدة البيانات',
    englishMessage: 'Database query execution failed',
    troubleshooting: 'يرجى المحاولة مرة أخرى أو الاتصال بالدعم',
    category: 'database'
  },
  DB_RECORD_NOT_FOUND: {
    code: 'DB_RECORD_NOT_FOUND',
    httpStatus: 404,
    arabicMessage: 'السجل المطلوب غير موجود',
    englishMessage: 'Requested record not found',
    troubleshooting: 'تحقق من صحة المعرف المستخدم',
    category: 'database'
  },
  DB_DUPLICATE_ENTRY: {
    code: 'DB_DUPLICATE_ENTRY',
    httpStatus: 409,
    arabicMessage: 'السجل موجود مسبقاً',
    englishMessage: 'Record already exists',
    troubleshooting: 'استخدم بيانات مختلفة أو قم بتحديث السجل الموجود',
    category: 'database'
  },

  // Network Errors (NET_*)
  NET_REQUEST_TIMEOUT: {
    code: 'NET_REQUEST_TIMEOUT',
    httpStatus: 408,
    arabicMessage: 'انتهت مهلة الطلب',
    englishMessage: 'Request timeout',
    troubleshooting: 'تحقق من اتصال الإنترنت وحاول مرة أخرى',
    category: 'network'
  },
  NET_SERVICE_UNAVAILABLE: {
    code: 'NET_SERVICE_UNAVAILABLE',
    httpStatus: 503,
    arabicMessage: 'الخدمة غير متاحة حالياً',
    englishMessage: 'Service temporarily unavailable',
    troubleshooting: 'يرجى المحاولة مرة أخرى بعد قليل',
    category: 'network'
  },

  // Publishing Errors (PUB_*)
  PUB_NO_SOCIAL_ACCOUNTS: {
    code: 'PUB_NO_SOCIAL_ACCOUNTS',
    httpStatus: 400,
    arabicMessage: 'لا توجد حسابات اجتماعية متصلة',
    englishMessage: 'No connected social media accounts found',
    troubleshooting: 'قم بربط حساب اجتماعي واحد على الأقل',
    category: 'publishing'
  },
  PUB_PLATFORM_ERROR: {
    code: 'PUB_PLATFORM_ERROR',
    httpStatus: 500,
    arabicMessage: 'فشل في النشر على المنصة الاجتماعية',
    englishMessage: 'Failed to publish to social media platform',
    troubleshooting: 'تحقق من صحة الاتصال بالمنصة وحاول مرة أخرى',
    category: 'publishing'
  },
  PUB_MEDIA_REQUIRED: {
    code: 'PUB_MEDIA_REQUIRED',
    httpStatus: 400,
    arabicMessage: 'منشورات انستغرام تتطلب صورة أو فيديو واحد على الأقل',
    englishMessage: 'Instagram posts require at least one image or video',
    troubleshooting: 'أضف صورة أو فيديو للمنشور',
    category: 'publishing'
  },
  PUB_CONTENT_REJECTED: {
    code: 'PUB_CONTENT_REJECTED',
    httpStatus: 400,
    arabicMessage: 'تم رفض المحتوى من قبل المنصة الاجتماعية',
    englishMessage: 'Content rejected by social media platform',
    troubleshooting: 'راجع سياسات المنصة وعدل المحتوى',
    category: 'publishing'
  },

  // OAuth Errors (OAUTH_*)
  OAUTH_STATE_INVALID: {
    code: 'OAUTH_STATE_INVALID',
    httpStatus: 400,
    arabicMessage: 'حالة OAuth غير صحيحة أو منتهية الصلاحية',
    englishMessage: 'Invalid or expired OAuth state',
    troubleshooting: 'ابدأ عملية الربط من جديد',
    category: 'oauth'
  },
  OAUTH_CODE_EXCHANGE_FAILED: {
    code: 'OAUTH_CODE_EXCHANGE_FAILED',
    httpStatus: 400,
    arabicMessage: 'فشل في تبديل رمز OAuth',
    englishMessage: 'OAuth code exchange failed',
    troubleshooting: 'حاول ربط الحساب مرة أخرى',
    category: 'oauth'
  },
  OAUTH_TOKEN_EXPIRED: {
    code: 'OAUTH_TOKEN_EXPIRED',
    httpStatus: 401,
    arabicMessage: 'انتهت صلاحية رمز الوصول للمنصة الاجتماعية',
    englishMessage: 'Social media access token has expired',
    troubleshooting: 'أعد ربط الحساب الاجتماعي',
    category: 'oauth'
  },
  OAUTH_REFRESH_FAILED: {
    code: 'OAUTH_REFRESH_FAILED',
    httpStatus: 401,
    arabicMessage: 'فشل في تحديث رمز الوصول',
    englishMessage: 'Failed to refresh access token',
    troubleshooting: 'أعد ربط الحساب الاجتماعي',
    category: 'oauth'
  },

  // System Errors (SYS_*)
  SYS_INTERNAL_ERROR: {
    code: 'SYS_INTERNAL_ERROR',
    httpStatus: 500,
    arabicMessage: 'خطأ داخلي في النظام',
    englishMessage: 'Internal system error',
    troubleshooting: 'يرجى المحاولة مرة أخرى أو الاتصال بالدعم',
    category: 'system'
  },
  SYS_MAINTENANCE: {
    code: 'SYS_MAINTENANCE',
    httpStatus: 503,
    arabicMessage: 'النظام قيد الصيانة',
    englishMessage: 'System is under maintenance',
    troubleshooting: 'يرجى المحاولة مرة أخرى لاحقاً',
    category: 'system'
  },
  SYS_RATE_LIMIT_EXCEEDED: {
    code: 'SYS_RATE_LIMIT_EXCEEDED',
    httpStatus: 429,
    arabicMessage: 'تم تجاوز الحد المسموح من الطلبات',
    englishMessage: 'Rate limit exceeded',
    troubleshooting: 'انتظر قليلاً قبل إرسال طلبات جديدة',
    category: 'system'
  },

  // Instagram Specific Errors (IG_*)
  IG_TOKEN_EXPIRED: {
    code: 'IG_TOKEN_EXPIRED',
    httpStatus: 401,
    arabicMessage: 'انتهت صلاحية رمز انستغرام',
    englishMessage: 'Instagram token has expired',
    troubleshooting: 'سيتم تحديث الرمز تلقائياً أو أعد ربط الحساب',
    category: 'oauth'
  },
  IG_MEDIA_UPLOAD_FAILED: {
    code: 'IG_MEDIA_UPLOAD_FAILED',
    httpStatus: 500,
    arabicMessage: 'فشل في رفع الوسائط إلى انستغرام',
    englishMessage: 'Failed to upload media to Instagram',
    troubleshooting: 'تحقق من تنسيق الملف وحجمه',
    category: 'publishing'
  },
  IG_CONTAINER_CREATION_FAILED: {
    code: 'IG_CONTAINER_CREATION_FAILED',
    httpStatus: 500,
    arabicMessage: 'فشل في إنشاء حاوية الوسائط لانستغرام',
    englishMessage: 'Failed to create Instagram media container',
    troubleshooting: 'تحقق من صحة الوسائط والمحتوى',
    category: 'publishing'
  },

  // Facebook Specific Errors (FB_*)
  FB_TOKEN_EXPIRED: {
    code: 'FB_TOKEN_EXPIRED',
    httpStatus: 401,
    arabicMessage: 'انتهت صلاحية رمز فيسبوك',
    englishMessage: 'Facebook token has expired',
    troubleshooting: 'سيتم تحديث الرمز تلقائياً أو أعد ربط الحساب',
    category: 'oauth'
  },
  FB_PAGE_NOT_FOUND: {
    code: 'FB_PAGE_NOT_FOUND',
    httpStatus: 404,
    arabicMessage: 'صفحة فيسبوك غير موجودة أو غير متاحة',
    englishMessage: 'Facebook page not found or not accessible',
    troubleshooting: 'تحقق من صلاحيات الوصول للصفحة',
    category: 'publishing'
  }
};

/**
 * Get error details by code
 */
export function getErrorByCode(code: string): ErrorCode | null {
  return ERROR_CODES[code] || null;
}

/**
 * Create standardized error response
 */
export function createErrorResponse(code: string, details?: Record<string, any>) {
  const errorCode = getErrorByCode(code);
  
  if (!errorCode) {
    return {
      error: {
        code: 'UNKNOWN_ERROR',
        arabicMessage: 'خطأ غير معروف',
        englishMessage: 'Unknown error occurred',
        httpStatus: 500
      },
      details
    };
  }

  return {
    error: {
      code: errorCode.code,
      arabicMessage: errorCode.arabicMessage,
      englishMessage: errorCode.englishMessage,
      troubleshooting: errorCode.troubleshooting,
      category: errorCode.category,
      httpStatus: errorCode.httpStatus
    },
    details
  };
}

/**
 * Map common error messages to error codes
 */
export function mapErrorToCode(error: Error | string): string {
  const message = typeof error === 'string' ? error : error.message;
  const lowerMessage = message.toLowerCase();

  // Authentication errors
  if (lowerMessage.includes('authentication') || lowerMessage.includes('unauthorized')) {
    return 'AUTH_REQUIRED';
  }
  if (lowerMessage.includes('token') && (lowerMessage.includes('expired') || lowerMessage.includes('invalid'))) {
    return 'AUTH_INVALID_TOKEN';
  }

  // Database errors
  if (lowerMessage.includes('database') || lowerMessage.includes('connection')) {
    return 'DB_CONNECTION_FAILED';
  }
  if (lowerMessage.includes('not found') || lowerMessage.includes('does not exist')) {
    return 'DB_RECORD_NOT_FOUND';
  }

  // Publishing errors
  if (lowerMessage.includes('instagram') && lowerMessage.includes('media')) {
    return 'PUB_MEDIA_REQUIRED';
  }
  if (lowerMessage.includes('social account') || lowerMessage.includes('no connected')) {
    return 'PUB_NO_SOCIAL_ACCOUNTS';
  }

  // OAuth errors
  if (lowerMessage.includes('oauth') && lowerMessage.includes('state')) {
    return 'OAUTH_STATE_INVALID';
  }
  if (lowerMessage.includes('refresh') && lowerMessage.includes('failed')) {
    return 'OAUTH_REFRESH_FAILED';
  }

  // Default to system error
  return 'SYS_INTERNAL_ERROR';
}

/**
 * Enhanced error handler with Arabic support
 */
export class EnhancedError extends Error {
  public readonly code: string;
  public readonly arabicMessage: string;
  public readonly httpStatus: number;
  public readonly category: string;
  public readonly troubleshooting?: string;
  public readonly details?: Record<string, any>;

  constructor(code: string, details?: Record<string, any>, originalError?: Error) {
    const errorCode = getErrorByCode(code);
    
    if (!errorCode) {
      super('Unknown error occurred');
      this.code = 'UNKNOWN_ERROR';
      this.arabicMessage = 'خطأ غير معروف';
      this.httpStatus = 500;
      this.category = 'system';
    } else {
      super(errorCode.englishMessage);
      this.code = errorCode.code;
      this.arabicMessage = errorCode.arabicMessage;
      this.httpStatus = errorCode.httpStatus;
      this.category = errorCode.category;
      this.troubleshooting = errorCode.troubleshooting;
    }

    this.details = details;
    this.name = 'EnhancedError';

    // Preserve original error stack if provided
    if (originalError) {
      this.stack = originalError.stack;
    }
  }

  toJSON() {
    return {
      code: this.code,
      arabicMessage: this.arabicMessage,
      englishMessage: this.message,
      troubleshooting: this.troubleshooting,
      category: this.category,
      httpStatus: this.httpStatus,
      details: this.details
    };
  }
}
