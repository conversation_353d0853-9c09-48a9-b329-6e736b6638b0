"use client";

import React, { useState, useEffect } from 'react';
import { EnhancedDashboard } from '@/components/dashboard/enhanced-dashboard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Activity,
  Info,
  Sparkles,
  Zap,
  Globe
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';

interface DashboardPageProps {
  searchParams?: {
    lang?: string;
  };
}

export default function EnhancedDashboardPage({ searchParams }: DashboardPageProps) {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const router = useRouter();
  const supabase = createClient();

  // Translations
  const t = {
    ar: {
      title: 'لوحة التحكم المحسنة',
      subtitle: 'تجربة محسنة مع تحليلات في الوقت الفعلي ورؤى متقدمة',
      welcome: 'مرحباً بك في لوحة التحكم الجديدة',
      description: 'تم تصميم لوحة التحكم المحسنة لتوفير رؤى شاملة وتحليلات في الوقت الفعلي لأداء وسائل التواصل الاجتماعي الخاصة بك.',
      features: 'الميزات الجديدة',
      realtimeAnalytics: 'تحليلات في الوقت الفعلي',
      realtimeDesc: 'مراقبة أداء منشوراتك وتفاعل الجمهور لحظة بلحظة',
      advancedCharts: 'رسوم بيانية متقدمة',
      advancedChartsDesc: 'تصورات تفاعلية لفهم اتجاهات البيانات بشكل أفضل',
      platformInsights: 'رؤى المنصات',
      platformInsightsDesc: 'تحليل مفصل لأداء كل منصة من منصات التواصل الاجتماعي',
      mobileOptimized: 'محسن للجوال',
      mobileOptimizedDesc: 'تجربة مثالية على جميع الأجهزة مع دعم كامل للغة العربية',
      getStarted: 'ابدأ الاستكشاف',
      backToDashboard: 'العودة للوحة التحكم الرئيسية',
      switchLanguage: 'English',
      newFeature: 'جديد',
      beta: 'تجريبي'
    },
    en: {
      title: 'Enhanced Dashboard',
      subtitle: 'Enhanced experience with real-time analytics and advanced insights',
      welcome: 'Welcome to the New Dashboard',
      description: 'The enhanced dashboard is designed to provide comprehensive insights and real-time analytics for your social media performance.',
      features: 'New Features',
      realtimeAnalytics: 'Real-time Analytics',
      realtimeDesc: 'Monitor your posts performance and audience engagement in real-time',
      advancedCharts: 'Advanced Charts',
      advancedChartsDesc: 'Interactive visualizations for better understanding of data trends',
      platformInsights: 'Platform Insights',
      platformInsightsDesc: 'Detailed analysis of each social media platform performance',
      mobileOptimized: 'Mobile Optimized',
      mobileOptimizedDesc: 'Perfect experience on all devices with full Arabic support',
      getStarted: 'Start Exploring',
      backToDashboard: 'Back to Main Dashboard',
      switchLanguage: 'العربية',
      newFeature: 'New',
      beta: 'Beta'
    }
  };

  const text = t[language];

  useEffect(() => {
    // Set language from search params
    if (searchParams?.lang === 'en') {
      setLanguage('en');
    }

    // Check authentication
    const checkAuth = async () => {
      try {
        const { data: { user }, error } = await supabase.auth.getUser();
        
        if (error || !user) {
          router.push('/auth/signin');
          return;
        }

        setUser(user);
      } catch (error) {
        console.error('Auth check failed:', error);
        toast.error(language === 'ar' ? 'فشل في التحقق من الهوية' : 'Authentication failed');
        router.push('/auth/signin');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [searchParams, language, router, supabase.auth]);

  const toggleLanguage = () => {
    const newLang = language === 'ar' ? 'en' : 'ar';
    setLanguage(newLang);
    
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('lang', newLang);
    window.history.replaceState({}, '', url.toString());
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="text-gray-600">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "min-h-screen bg-gray-50",
      language === 'ar' ? "rtl" : "ltr"
    )}>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className={cn(
          "flex items-center justify-between",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          <div className={language === 'ar' ? "text-right" : ""}>
            <div className={cn(
              "flex items-center gap-3 mb-2",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <h1 className="text-4xl font-bold text-gray-900">{text.title}</h1>
              <Badge variant="secondary" className="text-xs">
                <Sparkles className="w-3 h-3 mr-1" />
                {text.newFeature}
              </Badge>
            </div>
            <p className="text-xl text-gray-600">{text.subtitle}</p>
          </div>
          
          <div className={cn(
            "flex items-center gap-4",
            language === 'ar' ? "flex-row-reverse" : ""
          )}>
            <Button
              variant="outline"
              onClick={toggleLanguage}
              className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}
            >
              <Globe className="w-4 h-4" />
              {text.switchLanguage}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}
            >
              <BarChart3 className="w-4 h-4" />
              {text.backToDashboard}
            </Button>
          </div>
        </div>

        {/* Welcome Section */}
        <Alert className="border-blue-200 bg-blue-50">
          <Info className="h-4 w-4 text-blue-600" />
          <AlertTitle className={cn(
            "text-blue-900",
            language === 'ar' ? "text-right" : ""
          )}>
            {text.welcome}
          </AlertTitle>
          <AlertDescription className={cn(
            "text-blue-800",
            language === 'ar' ? "text-right" : ""
          )}>
            {text.description}
          </AlertDescription>
        </Alert>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Activity className="w-5 h-5 text-green-600" />
                </div>
                <CardTitle className="text-lg">{text.realtimeAnalytics}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.realtimeDesc}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-blue-600" />
                </div>
                <CardTitle className="text-lg">{text.advancedCharts}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.advancedChartsDesc}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-purple-600" />
                </div>
                <CardTitle className="text-lg">{text.platformInsights}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.platformInsightsDesc}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-orange-600" />
                </div>
                <CardTitle className="text-lg">{text.mobileOptimized}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.mobileOptimizedDesc}
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Dashboard Component */}
        <EnhancedDashboard 
          language={language}
          className="bg-white rounded-lg shadow-sm border p-6"
        />
      </div>
    </div>
  );
}
