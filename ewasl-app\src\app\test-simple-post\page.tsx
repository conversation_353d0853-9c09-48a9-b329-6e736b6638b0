'use client';

import { useState } from 'react';

export default function TestSimplePostPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testSimplePost = async () => {
    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/test-post-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: '🧪 Simple API Test - Basic Post Creation\n\nTesting if basic post creation works without complex logic.\n\nTimestamp: ' + new Date().toISOString()
        }),
      });

      const data = await response.json();
      setResult({
        status: response.status,
        data: data
      });

    } catch (error: any) {
      setResult({
        status: 'ERROR',
        data: {
          error: error.message
        }
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">🧪 Simple Post API Test</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Test Simple Post Creation</h2>
          <p className="text-gray-600 mb-4">
            This test will call the simplified API endpoint to create a basic post without complex logic.
          </p>
          
          <button
            onClick={testSimplePost}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium"
          >
            {loading ? 'Testing...' : 'Test Simple Post Creation'}
          </button>
        </div>

        {result && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-4">Test Result</h3>
            <div className="mb-4">
              <span className="font-medium">Status: </span>
              <span className={`px-2 py-1 rounded text-sm ${
                result.status === 200 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {result.status}
              </span>
            </div>
            
            <div className="bg-gray-100 rounded p-4 overflow-auto">
              <pre className="text-sm">
                {JSON.stringify(result.data, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
