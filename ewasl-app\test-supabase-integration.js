#!/usr/bin/env node

/**
 * Supabase Integration Test Script
 * Tests the new Supabase database using Supabase client
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

async function testSupabaseIntegration() {
  console.log('🔍 Testing eWasl Supabase Integration...\n');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase environment variables');
    return;
  }
  
  console.log('🔗 Connection Details:');
  console.log(`   URL: ${supabaseUrl}`);
  console.log(`   Key: ${supabaseKey.substring(0, 20)}...`);
  console.log('');
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test 1: Basic connection
    console.log('1. Testing basic database connection...');
    const { data: tables, error: tablesError } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (tablesError) {
      console.error('   ❌ Connection failed:', tablesError.message);
      return;
    }
    console.log('   ✅ Database connection successful\n');

    // Test 2: Test user operations
    console.log('2. Testing user operations...');
    
    // Create a test user
    const { data: newUser, error: createError } = await supabase
      .from('users')
      .insert({
        email: '<EMAIL>',
        name: 'Test User',
        role: 'USER'
      })
      .select()
      .single();
    
    if (createError) {
      console.error('   ❌ User creation failed:', createError.message);
      return;
    }
    console.log(`   ✅ Created test user: ${newUser.email} (ID: ${newUser.id})`);

    // Test 3: Test social account operations
    console.log('\n3. Testing social account operations...');
    
    const { data: socialAccount, error: socialError } = await supabase
      .from('social_accounts')
      .insert({
        user_id: newUser.id,
        platform: 'TWITTER',
        account_id: 'test_twitter_123',
        account_name: '@testuser',
        access_token: 'test_token_123'
      })
      .select()
      .single();
    
    if (socialError) {
      console.error('   ❌ Social account creation failed:', socialError.message);
    } else {
      console.log(`   ✅ Created social account: ${socialAccount.platform} - ${socialAccount.account_name}`);
    }

    // Test 4: Test post operations
    console.log('\n4. Testing post operations...');
    
    const { data: testPost, error: postError } = await supabase
      .from('posts')
      .insert({
        user_id: newUser.id,
        content: 'This is a test post for eWasl database integration! 🚀',
        status: 'DRAFT'
      })
      .select()
      .single();
    
    if (postError) {
      console.error('   ❌ Post creation failed:', postError.message);
    } else {
      console.log(`   ✅ Created test post: "${testPost.content.substring(0, 50)}..."`);
    }

    // Test 5: Test activity logging
    console.log('\n5. Testing activity logging...');
    
    const { data: activity, error: activityError } = await supabase
      .from('activities')
      .insert({
        user_id: newUser.id,
        post_id: testPost?.id,
        action: 'POST_SCHEDULED',
        details: 'Test post scheduled for publishing'
      })
      .select()
      .single();
    
    if (activityError) {
      console.error('   ❌ Activity creation failed:', activityError.message);
    } else {
      console.log(`   ✅ Created activity log: ${activity.action}`);
    }

    // Test 6: Test relationships with joins
    console.log('\n6. Testing database relationships...');
    
    const { data: userWithPosts, error: joinError } = await supabase
      .from('users')
      .select(`
        id,
        name,
        email,
        posts (
          id,
          content,
          status
        ),
        social_accounts (
          id,
          platform,
          account_name
        )
      `)
      .eq('id', newUser.id)
      .single();
    
    if (joinError) {
      console.error('   ❌ Relationship query failed:', joinError.message);
    } else {
      console.log(`   ✅ User has ${userWithPosts.posts?.length || 0} posts`);
      console.log(`   ✅ User has ${userWithPosts.social_accounts?.length || 0} social accounts`);
    }

    // Cleanup test data
    console.log('\n7. Cleaning up test data...');
    
    if (activity) {
      await supabase.from('activities').delete().eq('id', activity.id);
    }
    if (testPost) {
      await supabase.from('posts').delete().eq('id', testPost.id);
    }
    if (socialAccount) {
      await supabase.from('social_accounts').delete().eq('id', socialAccount.id);
    }
    await supabase.from('users').delete().eq('id', newUser.id);
    
    console.log('   ✅ Test data cleaned up');

    console.log('\n🎉 All Supabase integration tests passed successfully!');
    console.log('\n📊 Database Integration Summary:');
    console.log('   ✅ Connection: Working');
    console.log('   ✅ Tables: All accessible');
    console.log('   ✅ CRUD Operations: Working');
    console.log('   ✅ Relationships: Working');
    console.log('   ✅ Supabase Client: Working');
    
  } catch (error) {
    console.error('❌ Supabase integration test failed:', error.message);
    console.error('\n🔧 Troubleshooting steps:');
    console.error('   1. Check NEXT_PUBLIC_SUPABASE_URL in .env.local');
    console.error('   2. Check NEXT_PUBLIC_SUPABASE_ANON_KEY in .env.local');
    console.error('   3. Ensure Supabase project is active');
    console.error('   4. Check network connectivity');
    
    process.exit(1);
  }
}

// Run the test
testSupabaseIntegration();
