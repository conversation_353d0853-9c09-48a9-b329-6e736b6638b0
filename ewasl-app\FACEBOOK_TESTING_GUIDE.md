# 🔥 Facebook/Instagram API Integration Testing Guide

## 🎯 Overview
This guide provides comprehensive testing procedures for verifying real Facebook and Instagram API integration in the eWasl social media platform.

## 📋 Prerequisites

### 1. Facebook App Configuration
- **App ID**: `1366325774493759`
- **App Secret**: `0634e2f1d4552a6bb2dfa98287894328`
- **App Status**: Development/Live mode

### 2. Required Facebook App Settings
Navigate to [Facebook Developer Console](https://developers.facebook.com/apps/1366325774493759/dashboard/)

#### Facebook Login Settings:
- **Valid OAuth Redirect URIs**:
  - `https://localhost:3003/api/facebook/callback`
  - `https://localhost:3003/api/auth/callback/facebook`
- **Valid Deauthorize Callback URL**:
  - `https://localhost:3003/api/facebook/deauth`

#### Basic Settings:
- **App Domains**: `localhost`
- **Site URL**: `https://localhost:3003`

### 3. Required Permissions
- `pages_show_list` - Access to Facebook Pages
- `pages_manage_posts` - Publish to Facebook Pages
- `pages_read_engagement` - Read engagement metrics
- `business_management` - Access business accounts
- `instagram_basic` - Basic Instagram access
- `instagram_content_publish` - Publish to Instagram

## 🚀 Quick Start Testing

### Step 1: Generate SSL Certificates
```bash
# Option A: Using npm script
npm run generate-certs

# Option B: Manual generation (Windows)
powershell -ExecutionPolicy Bypass -File scripts/generate-ssl-certs.ps1

# Option C: Manual generation (Linux/Mac)
./scripts/generate-ssl-certs.sh
```

### Step 2: Test HTTPS Setup
```bash
npm run test:https
```
**Expected Output**: ✅ SSL certificates valid, HTTPS server working

### Step 3: Test Facebook Configuration
```bash
npm run test:facebook
```
**Expected Output**: ✅ App credentials valid, OAuth URLs generated

### Step 4: Start HTTPS Development Server
```bash
npm run dev
```
**Expected**: Server running at `https://localhost:3003`

### Step 5: Test OAuth Flow
```bash
npm run test:oauth
```
**Expected Output**: ✅ OAuth endpoints accessible, test URLs generated

## 🧪 Comprehensive Testing Procedure

### Phase 1: HTTPS and Configuration Testing

#### 1.1 SSL Certificate Validation
```bash
npm run test:https
```
**Verify**:
- ✅ SSL certificates exist and are valid
- ✅ HTTPS server can start successfully
- ✅ Environment variables are configured

#### 1.2 Facebook App Configuration
```bash
npm run test:facebook
```
**Verify**:
- ✅ Facebook App ID and Secret are valid
- ✅ App access token works
- ✅ Graph API connectivity established
- ✅ OAuth URLs generated correctly

### Phase 2: OAuth Integration Testing

#### 2.1 Manual OAuth Flow Test
1. **Start HTTPS server**: `npm run dev`
2. **Open browser**: Navigate to `https://localhost:3003`
3. **Accept SSL warning**: Click "Advanced" → "Proceed to localhost"
4. **Navigate to Social Accounts**: `/social-accounts`
5. **Click "Connect Facebook"**: Should redirect to Facebook OAuth
6. **Grant permissions**: Allow all requested permissions
7. **Verify redirect**: Should return to eWasl with success message

#### 2.2 OAuth Endpoint Testing
```bash
npm run test:oauth
```
**Verify**:
- ✅ OAuth initiation endpoint accessible
- ✅ OAuth callback handler exists
- ✅ Database connection working
- ✅ Test OAuth URLs generated

#### 2.3 Database Verification
Check Supabase tables after OAuth:
- `social_accounts` - Should contain new Facebook/Instagram accounts
- `oauth_logs` - Should show successful authentication
- `oauth_states` - Should track OAuth state parameters

### Phase 3: Post Publishing Testing

#### 3.1 Immediate Publishing Test
```bash
npm run test:publishing
```

**Manual Test**:
1. Navigate to `/posts/new`
2. Create test post: "🚀 Testing eWasl platform! #eWasl #Test"
3. Select connected Facebook/Instagram accounts
4. Click "Publish Now"
5. **Verify on platforms**: Check Facebook Page and Instagram for post

#### 3.2 Scheduled Publishing Test
1. Create post with future schedule (5 minutes)
2. Verify post appears in `/calendar`
3. Wait for scheduled time
4. **Verify on platforms**: Post should appear automatically

#### 3.3 Media Upload Test
1. Create post with image attachment
2. Upload test image (JPG/PNG, <10MB)
3. Publish to Facebook and Instagram
4. **Verify on platforms**: Image should appear correctly

### Phase 4: Error Handling Testing

#### 4.1 Rate Limiting Test
- Create multiple posts rapidly (>10 in 1 minute)
- **Expected**: Rate limiting should trigger
- **Verify**: Proper error messages displayed

#### 4.2 Token Expiration Test
- Manually expire access token in database
- Attempt to publish post
- **Expected**: Token refresh should trigger automatically
- **Verify**: Post publishes successfully after refresh

#### 4.3 Invalid Content Test
- Test empty content
- Test content >63,206 characters (Facebook limit)
- Test invalid media formats
- **Expected**: Proper validation errors

## 📊 Success Criteria

### ✅ OAuth Integration
- [ ] Facebook OAuth flow completes successfully
- [ ] Instagram Business accounts are retrieved
- [ ] Access tokens are stored securely
- [ ] Token refresh mechanism works

### ✅ Post Publishing
- [ ] Immediate posts appear on Facebook within 30 seconds
- [ ] Immediate posts appear on Instagram within 30 seconds
- [ ] Scheduled posts publish at correct time
- [ ] Media uploads work for both platforms
- [ ] Post URLs are captured and stored

### ✅ Error Handling
- [ ] Rate limiting prevents API abuse
- [ ] Token expiration is handled gracefully
- [ ] Invalid content is rejected with clear errors
- [ ] Network failures are retried appropriately

### ✅ Database Integration
- [ ] Social accounts are stored correctly
- [ ] Post metadata is tracked
- [ ] Publishing status is updated
- [ ] Analytics data is collected

## 🔍 Verification Checklist

### Facebook Page Verification
1. Go to your Facebook Page
2. Check recent posts for #eWasl hashtag
3. Verify post content matches exactly
4. Check post engagement metrics
5. Verify post URLs in eWasl dashboard

### Instagram Account Verification
1. Go to your Instagram Business account
2. Check recent posts for #eWasl hashtag
3. Verify images uploaded correctly
4. Check post captions and hashtags
5. Verify post URLs in eWasl dashboard

### Database Verification
```sql
-- Check social accounts
SELECT * FROM social_accounts WHERE platform IN ('facebook', 'instagram');

-- Check recent posts
SELECT * FROM posts WHERE created_at > NOW() - INTERVAL '1 hour';

-- Check publishing status
SELECT * FROM post_social_accounts WHERE created_at > NOW() - INTERVAL '1 hour';

-- Check scheduled posts
SELECT * FROM scheduled_posts_queue WHERE status = 'pending';
```

## 🚨 Troubleshooting

### Common Issues

#### SSL Certificate Issues
```bash
# Regenerate certificates
npm run generate-certs
# Test HTTPS setup
npm run test:https
```

#### Facebook OAuth Errors
- **Error**: "Invalid redirect URI"
  - **Solution**: Update Facebook App settings with exact HTTPS URLs
- **Error**: "App not approved for permission"
  - **Solution**: Submit app for review or use test users

#### Publishing Failures
- **Error**: "Token expired"
  - **Solution**: Check token refresh mechanism
- **Error**: "Content policy violation"
  - **Solution**: Review Facebook/Instagram content policies

#### Database Connection Issues
- **Error**: "Connection refused"
  - **Solution**: Check Supabase credentials and RLS policies

## 📈 Performance Monitoring

### Key Metrics to Track
- OAuth success rate: >95%
- Post publishing success rate: >98%
- Average publishing time: <30 seconds
- Token refresh success rate: >99%
- API error rate: <1%

### Monitoring Commands
```bash
# Check system status
npm run check:monitoring

# Test all integrations
npm run test:facebook-integration

# Performance testing
npm run test:performance
```

## 🎉 Success Confirmation

When all tests pass, you should see:
- ✅ Real Facebook posts appearing on your Page
- ✅ Real Instagram posts appearing on your account
- ✅ Scheduled posts publishing automatically
- ✅ Media uploads working correctly
- ✅ Analytics data being collected
- ✅ Error handling working properly

**🎯 Final Verification**: Create a test post with image, schedule it for 5 minutes, and verify it appears on both Facebook and Instagram at the correct time with proper analytics tracking.
