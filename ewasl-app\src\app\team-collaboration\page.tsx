"use client";

import React, { useState, useEffect } from 'react';
import { TeamCollaborationManager } from '@/components/team/team-collaboration-manager';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Users,
  ArrowLeft,
  Sparkles,
  Shield,
  Crown,
  Eye,
  Edit,
  UserPlus,
  Settings,
  CheckCircle,
  Clock,
  BarChart3,
  Briefcase,
  Globe,
  Info,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { useRTL } from '@/lib/i18n/arabic-rtl-optimizer';
import { toast } from 'sonner';

interface TeamCollaborationPageProps {
  searchParams?: {
    lang?: string;
  };
}

export default function TeamCollaborationPage({ searchParams }: TeamCollaborationPageProps) {
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const router = useRouter();
  const rtl = useRTL(language);

  useEffect(() => {
    // Set language from search params
    if (searchParams?.lang === 'en') {
      setLanguage('en');
    }

    // Apply RTL to document
    document.documentElement.dir = rtl.direction;
    document.documentElement.lang = language;
  }, [searchParams, language, rtl.direction]);

  const handleLanguageChange = (newLanguage: 'ar' | 'en') => {
    setLanguage(newLanguage);
    
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('lang', newLanguage);
    window.history.replaceState({}, '', url.toString());
  };

  // Team collaboration features
  const collaborationFeatures = [
    {
      icon: Users,
      title: language === 'ar' ? 'إدارة الفريق' : 'Team Management',
      description: language === 'ar' 
        ? 'دعوة الأعضاء وإدارة الأدوار والصلاحيات'
        : 'Invite members and manage roles and permissions',
      color: 'blue'
    },
    {
      icon: Shield,
      title: language === 'ar' ? 'نظام الأذونات' : 'Permission System',
      description: language === 'ar'
        ? 'تحكم دقيق في صلاحيات كل عضو في الفريق'
        : 'Granular control over each team member\'s permissions',
      color: 'green'
    },
    {
      icon: Briefcase,
      title: language === 'ar' ? 'مساحات العمل' : 'Workspaces',
      description: language === 'ar'
        ? 'تنظيم المشاريع في مساحات عمل منفصلة'
        : 'Organize projects in separate workspaces',
      color: 'purple'
    },
    {
      icon: CheckCircle,
      title: language === 'ar' ? 'سير الموافقات' : 'Approval Workflows',
      description: language === 'ar'
        ? 'سير عمل مخصص للموافقة على المحتوى'
        : 'Custom workflows for content approval',
      color: 'orange'
    },
    {
      icon: BarChart3,
      title: language === 'ar' ? 'تتبع النشاط' : 'Activity Tracking',
      description: language === 'ar'
        ? 'مراقبة نشاط الفريق والتعاون في الوقت الفعلي'
        : 'Monitor team activity and collaboration in real-time',
      color: 'red'
    },
    {
      icon: Settings,
      title: language === 'ar' ? 'إعدادات متقدمة' : 'Advanced Settings',
      description: language === 'ar'
        ? 'تخصيص إعدادات التعاون والأمان'
        : 'Customize collaboration and security settings',
      color: 'indigo'
    }
  ];

  // Role hierarchy
  const roleHierarchy = [
    {
      role: 'owner',
      icon: Crown,
      title: language === 'ar' ? 'المالك' : 'Owner',
      description: language === 'ar' ? 'صلاحيات كاملة' : 'Full permissions',
      color: 'yellow',
      permissions: 7
    },
    {
      role: 'admin',
      icon: Shield,
      title: language === 'ar' ? 'مدير' : 'Admin',
      description: language === 'ar' ? 'إدارة الفريق والمحتوى' : 'Team and content management',
      color: 'blue',
      permissions: 6
    },
    {
      role: 'editor',
      icon: Edit,
      title: language === 'ar' ? 'محرر' : 'Editor',
      description: language === 'ar' ? 'إنشاء وتعديل المحتوى' : 'Create and edit content',
      color: 'green',
      permissions: 4
    },
    {
      role: 'reviewer',
      icon: Eye,
      title: language === 'ar' ? 'مراجع' : 'Reviewer',
      description: language === 'ar' ? 'مراجعة والموافقة على المحتوى' : 'Review and approve content',
      color: 'purple',
      permissions: 3
    },
    {
      role: 'viewer',
      icon: Eye,
      title: language === 'ar' ? 'مشاهد' : 'Viewer',
      description: language === 'ar' ? 'عرض المحتوى فقط' : 'View content only',
      color: 'gray',
      permissions: 2
    }
  ];

  // Key benefits
  const keyBenefits = [
    {
      metric: language === 'ar' ? 'كفاءة التعاون' : 'Collaboration Efficiency',
      value: '90%',
      improvement: 'improvement'
    },
    {
      metric: language === 'ar' ? 'أمان المحتوى' : 'Content Security',
      value: '99.9%',
      improvement: 'uptime'
    },
    {
      metric: language === 'ar' ? 'سرعة الموافقات' : 'Approval Speed',
      value: '5x',
      improvement: 'faster'
    },
    {
      metric: language === 'ar' ? 'إدارة الفريق' : 'Team Management',
      value: '100+',
      improvement: 'members'
    }
  ];

  // Handle team events
  const handleMemberUpdate = (member: any) => {
    toast.success(
      language === 'ar' 
        ? `تم تحديث العضو ${member.name}`
        : `Member ${member.name} updated`
    );
  };

  const handleWorkspaceUpdate = (workspace: any) => {
    toast.success(
      language === 'ar' 
        ? `تم تحديث مساحة العمل ${workspace.name}`
        : `Workspace ${workspace.name} updated`
    );
  };

  return (
    <div className={cn(
      "min-h-screen bg-gray-50",
      language === 'ar' ? "rtl" : "ltr"
    )} dir={rtl.direction}>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className={rtl.cn(
          "flex items-center justify-between",
          rtl.flex()
        )}>
          <div className={rtl.textAlign()}>
            <div className={rtl.cn(
              "flex items-center gap-3 mb-2",
              rtl.flex()
            )}>
              <h1 className="text-4xl font-bold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                {language === 'ar' ? 'التعاون الجماعي' : 'Team Collaboration'}
              </h1>
              <Badge variant="secondary" className="text-xs">
                <Sparkles className="w-3 h-3 mr-1" />
                {language === 'ar' ? 'متقدم' : 'Advanced'}
              </Badge>
            </div>
            <p className="text-xl text-gray-600" style={{ fontFamily: rtl.getFontFamily('primary') }}>
              {language === 'ar'
                ? 'إدارة الفريق والأذونات وسير العمل التعاوني المتقدم'
                : 'Advanced team management, permissions, and collaborative workflows'
              }
            </p>
          </div>
          
          <div className={rtl.cn(
            "flex items-center gap-4",
            rtl.flex()
          )}>
            <Button
              variant="outline"
              onClick={() => handleLanguageChange(language === 'ar' ? 'en' : 'ar')}
              className={rtl.cn(
                "flex items-center gap-2",
                rtl.flex()
              )}
            >
              <Globe className="w-4 h-4" />
              {language === 'ar' ? 'English' : 'العربية'}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className={rtl.cn(
                "flex items-center gap-2",
                rtl.flex()
              )}
            >
              <ArrowLeft className={cn("w-4 h-4", language === 'ar' && "rotate-180")} />
              {language === 'ar' ? 'العودة للوحة التحكم' : 'Back to Dashboard'}
            </Button>
          </div>
        </div>

        {/* Team Collaboration Alert */}
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertTitle className={rtl.cn(
            "text-green-900",
            rtl.textAlign()
          )}>
            {language === 'ar' ? 'ميزات التعاون الجماعي نشطة' : 'Team Collaboration Features Active'}
          </AlertTitle>
          <AlertDescription className={rtl.cn(
            "text-green-800",
            rtl.textAlign()
          )}>
            {language === 'ar'
              ? 'إدارة شاملة للفريق مع نظام أذونات متقدم، مساحات عمل منظمة، وسير موافقات مخصص.'
              : 'Comprehensive team management with advanced permission system, organized workspaces, and custom approval workflows.'
            }
          </AlertDescription>
        </Alert>

        {/* Key Benefits */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {keyBenefits.map((benefit, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6 text-center">
                <div className="space-y-2">
                  <p className="text-3xl font-bold text-blue-600">{benefit.value}</p>
                  <p className="text-sm font-medium text-gray-900">{benefit.metric}</p>
                  <p className="text-xs text-green-600">{benefit.improvement}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Role Hierarchy */}
        <Card>
          <CardHeader>
            <CardTitle className={rtl.textAlign()}>
              {language === 'ar' ? 'هيكل الأدوار والصلاحيات' : 'Role Hierarchy & Permissions'}
            </CardTitle>
            <CardDescription className={rtl.textAlign()}>
              {language === 'ar' 
                ? 'نظام أدوار متدرج مع صلاحيات محددة لكل مستوى'
                : 'Hierarchical role system with specific permissions for each level'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {roleHierarchy.map((role, index) => {
                const Icon = role.icon;
                return (
                  <div key={role.role} className="text-center">
                    <div className={`w-16 h-16 bg-${role.color}-100 rounded-full flex items-center justify-center mx-auto mb-3`}>
                      <Icon className={`w-8 h-8 text-${role.color}-600`} />
                    </div>
                    <h3 className="font-semibold mb-1">{role.title}</h3>
                    <p className="text-sm text-gray-600 mb-2">{role.description}</p>
                    <Badge variant="outline">
                      {role.permissions}/7 {language === 'ar' ? 'صلاحيات' : 'permissions'}
                    </Badge>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Collaboration Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {collaborationFeatures.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className={rtl.cn(
                    "flex items-center gap-3",
                    rtl.flex()
                  )}>
                    <div className={`w-10 h-10 bg-${feature.color}-100 rounded-lg flex items-center justify-center`}>
                      <Icon className={`w-5 h-5 text-${feature.color}-600`} />
                    </div>
                    <CardTitle className="text-lg" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                      {feature.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className={rtl.textAlign()} style={{ fontFamily: rtl.getFontFamily('primary') }}>
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Team Collaboration Manager */}
        <TeamCollaborationManager
          language={language}
          organizationId="demo-org"
          currentUserId="current-user"
          currentUserRole="owner"
          onMemberUpdate={handleMemberUpdate}
          onWorkspaceUpdate={handleWorkspaceUpdate}
        />

        {/* Footer */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-6">
            <div className={rtl.cn(
              "flex items-center justify-center gap-4",
              rtl.flex()
            )}>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div className={rtl.textAlign()}>
                <h4 className="font-semibold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                  {language === 'ar' ? 'التعاون الجماعي جاهز' : 'Team Collaboration Ready'}
                </h4>
                <p className="text-sm text-gray-600" style={{ fontFamily: rtl.getFontFamily('primary') }}>
                  {language === 'ar'
                    ? 'إدارة فعالة للفريق مع نظام أذونات متقدم وسير عمل تعاوني'
                    : 'Efficient team management with advanced permission system and collaborative workflows'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
