/**
 * Background Job Processing System for eWasl Social Media Platform
 * Handles scheduled post processing and retry logic
 */

import { createServiceRoleClient } from '@/lib/supabase/service-role';
import { publishToFacebookReal, publishToInstagramReal } from '@/lib/social/real-api-implementations';

export interface ScheduledJob {
  id: string;
  post_id: string;
  scheduled_for: string;
  timezone: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  attempts: number;
  max_attempts: number;
  last_error?: string;
  next_retry_at?: string;
  created_at: string;
  updated_at: string;
}

export interface PostData {
  id: string;
  user_id: string;
  content: string;
  media_urls: string[];
  status: string;
  platform_settings: any;
}

export interface SocialAccountData {
  id: string;
  platform: string;
  account_name: string;
  access_token: string;
  page_id?: string;
  page_access_token?: string;
  instagram_business_account_id?: string;
}

export class JobProcessor {
  private supabase = createServiceRoleClient();
  private isProcessing = false;
  private processingInterval: NodeJS.Timeout | null = null;

  /**
   * Start the job processor
   */
  start(intervalMs: number = 60000) { // Check every minute
    if (this.processingInterval) {
      console.log('⚠️ Job processor already running');
      return;
    }

    console.log('🚀 Starting job processor...');
    this.processingInterval = setInterval(() => {
      this.processJobs();
    }, intervalMs);

    // Process immediately on start
    this.processJobs();
  }

  /**
   * Stop the job processor
   */
  stop() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      console.log('⏹️ Job processor stopped');
    }
  }

  /**
   * Process pending scheduled jobs
   */
  async processJobs() {
    if (this.isProcessing) {
      console.log('⏳ Job processor already running, skipping...');
      return;
    }

    this.isProcessing = true;
    console.log('🔄 Processing scheduled jobs...');

    try {
      // Get pending jobs that are due for processing
      const { data: jobs, error: jobsError } = await this.supabase
        .from('scheduled_posts_queue')
        .select('*')
        .eq('status', 'pending')
        .lte('scheduled_for', new Date().toISOString())
        .order('scheduled_for', { ascending: true })
        .limit(10); // Process up to 10 jobs at a time

      if (jobsError) {
        console.error('❌ Error fetching scheduled jobs:', jobsError);
        return;
      }

      if (!jobs || jobs.length === 0) {
        console.log('📭 No pending jobs to process');
        return;
      }

      console.log(`📋 Found ${jobs.length} jobs to process`);

      // Process each job
      for (const job of jobs) {
        await this.processJob(job);
      }

    } catch (error) {
      console.error('💥 Critical error in job processor:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process a single scheduled job
   */
  private async processJob(job: ScheduledJob) {
    console.log(`🎯 Processing job ${job.id} for post ${job.post_id}`);

    try {
      // Mark job as processing
      await this.supabase
        .from('scheduled_posts_queue')
        .update({
          status: 'processing',
          updated_at: new Date().toISOString()
        })
        .eq('id', job.id);

      // Get post data
      const { data: post, error: postError } = await this.supabase
        .from('posts')
        .select('*')
        .eq('id', job.post_id)
        .single();

      if (postError || !post) {
        throw new Error(`Post not found: ${job.post_id}`);
      }

      // Get associated social accounts from publishing_results
      const { data: publishingResults, error: accountsError } = await this.supabase
        .from('publishing_results')
        .select(`
          *,
          social_accounts (*)
        `)
        .eq('post_id', job.post_id)
        .eq('success', false);

      if (accountsError) {
        throw new Error(`Failed to fetch social accounts: ${accountsError.message}`);
      }

      if (!publishingResults || publishingResults.length === 0) {
        throw new Error('No social accounts found for scheduled post');
      }

      console.log(`📱 Publishing to ${publishingResults.length} social accounts`);

      const publishResults = [];

      // Publish to each social account
      for (const publishingResult of publishingResults) {
        const account = publishingResult.social_accounts;
        
        console.log(`📤 Publishing to ${account.platform} (${account.account_name})`);

        let result;

        // Route to appropriate publisher
        switch (account.platform.toUpperCase()) {
          case 'FACEBOOK':
            result = await publishToFacebookReal(
              post.content,
              post.media_url ? [post.media_url] : [],
              account,
              { timezone: job.timezone, postId: post.id }
            );
            break;
          case 'INSTAGRAM':
            result = await publishToInstagramReal(
              post.content,
              post.media_url ? [post.media_url] : [],
              account,
              { timezone: job.timezone, postId: post.id }
            );
            break;
          default:
            result = {
              success: false,
              error: `Platform ${account.platform} not supported`
            };
        }

        publishResults.push({ ...result, accountId: account.id });

        // Update publishing_results with result
        await this.supabase
          .from('publishing_results')
          .update({
            success: result.success,
            external_post_id: result.postId || null,
            external_url: result.url || null,
            published_at: result.success ? new Date().toISOString() : null,
            error_message: result.error || null,
            updated_at: new Date().toISOString()
          })
          .eq('id', publishingResult.id);

        console.log(`${result.success ? '✅' : '❌'} ${account.platform} result:`, {
          success: result.success,
          postId: result.postId,
          error: result.error
        });
      }

      // Update post status
      const allSuccessful = publishResults.every(r => r.success);
      const someSuccessful = publishResults.some(r => r.success);

      await this.supabase
        .from('posts')
        .update({
          status: someSuccessful ? 'PUBLISHED' : 'DRAFT',
          published_at: someSuccessful ? new Date().toISOString() : null,
          updated_at: new Date().toISOString()
        })
        .eq('id', job.post_id);

      // Mark job as completed
      await this.supabase
        .from('scheduled_posts_queue')
        .update({
          status: 'completed',
          processed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', job.id);

      console.log(`✅ Job ${job.id} completed successfully. Published to ${publishResults.filter(r => r.success).length}/${publishResults.length} accounts`);

    } catch (error) {
      console.error(`❌ Job ${job.id} failed:`, error);
      await this.handleJobFailure(job, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Handle job failure with retry logic
   */
  private async handleJobFailure(job: ScheduledJob, errorMessage: string) {
    const newAttempts = job.attempts + 1;
    const shouldRetry = newAttempts < job.max_attempts;

    if (shouldRetry) {
      // Calculate next retry time with exponential backoff
      const backoffMinutes = Math.pow(2, newAttempts) * 5; // 5, 10, 20 minutes
      const nextRetryAt = new Date(Date.now() + backoffMinutes * 60 * 1000);

      console.log(`🔄 Scheduling retry ${newAttempts}/${job.max_attempts} for job ${job.id} at ${nextRetryAt.toISOString()}`);

      await this.supabase
        .from('scheduled_posts_queue')
        .update({
          status: 'pending',
          attempts: newAttempts,
          last_error: errorMessage,
          next_retry_at: nextRetryAt.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', job.id);
    } else {
      console.log(`💀 Job ${job.id} failed permanently after ${newAttempts} attempts`);

      await this.supabase
        .from('scheduled_posts_queue')
        .update({
          status: 'failed',
          attempts: newAttempts,
          last_error: errorMessage,
          updated_at: new Date().toISOString()
        })
        .eq('id', job.id);
    }
  }

  /**
   * Get job processing statistics
   */
  async getStats() {
    const { data: stats, error } = await this.supabase
      .from('scheduled_posts_queue')
      .select('status')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Last 24 hours

    if (error) {
      console.error('Error fetching job stats:', error);
      return null;
    }

    const statusCounts = stats.reduce((acc, job) => {
      acc[job.status] = (acc[job.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: stats.length,
      ...statusCounts,
      timestamp: new Date().toISOString()
    };
  }
}

// Export singleton instance
export const jobProcessor = new JobProcessor();
