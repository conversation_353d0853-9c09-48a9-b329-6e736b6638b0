# Simplified DigitalOcean App Platform Specification
# Enhanced Media Processing Pipeline - Production Deployment

name: ewasl-production-v2
region: nyc3

services:
  - name: web
    source_dir: /
    github:
      repo: TahaOsa/eWasl.com
      branch: main
      deploy_on_push: true
    
    # Build Configuration
    build_command: npm ci --production=false && npm run build
    run_command: npm start
    
    # Instance Configuration
    instance_count: 1
    instance_size_slug: apps-s-1vcpu-2gb
    
    # HTTP Configuration
    http_port: 3000
    
    # Health Check Configuration
    health_check:
      http_path: /api/system/health
      initial_delay_seconds: 60
      period_seconds: 30
      timeout_seconds: 10
      success_threshold: 1
      failure_threshold: 3
    
    # Essential Environment Variables
    envs:
      # Application Environment
      - key: NODE_ENV
        value: production
        scope: RUN_TIME
      
      - key: NEXT_PUBLIC_APP_URL
        value: https://app.ewasl.com
        scope: RUN_TIME
      
      # Supabase Configuration
      - key: NEXT_PUBLIC_SUPABASE_URL
        value: https://ajpcbugydftdyhlbddpl.supabase.co
        scope: RUN_TIME
      
      - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.dHPGCdWDJV-BEeQTnNhSDcck8PnvRMYKCIo2xn49Yqg
        scope: RUN_TIME

      - key: SUPABASE_SERVICE_ROLE_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.oEzL8RGuwDyLOupfw2TQLbpViDujaFnTGfDl6sBMFkc
        scope: RUN_TIME

      # Database Configuration
      - key: DATABASE_URL
        value: postgresql://postgres.ajpcbugydftdyhlbddpl:<EMAIL>:5432/postgres
        scope: RUN_TIME

      - key: DATABASE_URL
        value: postgresql://postgres.ajpcbugydftdyhlbddpl:<EMAIL>:5432/postgres
        scope: BUILD_TIME
      
      # Media Processing (Basic)
      - key: MEDIA_MAX_FILE_SIZE
        value: "52428800"
        scope: RUN_TIME
      
      - key: MEDIA_ENABLE_AI_OPTIMIZATION
        value: "true"
        scope: RUN_TIME
      
      # Monitoring
      - key: MONITORING_ENABLE_HEALTH_CHECKS
        value: "true"
        scope: RUN_TIME

      # Authentication
      - key: NEXTAUTH_SECRET
        value: ewasl-nextauth-secret-2025-production
        scope: RUN_TIME

      - key: NEXTAUTH_URL
        value: https://app.ewasl.com
        scope: RUN_TIME

      # Auth Bypass for testing
      - key: ENABLE_AUTH_BYPASS
        value: "false"
        scope: RUN_TIME

      # Stripe Configuration (Live Mode)
      - key: STRIPE_SECRET_KEY
        value: ***********************************************************************************************************
        scope: RUN_TIME

      - key: NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
        value: pk_live_51NlyLHEpEYvJL85MdJvs7DMwtmJA4YiyBpvfqOPUncOcAGETbf4kqIv08PpnQiECVvhlCWMVYdip6kVjSkWZ6vlM00XkuCLofk
        scope: RUN_TIME

      - key: STRIPE_WEBHOOK_SECRET
        value: whsec_iEtzom9G1UZuMVlghIuIIFBF9jgc4eSj
        scope: RUN_TIME

      # Redis Configuration for Background Jobs (In-Memory Fallback)
      - key: REDIS_URL
        value: memory://localhost
        scope: RUN_TIME

      - key: REDIS_HOST
        value: localhost
        scope: RUN_TIME

      - key: REDIS_PORT
        value: "6379"
        scope: RUN_TIME

      - key: REDIS_PASSWORD
        value: ""
        scope: RUN_TIME

      # Background Job Configuration (In-Memory Mode)
      - key: USE_MEMORY_QUEUE
        value: "true"
        scope: RUN_TIME

      # Social Media API Configuration
      - key: LINKEDIN_CLIENT_ID
        value: 787coegnsdocvq
        scope: RUN_TIME

      - key: LINKEDIN_CLIENT_SECRET
        value: WPL_AP1.F6yN8tH55DHmqsqP.ep1SGQ==
        scope: RUN_TIME

      - key: FACEBOOK_APP_ID
        scope: RUN_TIME
        type: SECRET
        value: "YOUR_FACEBOOK_APP_ID"

      - key: FACEBOOK_APP_SECRET
        scope: RUN_TIME
        type: SECRET
        value: YOUR_FACEBOOK_APP_SECRET

      - key: TWITTER_API_KEY
        value: TWITTER_API_KEY_FULL_NEEDED_HINT_4pbzhu
        scope: RUN_TIME

      - key: TWITTER_API_SECRET
        value: TWITTER_API_SECRET_FULL_NEEDED
        scope: RUN_TIME

      - key: TWITTER_CLIENT_ID
        value: RkFrLW9lcHNaSlVaQUlfaklja1Y6MTpjaQ
        scope: RUN_TIME

      - key: TWITTER_CLIENT_SECRET
        value: p0Z2gP-OttRQkHI-0ZweC46NIZRHA0vJCSMQlQdVNZ30cU5-n3
        scope: RUN_TIME

      # OpenAI Configuration
      - key: OPENAI_API_KEY
        value: ********************************************************************************************************************************************************************
        scope: RUN_TIME

      - key: OPENAI_MODEL
        value: gpt-4
        scope: RUN_TIME

      - key: OPENAI_MAX_TOKENS
        value: "2000"
        scope: RUN_TIME

      # CDN Configuration for Media Processing (Supabase Storage)
      - key: CDN_PROVIDER
        value: supabase
        scope: RUN_TIME

      - key: CDN_BUCKET
        value: media
        scope: RUN_TIME

      - key: CDN_REGION
        value: eu-central-1
        scope: RUN_TIME

      - key: CDN_DOMAIN
        value: ajpcbugydftdyhlbddpl.supabase.co
        scope: RUN_TIME

      - key: SUPABASE_STORAGE_URL
        value: https://ajpcbugydftdyhlbddpl.supabase.co/storage/v1
        scope: RUN_TIME

      - key: MAX_FILE_SIZE
        value: "52428800"
        scope: RUN_TIME

      # Background Job Configuration
      - key: ENABLE_BACKGROUND_JOBS
        value: "true"
        scope: RUN_TIME

      - key: JOB_QUEUE_CONCURRENCY
        value: "5"
        scope: RUN_TIME

      - key: SCHEDULER_INTERVAL_SECONDS
        value: "30"
        scope: RUN_TIME
      
      # Build-time Environment Variables
      - key: NODE_ENV
        value: production
        scope: BUILD_TIME
      
      - key: NEXT_PUBLIC_APP_URL
        value: https://app.ewasl.com
        scope: BUILD_TIME
      
      - key: NEXT_PUBLIC_SUPABASE_URL
        value: https://ajpcbugydftdyhlbddpl.supabase.co
        scope: BUILD_TIME
      
      - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.dHPGCdWDJV-BEeQTnNhSDcck8PnvRMYKCIo2xn49Yqg
        scope: BUILD_TIME

# Domain Configuration
domains:
  - domain: app.ewasl.com
