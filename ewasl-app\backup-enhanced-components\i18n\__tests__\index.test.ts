import {
  isRTL,
  getDirection,
  getTextAlign,
  getFlexDirection,
  getSpacingClass,
  formatDate,
  formatTime,
  formatNumber,
  getRTLClasses,
  getLocalizedUrl,
  containsArabic,
  detectTextDirection,
  getFontFamily,
  type Locale,
} from '../index';

describe('i18n utilities', () => {
  describe('isRTL', () => {
    it('should return true for Arabic', () => {
      expect(isRTL('ar')).toBe(true);
    });

    it('should return false for English', () => {
      expect(isRTL('en')).toBe(false);
    });
  });

  describe('getDirection', () => {
    it('should return rtl for Arabic', () => {
      expect(getDirection('ar')).toBe('rtl');
    });

    it('should return ltr for English', () => {
      expect(getDirection('en')).toBe('ltr');
    });
  });

  describe('getTextAlign', () => {
    it('should return right for Arabic', () => {
      expect(getTextAlign('ar')).toBe('right');
    });

    it('should return left for English', () => {
      expect(getTextAlign('en')).toBe('left');
    });
  });

  describe('getFlexDirection', () => {
    it('should return row-reverse for Arabic with default row', () => {
      expect(getFlexDirection('ar')).toBe('row-reverse');
    });

    it('should return row for English with default row', () => {
      expect(getFlexDirection('en')).toBe('row');
    });

    it('should handle row-reverse default', () => {
      expect(getFlexDirection('ar', 'row-reverse')).toBe('row');
      expect(getFlexDirection('en', 'row-reverse')).toBe('row-reverse');
    });
  });

  describe('getSpacingClass', () => {
    it('should return correct margin classes for Arabic', () => {
      expect(getSpacingClass('ar', 'start', 4)).toBe('mr-4');
      expect(getSpacingClass('ar', 'end', 4)).toBe('ml-4');
    });

    it('should return correct margin classes for English', () => {
      expect(getSpacingClass('en', 'start', 4)).toBe('ml-4');
      expect(getSpacingClass('en', 'end', 4)).toBe('mr-4');
    });

    it('should handle padding classes', () => {
      expect(getSpacingClass('ar', 'start', 4, 'padding')).toBe('pr-4');
      expect(getSpacingClass('en', 'start', 4, 'padding')).toBe('pl-4');
    });
  });

  describe('formatDate', () => {
    const testDate = new Date('2024-01-15');

    it('should format date for Arabic locale', () => {
      const formatted = formatDate(testDate, 'ar');
      expect(typeof formatted).toBe('string');
      expect(formatted.length).toBeGreaterThan(0);
    });

    it('should format date for English locale', () => {
      const formatted = formatDate(testDate, 'en');
      expect(formatted).toContain('2024');
    });

    it('should handle string dates', () => {
      const formatted = formatDate('2024-01-15', 'ar');
      expect(typeof formatted).toBe('string');
      expect(formatted.length).toBeGreaterThan(0);
    });
  });

  describe('formatTime', () => {
    const testDate = new Date('2024-01-15T14:30:00');

    it('should format time for Arabic locale', () => {
      const formatted = formatTime(testDate, 'ar');
      expect(typeof formatted).toBe('string');
      expect(formatted.length).toBeGreaterThan(0);
    });

    it('should format time for English locale', () => {
      const formatted = formatTime(testDate, 'en');
      expect(formatted).toMatch(/\d{1,2}:\d{2}/);
    });
  });

  describe('formatNumber', () => {
    it('should format numbers for Arabic locale', () => {
      const formatted = formatNumber(1234.56, 'ar');
      expect(typeof formatted).toBe('string');
    });

    it('should format numbers for English locale', () => {
      const formatted = formatNumber(1234.56, 'en');
      expect(formatted).toContain('1,234');
    });
  });

  describe('getRTLClasses', () => {
    it('should return RTL classes for Arabic', () => {
      const classes = getRTLClasses('ar');
      expect(classes).toContain('dir-rtl');
      expect(classes).toContain('text-right');
    });

    it('should return LTR classes for English', () => {
      const classes = getRTLClasses('en');
      expect(classes).toContain('dir-ltr');
      expect(classes).toContain('text-left');
    });
  });

  describe('getLocalizedUrl', () => {
    it('should return path as-is for default locale', () => {
      expect(getLocalizedUrl('/dashboard', 'ar')).toBe('/dashboard');
    });

    it('should prepend locale for non-default locale', () => {
      expect(getLocalizedUrl('/dashboard', 'en')).toBe('/en/dashboard');
    });
  });

  describe('containsArabic', () => {
    it('should detect Arabic text', () => {
      expect(containsArabic('مرحبا')).toBe(true);
      expect(containsArabic('Hello مرحبا')).toBe(true);
    });

    it('should return false for non-Arabic text', () => {
      expect(containsArabic('Hello World')).toBe(false);
      expect(containsArabic('123456')).toBe(false);
    });

    it('should handle empty string', () => {
      expect(containsArabic('')).toBe(false);
    });
  });

  describe('detectTextDirection', () => {
    it('should detect RTL for Arabic text', () => {
      expect(detectTextDirection('مرحبا بكم')).toBe('rtl');
    });

    it('should detect LTR for English text', () => {
      expect(detectTextDirection('Hello World')).toBe('ltr');
    });

    it('should detect RTL for mixed text with Arabic', () => {
      expect(detectTextDirection('Hello مرحبا')).toBe('rtl');
    });
  });

  describe('getFontFamily', () => {
    it('should return Arabic font family for Arabic locale', () => {
      const fontFamily = getFontFamily('ar');
      expect(fontFamily).toContain('Noto Sans Arabic');
    });

    it('should return Inter font family for English locale', () => {
      const fontFamily = getFontFamily('en');
      expect(fontFamily).toContain('Inter');
    });
  });
});
