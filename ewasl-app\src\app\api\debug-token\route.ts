import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// DEBUG ENDPOINT: Token Validation Diagnostic
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 DEBUG TOKEN ENDPOINT - Starting diagnostic...');
    
    // Use unified authentication
    const { user, supabase } = await getAuthenticatedUser(request);
    console.log('✅ User authenticated:', user.id);

    // Get the Instagram account
    const { data: socialAccounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('id', '9b99c9ad-e11f-4f9a-8f19-51f5d696563c');

    if (accountsError) {
      console.error('❌ Error fetching social accounts:', accountsError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch social accounts',
        details: accountsError
      }, { status: 500 });
    }

    if (!socialAccounts || socialAccounts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No Instagram account found',
        details: 'Account ID 9b99c9ad-e11f-4f9a-8f19-51f5d696563c not found for user'
      }, { status: 404 });
    }

    const account = socialAccounts[0];

    // Comprehensive token analysis
    const tokenAnalysis = {
      account_id: account.id,
      platform: account.platform,
      account_name: account.account_name,
      user_id: account.user_id,
      
      // Access token analysis
      access_token_exists: !!account.access_token,
      access_token_type: typeof account.access_token,
      access_token_length: account.access_token?.length,
      access_token_is_null: account.access_token === null,
      access_token_is_undefined: account.access_token === undefined,
      access_token_is_empty_string: account.access_token === '',
      access_token_preview: account.access_token ? account.access_token.substring(0, 30) + '...' : 'NULL/UNDEFINED',
      
      // Other token fields
      page_access_token_exists: !!account.page_access_token,
      page_access_token_length: account.page_access_token?.length,
      
      // Instagram specific fields
      instagram_business_account_id: account.instagram_business_account_id,
      account_id_field: account.account_id,
      
      // Metadata analysis
      metadata: account.metadata,
      metadata_instagram_account_id: account.metadata?.instagramAccountId,
      
      // All account keys
      all_account_keys: Object.keys(account),
      
      // Timestamps
      created_at: account.created_at,
      updated_at: account.updated_at
    };

    console.log('🔍 COMPREHENSIVE TOKEN ANALYSIS:', tokenAnalysis);

    // Test token validation logic
    const tokenValidationTest = {
      truthy_check: !!account.access_token,
      falsy_check: !account.access_token,
      null_check: account.access_token === null,
      undefined_check: account.access_token === undefined,
      empty_string_check: account.access_token === '',
      length_check: account.access_token?.length > 0
    };

    console.log('🔍 TOKEN VALIDATION TESTS:', tokenValidationTest);

    // Test Instagram Account ID extraction
    const instagramAccountId = account.metadata?.instagramAccountId || account.account_id;
    const instagramIdAnalysis = {
      from_metadata: account.metadata?.instagramAccountId,
      from_account_id: account.account_id,
      final_instagram_id: instagramAccountId,
      instagram_id_exists: !!instagramAccountId
    };

    console.log('🔍 INSTAGRAM ACCOUNT ID ANALYSIS:', instagramIdAnalysis);

    return NextResponse.json({
      success: true,
      message: 'Token diagnostic completed',
      data: {
        user_id: user.id,
        account_found: true,
        token_analysis: tokenAnalysis,
        validation_tests: tokenValidationTest,
        instagram_id_analysis: instagramIdAnalysis,
        
        // Summary
        summary: {
          token_should_pass_validation: !!account.access_token && account.access_token !== null && account.access_token !== undefined && account.access_token !== '',
          instagram_id_should_pass_validation: !!instagramAccountId,
          likely_issue: !account.access_token ? 'ACCESS_TOKEN_MISSING' : 
                       !instagramAccountId ? 'INSTAGRAM_ID_MISSING' : 
                       'UNKNOWN_ISSUE'
        }
      }
    });

  } catch (error) {
    console.error('❌ Debug token endpoint error:', error);
    return NextResponse.json({
      success: false,
      error: 'Debug endpoint failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
<<<<<<< HEAD
}
=======
}
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
