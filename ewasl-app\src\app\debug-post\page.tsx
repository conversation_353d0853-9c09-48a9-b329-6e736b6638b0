'use client';

import { useState } from 'react';

export default function DebugPostPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const runDebugTest = async () => {
    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/debug-post-creation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: '🔍 DEBUG TEST - Step-by-step post creation analysis\n\nTesting each step individually to isolate the exact issue.\n\nTimestamp: ' + new Date().toISOString(),
          platforms: ['facebook'],
          status: 'PUBLISHED'
        }),
      });

      const data = await response.json();
      setResult({
        status: response.status,
        data: data
      });

    } catch (error: any) {
      setResult({
        status: 'ERROR',
        data: {
          error: error.message
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const getStepStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'starting': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">🔍 Post Creation Debug Tool</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Step-by-Step Analysis</h2>
          <p className="text-gray-600 mb-4">
            This tool will test each step of the post creation process individually to identify exactly where the error occurs.
          </p>
          
          <button
            onClick={runDebugTest}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium"
          >
            {loading ? 'Running Debug Test...' : 'Run Step-by-Step Debug Test'}
          </button>
        </div>

        {result && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-4">Debug Results</h3>
            
            <div className="mb-4">
              <span className="font-medium">Overall Status: </span>
              <span className={`px-2 py-1 rounded text-sm ${
                result.status === 200 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {result.status}
              </span>
              {result.data.success !== undefined && (
                <span className={`ml-2 px-2 py-1 rounded text-sm ${
                  result.data.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {result.data.success ? 'SUCCESS' : 'FAILED'}
                </span>
              )}
            </div>

            {result.data.error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded">
                <h4 className="font-medium text-red-800 mb-2">Error:</h4>
                <p className="text-red-700">{result.data.error}</p>
              </div>
            )}

            {result.data.steps && (
              <div className="mb-4">
                <h4 className="font-medium mb-3">Step-by-Step Results:</h4>
                <div className="space-y-3">
                  {result.data.steps.map((step: any, index: number) => (
                    <div key={index} className="border rounded p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">Step {step.step}: {step.name}</span>
                        <span className={`px-2 py-1 rounded text-xs ${getStepStatusColor(step.status)}`}>
                          {step.status.toUpperCase()}
                        </span>
                      </div>
                      
                      {step.data && (
                        <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                          <pre>{JSON.stringify(step.data, null, 2)}</pre>
                        </div>
                      )}
                      
                      {step.error && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm">
                          <span className="font-medium text-red-800">Error: </span>
                          <span className="text-red-700">{step.error}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            <div className="bg-gray-100 rounded p-4 overflow-auto">
              <h4 className="font-medium mb-2">Full Response:</h4>
              <pre className="text-sm">
                {JSON.stringify(result.data, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
