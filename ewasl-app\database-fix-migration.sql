-- Database Fix Migration for eWasl OAuth System
-- This migration fixes the critical database schema issues blocking Facebook API testing
-- Run this in Supabase SQL Editor

-- 1. Fix oauth_states table schema
-- Drop existing table if it has wrong schema
DROP TABLE IF EXISTS public.oauth_states CASCADE;

-- Create oauth_states table with correct schema
CREATE TABLE public.oauth_states (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    platform VARCHAR(50) NOT NULL,
    state_token VARCHAR(255) NOT NULL UNIQUE,
    redirect_uri TEXT NOT NULL,
    code_verifier VARCHAR(255), -- For PKCE (Twitter OAuth 2.0)
    expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '10 minutes'),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient lookups
CREATE INDEX IF NOT EXISTS idx_oauth_states_token ON public.oauth_states(state_token);
CREATE INDEX IF NOT EXISTS idx_oauth_states_user_platform ON public.oauth_states(user_id, platform);
CREATE INDEX IF NOT EXISTS idx_oauth_states_expires ON public.oauth_states(expires_at);

-- Enable Row Level Security
ALTER TABLE public.oauth_states ENABLE ROW LEVEL SECURITY;

-- Create RLS Policy
CREATE POLICY "Users can manage their oauth states" ON public.oauth_states
    FOR ALL USING (auth.uid() = user_id);

-- 2. Ensure oauth_connections table exists with correct schema
CREATE TABLE IF NOT EXISTS public.oauth_connections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('facebook', 'instagram', 'linkedin', 'twitter', 'youtube', 'tiktok')),
    platform_user_id VARCHAR(255) NOT NULL,
    
    -- Encrypted token storage
    access_token_encrypted TEXT NOT NULL,
    refresh_token_encrypted TEXT,
    token_expires_at TIMESTAMPTZ,
    
    -- Platform metadata
    platform_username VARCHAR(255),
    platform_display_name VARCHAR(255),
    platform_avatar_url TEXT,
    platform_data JSONB DEFAULT '{}',
    
    -- Connection health tracking
    is_active BOOLEAN DEFAULT true,
    last_health_check TIMESTAMPTZ,
    health_status VARCHAR(20) DEFAULT 'healthy' CHECK (health_status IN ('healthy', 'warning', 'error', 'expired')),
    error_message TEXT,
    
    -- OAuth metadata
    scopes TEXT[] DEFAULT '{}',
    auth_state VARCHAR(255),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id, platform, platform_user_id)
);

-- Enable RLS for oauth_connections
ALTER TABLE public.oauth_connections ENABLE ROW LEVEL SECURITY;

-- Create RLS Policy for oauth_connections
CREATE POLICY "Users can manage their oauth connections" ON public.oauth_connections
    FOR ALL USING (auth.uid() = user_id);

-- 3. Create audit_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS for audit_logs
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS Policy for audit_logs
CREATE POLICY "Users can view their audit logs" ON public.audit_logs
    FOR SELECT USING (auth.uid() = user_id);

-- Create policy for service role to insert audit logs
CREATE POLICY "Service role can insert audit logs" ON public.audit_logs
    FOR INSERT WITH CHECK (true);

-- 4. Create cleanup function for expired OAuth states
CREATE OR REPLACE FUNCTION cleanup_expired_oauth_states()
RETURNS void AS $$
BEGIN
    DELETE FROM public.oauth_states WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION cleanup_expired_oauth_states() TO authenticated;

-- 5. Create function to update oauth_connections updated_at
CREATE OR REPLACE FUNCTION update_oauth_connections_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for oauth_connections
DROP TRIGGER IF EXISTS trigger_update_oauth_connections_updated_at ON public.oauth_connections;
CREATE TRIGGER trigger_update_oauth_connections_updated_at
    BEFORE UPDATE ON public.oauth_connections
    FOR EACH ROW
    EXECUTE FUNCTION update_oauth_connections_updated_at();

-- 6. Grant necessary permissions
GRANT ALL ON public.oauth_states TO authenticated;
GRANT ALL ON public.oauth_connections TO authenticated;
GRANT ALL ON public.audit_logs TO authenticated;

-- 7. Verify tables were created successfully
SELECT 
    'oauth_states' as table_name,
    COUNT(*) as column_count
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'oauth_states'

UNION ALL

SELECT 
    'oauth_connections' as table_name,
    COUNT(*) as column_count
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'oauth_connections'

UNION ALL

SELECT 
    'audit_logs' as table_name,
    COUNT(*) as column_count
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'audit_logs';

-- Success message
SELECT 'Database migration completed successfully! All tables created with correct schema.' as status;
