import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Initialize Supabase client
    const supabase = createRouteHandlerClient({ cookies });
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'غير مصرح - يرجى تسجيل الدخول أولاً',
        error_en: 'Unauthorized - Please login first',
        response_time: `${Date.now() - startTime}ms`
      }, { status: 401 });
    }

    // Get Facebook accounts from database
    const { data: facebookAccounts, error: dbError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'facebook')
      .eq('is_active', true);

    if (dbError) {
      return NextResponse.json({
        success: false,
        error: 'خطأ في قاعدة البيانات - فشل في استعلام حسابات فيسبوك',
        error_en: 'Database error - Failed to query Facebook accounts',
        details: dbError.message,
        response_time: `${Date.now() - startTime}ms`
      }, { status: 500 });
    }

    if (!facebookAccounts || facebookAccounts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لا توجد حسابات فيسبوك متصلة - يرجى ربط حساب فيسبوك أولاً',
        error_en: 'No Facebook accounts connected - Please connect a Facebook account first',
        response_time: `${Date.now() - startTime}ms`
      }, { status: 404 });
    }

    const testResults = [];

    // Test each Facebook account
    for (const account of facebookAccounts) {
      const accountTest = {
        account_id: account.id,
        platform_username: account.platform_username,
        tests: [] as any[]
      };

      // Test 1: Validate access token
      try {
        const tokenValidationResponse = await fetch(
          `https://graph.facebook.com/v18.0/me?access_token=${account.access_token}`
        );
        const tokenData = await tokenValidationResponse.json();

        if (tokenValidationResponse.ok) {
          accountTest.tests.push({
            test: 'التحقق من صحة الرمز المميز',
            test_en: 'Access Token Validation',
            status: 'نجح',
            details: `المستخدم: ${tokenData.name}`,
            api_response_code: tokenValidationResponse.status
          });
        } else {
          accountTest.tests.push({
            test: 'التحقق من صحة الرمز المميز',
            test_en: 'Access Token Validation',
            status: 'فشل',
            error: 'الرمز المميز غير صالح أو منتهي الصلاحية',
            details: tokenData.error?.message || 'Unknown error',
            api_response_code: tokenValidationResponse.status
          });
        }
      } catch (error) {
        accountTest.tests.push({
          test: 'التحقق من صحة الرمز المميز',
          test_en: 'Access Token Validation',
          status: 'خطأ',
          error: 'فشل في الاتصال بـ Facebook Graph API',
          details: error instanceof Error ? error.message : 'Network error'
        });
      }

      // Test 2: Check token permissions
      try {
        const permissionsResponse = await fetch(
          `https://graph.facebook.com/v18.0/me/permissions?access_token=${account.access_token}`
        );
        const permissionsData = await permissionsResponse.json();

        if (permissionsResponse.ok) {
          const requiredPermissions = [
            'pages_manage_posts',
            'pages_read_engagement', 
            'pages_show_list',
            'instagram_basic',
            'instagram_content_publish'
          ];

          const grantedPermissions = permissionsData.data
            ?.filter((p: any) => p.status === 'granted')
            ?.map((p: any) => p.permission) || [];

          const missingPermissions = requiredPermissions.filter(
            perm => !grantedPermissions.includes(perm)
          );

          accountTest.tests.push({
            test: 'فحص صلاحيات الرمز المميز',
            test_en: 'Token Permissions Check',
            status: missingPermissions.length === 0 ? 'نجح' : 'فشل',
            granted_permissions: grantedPermissions,
            missing_permissions: missingPermissions,
            error: missingPermissions.length > 0 ? 
              `صلاحيات مفقودة: ${missingPermissions.join(', ')}` : null,
            api_response_code: permissionsResponse.status
          });
        }
      } catch (error) {
        accountTest.tests.push({
          test: 'فحص صلاحيات الرمز المميز',
          test_en: 'Token Permissions Check',
          status: 'خطأ',
          error: 'فشل في فحص الصلاحيات',
          details: error instanceof Error ? error.message : 'Network error'
        });
      }

      // Test 3: Fetch user's Facebook pages
      try {
        const pagesResponse = await fetch(
          `https://graph.facebook.com/v18.0/me/accounts?access_token=${account.access_token}`
        );
        const pagesData = await pagesResponse.json();

        if (pagesResponse.ok && pagesData.data) {
          accountTest.tests.push({
            test: 'استعلام صفحات فيسبوك',
            test_en: 'Facebook Pages Query',
            status: 'نجح',
            pages_count: pagesData.data.length,
            pages: pagesData.data.map((page: any) => ({
              id: page.id,
              name: page.name,
              category: page.category,
              access_token_available: !!page.access_token
            })),
            api_response_code: pagesResponse.status
          });

          // Test 4: For each page, check Instagram Business Account
          for (const page of pagesData.data) {
            try {
              const instagramResponse = await fetch(
                `https://graph.facebook.com/v18.0/${page.id}?fields=instagram_business_account&access_token=${account.access_token}`
              );
              const instagramData = await instagramResponse.json();

              accountTest.tests.push({
                test: `فحص حساب إنستغرام للصفحة: ${page.name}`,
                test_en: `Instagram Account Check for Page: ${page.name}`,
                status: instagramData.instagram_business_account ? 'نجح' : 'فشل',
                page_id: page.id,
                page_name: page.name,
                instagram_business_account_id: instagramData.instagram_business_account?.id || null,
                error: !instagramData.instagram_business_account ? 
                  'لا يوجد حساب إنستغرام تجاري مربوط بهذه الصفحة' : null,
                api_response_code: instagramResponse.status
              });
            } catch (error) {
              accountTest.tests.push({
                test: `فحص حساب إنستغرام للصفحة: ${page.name}`,
                test_en: `Instagram Account Check for Page: ${page.name}`,
                status: 'خطأ',
                error: 'فشل في فحص حساب إنستغرام',
                details: error instanceof Error ? error.message : 'Network error'
              });
            }
          }
        } else {
          accountTest.tests.push({
            test: 'استعلام صفحات فيسبوك',
            test_en: 'Facebook Pages Query',
            status: 'فشل',
            error: 'فشل في استعلام صفحات فيسبوك',
            details: pagesData.error?.message || 'Unknown error',
            api_response_code: pagesResponse.status
          });
        }
      } catch (error) {
        accountTest.tests.push({
          test: 'استعلام صفحات فيسبوك',
          test_en: 'Facebook Pages Query',
          status: 'خطأ',
          error: 'فشل في الاتصال لاستعلام الصفحات',
          details: error instanceof Error ? error.message : 'Network error'
        });
      }

      testResults.push(accountTest);
    }

    // Generate summary
    const allTests = testResults.flatMap(result => result.tests);
    const summary = {
      total_accounts_tested: testResults.length,
      total_tests_run: allTests.length,
      successful_tests: allTests.filter(test => test.status === 'نجح').length,
      failed_tests: allTests.filter(test => test.status === 'فشل').length,
      error_tests: allTests.filter(test => test.status === 'خطأ').length
    };

    return NextResponse.json({
      success: true,
      message: 'تم اختبار Facebook Graph API بنجاح',
      message_en: 'Facebook Graph API test completed successfully',
      summary,
      test_results: testResults,
      response_time: `${Date.now() - startTime}ms`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Facebook test endpoint error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'خطأ داخلي في الخادم أثناء اختبار Facebook API',
      error_en: 'Internal server error during Facebook API test',
      details: error instanceof Error ? error.message : 'Unknown error',
      response_time: `${Date.now() - startTime}ms`
    }, { status: 500 });
  }
}
