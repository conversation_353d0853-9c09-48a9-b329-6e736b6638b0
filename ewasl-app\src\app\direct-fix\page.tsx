'use client';

import { useState } from 'react';

export default function DirectFixPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const executeDirectFix = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      console.log('🚨 Executing direct Supabase API schema fix...');
      
      const response = await fetch('/api/direct-schema-fix', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      console.log('Direct fix result:', data);
      setResult(data);
      
    } catch (error) {
      console.error('Direct fix failed:', error);
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  const testPublishing = () => {
    window.location.href = '/posts/new';
  };

  const testOAuthTokens = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-oauth-tokens');
      const data = await response.json();
      setResult({
        test: 'oauth-tokens-verification',
        ...data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-blue-50 py-8" dir="rtl">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6 border-l-4 border-blue-500">
          <div className="flex items-center mb-6">
            <div className="text-3xl ml-3">🔧</div>
            <div>
              <h1 className="text-2xl font-bold text-blue-900 mb-2">
                إصلاح مباشر لمخطط قاعدة البيانات
              </h1>
              <p className="text-blue-700">
                استخدام Supabase API مباشرة مع Token: ********************************************
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <button
              onClick={executeDirectFix}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-4 rounded-lg font-bold disabled:opacity-50 transition-colors"
            >
              {loading ? '🔄 جاري التنفيذ...' : '🔧 تنفيذ الإصلاح المباشر'}
            </button>
            
            <button
              onClick={testOAuthTokens}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-4 rounded-lg font-medium disabled:opacity-50 transition-colors"
            >
              {loading ? '🔄 جاري الاختبار...' : '🔍 اختبار OAuth Tokens'}
            </button>
            
            <button
              onClick={testPublishing}
              disabled={loading}
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-4 rounded-lg font-medium disabled:opacity-50 transition-colors"
            >
              📝 اختبار النشر
            </button>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-bold text-blue-800 mb-2">🔧 خطوات الإصلاح المباشر:</h3>
            <ol className="list-decimal list-inside text-blue-700 space-y-1">
              <li>فحص هيكل الجدول الحالي</li>
              <li>إضافة عمود connection_status مع القيود</li>
              <li>إضافة عمود status مع التحقق</li>
              <li>إضافة أعمدة Facebook (page_id, page_access_token, page_name)</li>
              <li>إضافة عمود instagram_business_account_id</li>
              <li>تحديث السجلات الموجودة بالقيم الافتراضية</li>
              <li>التحقق من المخطط النهائي</li>
              <li>اختبار OAuth tokens endpoint</li>
            </ol>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <h3 className="font-bold text-yellow-800 mb-2">⚡ معلومات الاتصال:</h3>
            <ul className="list-disc list-inside text-yellow-700 space-y-1">
              <li><strong>Supabase URL:</strong> https://nnxfzhxqzmriggulsudr.supabase.co</li>
              <li><strong>Token:</strong> ********************************************</li>
              <li><strong>Project ID:</strong> nnxfzhxqzmriggulsudr</li>
              <li><strong>Method:</strong> Direct API access with service role token</li>
            </ul>
          </div>

          {result && (
            <div className="bg-gray-100 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <div className="text-xl ml-2">
                  {result.success ? '✅' : '❌'}
                </div>
                <h2 className="text-lg font-semibold">
                  {result.success ? 'نجح الإصلاح المباشر!' : 'فشل الإصلاح المباشر'}
                </h2>
              </div>
              <pre className="text-sm overflow-auto max-h-96 bg-white p-4 rounded border font-mono">
                {JSON.stringify(result, null, 2)}
              </pre>
              
              {result.success && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded">
                  <p className="text-green-800 font-medium">
                    🎉 تم إصلاح قاعدة البيانات بنجاح باستخدام Supabase API! يمكنك الآن اختبار نظام النشر.
                  </p>
                  <button
                    onClick={testPublishing}
                    className="mt-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded font-medium"
                  >
                    🚀 اختبار النشر الآن
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
