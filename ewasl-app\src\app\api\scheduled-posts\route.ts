import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getAuthenticatedUser, createAuthenticatedResponse, createErrorResponse } from '@/lib/auth/api-auth';
import { z } from 'zod';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Enhanced Scheduled Posting System - Phase 2B.1 Extension
// Validation schema for scheduled posts
const scheduledPostSchema = z.object({
  content: z.string().min(1, 'المحتوى مطلوب').max(2800, 'المحتوى طويل جداً'),
  media_urls: z.array(z.string().url()).default([]),
  social_account_ids: z.array(z.string()).min(1, 'يرجى اختيار منصة واحدة على الأقل'),
  scheduled_for: z.string().datetime('تاريخ الجدولة غير صحيح'),
  timezone: z.string().default('UTC'),
  retry_count: z.number().min(0).max(5).default(0),
  priority: z.enum(['LOW', 'NORMAL', 'HIGH']).default('NORMAL')
});

// GET - Fetch scheduled posts
export async function GET(request: NextRequest) {
  try {
    console.log('📅 Fetching scheduled posts...');
    
    const { user, supabase } = await getAuthenticatedUser(request);
    console.log('✅ User authenticated:', user.id);

    const { searchParams } = new URL(request.url);
<<<<<<< HEAD
    const status = searchParams.get('status') || 'PENDING';
=======
    const status = searchParams.get('status') || 'pending';
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Fetch scheduled posts with related data
    let query = supabase
      .from('scheduled_posts_queue')
      .select(`
        id,
        post_id,
        scheduled_for,
        timezone,
        status,
        retry_count,
        priority,
        error_message,
        created_at,
        updated_at,
        posts!inner(
          id,
          content,
          media_urls,
          user_id
        )
      `)
      .eq('posts.user_id', user.id)
      .order('scheduled_for', { ascending: true })
      .range(offset, offset + limit - 1);

    // Add status filter
    if (status !== 'ALL') {
<<<<<<< HEAD
      query = query.eq('status', status);
=======
      query = query.eq('status', status.toLowerCase());
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
    }

    const { data: scheduledPosts, error } = await query;

    if (error) {
      console.error('❌ Error fetching scheduled posts:', error);
      return createErrorResponse('فشل في جلب المنشورات المجدولة', 500, error);
    }

    console.log(`✅ Fetched ${scheduledPosts?.length || 0} scheduled posts`);

    return createAuthenticatedResponse({
      scheduled_posts: scheduledPosts || [],
      total: scheduledPosts?.length || 0,
      status_filter: status,
      user: {
        id: user.id,
        email: user.email
      }
    });

  } catch (error) {
    console.error('❌ Scheduled posts fetch error:', error);
    return createErrorResponse('خطأ في جلب المنشورات المجدولة', 500, error);
  }
}

// POST - Create scheduled post
export async function POST(request: NextRequest) {
  console.log('📅 Creating scheduled post...');
  
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    console.log('✅ User authenticated:', user.id);

    const body = await request.json();
    console.log('📋 Scheduled post request:', { ...body, content: body.content?.substring(0, 50) + '...' });

    // Validate request body
    const validation = scheduledPostSchema.safeParse(body);
    if (!validation.success) {
      console.log('❌ Validation failed:', validation.error.errors);
      return createErrorResponse(
        'بيانات غير صحيحة',
        400,
        validation.error.errors.map(err => err.message).join(', ')
      );
    }

    const { content, media_urls, social_account_ids, scheduled_for, timezone, retry_count, priority } = validation.data;

    // Validate scheduled time is in the future
    const scheduledDate = new Date(scheduled_for);
    const now = new Date();
    if (scheduledDate <= now) {
      return createErrorResponse(
        'وقت الجدولة يجب أن يكون في المستقبل',
        400,
        'Scheduled time must be in the future'
      );
    }

    // Get social accounts
    const { data: socialAccounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .in('id', social_account_ids);

    if (accountsError || !socialAccounts || socialAccounts.length === 0) {
      console.error('❌ Error fetching social accounts:', accountsError);
      return createErrorResponse('لا توجد حسابات اجتماعية متصلة', 400, accountsError);
    }

    // Create post as SCHEDULED
    const { data: post, error: createError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content,
        media_urls,
        status: 'SCHEDULED',
        scheduled_at: scheduledDate.toISOString(),
        timezone,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ Error creating post:', createError);
      return createErrorResponse('فشل في إنشاء المنشور', 500, createError);
    }

    console.log('✅ Post created successfully:', post.id);

    // Create post_social_accounts entries
    const postSocialAccountsData = socialAccounts.map(account => ({
      post_id: post.id,
      social_account_id: account.id,
      platform: account.platform,
      status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    const { data: postSocialAccounts, error: psaError } = await supabase
      .from('post_social_accounts')
      .insert(postSocialAccountsData)
      .select();

    if (psaError) {
      console.error('❌ Failed to create post_social_accounts entries:', psaError);
      // Clean up: Delete the created post
      await supabase.from('posts').delete().eq('id', post.id);
      return createErrorResponse('فشل في ربط المنشور بالحسابات الاجتماعية', 500, psaError);
    }

    // Add to scheduled posts queue
    const { data: queueEntry, error: queueError } = await supabase
      .from('scheduled_posts_queue')
      .insert({
        post_id: post.id,
        scheduled_for: scheduledDate.toISOString(),
        timezone,
        status: 'pending', // Use lowercase to match database constraint
        retry_count,
        priority,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (queueError) {
      console.error('❌ Error adding to scheduled queue:', queueError);
      // Clean up: Delete the created post and relationships
      await supabase.from('post_social_accounts').delete().eq('post_id', post.id);
      await supabase.from('posts').delete().eq('id', post.id);
      return createErrorResponse('فشل في جدولة المنشور', 500, queueError);
    }

    console.log('✅ Post scheduled successfully:', queueEntry.id);

    return createAuthenticatedResponse({
      message: 'تم جدولة المنشور بنجاح',
      data: {
        post: {
          id: post.id,
          content: post.content,
          status: post.status,
          scheduled_at: post.scheduled_at,
          created_at: post.created_at
        },
        queue_entry: {
          id: queueEntry.id,
          scheduled_for: queueEntry.scheduled_for,
          timezone: queueEntry.timezone,
          status: queueEntry.status,
          priority: queueEntry.priority
        },
        social_accounts: socialAccounts.map(account => ({
          id: account.id,
          platform: account.platform,
          account_name: account.account_name
        }))
      }
    });

  } catch (error) {
    console.error('❌ Unexpected error in scheduled post creation:', error);
    return createErrorResponse('حدث خطأ غير متوقع في جدولة المنشور', 500, error);
  }
}

// PUT - Update scheduled post
export async function PUT(request: NextRequest) {
  try {
    console.log('📅 Updating scheduled post...');
    
    const { user, supabase } = await getAuthenticatedUser(request);
    console.log('✅ User authenticated:', user.id);

    const body = await request.json();
    const { queue_id, scheduled_for, timezone, priority, status } = body;

    if (!queue_id) {
      return createErrorResponse('معرف المنشور المجدول مطلوب', 400);
    }

    // Verify ownership through post relationship
    const { data: queueEntry, error: fetchError } = await supabase
      .from('scheduled_posts_queue')
      .select(`
        id,
        post_id,
        status,
        posts!inner(
          id,
          user_id
        )
      `)
      .eq('id', queue_id)
      .eq('posts.user_id', user.id)
      .single();

    if (fetchError || !queueEntry) {
      console.error('❌ Scheduled post not found or not owned by user:', fetchError);
      return createErrorResponse('المنشور المجدول غير موجود أو لا تملك صلاحية تعديله', 404);
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (scheduled_for) {
      const scheduledDate = new Date(scheduled_for);
      const now = new Date();
      if (scheduledDate <= now) {
        return createErrorResponse(
          'وقت الجدولة يجب أن يكون في المستقبل',
          400,
          'Scheduled time must be in the future'
        );
      }
      updateData.scheduled_for = scheduledDate.toISOString();
    }

    if (timezone) updateData.timezone = timezone;
    if (priority) updateData.priority = priority;
<<<<<<< HEAD
    if (status) updateData.status = status;
=======
    if (status) updateData.status = status.toLowerCase();
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da

    // Update the scheduled post
    const { data: updatedEntry, error: updateError } = await supabase
      .from('scheduled_posts_queue')
      .update(updateData)
      .eq('id', queue_id)
      .select()
      .single();

    if (updateError) {
      console.error('❌ Error updating scheduled post:', updateError);
      return createErrorResponse('فشل في تحديث المنشور المجدول', 500, updateError);
    }

    console.log('✅ Scheduled post updated successfully:', updatedEntry.id);

    return createAuthenticatedResponse({
      message: 'تم تحديث المنشور المجدول بنجاح',
      data: {
        queue_entry: updatedEntry
      }
    });

  } catch (error) {
    console.error('❌ Unexpected error in scheduled post update:', error);
    return createErrorResponse('حدث خطأ غير متوقع في تحديث المنشور المجدول', 500, error);
  }
}

// DELETE - Cancel scheduled post
export async function DELETE(request: NextRequest) {
  try {
    console.log('📅 Canceling scheduled post...');
    
    const { user, supabase } = await getAuthenticatedUser(request);
    console.log('✅ User authenticated:', user.id);

    const { searchParams } = new URL(request.url);
    const queue_id = searchParams.get('queue_id');

    if (!queue_id) {
      return createErrorResponse('معرف المنشور المجدول مطلوب', 400);
    }

    // Verify ownership and get post details
    const { data: queueEntry, error: fetchError } = await supabase
      .from('scheduled_posts_queue')
      .select(`
        id,
        post_id,
        status,
        posts!inner(
          id,
          user_id
        )
      `)
      .eq('id', queue_id)
      .eq('posts.user_id', user.id)
      .single();

    if (fetchError || !queueEntry) {
      console.error('❌ Scheduled post not found or not owned by user:', fetchError);
      return createErrorResponse('المنشور المجدول غير موجود أو لا تملك صلاحية حذفه', 404);
    }

    // Check if post can be canceled
<<<<<<< HEAD
    if (queueEntry.status === 'PROCESSING' || queueEntry.status === 'COMPLETED') {
=======
    if (queueEntry.status === 'processing' || queueEntry.status === 'completed') {
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
      return createErrorResponse(
        'لا يمكن إلغاء منشور قيد المعالجة أو مكتمل',
        400,
        'Cannot cancel post that is processing or completed'
      );
    }

    // Delete from queue (this will cascade to related records if configured)
    const { error: deleteQueueError } = await supabase
      .from('scheduled_posts_queue')
      .delete()
      .eq('id', queue_id);

    if (deleteQueueError) {
      console.error('❌ Error deleting from queue:', deleteQueueError);
      return createErrorResponse('فشل في إلغاء المنشور المجدول', 500, deleteQueueError);
    }

    // Update post status to DRAFT
    const { error: updatePostError } = await supabase
      .from('posts')
      .update({
        status: 'DRAFT',
        updated_at: new Date().toISOString()
      })
      .eq('id', queueEntry.post_id);

    if (updatePostError) {
      console.error('❌ Error updating post status:', updatePostError);
      // Don't fail the request, just log the warning
    }

    console.log('✅ Scheduled post canceled successfully:', queue_id);

    return createAuthenticatedResponse({
      message: 'تم إلغاء المنشور المجدول بنجاح'
    });

  } catch (error) {
    console.error('❌ Unexpected error in scheduled post cancellation:', error);
    return createErrorResponse('حدث خطأ غير متوقع في إلغاء المنشور المجدول', 500, error);
  }
<<<<<<< HEAD
}
=======
}
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
