/**
 * Facebook OAuth Callback Handler
 * Handles the OAuth callback from Facebook and processes the authorization code
 */

import { NextRequest, NextResponse } from 'next/server'
import { createFacebookOAuthService } from '@/lib/oauth/facebook'
import { createClient } from '@/lib/supabase/server'
import { createClient as createServiceClient } from '@supabase/supabase-js'
import { SocialPlatform } from '@/types/social-enhanced'
import fs from 'fs'
import path from 'path'

// File-based logging for debugging
function debugLog(message: string, data?: any) {
  const timestamp = new Date().toISOString()
  const logEntry = `[${timestamp}] ${message}${data ? '\n' + JSON.stringify(data, null, 2) : ''}\n\n`

  try {
    const logPath = path.join(process.cwd(), 'debug-oauth.log')
    fs.appendFileSync(logPath, logEntry)
  } catch (error) {
    console.error('Failed to write to debug log:', error)
  }

  // Also log to console
  console.log(message, data || '')
}

// Define ConnectionStatus locally to avoid import issues
type ConnectionStatus = 'connected' | 'expired' | 'error' | 'reconnecting' | 'disconnected';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')
    const errorDescription = searchParams.get('error_description')

    // Handle OAuth errors
    if (error) {
      debugLog('Facebook OAuth error:', { error, errorDescription })
      return NextResponse.redirect(
        new URL(`/social?error=oauth_error&message=${encodeURIComponent(errorDescription || error)}`, request.url)
      )
    }

    // Validate authorization code
    if (!code) {
      return NextResponse.redirect(
        new URL('/social?error=missing_code&message=Authorization code not provided', request.url)
      )
    }

    // Initialize Facebook OAuth service
    const facebookService = createFacebookOAuthService()
    const supabase = createClient()

    // Create service role client for database operations (bypasses RLS)
    const supabaseService = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Exchange code for access token
    debugLog('🔍 DEBUGGING: Exchanging authorization code for access token...')
    const tokenResponse = await facebookService.exchangeCodeForToken(code)
    debugLog('🔍 DEBUGGING: Token exchange response:', {
      hasAccessToken: !!tokenResponse.access_token,
      tokenType: tokenResponse.token_type,
      expiresIn: tokenResponse.expires_in
    })

    // Get long-lived token
    debugLog('🔍 DEBUGGING: Getting long-lived access token...')
    const longLivedToken = await facebookService.getLongLivedToken(tokenResponse.access_token)
    debugLog('🔍 DEBUGGING: Long-lived token response:', {
      hasAccessToken: !!longLivedToken.access_token,
      tokenType: longLivedToken.token_type,
      expiresIn: longLivedToken.expires_in
    })

    // Validate token and get user info
    debugLog('🔍 DEBUGGING: Validating token and fetching user info...')
    const userInfo = await facebookService.validateToken(longLivedToken.access_token)
    debugLog('🔍 DEBUGGING: User info retrieved:', {
      id: userInfo.id,
      name: userInfo.name,
      hasEmail: !!userInfo.email
    })

    // Get user's Facebook pages and Instagram accounts
    debugLog('🔍 DEBUGGING: Fetching user accounts...')
    debugLog('🔍 DEBUGGING: Using access token:', longLivedToken.access_token.substring(0, 20) + '...')

    let accounts;
    try {
      accounts = await facebookService.getUserAccounts(longLivedToken.access_token)
      debugLog(`🔍 DEBUGGING: Found ${accounts.length} accounts:`, accounts)
    } catch (error) {
      debugLog('🚨 ERROR: Failed to fetch user accounts:', error)
      return NextResponse.redirect(
        new URL('/social?error=api_error&message=Failed to fetch Facebook accounts', request.url)
      )
    }

    if (accounts.length === 0) {
      debugLog('⚠️ WARNING: No accounts returned from Facebook API')
      return NextResponse.redirect(
        new URL('/social?error=no_accounts&message=No Facebook accounts found', request.url)
      )
    }

    // Validate OAuth state and get user ID
    if (!state) {
      return NextResponse.redirect(
        new URL('/social?error=missing_state&message=OAuth state parameter missing', request.url)
      )
    }

    let userId: string
    try {
      // Validate OAuth state using direct database operations
      const { data: stateData, error: stateError } = await supabase
        .from('oauth_states')
        .select('user_id, expires_at')
        .eq('state_token', state)
        .eq('platform', 'FACEBOOK')
        .single()

      if (stateError || !stateData) {
        debugLog('OAuth state validation failed:', stateError)
        return NextResponse.redirect(
          new URL('/social?error=invalid_state&message=Invalid or expired OAuth state', request.url)
        )
      }

      // Check if state has expired
      if (new Date(stateData.expires_at) < new Date()) {
        debugLog('OAuth state expired:', stateData.expires_at)
        return NextResponse.redirect(
          new URL('/social?error=expired_state&message=OAuth state has expired', request.url)
        )
      }

      userId = stateData.user_id

      // Clean up used state token
      await supabase
        .from('oauth_states')
        .delete()
        .eq('state_token', state)

    } catch (error) {
      debugLog('Error validating OAuth state:', error)
      return NextResponse.redirect(
        new URL('/social?error=state_validation_error&message=Failed to validate OAuth state', request.url)
      )
    }

    // Save accounts to database
    debugLog(`🔍 DEBUGGING: Saving ${accounts.length} accounts to database...`)
    let savedCount = 0

    for (let i = 0; i < accounts.length; i++) {
      const account = accounts[i]
      debugLog(`🔍 DEBUGGING: ========== PROCESSING ACCOUNT ${i + 1}/${accounts.length} ==========`)
      debugLog(`🔍 DEBUGGING: Account details:`, {
        id: account.id,
        name: account.accountName,
        platform: account.platform,
        hasAccessToken: !!account.accessToken,
        accessTokenPreview: account.accessToken ? account.accessToken.substring(0, 20) + '...' : 'null',
        metadata: account.metadata,
        connectionStatus: account.connectionStatus,
        accountHandle: account.accountHandle
      })

      try {
        debugLog(`🔍 DEBUGGING: Step 1 - Starting account processing for ${account.accountName}...`)
        // Step 2: Get account metrics
        debugLog(`🔍 DEBUGGING: Step 2 - Fetching metrics for account ${account.id}...`)
        const metricsId = account.metadata?.pageId || account.metadata?.instagramAccountId || account.id
        debugLog(`🔍 DEBUGGING: Using metrics ID: ${metricsId}`)

        // Use account's access token or fall back to the main long-lived token
        const tokenToUse = account.accessToken || longLivedToken.access_token
        debugLog(`🔍 DEBUGGING: Using token: ${tokenToUse ? tokenToUse.substring(0, 20) + '...' : 'null'}`)

        let metrics
        try {
          debugLog(`🔍 DEBUGGING: Calling getAccountMetrics...`)
          metrics = await facebookService.getAccountMetrics(
            metricsId,
            tokenToUse,
            account.platform
          )
          debugLog(`🔍 DEBUGGING: ✅ Metrics fetched successfully:`, metrics)
        } catch (metricsError) {
          debugLog(`🚨 METRICS ERROR for account ${account.id}:`, metricsError)
          // Use default metrics if fetching fails
          metrics = {
            followerCount: 0,
            engagementRate: 0,
            postCount: 0
          }
          debugLog(`🔍 DEBUGGING: ⚠️ Using default metrics due to error`)
        }
        debugLog(`🔍 DEBUGGING: Step 2 completed - Metrics ready`)

        // Step 3: Prepare account data for database
        debugLog(`🔍 DEBUGGING: Step 3 - Preparing account data for database...`)
        const accountData = {
          account_id: account.id, // Facebook/Instagram account ID (text) - REQUIRED
          user_id: userId, // UUID - REQUIRED
          platform: account.platform, // Platform enum (FACEBOOK/INSTAGRAM) - REQUIRED
          account_name: account.accountName, // text - REQUIRED
          access_token: tokenToUse, // text - REQUIRED
          // Optional fields
          account_handle: account.accountHandle || null,
          connection_status: account.connectionStatus, // account_status enum
          refresh_token: account.refreshToken || null,
          expires_at: account.expiresAt ? new Date(account.expiresAt).toISOString() : null,
          last_validated_at: account.lastValidatedAt ? new Date(account.lastValidatedAt).toISOString() : null,
          metadata: {
            ...account.metadata,
            metrics: {
              followerCount: metrics.followerCount,
              engagementRate: metrics.engagementRate,
              postCount: metrics.postCount
            }
          },
          profile_image_url: account.profileImageUrl || null,
          permissions: account.permissions || [], // JSONB array
          // Facebook page-specific fields (CRITICAL FIX for publishing)
          page_id: account.pageId || null,
          page_access_token: account.pageAccessToken || null,
          page_name: account.pageName || null
          // Note: created_at and updated_at are auto-generated, don't include them
        }

        debugLog(`🔍 DEBUGGING: ✅ Account data prepared successfully`)
        debugLog(`🔍 DEBUGGING: Account data summary:`, {
          account_id: accountData.account_id,
          user_id: accountData.user_id,
          platform: accountData.platform,
          account_name: accountData.account_name,
          has_access_token: !!accountData.access_token,
          connection_status: accountData.connection_status
        })
        debugLog(`🔍 DEBUGGING: Full account data for database:`, accountData)

        // Step 4: Validate required fields
        debugLog(`🔍 DEBUGGING: Step 4 - Validating required fields...`)
        const requiredFields = ['account_id', 'user_id', 'platform', 'account_name', 'access_token']
        const missingFields = requiredFields.filter(field => !accountData[field])

        if (missingFields.length > 0) {
          debugLog(`🚨 VALIDATION ERROR: Missing required fields: ${missingFields.join(', ')}`)
          debugLog(`🚨 Account data:`, accountData)
          debugLog(`🔍 DEBUGGING: ❌ Skipping account ${account.id} due to validation failure`)
          continue // Skip this account and move to the next one
        }
        debugLog(`🔍 DEBUGGING: ✅ All required fields validated successfully`)

        // Step 5: Insert or update account in database
        debugLog(`🔍 DEBUGGING: Step 5 - Attempting to upsert account to database...`)
        debugLog(`🔍 DEBUGGING: Using service role client for database operation`)

        const { data: upsertResult, error: dbError } = await supabaseService
          .from('social_accounts')
          .upsert(accountData, {
            onConflict: 'user_id,platform,account_id'
          })
          .select()

        debugLog(`🔍 DEBUGGING: Database operation completed, checking results...`)

        if (dbError) {
          debugLog(`🚨 DATABASE ERROR saving account ${account.id}:`, dbError)
          debugLog('🚨 Account data that failed:', accountData)
          debugLog('🚨 Error details:', {
            code: dbError.code,
            message: dbError.message,
            details: dbError.details,
            hint: dbError.hint
          })
          debugLog(`🔍 DEBUGGING: ❌ Account ${account.id} failed to save`)
        } else {
          debugLog(`🔍 DEBUGGING: ✅ Database insertion successful!`)
          debugLog(`✅ Successfully saved account: ${account.accountName} (${account.platform})`)
          debugLog(`✅ Upsert result:`, upsertResult)
          savedCount++
          debugLog(`🔍 DEBUGGING: ✅ Saved count incremented to: ${savedCount}`)

          // Log the connection in audit logs using service client
          debugLog(`🔍 DEBUGGING: Logging audit entry for account ${account.id}...`)
          try {
            const auditResult = await supabaseService
              .from('audit_logs')
              .insert({
                user_id: userId,
                action: 'connect_social_account',
                resource_type: 'social_accounts',
                resource_ids: [account.id],
                metadata: {
                  platform: account.platform,
                  account_name: account.accountName,
                  connection_method: 'oauth_facebook'
                }
              })

            if (auditResult.error) {
              debugLog('🚨 AUDIT LOG ERROR (non-critical):', auditResult.error)
            } else {
              debugLog(`✅ Audit log created for account ${account.id}`)
            }
          } catch (auditError) {
            debugLog('🚨 AUDIT LOG EXCEPTION (non-critical):', auditError)
          }
          debugLog(`🔍 DEBUGGING: ========== ACCOUNT ${i + 1}/${accounts.length} PROCESSING COMPLETE ==========`)
          debugLog(`🔍 DEBUGGING: Account ${account.id} result: ${dbError ? 'FAILED' : 'SUCCESS'}`)
        }

      } catch (accountError) {
        debugLog(`🚨 CRITICAL ERROR processing account ${account.accountName} (${account.platform}):`, {
          error: accountError,
          message: accountError instanceof Error ? accountError.message : String(accountError),
          stack: accountError instanceof Error ? accountError.stack : undefined,
          accountId: account.id,
          accountName: account.accountName,
          platform: account.platform,
          hasAccessToken: !!account.accessToken
        })
        debugLog(`🔍 DEBUGGING: ========== ACCOUNT ${i + 1}/${accounts.length} PROCESSING FAILED ==========`)
      }
    }

    debugLog(`🔍 DEBUGGING: Final results - Found: ${accounts.length}, Saved: ${savedCount}`)

    if (savedCount === 0 && accounts.length > 0) {
      debugLog('🚨 WARNING: No real Facebook accounts were saved despite being found')
      debugLog('🔍 This suggests an issue in the account processing loop')
    }

    // Redirect to social accounts page with success message
    const successMessage = savedCount > 0
      ? `Successfully connected ${savedCount} account(s)`
      : `OAuth completed but no accounts were saved (${accounts.length} found)`

    debugLog(`🔍 DEBUGGING: Redirecting with message: ${successMessage}`)

    return NextResponse.redirect(
      new URL(`/social?success=true&message=${encodeURIComponent(successMessage)}`, request.url)
    )

  } catch (error) {
    debugLog('Facebook OAuth callback error:', error)

    // Redirect to social accounts page with error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    return NextResponse.redirect(
      new URL(`/social?error=callback_error&message=${encodeURIComponent(errorMessage)}`, request.url)
    )
  }
}

// Handle POST requests (not typically used for OAuth callbacks)
export async function POST(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
