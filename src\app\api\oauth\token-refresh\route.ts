import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { TokenRefreshService } from '@/lib/oauth/token-refresh-service';

/**
 * OAuth Token Refresh Management API
 * Handles token expiration monitoring, automatic refresh, and status reporting
 */

export async function GET(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || user.id;
    const action = searchParams.get('action') || 'status';

    console.log(`🔍 Token refresh ${action} for user:`, userId);

    const tokenService = new TokenRefreshService();

    switch (action) {
      case 'status':
        return await handleTokenStatus(userId, supabase);
      
      case 'check-expiring':
        return await handleCheckExpiring(tokenService);
      
      case 'refresh-all':
        return await handleRefreshAll(tokenService);
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: status, check-expiring, or refresh-all' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Error in token refresh API:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في إدارة رموز المصادقة',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Manual token refresh for specific account
 */
export async function POST(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    const body = await request.json();
    const { accountId, platform, userId = user.id } = body;

    if (!accountId || !platform) {
      return NextResponse.json(
        { error: 'accountId and platform are required' },
        { status: 400 }
      );
    }

    console.log(`🔄 Manual token refresh for ${platform} account:`, accountId);

    // Get account details
    const { data: account, error: accountError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('user_id', userId)
      .single();

    if (accountError || !account) {
      return NextResponse.json(
        { error: 'لم يتم العثور على الحساب' },
        { status: 404 }
      );
    }

    const tokenService = new TokenRefreshService();
    
    const tokenInfo = {
      id: account.id,
      userId: account.user_id,
      platform: account.platform,
      accessToken: account.access_token,
      refreshToken: account.refresh_token,
      expiresAt: account.expires_at,
      lastValidatedAt: account.last_validated_at,
      connectionStatus: account.connection_status
    };

    const refreshResult = await tokenService.refreshToken(tokenInfo);
    
    if (refreshResult.success) {
      await tokenService.updateTokenInDatabase(tokenInfo, refreshResult);
      
      return NextResponse.json({
        success: true,
        message: 'تم تحديث رمز المصادقة بنجاح',
        platform: account.platform,
        accountName: account.account_name,
        expiresAt: refreshResult.expiresAt,
        lastRefreshed: new Date().toISOString()
      });
    } else {
      return NextResponse.json({
        success: false,
        error: refreshResult.error,
        shouldReconnect: refreshResult.shouldReconnect,
        platform: account.platform,
        accountName: account.account_name
      }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Error in manual token refresh:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في تحديث رمز المصادقة',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Handle token status check for user's accounts
 */
async function handleTokenStatus(userId: string, supabase: any) {
  const { data: accounts, error } = await supabase
    .from('social_accounts')
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true);

  if (error) {
    throw new Error(`Database error: ${error.message}`);
  }

  const tokenService = new TokenRefreshService();
  
  const accountsWithStatus = accounts.map((account: any) => {
    const isExpired = tokenService.isTokenExpired(account.expires_at);
    const isExpiringSoon = tokenService.isTokenExpiringSoon(account.expires_at);
    
    return {
      id: account.id,
      platform: account.platform,
      accountName: account.account_name,
      connectionStatus: account.connection_status,
      expiresAt: account.expires_at,
      lastValidatedAt: account.last_validated_at,
      isExpired,
      isExpiringSoon,
      needsRefresh: isExpired || isExpiringSoon || account.connection_status === 'expired',
      daysUntilExpiry: account.expires_at ? 
        Math.ceil((new Date(account.expires_at).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : null
    };
  });

  const summary = {
    total: accountsWithStatus.length,
    connected: accountsWithStatus.filter(acc => acc.connectionStatus === 'connected' && !acc.isExpired).length,
    expired: accountsWithStatus.filter(acc => acc.isExpired).length,
    expiringSoon: accountsWithStatus.filter(acc => acc.isExpiringSoon && !acc.isExpired).length,
    needsRefresh: accountsWithStatus.filter(acc => acc.needsRefresh).length
  };

  return NextResponse.json({
    success: true,
    accounts: accountsWithStatus,
    summary,
    lastChecked: new Date().toISOString()
  });
}

/**
 * Handle checking for expiring tokens across all users
 */
async function handleCheckExpiring(tokenService: TokenRefreshService) {
  const expiringTokens = await tokenService.getTokensNeedingRefresh();
  
  const tokensByPlatform = expiringTokens.reduce((acc, token) => {
    if (!acc[token.platform]) {
      acc[token.platform] = [];
    }
    acc[token.platform].push({
      userId: token.userId,
      expiresAt: token.expiresAt,
      connectionStatus: token.connectionStatus
    });
    return acc;
  }, {} as Record<string, any[]>);

  return NextResponse.json({
    success: true,
    totalExpiring: expiringTokens.length,
    tokensByPlatform,
    lastChecked: new Date().toISOString()
  });
}

/**
 * Handle refreshing all expired tokens
 */
async function handleRefreshAll(tokenService: TokenRefreshService) {
  const result = await tokenService.refreshAllExpiredTokens();
  
  return NextResponse.json({
    success: true,
    message: 'تم تحديث رموز المصادقة المنتهية الصلاحية',
    ...result,
    lastRefreshed: new Date().toISOString()
  });
}

/**
 * DELETE - Revoke and remove token
 */
export async function DELETE(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');
    const userId = searchParams.get('userId') || user.id;

    if (!accountId) {
      return NextResponse.json(
        { error: 'accountId is required' },
        { status: 400 }
      );
    }

    // Get account details
    const { data: account, error: accountError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('user_id', userId)
      .single();

    if (accountError || !account) {
      return NextResponse.json(
        { error: 'لم يتم العثور على الحساب' },
        { status: 404 }
      );
    }

    // Revoke token with platform
    // TODO: Implement platform-specific token revocation
    
    // Update account status in database
    const { error: updateError } = await supabase
      .from('social_accounts')
      .update({
        connection_status: 'disconnected',
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', accountId);

    if (updateError) {
      throw new Error(`Failed to update account: ${updateError.message}`);
    }

    return NextResponse.json({
      success: true,
      message: 'تم إلغاء رمز المصادقة وقطع الاتصال',
      platform: account.platform,
      accountName: account.account_name
    });

  } catch (error) {
    console.error('❌ Error revoking token:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في إلغاء رمز المصادقة',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
