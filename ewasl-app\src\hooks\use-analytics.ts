/**
 * Real-time Analytics Hook
 * Provides real-time analytics data with caching and auto-refresh
 */

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

export interface AnalyticsMetrics {
  platform: string;
  accountId: string;
  accountName: string;
  metrics: {
    followers: number;
    following: number;
    posts: number;
    engagement: {
      likes: number;
      comments: number;
      shares: number;
      saves?: number;
      clicks?: number;
    };
    reach: {
      impressions: number;
      reach: number;
      profileViews?: number;
    };
    growth: {
      followersChange: number;
      engagementRate: number;
      averageLikes: number;
      averageComments: number;
    };
  };
  lastUpdated: Date;
}

export interface PostAnalytics {
  postId: string;
  platform: string;
  content: string;
  publishedAt: Date;
  metrics: {
    likes: number;
    comments: number;
    shares: number;
    saves?: number;
    clicks?: number;
    impressions: number;
    reach: number;
  };
  engagement: {
    rate: number;
    score: number;
  };
}

export interface UseAnalyticsOptions {
  platform?: string;
  accountId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
}

export function useAnalytics(options: UseAnalyticsOptions = {}) {
  const {
    platform,
    accountId,
    autoRefresh = true,
    refreshInterval = 5 * 60 * 1000, // 5 minutes
  } = options;

  const [analytics, setAnalytics] = useState<AnalyticsMetrics[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isCached, setIsCached] = useState(false);

  const fetchAnalytics = useCallback(async (forceRefresh = false) => {
    try {
      setError(null);
      if (forceRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      const params = new URLSearchParams();
      if (platform) params.append('platform', platform);
      if (accountId) params.append('accountId', accountId);
      if (forceRefresh) params.append('refresh', 'true');

      const response = await fetch(`/api/analytics?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch analytics');
      }

      if (data.success) {
        const analyticsData = Array.isArray(data.analytics) 
          ? data.analytics 
          : [data.analytics];
        
        // Convert date strings back to Date objects
        const processedAnalytics = analyticsData.map((item: any) => ({
          ...item,
          lastUpdated: new Date(item.lastUpdated),
        }));

        setAnalytics(processedAnalytics);
        setLastUpdated(new Date());
        setIsCached(data.cached || false);

        if (forceRefresh && !data.cached) {
          toast.success('تم تحديث البيانات بنجاح');
        }
      }
    } catch (err: any) {
      console.error('Analytics fetch error:', err);
      setError(err.message);
      toast.error('فشل في تحميل بيانات التحليلات');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [platform, accountId]);

  const refreshAnalytics = useCallback(() => {
    fetchAnalytics(true);
  }, [fetchAnalytics]);

  const getPostAnalytics = useCallback(async (
    postPlatform: string,
    postId: string
  ): Promise<PostAnalytics | null> => {
    try {
      const response = await fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'post-analytics',
          platform: postPlatform,
          postId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch post analytics');
      }

      if (data.success && data.postAnalytics) {
        return {
          ...data.postAnalytics,
          publishedAt: new Date(data.postAnalytics.publishedAt),
        };
      }

      return null;
    } catch (err: any) {
      console.error('Post analytics error:', err);
      toast.error('فشل في تحميل تحليلات المنشور');
      return null;
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchAnalytics();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchAnalytics]);

  // Calculate summary metrics
  const summaryMetrics = {
    totalFollowers: analytics.reduce((sum, item) => sum + item.metrics.followers, 0),
    totalEngagement: analytics.reduce((sum, item) => 
      sum + item.metrics.engagement.likes + 
      item.metrics.engagement.comments + 
      item.metrics.engagement.shares, 0
    ),
    totalImpressions: analytics.reduce((sum, item) => sum + item.metrics.reach.impressions, 0),
    averageEngagementRate: analytics.length > 0 
      ? analytics.reduce((sum, item) => sum + item.metrics.growth.engagementRate, 0) / analytics.length
      : 0,
    connectedPlatforms: analytics.length,
  };

  // Get analytics for specific platform
  const getPlatformAnalytics = useCallback((platformName: string) => {
    return analytics.find(item => 
      item.platform.toLowerCase() === platformName.toLowerCase()
    );
  }, [analytics]);

  // Get top performing platform
  const getTopPerformingPlatform = useCallback(() => {
    if (analytics.length === 0) return null;

    return analytics.reduce((top, current) => {
      const currentScore = current.metrics.growth.engagementRate;
      const topScore = top.metrics.growth.engagementRate;
      return currentScore > topScore ? current : top;
    });
  }, [analytics]);

  // Get growth trends
  const getGrowthTrends = useCallback(() => {
    return analytics.map(item => ({
      platform: item.platform,
      followersChange: item.metrics.growth.followersChange,
      engagementRate: item.metrics.growth.engagementRate,
      trend: item.metrics.growth.followersChange > 0 ? 'up' : 
             item.metrics.growth.followersChange < 0 ? 'down' : 'stable',
    }));
  }, [analytics]);

  // Get platform comparison
  const getPlatformComparison = useCallback(() => {
    return analytics.map(item => ({
      platform: item.platform,
      followers: item.metrics.followers,
      engagement: item.metrics.engagement.likes + 
                 item.metrics.engagement.comments + 
                 item.metrics.engagement.shares,
      engagementRate: item.metrics.growth.engagementRate,
      impressions: item.metrics.reach.impressions,
    }));
  }, [analytics]);

  return {
    // Data
    analytics,
    summaryMetrics,
    
    // State
    isLoading,
    isRefreshing,
    error,
    lastUpdated,
    isCached,
    
    // Actions
    refreshAnalytics,
    getPostAnalytics,
    
    // Computed values
    getPlatformAnalytics,
    getTopPerformingPlatform,
    getGrowthTrends,
    getPlatformComparison,
  };
}

// Hook for post-specific analytics
export function usePostAnalytics(platform: string, postId: string) {
  const [postAnalytics, setPostAnalytics] = useState<PostAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPostAnalytics = useCallback(async () => {
    if (!platform || !postId) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'post-analytics',
          platform,
          postId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch post analytics');
      }

      if (data.success && data.postAnalytics) {
        setPostAnalytics({
          ...data.postAnalytics,
          publishedAt: new Date(data.postAnalytics.publishedAt),
        });
      }
    } catch (err: any) {
      console.error('Post analytics error:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [platform, postId]);

  useEffect(() => {
    fetchPostAnalytics();
  }, [fetchPostAnalytics]);

  return {
    postAnalytics,
    isLoading,
    error,
    refresh: fetchPostAnalytics,
  };
}
