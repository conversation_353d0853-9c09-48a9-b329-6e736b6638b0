{"version": 3, "sources": ["../../src/lib/import-next-warning.ts"], "sourcesContent": ["import * as Log from '../build/output/log'\n\nLog.warn(\n  `\"next\" should not be imported directly, imported in ${module.parent?.filename}\\nSee more info here: https://nextjs.org/docs/messages/import-next`\n)\n"], "names": ["module", "Log", "warn", "parent", "filename"], "mappings": ";;;;6DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAGoCA;AADzDC,KAAIC,IAAI,CACN,CAAC,oDAAoD,GAAEF,iBAAAA,OAAOG,MAAM,qBAAbH,eAAeI,QAAQ,CAAC,kEAAkE,CAAC"}