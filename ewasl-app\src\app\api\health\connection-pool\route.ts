/**
 * Connection Pool Health Check API
 * Provides monitoring and diagnostics for Supabase connection pooling
 */

import { NextRequest, NextResponse } from 'next/server';
import { checkConnectionPoolHealth, getConnectionPoolManager } from '@/lib/supabase/connection-pool';
import { createServiceRoleClient } from '@/lib/supabase/server';
import { logger } from '@/lib/monitoring/enhanced-logger';

export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now();
    
    // Get connection pool health
    const poolHealth = await checkConnectionPoolHealth();
    
    // Test database connectivity
    const supabase = createServiceRoleClient();
    const { data: dbTest, error: dbError } = await supabase
      .from('posts')
      .select('count')
      .limit(1);
    
    const responseTime = Date.now() - startTime;
    
    // Test query performance
    const performanceTest = await testQueryPerformance();
    
    const healthData = {
      status: poolHealth.healthy && !dbError ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      responseTime,
      connectionPool: {
        enabled: getConnectionPoolManager().isSupavisorEnabled(),
        metrics: poolHealth.metrics,
        config: {
          max: poolHealth.config.max,
          min: poolHealth.config.min,
          connectionTimeout: poolHealth.config.connectionTimeoutMillis,
          idleTimeout: poolHealth.config.idleTimeoutMillis,
        }
      },
      database: {
        connected: !dbError,
        error: dbError?.message || null,
        responseTime: performanceTest.responseTime,
        queryCount: performanceTest.queryCount
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        platform: process.env.VERCEL ? 'vercel' : 'other',
        region: process.env.VERCEL_REGION || 'unknown'
      }
    };
    
    // Log health check
    logger.info('Connection pool health check completed', {
      component: 'connection-pool-health',
      status: healthData.status,
      responseTime,
      poolEnabled: healthData.connectionPool.enabled
    });
    
    return NextResponse.json(healthData, {
      status: healthData.status === 'healthy' ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      }
    });
    
  } catch (error) {
    logger.error('Connection pool health check failed', {
      component: 'connection-pool-health',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Health check failed'
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      }
    });
  }
}

/**
 * Test query performance
 */
async function testQueryPerformance() {
  const startTime = Date.now();
  const supabase = createServiceRoleClient();
  
  try {
    // Run a series of test queries
    const queries = [
      supabase.from('posts').select('count').limit(1),
      supabase.from('social_accounts').select('count').limit(1),
      supabase.rpc('version'), // PostgreSQL version check
    ];
    
    await Promise.all(queries);
    
    return {
      responseTime: Date.now() - startTime,
      queryCount: queries.length,
      success: true
    };
  } catch (error) {
    return {
      responseTime: Date.now() - startTime,
      queryCount: 0,
      success: false,
      error: error instanceof Error ? error.message : 'Query test failed'
    };
  }
}

/**
 * POST endpoint for connection pool configuration updates
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, config } = body;
    
    if (action === 'update-config') {
      // Update connection pool configuration
      const poolManager = getConnectionPoolManager();
      
      logger.info('Connection pool configuration update requested', {
        component: 'connection-pool-health',
        action,
        config
      });
      
      return NextResponse.json({
        status: 'success',
        message: 'Configuration update logged',
        timestamp: new Date().toISOString()
      });
    }
    
    if (action === 'force-health-check') {
      // Force a comprehensive health check
      const healthData = await checkConnectionPoolHealth();
      
      return NextResponse.json({
        status: 'success',
        healthCheck: healthData,
        timestamp: new Date().toISOString()
      });
    }
    
    return NextResponse.json({
      status: 'error',
      message: 'Unknown action'
    }, { status: 400 });
    
  } catch (error) {
    logger.error('Connection pool configuration update failed', {
      component: 'connection-pool-health',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    return NextResponse.json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Configuration update failed'
    }, { status: 500 });
  }
}
