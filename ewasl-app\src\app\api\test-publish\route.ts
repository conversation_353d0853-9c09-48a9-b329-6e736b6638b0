import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// Test Facebook token validation
async function validateFacebookToken(accessToken: string) {
  try {
    console.log('🔍 [TEST-PUBLISH] Validating Facebook token...');
    const response = await fetch(`https://graph.facebook.com/v19.0/me?access_token=${accessToken}`)
    const result = await response.json()

    console.log(`📡 [TEST-PUBLISH] Token validation response:`, {
      status: response.status,
      success: response.ok,
      user_name: result.name,
      user_id: result.id
    });

    if (!response.ok) {
      console.error('❌ [TEST-PUBLISH] Token validation failed:', result.error);
      return {
        valid: false,
        error: result.error?.message || 'Token validation failed',
        error_code: result.error?.code
      }
    }

    console.log(`✅ [TEST-PUBLISH] Token is valid for user: ${result.name}`);
    return {
      valid: true,
      user: result
    }
  } catch (error) {
    console.error('❌ [TEST-PUBLISH] Token validation error:', error);
    return {
      valid: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Test Facebook publishing with minimal content
async function testFacebookPublish(accessToken: string, pageId: string) {
  try {
    console.log(`🧪 [TEST-PUBLISH] Testing Facebook publish to page: ${pageId}`);

    // Facebook Graph API requires form-encoded data, not JSON
    const formData = new URLSearchParams();
    formData.append('message', 'Test post from eWasl platform - تجربة نشر من منصة إي وصل');
    formData.append('access_token', accessToken);

    const response = await fetch(`https://graph.facebook.com/v19.0/${pageId}/feed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'eWasl-Test-Publisher/1.0'
      },
      body: formData
    })

    const result = await response.json()

    console.log(`📡 [TEST-PUBLISH] Facebook API Response:`, {
      status: response.status,
      statusText: response.statusText,
      success: response.ok,
      result
    });

    if (!response.ok) {
      console.error(`❌ [TEST-PUBLISH] Facebook publishing failed:`, result.error);
      return {
        success: false,
        error: result.error?.message || 'Publishing failed',
        error_code: result.error?.code,
        error_type: result.error?.type,
        details: result
      }
    }

    console.log(`✅ [TEST-PUBLISH] Facebook post published successfully: ${result.id}`);
    return {
      success: true,
      postId: result.id,
      postUrl: `https://facebook.com/${result.id}`,
      details: result
    }

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // Get user's social accounts
    const { data: socialAccounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'FACEBOOK')
      .eq('is_active', true)

    if (accountsError) {
      return NextResponse.json({
        error: 'Failed to fetch social accounts',
        details: accountsError.message
      }, { status: 500 })
    }

    if (!socialAccounts || socialAccounts.length === 0) {
      return NextResponse.json({
        error: 'No Facebook accounts found',
        message: 'Please connect a Facebook account first'
      }, { status: 404 })
    }

    const results = []

    // Test each Facebook account
    for (const account of socialAccounts) {
      console.log(`Testing Facebook account: ${account.account_name}`)
      
      // Validate token
      const tokenValidation = await validateFacebookToken(account.access_token)
      
      let publishResult = null
      if (tokenValidation.valid) {
        // Test publishing
        publishResult = await testFacebookPublish(account.access_token, account.account_id)
      }

      results.push({
        accountId: account.account_id,
        accountName: account.account_name,
        tokenValidation,
        publishResult
      })
    }

    return NextResponse.json({
      success: true,
      userId: user.id,
      accountsCount: socialAccounts.length,
      results
    })

  } catch (error) {
    console.error('Test publish error:', error)
    return NextResponse.json({
      error: 'Test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}
