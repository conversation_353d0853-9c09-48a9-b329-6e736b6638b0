/**
 * Real-time Connection Validation Service
 * Validates social media account connections and refreshes tokens as needed
 */

import { createFacebookOAuthService } from './facebook'
import { SocialPlatform } from '@/types/social-enhanced'
import { createClient } from '@/lib/supabase/client'

// Define ConnectionStatus locally to avoid import issues
type ConnectionStatus = 'connected' | 'expired' | 'error' | 'reconnecting' | 'disconnected';

export interface ValidationResult {
  isValid: boolean
  status: ConnectionStatus
  error?: string
  needsRefresh?: boolean
  newToken?: string
}

export interface AccountToValidate {
  id: string
  platform: SocialPlatform
  accessToken: string
  refreshToken?: string
  expiresAt?: Date
  lastValidatedAt?: Date
}

export class ConnectionValidator {
  private facebookService?: ReturnType<typeof createFacebookOAuthService>

  constructor() {
    // Don't initialize services during construction to avoid build-time errors
    // Services will be lazy-loaded when needed
  }

  /**
   * Get Facebook OAuth service, creating it if needed
   */
  private getFacebookService(): ReturnType<typeof createFacebookOAuthService> | null {
    if (!this.facebookService) {
      try {
        this.facebookService = createFacebookOAuthService()
      } catch (error) {
        console.warn('Facebook OAuth service not available:', error)
        return null
      }
    }
    return this.facebookService
  }

  /**
   * Validate a single social media account connection
   */
  async validateAccount(account: AccountToValidate): Promise<ValidationResult> {
    try {
      switch (account.platform) {
        case 'FACEBOOK':
        case 'INSTAGRAM':
          return await this.validateFacebookAccount(account)

        case 'TWITTER':
          return await this.validateTwitterAccount(account)

        case 'LINKEDIN':
          return await this.validateLinkedInAccount(account)

        case 'TIKTOK':
          return await this.validateTikTokAccount(account)

        default:
          return {
            isValid: false,
            status: ConnectionStatus.ERROR,
            error: `Unsupported platform: ${account.platform}`
          }
      }
    } catch (error) {
      console.error(`Error validating ${account.platform} account:`, error)
      return {
        isValid: false,
        status: ConnectionStatus.ERROR,
        error: error instanceof Error ? error.message : 'Unknown validation error'
      }
    }
  }

  /**
   * Validate Facebook/Instagram account using Facebook Graph API
   */
  private async validateFacebookAccount(account: AccountToValidate): Promise<ValidationResult> {
    const facebookService = this.getFacebookService()
    if (!facebookService) {
      return {
        isValid: false,
        status: ConnectionStatus.ERROR,
        error: 'Facebook service not configured'
      }
    }

    try {
      // Check if token is expired
      if (account.expiresAt && account.expiresAt < new Date()) {
        return {
          isValid: false,
          status: ConnectionStatus.EXPIRED,
          error: 'Token has expired',
          needsRefresh: true
        }
      }

      // Validate token by making a test API call
      const userInfo = await facebookService.validateToken(account.accessToken)
      
      if (userInfo && userInfo.id) {
        return {
          isValid: true,
          status: ConnectionStatus.CONNECTED
        }
      } else {
        return {
          isValid: false,
          status: ConnectionStatus.ERROR,
          error: 'Invalid token response'
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      
      // Check if error indicates expired token
      if (errorMessage.includes('expired') || errorMessage.includes('invalid')) {
        return {
          isValid: false,
          status: ConnectionStatus.EXPIRED,
          error: errorMessage,
          needsRefresh: true
        }
      }

      return {
        isValid: false,
        status: ConnectionStatus.ERROR,
        error: errorMessage
      }
    }
  }

  /**
   * Validate Twitter account (placeholder - implement with Twitter API)
   */
  private async validateTwitterAccount(account: AccountToValidate): Promise<ValidationResult> {
    // TODO: Implement Twitter API validation
    // For now, return mock validation based on token expiry
    if (account.expiresAt && account.expiresAt < new Date()) {
      return {
        isValid: false,
        status: ConnectionStatus.EXPIRED,
        needsRefresh: true
      }
    }

    return {
      isValid: true,
      status: ConnectionStatus.CONNECTED
    }
  }

  /**
   * Validate LinkedIn account (placeholder - implement with LinkedIn API)
   */
  private async validateLinkedInAccount(account: AccountToValidate): Promise<ValidationResult> {
    // TODO: Implement LinkedIn API validation
    if (account.expiresAt && account.expiresAt < new Date()) {
      return {
        isValid: false,
        status: ConnectionStatus.EXPIRED,
        needsRefresh: true
      }
    }

    return {
      isValid: true,
      status: ConnectionStatus.CONNECTED
    }
  }

  /**
   * Validate TikTok account (placeholder - implement with TikTok API)
   */
  private async validateTikTokAccount(account: AccountToValidate): Promise<ValidationResult> {
    // TODO: Implement TikTok API validation
    if (account.expiresAt && account.expiresAt < new Date()) {
      return {
        isValid: false,
        status: ConnectionStatus.EXPIRED,
        needsRefresh: true
      }
    }

    return {
      isValid: true,
      status: ConnectionStatus.CONNECTED
    }
  }

  /**
   * Validate multiple accounts in parallel
   */
  async validateAccounts(accounts: AccountToValidate[]): Promise<Map<string, ValidationResult>> {
    const results = new Map<string, ValidationResult>()
    
    // Validate accounts in parallel with a reasonable concurrency limit
    const BATCH_SIZE = 5
    for (let i = 0; i < accounts.length; i += BATCH_SIZE) {
      const batch = accounts.slice(i, i + BATCH_SIZE)
      const batchPromises = batch.map(async (account) => {
        const result = await this.validateAccount(account)
        return { accountId: account.id, result }
      })

      const batchResults = await Promise.allSettled(batchPromises)
      
      batchResults.forEach((promiseResult) => {
        if (promiseResult.status === 'fulfilled') {
          results.set(promiseResult.value.accountId, promiseResult.value.result)
        } else {
          // Handle failed validation
          const accountId = batch[batchResults.indexOf(promiseResult)]?.id || 'unknown'
          results.set(accountId, {
            isValid: false,
            status: ConnectionStatus.ERROR,
            error: 'Validation failed'
          })
        }
      })
    }

    return results
  }

  /**
   * Update account status in database based on validation result
   */
  async updateAccountStatus(accountId: string, validationResult: ValidationResult): Promise<void> {
    try {
      const supabase = createClient()
      
      const updateData: any = {
        connection_status: validationResult.status,
        last_validated_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      // Update token if refreshed
      if (validationResult.newToken) {
        updateData.access_token = validationResult.newToken
      }

      // Store error in metadata if present
      if (validationResult.error) {
        updateData.metadata = {
          lastError: validationResult.error,
          lastErrorAt: new Date().toISOString()
        }
      }

      const { error } = await supabase
        .from('social_accounts')
        .update(updateData)
        .eq('id', accountId)

      if (error) {
        console.error('Error updating account status:', error)
      }
    } catch (error) {
      console.error('Error updating account status in database:', error)
    }
  }

  /**
   * Validate all accounts for a user and update their status
   */
  async validateUserAccounts(userId: string): Promise<void> {
    try {
      const supabase = createClient()
      
      // Fetch user's accounts
      const { data: accounts, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId)

      if (error || !accounts) {
        console.error('Error fetching user accounts:', error)
        return
      }

      // Convert to validation format
      const accountsToValidate: AccountToValidate[] = accounts.map(account => ({
        id: account.id,
        platform: account.platform,
        accessToken: account.access_token,
        refreshToken: account.refresh_token,
        expiresAt: account.expires_at ? new Date(account.expires_at) : undefined,
        lastValidatedAt: account.last_validated_at ? new Date(account.last_validated_at) : undefined
      }))

      // Validate accounts
      const validationResults = await this.validateAccounts(accountsToValidate)

      // Update account statuses
      const updatePromises = Array.from(validationResults.entries()).map(
        ([accountId, result]) => this.updateAccountStatus(accountId, result)
      )

      await Promise.allSettled(updatePromises)

      console.log(`Validated ${accounts.length} accounts for user ${userId}`)
    } catch (error) {
      console.error('Error validating user accounts:', error)
    }
  }
}

// Export singleton instance
export const connectionValidator = new ConnectionValidator()
