import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Simple health check for rich text editor component
    return NextResponse.json({
      success: true,
      component: 'rich-text-editor',
      status: 'healthy',
      features: [
        'Arabic RTL support',
        'Character counting',
        'Hashtag detection',
        'Mention support',
        'Platform-specific limits',
        'Auto-save functionality'
      ],
      message: 'Rich text editor component is working correctly'
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Rich text editor test failed' },
      { status: 500 }
    )
  }
}
