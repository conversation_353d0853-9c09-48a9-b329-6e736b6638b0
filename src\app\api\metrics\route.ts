import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createServiceClient } from '@/lib/supabase/service';

/**
 * Metrics Collection Endpoint
 * Provides system performance and usage metrics
 * 
 * GET /api/metrics
 * Query params: ?period=24h|7d|30d&detailed=true
 */
export async function GET(request: NextRequest) {
  try {
    console.log('📊 Metrics collection requested');

    // Get authenticated user (optional for system metrics)
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '24h';
    const detailed = searchParams.get('detailed') === 'true';
    const userId = searchParams.get('userId') || user?.id;

    console.log('📊 Metrics request:', { period, detailed, userId: !!userId });

    // Create service client for admin queries
    const supabaseService = createServiceClient();

    // Calculate time range
    const now = new Date();
    const timeRange = getTimeRange(period);
    const startTime = new Date(now.getTime() - timeRange);

    console.log('📊 Time range:', { startTime: startTime.toISOString(), endTime: now.toISOString() });

    const metrics = {
      timestamp: now.toISOString(),
      period,
      timeRange: {
        start: startTime.toISOString(),
        end: now.toISOString()
      },
      system: await getSystemMetrics(supabaseService, startTime, now),
      posts: await getPostMetrics(supabaseService, startTime, now, userId),
      publishing: await getPublishingMetrics(supabaseService, startTime, now, userId),
      users: await getUserMetrics(supabaseService, startTime, now),
      performance: await getPerformanceMetrics(supabaseService, startTime, now)
    };

    if (detailed) {
      metrics.detailed = {
        platforms: await getPlatformMetrics(supabaseService, startTime, now, userId),
        errors: await getErrorMetrics(supabaseService, startTime, now),
        queue: await getQueueMetrics(supabaseService, startTime, now)
      };
    }

    console.log('📊 Metrics collected successfully');

    return NextResponse.json(metrics, {
      headers: {
        'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('❌ Error collecting metrics:', error);
    return NextResponse.json(
      { 
        error: 'فشل في جمع المقاييس',
        message: 'Failed to collect metrics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Get time range in milliseconds based on period
 */
function getTimeRange(period: string): number {
  switch (period) {
    case '1h': return 60 * 60 * 1000;
    case '24h': return 24 * 60 * 60 * 1000;
    case '7d': return 7 * 24 * 60 * 60 * 1000;
    case '30d': return 30 * 24 * 60 * 60 * 1000;
    default: return 24 * 60 * 60 * 1000;
  }
}

/**
 * Get system-wide metrics
 */
async function getSystemMetrics(supabase: any, startTime: Date, endTime: Date) {
  try {
    // Get total users
    const { count: totalUsers } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    // Get active users (users who logged in during the period)
    const { count: activeUsers } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .gte('last_sign_in_at', startTime.toISOString())
      .lte('last_sign_in_at', endTime.toISOString());

    // Get total social accounts
    const { count: totalAccounts } = await supabase
      .from('social_accounts')
      .select('*', { count: 'exact', head: true });

    // Get connected accounts
    const { count: connectedAccounts } = await supabase
      .from('social_accounts')
      .select('*', { count: 'exact', head: true })
      .eq('connection_status', 'CONNECTED');

    return {
      totalUsers: totalUsers || 0,
      activeUsers: activeUsers || 0,
      totalAccounts: totalAccounts || 0,
      connectedAccounts: connectedAccounts || 0,
      accountConnectionRate: totalAccounts > 0 ? ((connectedAccounts || 0) / totalAccounts * 100).toFixed(1) + '%' : '0%'
    };
  } catch (error) {
    console.error('Error getting system metrics:', error);
    return {
      totalUsers: 0,
      activeUsers: 0,
      totalAccounts: 0,
      connectedAccounts: 0,
      accountConnectionRate: '0%',
      error: 'Failed to collect system metrics'
    };
  }
}

/**
 * Get post metrics
 */
async function getPostMetrics(supabase: any, startTime: Date, endTime: Date, userId?: string) {
  try {
    let query = supabase
      .from('posts')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { count: totalPosts } = await query;

    // Get published posts
    let publishedQuery = supabase
      .from('posts')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'PUBLISHED')
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    if (userId) {
      publishedQuery = publishedQuery.eq('user_id', userId);
    }

    const { count: publishedPosts } = await publishedQuery;

    // Get scheduled posts
    let scheduledQuery = supabase
      .from('posts')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'SCHEDULED')
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    if (userId) {
      scheduledQuery = scheduledQuery.eq('user_id', userId);
    }

    const { count: scheduledPosts } = await scheduledQuery;

    return {
      totalPosts: totalPosts || 0,
      publishedPosts: publishedPosts || 0,
      scheduledPosts: scheduledPosts || 0,
      draftPosts: (totalPosts || 0) - (publishedPosts || 0) - (scheduledPosts || 0),
      publishRate: totalPosts > 0 ? ((publishedPosts || 0) / totalPosts * 100).toFixed(1) + '%' : '0%'
    };
  } catch (error) {
    console.error('Error getting post metrics:', error);
    return {
      totalPosts: 0,
      publishedPosts: 0,
      scheduledPosts: 0,
      draftPosts: 0,
      publishRate: '0%',
      error: 'Failed to collect post metrics'
    };
  }
}

/**
 * Get publishing metrics
 */
async function getPublishingMetrics(supabase: any, startTime: Date, endTime: Date, userId?: string) {
  try {
    // Get successful publishes
    let successQuery = supabase
      .from('post_social_accounts')
      .select('*', { count: 'exact', head: true })
      .eq('publish_status', 'SUCCESS')
      .gte('published_at', startTime.toISOString())
      .lte('published_at', endTime.toISOString());

    if (userId) {
      successQuery = successQuery.eq('user_id', userId);
    }

    const { count: successfulPublishes } = await successQuery;

    // Get failed publishes
    let failedQuery = supabase
      .from('post_social_accounts')
      .select('*', { count: 'exact', head: true })
      .eq('publish_status', 'FAILED')
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    if (userId) {
      failedQuery = failedQuery.eq('user_id', userId);
    }

    const { count: failedPublishes } = await failedQuery;

    const totalAttempts = (successfulPublishes || 0) + (failedPublishes || 0);

    return {
      successfulPublishes: successfulPublishes || 0,
      failedPublishes: failedPublishes || 0,
      totalAttempts,
      successRate: totalAttempts > 0 ? ((successfulPublishes || 0) / totalAttempts * 100).toFixed(1) + '%' : '0%'
    };
  } catch (error) {
    console.error('Error getting publishing metrics:', error);
    return {
      successfulPublishes: 0,
      failedPublishes: 0,
      totalAttempts: 0,
      successRate: '0%',
      error: 'Failed to collect publishing metrics'
    };
  }
}

/**
 * Get user metrics
 */
async function getUserMetrics(supabase: any, startTime: Date, endTime: Date) {
  try {
    // Get new users
    const { count: newUsers } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    return {
      newUsers: newUsers || 0,
      note: 'User metrics collection basic implementation'
    };
  } catch (error) {
    console.error('Error getting user metrics:', error);
    return {
      newUsers: 0,
      error: 'Failed to collect user metrics'
    };
  }
}

/**
 * Get performance metrics
 */
async function getPerformanceMetrics(supabase: any, startTime: Date, endTime: Date) {
  try {
    // This is a placeholder for performance metrics
    // In a real implementation, you might collect response times, error rates, etc.
    return {
      averageResponseTime: '< 2s',
      errorRate: '< 0.1%',
      uptime: '99.9%',
      note: 'Performance metrics collection not fully implemented'
    };
  } catch (error) {
    console.error('Error getting performance metrics:', error);
    return {
      averageResponseTime: 'unknown',
      errorRate: 'unknown',
      uptime: 'unknown',
      error: 'Failed to collect performance metrics'
    };
  }
}

/**
 * Get platform-specific metrics
 */
async function getPlatformMetrics(supabase: any, startTime: Date, endTime: Date, userId?: string) {
  try {
    const platforms = ['FACEBOOK', 'INSTAGRAM', 'TWITTER', 'LINKEDIN'];
    const platformMetrics = {};

    for (const platform of platforms) {
      let query = supabase
        .from('post_social_accounts')
        .select('publish_status', { count: 'exact' })
        .eq('platform', platform)
        .gte('created_at', startTime.toISOString())
        .lte('created_at', endTime.toISOString());

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data: posts, count: totalPosts } = await query;

      const successfulPosts = posts?.filter(p => p.publish_status === 'SUCCESS').length || 0;
      const failedPosts = posts?.filter(p => p.publish_status === 'FAILED').length || 0;

      platformMetrics[platform.toLowerCase()] = {
        totalPosts: totalPosts || 0,
        successfulPosts,
        failedPosts,
        successRate: totalPosts > 0 ? (successfulPosts / totalPosts * 100).toFixed(1) + '%' : '0%'
      };
    }

    return platformMetrics;
  } catch (error) {
    console.error('Error getting platform metrics:', error);
    return {
      facebook: { totalPosts: 0, successfulPosts: 0, failedPosts: 0, successRate: '0%' },
      instagram: { totalPosts: 0, successfulPosts: 0, failedPosts: 0, successRate: '0%' },
      twitter: { totalPosts: 0, successfulPosts: 0, failedPosts: 0, successRate: '0%' },
      linkedin: { totalPosts: 0, successfulPosts: 0, failedPosts: 0, successRate: '0%' },
      error: 'Failed to collect platform metrics'
    };
  }
}

/**
 * Get error metrics
 */
async function getErrorMetrics(supabase: any, startTime: Date, endTime: Date) {
  try {
    // Get publishing errors
    const { data: publishingErrors, count: publishingErrorCount } = await supabase
      .from('post_social_accounts')
      .select('error_message, platform', { count: 'exact' })
      .eq('publish_status', 'FAILED')
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString());

    // Group errors by type
    const errorTypes = {};
    publishingErrors?.forEach(error => {
      const errorType = error.error_message || 'Unknown error';
      errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
    });

    return {
      totalErrors: publishingErrorCount || 0,
      publishingErrors: publishingErrorCount || 0,
      errorTypes,
      note: 'Error metrics collection basic implementation'
    };
  } catch (error) {
    console.error('Error getting error metrics:', error);
    return {
      totalErrors: 0,
      publishingErrors: 0,
      errorTypes: {},
      error: 'Failed to collect error metrics'
    };
  }
}

/**
 * Get queue metrics
 */
async function getQueueMetrics(supabase: any, startTime: Date, endTime: Date) {
  try {
    // Get scheduled posts queue
    const { count: queuedPosts } = await supabase
      .from('scheduled_posts_queue')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'PENDING');

    // Get processed posts
    const { count: processedPosts } = await supabase
      .from('scheduled_posts_queue')
      .select('*', { count: 'exact', head: true })
      .in('status', ['COMPLETED', 'FAILED'])
      .gte('processed_at', startTime.toISOString())
      .lte('processed_at', endTime.toISOString());

    return {
      queuedPosts: queuedPosts || 0,
      processedPosts: processedPosts || 0,
      note: 'Queue metrics collection basic implementation'
    };
  } catch (error) {
    console.error('Error getting queue metrics:', error);
    return {
      queuedPosts: 0,
      processedPosts: 0,
      error: 'Failed to collect queue metrics'
    };
  }
}
