import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { FacebookOAuthService } from '@/lib/oauth/facebook';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [TEST] Starting Facebook metrics test...');

    // Get social accounts from database
    const { data: accounts, error } = await supabase
      .from('social_accounts')
      .select('*')
      .in('platform', ['FACEBOOK', 'INSTAGRAM']);

    if (error) {
      console.error('❌ [TEST] Database error:', error);
      return NextResponse.json({ error: 'Database error', details: error }, { status: 500 });
    }

    if (!accounts || accounts.length === 0) {
      console.log('⚠️ [TEST] No Facebook/Instagram accounts found');
      return NextResponse.json({ message: 'No accounts found' });
    }

    console.log(`🔍 [TEST] Found ${accounts.length} accounts:`, accounts.map(acc => ({
      id: acc.id,
      platform: acc.platform,
      name: acc.account_name,
      hasToken: !!acc.access_token
    })));

    const facebookService = new FacebookOAuthService();
    const results = [];

    for (const account of accounts) {
      console.log(`\n🔍 [TEST] Testing account: ${account.platform} - ${account.account_name}`);
      console.log(`🔍 [TEST] Account ID: ${account.account_id}`);
      console.log(`🔍 [TEST] Has token: ${!!account.access_token}`);
      console.log(`🔍 [TEST] Token expires: ${account.expires_at}`);

      if (!account.access_token) {
        console.warn(`⚠️ [TEST] No access token for ${account.platform}`);
        results.push({
          account: account.account_name,
          platform: account.platform,
          error: 'No access token'
        });
        continue;
      }

      try {
        const metrics = await facebookService.getAccountMetrics(
          account.account_id,
          account.access_token,
          account.platform
        );

        console.log(`✅ [TEST] Metrics for ${account.platform}:`, metrics);

        results.push({
          account: account.account_name,
          platform: account.platform,
          accountId: account.account_id,
          metrics: metrics,
          success: true
        });
      } catch (apiError) {
        console.error(`❌ [TEST] API error for ${account.platform}:`, apiError);
        results.push({
          account: account.account_name,
          platform: account.platform,
          error: apiError.message,
          success: false
        });
      }
    }

    return NextResponse.json({
      message: 'Facebook metrics test completed',
      results: results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [TEST] General error:', error);
    return NextResponse.json({ 
      error: 'Test failed', 
      details: error.message,
      stack: error.stack 
    }, { status: 500 });
  }
}
