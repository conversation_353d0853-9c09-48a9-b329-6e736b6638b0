/**
 * Arabic RTL Optimization and Localization System
 * Provides comprehensive Arabic language support with RTL layout optimization
 * Part of Phase 2A: Core UX Enhancements
 */

import { ar, enUS } from 'date-fns/locale';
import { format, formatDistanceToNow, parseISO } from 'date-fns';

export type Language = 'ar' | 'en';
export type Direction = 'rtl' | 'ltr';

/**
 * Arabic RTL Configuration
 */
export const RTL_CONFIG = {
  // Text direction
  direction: {
    ar: 'rtl' as const,
    en: 'ltr' as const
  },
  
  // Font families optimized for Arabic
  fonts: {
    ar: {
      primary: '"Noto Sans Arabic", "Cairo", "Amiri", "Tajawal", sans-serif',
      heading: '"Cairo", "Amiri", "Noto Sans Arabic", sans-serif',
      mono: '"Noto Sans Mono", "Courier New", monospace'
    },
    en: {
      primary: '"Inter", "Roboto", "Helvetica Neue", sans-serif',
      heading: '"Inter", "Roboto", sans-serif',
      mono: '"JetBrains Mono", "Fira Code", monospace'
    }
  },
  
  // Layout adjustments for RTL
  layout: {
    // Flex direction adjustments
    flexRow: {
      ar: 'flex-row-reverse',
      en: 'flex-row'
    },
    
    // Text alignment
    textAlign: {
      ar: 'text-right',
      en: 'text-left'
    },
    
    // Margin and padding adjustments
    marginLeft: {
      ar: 'mr-',
      en: 'ml-'
    },
    marginRight: {
      ar: 'ml-',
      en: 'mr-'
    },
    paddingLeft: {
      ar: 'pr-',
      en: 'pl-'
    },
    paddingRight: {
      ar: 'pl-',
      en: 'pr-'
    },
    
    // Border radius adjustments
    roundedLeft: {
      ar: 'rounded-r-',
      en: 'rounded-l-'
    },
    roundedRight: {
      ar: 'rounded-l-',
      en: 'rounded-r-'
    }
  },
  
  // Number formatting
  numbers: {
    ar: {
      locale: 'ar-SA',
      numerals: 'arab', // Use Arabic-Indic numerals
      currency: 'SAR'
    },
    en: {
      locale: 'en-US',
      numerals: 'latn', // Use Latin numerals
      currency: 'USD'
    }
  }
};

/**
 * Comprehensive Arabic translations
 */
export const ARABIC_TRANSLATIONS = {
  // Navigation
  navigation: {
    dashboard: 'لوحة التحكم',
    posts: 'المنشورات',
    analytics: 'التحليلات',
    schedule: 'الجدولة',
    social: 'الحسابات الاجتماعية',
    settings: 'الإعدادات',
    profile: 'الملف الشخصي',
    help: 'المساعدة',
    logout: 'تسجيل الخروج'
  },
  
  // Common actions
  actions: {
    create: 'إنشاء',
    edit: 'تعديل',
    delete: 'حذف',
    save: 'حفظ',
    cancel: 'إلغاء',
    submit: 'إرسال',
    search: 'بحث',
    filter: 'تصفية',
    sort: 'ترتيب',
    refresh: 'تحديث',
    export: 'تصدير',
    import: 'استيراد',
    copy: 'نسخ',
    share: 'مشاركة',
    download: 'تحميل',
    upload: 'رفع',
    view: 'عرض',
    preview: 'معاينة',
    publish: 'نشر',
    schedule: 'جدولة',
    draft: 'مسودة'
  },
  
  // Post management
  posts: {
    title: 'المنشورات',
    newPost: 'منشور جديد',
    editPost: 'تعديل المنشور',
    deletePost: 'حذف المنشور',
    duplicatePost: 'نسخ المنشور',
    content: 'المحتوى',
    media: 'الوسائط',
    platforms: 'المنصات',
    status: {
      draft: 'مسودة',
      scheduled: 'مجدول',
      published: 'منشور',
      failed: 'فشل'
    },
    scheduling: {
      publishNow: 'نشر الآن',
      scheduleFor: 'جدولة لـ',
      selectDate: 'اختر التاريخ',
      selectTime: 'اختر الوقت',
      timezone: 'المنطقة الزمنية',
      recurring: 'متكرر'
    }
  },
  
  // Analytics
  analytics: {
    title: 'التحليلات',
    overview: 'نظرة عامة',
    performance: 'الأداء',
    engagement: 'التفاعل',
    reach: 'الوصول',
    impressions: 'المشاهدات',
    clicks: 'النقرات',
    likes: 'الإعجابات',
    comments: 'التعليقات',
    shares: 'المشاركات',
    followers: 'المتابعون',
    growth: 'النمو',
    trends: 'الاتجاهات',
    reports: 'التقارير',
    metrics: {
      totalPosts: 'إجمالي المنشورات',
      totalEngagement: 'إجمالي التفاعل',
      totalReach: 'إجمالي الوصول',
      totalImpressions: 'إجمالي المشاهدات',
      engagementRate: 'معدل التفاعل',
      clickThroughRate: 'معدل النقر',
      conversionRate: 'معدل التحويل'
    }
  },
  
  // Social platforms
  platforms: {
    instagram: 'إنستغرام',
    facebook: 'فيسبوك',
    twitter: 'تويتر',
    linkedin: 'لينكد إن',
    tiktok: 'تيك توك',
    youtube: 'يوتيوب',
    snapchat: 'سناب شات',
    pinterest: 'بينتريست'
  },
  
  // Time and dates
  time: {
    now: 'الآن',
    today: 'اليوم',
    yesterday: 'أمس',
    tomorrow: 'غداً',
    thisWeek: 'هذا الأسبوع',
    lastWeek: 'الأسبوع الماضي',
    thisMonth: 'هذا الشهر',
    lastMonth: 'الشهر الماضي',
    thisYear: 'هذا العام',
    lastYear: 'العام الماضي',
    minutes: 'دقائق',
    hours: 'ساعات',
    days: 'أيام',
    weeks: 'أسابيع',
    months: 'أشهر',
    years: 'سنوات',
    ago: 'منذ',
    in: 'خلال'
  },
  
  // Form validation
  validation: {
    required: 'هذا الحقل مطلوب',
    email: 'يرجى إدخال بريد إلكتروني صحيح',
    password: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
    confirmPassword: 'كلمات المرور غير متطابقة',
    minLength: 'يجب أن يكون النص {min} أحرف على الأقل',
    maxLength: 'يجب أن يكون النص {max} أحرف كحد أقصى',
    invalidUrl: 'يرجى إدخال رابط صحيح',
    invalidDate: 'يرجى إدخال تاريخ صحيح',
    invalidNumber: 'يرجى إدخال رقم صحيح'
  },
  
  // Messages
  messages: {
    success: {
      saved: 'تم الحفظ بنجاح',
      published: 'تم النشر بنجاح',
      deleted: 'تم الحذف بنجاح',
      updated: 'تم التحديث بنجاح',
      uploaded: 'تم الرفع بنجاح',
      connected: 'تم الاتصال بنجاح',
      disconnected: 'تم قطع الاتصال بنجاح'
    },
    error: {
      general: 'حدث خطأ غير متوقع',
      network: 'خطأ في الاتصال بالشبكة',
      unauthorized: 'غير مصرح لك بهذا الإجراء',
      notFound: 'العنصر المطلوب غير موجود',
      validation: 'يرجى التحقق من البيانات المدخلة',
      upload: 'فشل في رفع الملف',
      connection: 'فشل في الاتصال'
    },
    loading: {
      general: 'جاري التحميل...',
      saving: 'جاري الحفظ...',
      publishing: 'جاري النشر...',
      uploading: 'جاري الرفع...',
      connecting: 'جاري الاتصال...',
      processing: 'جاري المعالجة...'
    }
  },
  
  // Settings
  settings: {
    title: 'الإعدادات',
    general: 'عام',
    account: 'الحساب',
    privacy: 'الخصوصية',
    notifications: 'الإشعارات',
    language: 'اللغة',
    theme: 'المظهر',
    timezone: 'المنطقة الزمنية',
    preferences: 'التفضيلات'
  }
};

/**
 * RTL-aware CSS class generator
 */
export class RTLClassGenerator {
  private language: Language;
  
  constructor(language: Language = 'ar') {
    this.language = language;
  }
  
  /**
   * Generate RTL-aware flex classes
   */
  flex(direction: 'row' | 'col' = 'row'): string {
    if (direction === 'col') return 'flex-col';
    return this.language === 'ar' ? 'flex-row-reverse' : 'flex-row';
  }
  
  /**
   * Generate RTL-aware text alignment
   */
  textAlign(align: 'left' | 'right' | 'center' = 'left'): string {
    if (align === 'center') return 'text-center';
    if (this.language === 'ar') {
      return align === 'left' ? 'text-right' : 'text-left';
    }
    return `text-${align}`;
  }
  
  /**
   * Generate RTL-aware margin classes
   */
  margin(side: 'left' | 'right', size: string): string {
    if (this.language === 'ar') {
      const oppositeSide = side === 'left' ? 'right' : 'left';
      return `m${oppositeSide.charAt(0)}-${size}`;
    }
    return `m${side.charAt(0)}-${size}`;
  }
  
  /**
   * Generate RTL-aware padding classes
   */
  padding(side: 'left' | 'right', size: string): string {
    if (this.language === 'ar') {
      const oppositeSide = side === 'left' ? 'right' : 'left';
      return `p${oppositeSide.charAt(0)}-${size}`;
    }
    return `p${side.charAt(0)}-${size}`;
  }
  
  /**
   * Generate RTL-aware border radius classes
   */
  rounded(side: 'left' | 'right', size: string = ''): string {
    if (this.language === 'ar') {
      const oppositeSide = side === 'left' ? 'r' : 'l';
      return `rounded-${oppositeSide}${size ? '-' + size : ''}`;
    }
    return `rounded-${side.charAt(0)}${size ? '-' + size : ''}`;
  }
}

/**
 * Arabic number formatter
 */
export class ArabicNumberFormatter {
  private language: Language;
  
  constructor(language: Language = 'ar') {
    this.language = language;
  }
  
  /**
   * Format number with Arabic or English numerals
   */
  formatNumber(num: number, options?: Intl.NumberFormatOptions): string {
    const config = RTL_CONFIG.numbers[this.language];
    return new Intl.NumberFormat(config.locale, {
      ...options,
      numberingSystem: config.numerals
    }).format(num);
  }
  
  /**
   * Format currency
   */
  formatCurrency(amount: number, currency?: string): string {
    const config = RTL_CONFIG.numbers[this.language];
    return new Intl.NumberFormat(config.locale, {
      style: 'currency',
      currency: currency || config.currency,
      numberingSystem: config.numerals
    }).format(amount);
  }
  
  /**
   * Format percentage
   */
  formatPercentage(value: number, decimals: number = 1): string {
    const config = RTL_CONFIG.numbers[this.language];
    return new Intl.NumberFormat(config.locale, {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
      numberingSystem: config.numerals
    }).format(value / 100);
  }
  
  /**
   * Format large numbers (K, M, B)
   */
  formatCompactNumber(num: number): string {
    const config = RTL_CONFIG.numbers[this.language];
    
    if (this.language === 'ar') {
      // Arabic compact notation
      if (num >= 1000000000) return `${(num / 1000000000).toFixed(1)} مليار`;
      if (num >= 1000000) return `${(num / 1000000).toFixed(1)} مليون`;
      if (num >= 1000) return `${(num / 1000).toFixed(1)} ألف`;
      return this.formatNumber(num);
    }
    
    // English compact notation
    return new Intl.NumberFormat(config.locale, {
      notation: 'compact',
      compactDisplay: 'short',
      numberingSystem: config.numerals
    }).format(num);
  }
}

/**
 * Arabic date formatter
 */
export class ArabicDateFormatter {
  private language: Language;
  
  constructor(language: Language = 'ar') {
    this.language = language;
  }
  
  /**
   * Format date with Arabic or English locale
   */
  formatDate(date: Date | string, formatStr: string = 'PPP'): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    const locale = this.language === 'ar' ? ar : enUS;
    return format(dateObj, formatStr, { locale });
  }
  
  /**
   * Format relative time (e.g., "منذ ساعتين", "2 hours ago")
   */
  formatRelativeTime(date: Date | string): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    const locale = this.language === 'ar' ? ar : enUS;
    return formatDistanceToNow(dateObj, { 
      addSuffix: true, 
      locale 
    });
  }
  
  /**
   * Format time only
   */
  formatTime(date: Date | string): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    const locale = this.language === 'ar' ? ar : enUS;
    return format(dateObj, 'p', { locale });
  }
  
  /**
   * Format date and time
   */
  formatDateTime(date: Date | string): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    const locale = this.language === 'ar' ? ar : enUS;
    return format(dateObj, 'PPp', { locale });
  }
}

/**
 * Main RTL utility class
 */
export class ArabicRTLOptimizer {
  public language: Language;
  public direction: Direction;
  public classGenerator: RTLClassGenerator;
  public numberFormatter: ArabicNumberFormatter;
  public dateFormatter: ArabicDateFormatter;
  
  constructor(language: Language = 'ar') {
    this.language = language;
    this.direction = RTL_CONFIG.direction[language];
    this.classGenerator = new RTLClassGenerator(language);
    this.numberFormatter = new ArabicNumberFormatter(language);
    this.dateFormatter = new ArabicDateFormatter(language);
  }
  
  /**
   * Get translation for a key
   */
  t(key: string, params?: Record<string, any>): string {
    const keys = key.split('.');
    let value: any = ARABIC_TRANSLATIONS;
    
    for (const k of keys) {
      value = value?.[k];
      if (value === undefined) break;
    }
    
    if (typeof value !== 'string') {
      return key; // Return key if translation not found
    }
    
    // Replace parameters
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match: string, paramKey: string) => {
        return params[paramKey] || match;
      });
    }
    
    return value;
  }
  
  /**
   * Get RTL-aware CSS classes
   */
  cn(...classes: (string | undefined | null | false)[]): string {
    return classes.filter(Boolean).join(' ');
  }
  
  /**
   * Apply RTL direction to element
   */
  applyDirection(element: HTMLElement): void {
    element.dir = this.direction;
    element.lang = this.language;
  }
  
  /**
   * Get font family for current language
   */
  getFontFamily(type: 'primary' | 'heading' | 'mono' = 'primary'): string {
    return RTL_CONFIG.fonts[this.language][type];
  }
  
  /**
   * Switch language
   */
  switchLanguage(newLanguage: Language): ArabicRTLOptimizer {
    return new ArabicRTLOptimizer(newLanguage);
  }
}

/**
 * Create RTL optimizer instance
 */
export function createRTLOptimizer(language: Language = 'ar'): ArabicRTLOptimizer {
  return new ArabicRTLOptimizer(language);
}

/**
 * React hook for RTL optimization
 */
export function useRTL(language: Language = 'ar') {
  const rtl = new ArabicRTLOptimizer(language);
  
  return {
    language: rtl.language,
    direction: rtl.direction,
    t: rtl.t.bind(rtl),
    cn: rtl.cn.bind(rtl),
    flex: rtl.classGenerator.flex.bind(rtl.classGenerator),
    textAlign: rtl.classGenerator.textAlign.bind(rtl.classGenerator),
    margin: rtl.classGenerator.margin.bind(rtl.classGenerator),
    padding: rtl.classGenerator.padding.bind(rtl.classGenerator),
    rounded: rtl.classGenerator.rounded.bind(rtl.classGenerator),
    formatNumber: rtl.numberFormatter.formatNumber.bind(rtl.numberFormatter),
    formatCurrency: rtl.numberFormatter.formatCurrency.bind(rtl.numberFormatter),
    formatPercentage: rtl.numberFormatter.formatPercentage.bind(rtl.numberFormatter),
    formatCompactNumber: rtl.numberFormatter.formatCompactNumber.bind(rtl.numberFormatter),
    formatDate: rtl.dateFormatter.formatDate.bind(rtl.dateFormatter),
    formatRelativeTime: rtl.dateFormatter.formatRelativeTime.bind(rtl.dateFormatter),
    formatTime: rtl.dateFormatter.formatTime.bind(rtl.dateFormatter),
    formatDateTime: rtl.dateFormatter.formatDateTime.bind(rtl.dateFormatter),
    getFontFamily: rtl.getFontFamily.bind(rtl),
    switchLanguage: rtl.switchLanguage.bind(rtl)
  };
}
