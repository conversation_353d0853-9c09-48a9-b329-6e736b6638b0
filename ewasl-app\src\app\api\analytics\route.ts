/**
 * Analytics API Endpoint
 * Provides real-time analytics data from social media platforms
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { SocialAnalyticsService } from '@/lib/analytics/social-analytics-service';
import { createClient } from '@/lib/supabase/client';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const { user } = await getAuthenticatedUser(request);
    
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform');
    const accountId = searchParams.get('accountId');
    const refresh = searchParams.get('refresh') === 'true';

    const analyticsService = new SocialAnalyticsService();
    const supabase = createClient();

    if (platform && accountId) {
      // Get analytics for specific account
      try {
        const analytics = await analyticsService.getAccountAnalytics(
          user.id,
          platform,
          accountId
        );

        if (analytics) {
          // Store analytics in database for caching
          await supabase
            .from('analytics_cache')
            .upsert({
              user_id: user.id,
              platform: analytics.platform,
              account_id: analytics.accountId,
              metrics: analytics.metrics,
              last_updated: analytics.lastUpdated.toISOString(),
            }, {
              onConflict: 'user_id,platform,account_id',
            });

          return NextResponse.json({
            success: true,
            analytics,
          });
        } else {
          return NextResponse.json(
            { error: 'Failed to fetch analytics for this account' },
            { status: 404 }
          );
        }
      } catch (error: any) {
        console.error(`Analytics error for ${platform}:`, error);
        
        // Try to get cached data if real-time fetch fails
        if (!refresh) {
          const { data: cachedData } = await supabase
            .from('analytics_cache')
            .select('*')
            .eq('user_id', user.id)
            .eq('platform', platform.toUpperCase())
            .eq('account_id', accountId)
            .single();

          if (cachedData) {
            return NextResponse.json({
              success: true,
              analytics: {
                platform: cachedData.platform,
                accountId: cachedData.account_id,
                accountName: cachedData.account_name || 'Unknown',
                metrics: cachedData.metrics,
                lastUpdated: new Date(cachedData.last_updated),
              },
              cached: true,
            });
          }
        }

        return NextResponse.json(
          { error: error.message || 'Failed to fetch analytics' },
          { status: 500 }
        );
      }
    } else {
      // Get analytics for all connected accounts
      try {
        const allAnalytics = await analyticsService.getAllAccountsAnalytics(user.id);

        // Store all analytics in cache
        for (const analytics of allAnalytics) {
          await supabase
            .from('analytics_cache')
            .upsert({
              user_id: user.id,
              platform: analytics.platform,
              account_id: analytics.accountId,
              account_name: analytics.accountName,
              metrics: analytics.metrics,
              last_updated: analytics.lastUpdated.toISOString(),
            }, {
              onConflict: 'user_id,platform,account_id',
            });
        }

        return NextResponse.json({
          success: true,
          analytics: allAnalytics,
        });
      } catch (error: any) {
        console.error('All analytics error:', error);
        
        // Try to get cached data if real-time fetch fails
        if (!refresh) {
          const { data: cachedData } = await supabase
            .from('analytics_cache')
            .select('*')
            .eq('user_id', user.id)
            .order('last_updated', { ascending: false });

          if (cachedData && cachedData.length > 0) {
            const cachedAnalytics = cachedData.map(cache => ({
              platform: cache.platform,
              accountId: cache.account_id,
              accountName: cache.account_name || 'Unknown',
              metrics: cache.metrics,
              lastUpdated: new Date(cache.last_updated),
            }));

            return NextResponse.json({
              success: true,
              analytics: cachedAnalytics,
              cached: true,
            });
          }
        }

        return NextResponse.json(
          { error: error.message || 'Failed to fetch analytics' },
          { status: 500 }
        );
      }
    }
  } catch (error: any) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const { user } = await getAuthenticatedUser(request);
    
    const { action, platform, accountId, postId } = await request.json();

    const analyticsService = new SocialAnalyticsService();

    if (action === 'refresh') {
      // Force refresh analytics data
      if (platform && accountId) {
        const analytics = await analyticsService.getAccountAnalytics(
          user.id,
          platform,
          accountId
        );

        return NextResponse.json({
          success: true,
          analytics,
          refreshed: true,
        });
      } else {
        const allAnalytics = await analyticsService.getAllAccountsAnalytics(user.id);

        return NextResponse.json({
          success: true,
          analytics: allAnalytics,
          refreshed: true,
        });
      }
    } else if (action === 'post-analytics' && postId) {
      // Get specific post analytics
      const postAnalytics = await analyticsService.getPostAnalytics(
        user.id,
        platform,
        postId
      );

      return NextResponse.json({
        success: true,
        postAnalytics,
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid action' },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('Analytics POST error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process analytics request' },
      { status: 500 }
    );
  }
}
