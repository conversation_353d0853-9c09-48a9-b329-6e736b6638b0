'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Link from '@tiptap/extension-link';
import Mention from '@tiptap/extension-mention';
import CharacterCount from '@tiptap/extension-character-count';
import Placeholder from '@tiptap/extension-placeholder';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Bold,
  Italic,
  Underline,
  Link as LinkIcon,
  Hash,
  AtSign,
  Type,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  Save,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface RichTextEditorProps {
  content?: string;
  placeholder?: string;
  maxLength?: number;
  platform?: 'facebook' | 'twitter' | 'linkedin' | 'instagram';
  onChange?: (content: string) => void;
  onSave?: (content: string) => void;
  className?: string;
  dir?: 'ltr' | 'rtl';
  autoSave?: boolean;
  autoSaveDelay?: number;
}

// Platform-specific character limits
const PLATFORM_LIMITS = {
  twitter: 280,
  facebook: 63206,
  linkedin: 3000,
  instagram: 2200,
};

// Custom hashtag extension
const HashtagExtension = StarterKit.configure({
  // Configure hashtag highlighting
});

export function RichTextEditor({
  content = '',
  placeholder = 'اكتب محتواك هنا...',
  maxLength,
  platform = 'facebook',
  onChange,
  onSave,
  className,
  dir = 'rtl',
  autoSave = true,
  autoSaveDelay = 2000,
}: RichTextEditorProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  
  const characterLimit = maxLength || PLATFORM_LIMITS[platform];

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-500 underline',
        },
      }),
      Mention.configure({
        HTMLAttributes: {
          class: 'text-blue-600 bg-blue-100 px-1 rounded',
        },
        suggestion: {
          items: ({ query }) => {
            // Mock mention suggestions - in real app, fetch from API
            const suggestions = [
              { id: '1', label: 'eWasl' },
              { id: '2', label: 'SocialMedia' },
              { id: '3', label: 'Marketing' },
            ];
            return suggestions.filter(item =>
              item.label.toLowerCase().includes(query.toLowerCase())
            );
          },
        },
      }),
      CharacterCount.configure({
        limit: characterLimit,
      }),
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
    ],
    content,
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
          'min-h-[200px] p-4 border rounded-md',
          dir === 'rtl' ? 'text-right' : 'text-left'
        ),
        dir,
      },
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange?.(html);
    },
  });

  // Auto-save functionality
  useEffect(() => {
    if (!editor || !autoSave) return;

    const timer = setTimeout(() => {
      if (editor.getHTML() !== content && onSave) {
        handleSave();
      }
    }, autoSaveDelay);

    return () => clearTimeout(timer);
  }, [editor?.getHTML(), autoSave, autoSaveDelay]);

  const handleSave = useCallback(async () => {
    if (!editor || !onSave) return;

    setIsSaving(true);
    try {
      await onSave(editor.getHTML());
      setLastSaved(new Date());
    } catch (error) {
      console.error('Save failed:', error);
    } finally {
      setIsSaving(false);
    }
  }, [editor, onSave]);

  const addLink = useCallback(() => {
    if (!editor) return;

    const url = window.prompt('أدخل الرابط:');
    if (url) {
      editor.chain().focus().setLink({ href: url }).run();
    }
  }, [editor]);

  const addHashtag = useCallback(() => {
    if (!editor) return;

    const hashtag = window.prompt('أدخل الهاشتاغ (بدون #):');
    if (hashtag) {
      editor.chain().focus().insertContent(`#${hashtag} `).run();
    }
  }, [editor]);

  const addMention = useCallback(() => {
    if (!editor) return;

    const mention = window.prompt('أدخل الإشارة (بدون @):');
    if (mention) {
      editor.chain().focus().insertContent(`@${mention} `).run();
    }
  }, [editor]);

  if (!editor) {
    return <div className="animate-pulse bg-gray-200 h-64 rounded-md" />;
  }

  const characterCount = editor.storage.characterCount.characters();
  const wordCount = editor.storage.characterCount.words();
  const isOverLimit = characterCount > characterLimit;

  return (
    <div className={cn('border rounded-lg', className)}>
      {/* Toolbar */}
      <div className="border-b p-2 flex flex-wrap items-center gap-1">
        {/* Text formatting */}
        <div className="flex items-center gap-1">
          <Button
            variant={editor.isActive('bold') ? 'default' : 'ghost'}
            size="sm"
            onClick={() => editor.chain().focus().toggleBold().run()}
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            variant={editor.isActive('italic') ? 'default' : 'ghost'}
            size="sm"
            onClick={() => editor.chain().focus().toggleItalic().run()}
          >
            <Italic className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Lists */}
        <div className="flex items-center gap-1">
          <Button
            variant={editor.isActive('bulletList') ? 'default' : 'ghost'}
            size="sm"
            onClick={() => editor.chain().focus().toggleBulletList().run()}
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant={editor.isActive('orderedList') ? 'default' : 'ghost'}
            size="sm"
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
          >
            <ListOrdered className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Special content */}
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="sm" onClick={addLink}>
            <LinkIcon className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={addHashtag}>
            <Hash className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={addMention}>
            <AtSign className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Undo/Redo */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
          >
            <Redo className="h-4 w-4" />
          </Button>
        </div>

        {/* Save button */}
        {onSave && (
          <>
            <Separator orientation="vertical" className="h-6" />
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSave}
              disabled={isSaving}
            >
              <Save className="h-4 w-4" />
              {isSaving ? 'حفظ...' : 'حفظ'}
            </Button>
          </>
        )}
      </div>

      {/* Editor */}
      <div className="relative">
        <EditorContent editor={editor} />
        
        {/* Character count overlay */}
        <div className="absolute bottom-2 right-2 flex items-center gap-2 text-sm text-gray-500">
          <Badge variant={isOverLimit ? 'destructive' : 'secondary'}>
            {characterCount}/{characterLimit}
          </Badge>
          <span>{wordCount} كلمة</span>
          {lastSaved && (
            <span className="text-xs">
              آخر حفظ: {lastSaved.toLocaleTimeString('ar-SA')}
            </span>
          )}
        </div>
      </div>

      {/* Platform-specific tips */}
      <div className="border-t p-2 text-xs text-gray-500">
        <div className="flex items-center justify-between">
          <span>منصة: {platform}</span>
          <span>الحد الأقصى: {characterLimit.toLocaleString()} حرف</span>
        </div>
      </div>
    </div>
  );
}
