import { createClient } from '@supabase/supabase-js'

// Create service role client function - bypasses RLS
export function createServiceRoleClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-key'

  if (!supabaseUrl) {
    throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
  }

  // Only check for service key at runtime, not build time
  if (!process.env.SUPABASE_SERVICE_ROLE_KEY && typeof window === 'undefined') {
    console.warn('SUPABASE_SERVICE_ROLE_KEY environment variable not set - using anon key as fallback')
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

// Export function for compatibility with existing imports
export const supabaseServiceRole = createServiceRoleClient()

export default createServiceRoleClient

// Service role client for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'placeholder-key'

export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
})

// Create service client function for compatibility
export function createServiceClient() {
  return createServiceRoleClient()
}
