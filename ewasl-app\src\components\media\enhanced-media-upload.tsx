'use client';

import React, { useState, useRef } from 'react';
import { Upload, X, Image, Video, Loader2, Settings, Check } from 'lucide-react';
import { toast } from 'sonner';
import { MediaStorageService } from '@/lib/media/storage-service';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';
import { useSupabase } from '@/components/auth/supabase-provider';

interface MediaFile {
  id: string;
  fileName: string;
  originalName: string;
  fileType: string;
  fileSize: number;
  publicUrl: string;
  cdnUrl: string;
  userId: string;
  folder?: string;
  metadata?: {
    width?: number;
    height?: number;
    compressed?: boolean;
    originalSize?: number;
  };
  createdAt: Date;
}

interface EnhancedMediaUploadProps {
  onUploadComplete?: (media: MediaFile) => void;
  onUploadStart?: () => void;
  onUploadProgress?: (progress: number) => void;
  acceptedTypes?: string[];
  maxSize?: number;
  folder?: string;
  enableCompression?: boolean;
  className?: string;
  multiple?: boolean;
}

export function EnhancedMediaUpload({
  onUploadComplete,
  onUploadStart,
  onUploadProgress,
  acceptedTypes = ['image/*', 'video/*'],
  maxSize = 50 * 1024 * 1024, // 50MB
  folder = 'uploads',
  enableCompression = true,
  className = '',
  multiple = false,
}: EnhancedMediaUploadProps) {
  const { user } = useSupabase();
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [compressionEnabled, setCompressionEnabled] = useState(enableCompression);
  const [quality, setQuality] = useState([0.8]);
  const [maxWidth, setMaxWidth] = useState([1920]);
  const [maxHeight, setMaxHeight] = useState([1080]);
  const [uploadedFiles, setUploadedFiles] = useState<MediaFile[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const storageService = new MediaStorageService();

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const uploadFile = async (file: File) => {
    if (!user) {
      toast.error('يجب تسجيل الدخول لرفع الملفات');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    onUploadStart?.();

    try {
      const validation = storageService.validateFile(file);
      if (!validation.isValid) {
        toast.error(validation.error);
        return;
      }

      console.log('Uploading file:', file.name);

      const mediaFile = await storageService.uploadFile(
        file,
        user.id,
        {
          folder,
          compress: compressionEnabled && file.type.startsWith('image/'),
          maxWidth: maxWidth[0],
          maxHeight: maxHeight[0],
          quality: quality[0],
        },
        (progress) => {
          setUploadProgress(progress.percentage);
          onUploadProgress?.(progress.percentage);
        }
      );

      console.log('Upload successful:', mediaFile);
      setUploadedFiles(prev => [...prev, mediaFile]);
      toast.success(`تم رفع ${mediaFile.originalName} بنجاح!`);
      onUploadComplete?.(mediaFile);

    } catch (error: any) {
      console.error('Upload error:', error);
      toast.error(error.message || 'فشل في رفع الملف');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      if (multiple) {
        Array.from(e.dataTransfer.files).forEach(uploadFile);
      } else {
        uploadFile(e.dataTransfer.files[0]);
      }
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      if (multiple) {
        Array.from(e.target.files).forEach(uploadFile);
      } else if (e.target.files[0]) {
        uploadFile(e.target.files[0]);
      }
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const removeUploadedFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Settings */}
      <Collapsible open={showSettings} onOpenChange={setShowSettings}>
        <CollapsibleTrigger asChild>
          <Button variant="outline" size="sm" className="w-full">
            <Settings className="h-4 w-4 mr-2" />
            إعدادات الرفع
            {compressionEnabled && (
              <Badge variant="secondary" className="ml-2">
                ضغط مفعل
              </Badge>
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-4 p-4 border rounded-lg bg-gray-50 mt-2">
          <div className="flex items-center space-x-2 space-x-reverse">
            <Switch
              id="compression"
              checked={compressionEnabled}
              onCheckedChange={setCompressionEnabled}
            />
            <Label htmlFor="compression">ضغط الصور تلقائياً</Label>
          </div>
          
          {compressionEnabled && (
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">
                  جودة الضغط: {Math.round(quality[0] * 100)}%
                </Label>
                <Slider
                  value={quality}
                  onValueChange={setQuality}
                  max={1}
                  min={0.1}
                  step={0.1}
                  className="mt-2"
                />
              </div>
              
              <div>
                <Label className="text-sm font-medium">
                  العرض الأقصى: {maxWidth[0]}px
                </Label>
                <Slider
                  value={maxWidth}
                  onValueChange={setMaxWidth}
                  max={3840}
                  min={480}
                  step={120}
                  className="mt-2"
                />
              </div>
              
              <div>
                <Label className="text-sm font-medium">
                  الارتفاع الأقصى: {maxHeight[0]}px
                </Label>
                <Slider
                  value={maxHeight}
                  onValueChange={setMaxHeight}
                  max={2160}
                  min={320}
                  step={120}
                  className="mt-2"
                />
              </div>
            </div>
          )}
        </CollapsibleContent>
      </Collapsible>

      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
          dragActive 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={!isUploading ? openFileDialog : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
          disabled={isUploading}
        />

        {isUploading ? (
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
            <p className="text-sm text-gray-600 mb-2">جاري رفع الملف...</p>
            <Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
            <p className="text-xs text-gray-500 mt-2">{Math.round(uploadProgress)}%</p>
          </div>
        ) : (
          <div className="text-center">
            <div className="mb-4">
              <Upload className="h-12 w-12 mx-auto text-gray-400" />
            </div>
            
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              ارفع ملفاتك هنا
            </h3>
            
            <p className="text-sm text-gray-500 mb-4">
              اسحب وأفلت الملفات أو انقر للاختيار
            </p>

            <div className="flex gap-2 justify-center">
              <Button
                type="button"
                variant="default"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  openFileDialog();
                }}
              >
                <Image className="h-4 w-4 mr-2" />
                اختيار صورة
              </Button>
              <Button
                type="button"
                variant="secondary"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  openFileDialog();
                }}
              >
                <Video className="h-4 w-4 mr-2" />
                اختيار فيديو
              </Button>
            </div>
            
            <p className="text-xs text-gray-400 mt-4">
              الحد الأقصى: {formatFileSize(maxSize)} | {multiple ? 'ملفات متعددة' : 'ملف واحد'}
            </p>
          </div>
        )}

        {dragActive && (
          <div className="absolute inset-0 bg-blue-500 bg-opacity-10 border-2 border-blue-500 border-dashed rounded-lg flex items-center justify-center">
            <p className="text-blue-600 font-medium">أفلت الملف هنا</p>
          </div>
        )}
      </div>

      {/* Uploaded Files Preview */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm font-medium">الملفات المرفوعة:</Label>
          <div className="grid grid-cols-1 gap-2">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <Check className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="text-sm font-medium text-green-900">
                      {file.originalName}
                    </p>
                    <p className="text-xs text-green-600">
                      {formatFileSize(file.fileSize)}
                      {file.metadata?.compressed && ' (مضغوط)'}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeUploadedFile(file.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
