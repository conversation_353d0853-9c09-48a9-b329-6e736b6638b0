/**
 * Comprehensive OAuth Integration Tests
 * Tests OAuth flows, account management, and publishing functionality
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const TEST_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com',
  testUser: {
    email: process.env.TEST_USER_EMAIL || '<EMAIL>',
    password: process.env.TEST_USER_PASSWORD || '111333Tt'
  },
  platforms: ['facebook', 'instagram', 'linkedin', 'twitter'],
  timeout: 30000
};

// Test data for Arabic RTL content
const ARABIC_TEST_CONTENT = {
  shortPost: 'مرحباً بكم في منصة eWasl للتسويق الرقمي! 🚀',
  longPost: `
    🌟 اكتشف قوة التسويق الرقمي مع منصة eWasl

    ✨ ميزات رائعة:
    • جدولة المحتوى التلقائية
    • تحليلات شاملة ومفصلة
    • دعم جميع منصات التواصل الاجتماعي
    • واجهة سهلة الاستخدام باللغة العربية

    🎯 ابدأ رحلتك نحو النجاح الرقمي اليوم!

    #التسويق_الرقمي #eWasl #نجاح_رقمي
  `,
  hashtags: ['#التسويق_الرقمي', '#eWasl', '#نجاح_رقمي', '#تطوير_الأعمال'],
  emojis: ['🚀', '✨', '🌟', '🎯', '💡', '📈']
};

test.describe('OAuth Integration Tests', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    
    // Set Arabic locale and RTL direction
    await page.setExtraHTTPHeaders({
      'Accept-Language': 'ar-SA,ar;q=0.9,en;q=0.8'
    });
    
    // Navigate to login page
    await page.goto(`${TEST_CONFIG.baseUrl}/auth/signin`);
    
    // Login with test user
    await page.fill('input[type="email"]', TEST_CONFIG.testUser.email);
    await page.fill('input[type="password"]', TEST_CONFIG.testUser.password);
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await page.waitForURL('**/dashboard', { timeout: TEST_CONFIG.timeout });
    await expect(page.locator('h1')).toContainText('لوحة التحكم');
  });

  test.afterEach(async () => {
    await page.close();
  });

  test('should display social accounts page with Arabic RTL layout', async () => {
    // Navigate to social accounts page
    await page.goto(`${TEST_CONFIG.baseUrl}/social`);
    
    // Verify Arabic RTL layout
    const body = page.locator('body');
    await expect(body).toHaveAttribute('dir', 'rtl');
    
    // Verify Arabic text content
    await expect(page.locator('h1')).toContainText('الحسابات الاجتماعية');
    
    // Take screenshot for documentation
    await page.screenshot({ 
      path: 'test-results/social-accounts-arabic-rtl.png',
      fullPage: true 
    });
  });

  test('should show available OAuth platforms', async () => {
    await page.goto(`${TEST_CONFIG.baseUrl}/social`);
    
    // Check for platform connection buttons
    for (const platform of TEST_CONFIG.platforms) {
      const platformButton = page.locator(`[data-platform="${platform}"]`);
      await expect(platformButton).toBeVisible();
    }
    
    // Verify Arabic platform names
    await expect(page.locator('text=فيسبوك')).toBeVisible();
    await expect(page.locator('text=إنستغرام')).toBeVisible();
    await expect(page.locator('text=لينكد إن')).toBeVisible();
    await expect(page.locator('text=تويتر')).toBeVisible();
  });

  test('should initiate Facebook OAuth flow', async () => {
    await page.goto(`${TEST_CONFIG.baseUrl}/social`);
    
    // Click Facebook connection button
    const facebookButton = page.locator('[data-platform="facebook"]');
    await facebookButton.click();
    
    // Should redirect to Facebook OAuth
    await page.waitForURL('**/facebook.com/**', { timeout: TEST_CONFIG.timeout });
    
    // Verify Facebook OAuth page elements
    await expect(page.locator('text=Facebook')).toBeVisible();
    await expect(page.locator('input[name="email"]')).toBeVisible();
    
    // Take screenshot of OAuth page
    await page.screenshot({ 
      path: 'test-results/facebook-oauth-page.png' 
    });
  });

  test('should handle OAuth callback and store account data', async () => {
    // This test requires manual OAuth completion or mocked responses
    // For now, we'll test the callback endpoint directly
    
    const response = await page.request.get(`${TEST_CONFIG.baseUrl}/api/oauth/facebook/callback?code=test_code&state=test_state`);
    
    // Should handle callback (even if it fails due to invalid code)
    expect(response.status()).toBeLessThan(500);
  });

  test('should display connected accounts with Arabic names', async () => {
    await page.goto(`${TEST_CONFIG.baseUrl}/social`);
    
    // Wait for accounts to load
    await page.waitForSelector('[data-testid="connected-accounts"]', { timeout: TEST_CONFIG.timeout });
    
    // Check for account cards
    const accountCards = page.locator('[data-testid="account-card"]');
    const count = await accountCards.count();
    
    if (count > 0) {
      // Verify Arabic account information
      await expect(accountCards.first()).toContainText('متصل');
      
      // Take screenshot of connected accounts
      await page.screenshot({ 
        path: 'test-results/connected-accounts.png' 
      });
    }
  });

  test('should show token status monitoring', async () => {
    await page.goto(`${TEST_CONFIG.baseUrl}/social`);
    
    // Navigate to token status section
    const tokenStatusButton = page.locator('text=حالة رموز المصادقة');
    if (await tokenStatusButton.isVisible()) {
      await tokenStatusButton.click();
      
      // Verify token status display
      await expect(page.locator('text=إجمالي الحسابات')).toBeVisible();
      await expect(page.locator('text=متصلة')).toBeVisible();
      
      // Take screenshot
      await page.screenshot({ 
        path: 'test-results/token-status-monitoring.png' 
      });
    }
  });

  test('should create post with Arabic content', async () => {
    await page.goto(`${TEST_CONFIG.baseUrl}/posts/new`);
    
    // Fill post content with Arabic text
    const contentEditor = page.locator('[data-testid="post-editor"]');
    await contentEditor.fill(ARABIC_TEST_CONTENT.shortPost);
    
    // Verify RTL text direction
    await expect(contentEditor).toHaveAttribute('dir', 'rtl');
    
    // Add hashtags
    for (const hashtag of ARABIC_TEST_CONTENT.hashtags.slice(0, 2)) {
      await contentEditor.fill(`${await contentEditor.inputValue()} ${hashtag}`);
    }
    
    // Select platforms (if accounts are connected)
    const platformSelectors = page.locator('[data-testid="platform-selector"]');
    if (await platformSelectors.count() > 0) {
      await platformSelectors.first().click();
    }
    
    // Take screenshot of post creation
    await page.screenshot({ 
      path: 'test-results/arabic-post-creation.png' 
    });
    
    // Save as draft (don't publish in test)
    const saveDraftButton = page.locator('text=حفظ كمسودة');
    if (await saveDraftButton.isVisible()) {
      await saveDraftButton.click();
      
      // Verify success message
      await expect(page.locator('text=تم حفظ المنشور')).toBeVisible();
    }
  });

  test('should handle post scheduling with Arabic interface', async () => {
    await page.goto(`${TEST_CONFIG.baseUrl}/posts/new`);
    
    // Fill content
    await page.locator('[data-testid="post-editor"]').fill(ARABIC_TEST_CONTENT.shortPost);
    
    // Open scheduling options
    const scheduleButton = page.locator('text=جدولة المنشور');
    if (await scheduleButton.isVisible()) {
      await scheduleButton.click();
      
      // Set future date
      const dateInput = page.locator('input[type="datetime-local"]');
      if (await dateInput.isVisible()) {
        const futureDate = new Date();
        futureDate.setHours(futureDate.getHours() + 2);
        await dateInput.fill(futureDate.toISOString().slice(0, 16));
      }
      
      // Verify Arabic scheduling interface
      await expect(page.locator('text=تاريخ النشر')).toBeVisible();
      await expect(page.locator('text=وقت النشر')).toBeVisible();
      
      // Take screenshot
      await page.screenshot({ 
        path: 'test-results/arabic-post-scheduling.png' 
      });
    }
  });

  test('should display analytics with Arabic labels', async () => {
    await page.goto(`${TEST_CONFIG.baseUrl}/analytics`);
    
    // Wait for analytics to load
    await page.waitForSelector('[data-testid="analytics-dashboard"]', { timeout: TEST_CONFIG.timeout });
    
    // Verify Arabic analytics labels
    await expect(page.locator('text=التحليلات')).toBeVisible();
    await expect(page.locator('text=المنشورات')).toBeVisible();
    await expect(page.locator('text=التفاعل')).toBeVisible();
    await expect(page.locator('text=الوصول')).toBeVisible();
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/arabic-analytics-dashboard.png',
      fullPage: true 
    });
  });

  test('should handle account disconnection flow', async () => {
    await page.goto(`${TEST_CONFIG.baseUrl}/social`);
    
    // Find first connected account
    const accountCard = page.locator('[data-testid="account-card"]').first();
    if (await accountCard.isVisible()) {
      
      // Open account menu
      const menuButton = accountCard.locator('[data-testid="account-menu"]');
      await menuButton.click();
      
      // Click disconnect option
      const disconnectButton = page.locator('text=قطع الاتصال');
      if (await disconnectButton.isVisible()) {
        await disconnectButton.click();
        
        // Verify confirmation dialog
        await expect(page.locator('text=قطع اتصال الحساب')).toBeVisible();
        await expect(page.locator('text=هل أنت متأكد')).toBeVisible();
        
        // Take screenshot of confirmation dialog
        await page.screenshot({ 
          path: 'test-results/disconnect-confirmation-dialog.png' 
        });
        
        // Cancel disconnection (don't actually disconnect in test)
        await page.locator('text=إلغاء').click();
      }
    }
  });

  test('should validate Arabic RTL text rendering', async () => {
    await page.goto(`${TEST_CONFIG.baseUrl}/posts/new`);
    
    // Test various Arabic text scenarios
    const testTexts = [
      'النص العربي يجب أن يظهر من اليمين إلى اليسار',
      'Mixed text: English and العربية together',
      'أرقام: ١٢٣٤٥٦٧٨٩٠ و 1234567890',
      'رموز تعبيرية: 😀 🎉 🚀 مع النص العربي'
    ];
    
    const editor = page.locator('[data-testid="post-editor"]');
    
    for (const text of testTexts) {
      await editor.clear();
      await editor.fill(text);
      
      // Verify text direction
      await expect(editor).toHaveAttribute('dir', 'rtl');
      
      // Verify text is properly rendered
      await expect(editor).toHaveValue(text);
    }
    
    // Take final screenshot
    await page.screenshot({ 
      path: 'test-results/arabic-rtl-text-validation.png' 
    });
  });

  test('should test responsive design on mobile viewport', async () => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto(`${TEST_CONFIG.baseUrl}/social`);
    
    // Verify mobile layout
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
    
    // Test navigation
    await page.locator('[data-testid="mobile-menu-toggle"]').click();
    await expect(page.locator('[data-testid="mobile-nav"]')).toBeVisible();
    
    // Take mobile screenshot
    await page.screenshot({ 
      path: 'test-results/mobile-social-accounts.png' 
    });
  });
});

});

// Performance tests
test.describe('Performance Tests', () => {
  test('should load social accounts page within performance budget', async ({ page }) => {
    const startTime = Date.now();

    await page.goto(`${TEST_CONFIG.baseUrl}/social`);
    await page.waitForLoadState('networkidle');

    const loadTime = Date.now() - startTime;

    // Should load within 3 seconds
    expect(loadTime).toBeLessThan(3000);

    console.log(`Social accounts page loaded in ${loadTime}ms`);
  });

  test('should have good Lighthouse scores', async ({ page }) => {
    await page.goto(`${TEST_CONFIG.baseUrl}/social`);

    // This would require lighthouse integration
    // For now, just verify page loads without errors
    const errors: Error[] = [];
    page.on('pageerror', error => errors.push(error));

    await page.waitForLoadState('networkidle');

    expect(errors).toHaveLength(0);
  });
});
