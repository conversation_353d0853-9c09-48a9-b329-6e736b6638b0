#!/usr/bin/env node

/**
 * Fix OAuth Production Issues
 * Diagnoses and fixes the specific Facebook, Twitter, LinkedIn OAuth errors
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

console.log('🔧 OAuth Production Issues Diagnostic & Fix');
console.log('============================================\n');

// Test OAuth endpoints
async function testOAuthEndpoint(platform, url) {
  return new Promise((resolve) => {
    const req = https.get(url, (res) => {
      console.log(`📊 ${platform} OAuth Test:`);
      console.log(`   Status: ${res.statusCode}`);
      console.log(`   Headers:`, Object.keys(res.headers).join(', '));
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          platform,
          status: res.statusCode,
          success: res.statusCode === 200 || res.statusCode === 302,
          data: data.slice(0, 200) + '...'
        });
      });
    }).on('error', (err) => {
      console.log(`❌ ${platform} OAuth Error:`, err.message);
      resolve({ platform, status: 'ERROR', success: false, error: err.message });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({ platform, status: 'TIMEOUT', success: false, error: 'Timeout' });
    });
  });
}

// Check app URL and OAuth configuration
async function diagnoseIssues() {
  console.log('🔍 Diagnosing OAuth Issues...\n');
  
  // The URLs from your error messages
  const tests = [
    {
      platform: 'App OAuth Connect API',
      url: 'https://app.ewasl.com/api/social/connect?platform=facebook'
    },
    {
      platform: 'App Health Check',
      url: 'https://app.ewasl.com/api/system/health'
    },
    {
      platform: 'App Social Page',
      url: 'https://app.ewasl.com/social'
    }
  ];
  
  console.log('📡 Testing production endpoints...\n');
  
  for (const test of tests) {
    await testOAuthEndpoint(test.platform, test.url);
    console.log('');
  }
}

// Fix Facebook OAuth Configuration
function createFacebookFix() {
  console.log('🔧 Creating Facebook OAuth Fix...\n');
  
  const facebookFixCode = `
// Facebook OAuth Fix - Enhanced Error Handling
// Place this in your Facebook OAuth handler

// 1. VERIFY CLIENT SECRET FORMAT
const facebookClientSecret = process.env.FACEBOOK_CLIENT_SECRET;
if (!facebookClientSecret || facebookClientSecret === 'YOUR_FACEBOOK_APP_SECRET') {
  throw new Error('Facebook Client Secret not properly configured');
}

// 2. CORRECT FACEBOOK TOKEN EXCHANGE
const tokenParams = new URLSearchParams({
  client_id: process.env.FACEBOOK_CLIENT_ID,
  client_secret: process.env.FACEBOOK_CLIENT_SECRET, // Must be exact
  code: authCode,
  redirect_uri: \`\${process.env.NEXT_PUBLIC_APP_URL}/api/facebook/callback\`,
  grant_type: 'authorization_code'
});

// 3. FACEBOOK API CALL WITH PROPER HEADERS
const tokenResponse = await fetch('https://graph.facebook.com/v18.0/oauth/access_token', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
    'User-Agent': 'eWasl-Social-Scheduler/1.0'
  },
  body: tokenParams.toString()
});

if (!tokenResponse.ok) {
  const errorData = await tokenResponse.text();
  console.error('Facebook OAuth Error:', errorData);
  throw new Error(\`Facebook OAuth failed: \${tokenResponse.status} - \${errorData}\`);
}
`;

  const facebookFixPath = path.join(__dirname, '..', 'FACEBOOK_OAUTH_FIX.md');
  fs.writeFileSync(facebookFixPath, `# Facebook OAuth Fix\n\n\`\`\`javascript${facebookFixCode}\`\`\``);
  console.log('✅ Facebook fix code saved to:', facebookFixPath);
}

// Create environment variable verification
function createEnvCheck() {
  console.log('🔧 Creating Environment Variable Check...\n');
  
  const envCheckCode = `
// Environment Variables Check for Production
const requiredEnvVars = {
  // Facebook
  FACEBOOK_CLIENT_ID: process.env.FACEBOOK_CLIENT_ID,
  FACEBOOK_CLIENT_SECRET: process.env.FACEBOOK_CLIENT_SECRET,
  
  // Twitter/X
  X_CLIENT_ID: process.env.X_CLIENT_ID,
  X_CLIENT_SECRET: process.env.X_CLIENT_SECRET,
  
  // LinkedIn
  LINKEDIN_CLIENT_ID: process.env.LINKEDIN_CLIENT_ID,
  LINKEDIN_CLIENT_SECRET: process.env.LINKEDIN_CLIENT_SECRET,
  
  // App URLs
  NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
  NEXTAUTH_URL: process.env.NEXTAUTH_URL
};

// Check each variable
Object.entries(requiredEnvVars).forEach(([key, value]) => {
  if (!value || value.includes('YOUR_') || value === 'undefined') {
    console.error(\`❌ Missing or invalid: \${key} = \${value}\`);
  } else {
    console.log(\`✅ \${key}: \${value.slice(0, 10)}...\`);
  }
});
`;

  const envCheckPath = path.join(__dirname, '..', 'ENV_CHECK.js');
  fs.writeFileSync(envCheckPath, envCheckCode);
  console.log('✅ Environment check saved to:', envCheckPath);
}

// Main function to create fixes
async function main() {
  try {
    await diagnoseIssues();
    createFacebookFix();
    createEnvCheck();
    
    console.log('\n🎯 IMMEDIATE ACTION PLAN:');
    console.log('========================\n');
    
    console.log('1. 🔍 FACEBOOK CLIENT SECRET ISSUE:');
    console.log('   • Current secret might be test/sandbox secret');
    console.log('   • Go to https://developers.facebook.com/apps/1366325774493759');
    console.log('   • Check App Settings > Basic > App Secret');
    console.log('   • Copy the PRODUCTION app secret (not test secret)');
    console.log('   • Update Digital Ocean environment variables\n');
    
    console.log('2. 🔧 TWITTER/X CONFIGURATION:');
    console.log('   • Go to https://developer.twitter.com/en/portal/dashboard');
    console.log('   • Check your app settings');
    console.log('   • Verify Callback URLs include: https://app.ewasl.com/api/x/callback');
    console.log('   • Enable OAuth 2.0 with PKCE\n');
    
    console.log('3. 🔗 LINKEDIN CONFIGURATION:');
    console.log('   • Go to https://www.linkedin.com/developers/apps');
    console.log('   • Check OAuth redirect URLs');
    console.log('   • Add: https://app.ewasl.com/api/linkedin/callback\n');
    
    console.log('4. 🚀 IMMEDIATE FIXES TO DEPLOY:');
    console.log('   • Updated error handling');
    console.log('   • Proper client secret validation');
    console.log('   • Enhanced OAuth flow debugging');
    
    console.log('\n⚡ CRITICAL: Run the following commands:');
    console.log('   1. node scripts/update-oauth-credentials.js');
    console.log('   2. git add . && git commit -m "🔧 OAuth Production Fixes"');
    console.log('   3. git push origin main');
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

if (require.main === module) {
  main();
}

module.exports = { diagnoseIssues, createFacebookFix, createEnvCheck }; 