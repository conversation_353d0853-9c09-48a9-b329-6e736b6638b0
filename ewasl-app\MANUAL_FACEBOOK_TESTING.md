# 🔥 Manual Facebook/Instagram Integration Testing Guide

## 🎯 Current Status
- ✅ eWasl app running on `http://localhost:3003`
- ✅ Facebook App ID: `****************` configured
- ✅ OAuth URLs generated successfully
- ⚠️ HTTPS setup needed for Facebook OAuth (Facebook requirement)

## 🚀 IMMEDIATE TESTING OPTIONS

### Option 1: Test with HTTP (Limited Functionality)
**Current Setup**: App running on `http://localhost:3003`
**Limitation**: Facebook OAuth requires HTTPS, so we can test UI but not real OAuth

#### Test Steps:
1. **Navigate to Social Accounts Page**:
   ```
   http://localhost:3003/social-accounts
   ```

2. **Test UI Components**:
   - ✅ Social accounts interface loads
   - ✅ "Connect Facebook" button appears
   - ✅ Platform selection works
   - ⚠️ OAuth redirect will fail (HTTPS required)

3. **Test Post Creation**:
   ```
   http://localhost:3003/posts/new
   ```
   - ✅ Post creation form works
   - ✅ Platform selection available
   - ✅ Content creation functional
   - ⚠️ Publishing will fail without connected accounts

### Option 2: Setup HTTPS for Full Testing

#### Prerequisites:
1. **Install OpenSSL** (for certificate generation):
   - Download from: https://slproweb.com/products/Win32OpenSSL.html
   - Or install via Chocolatey: `choco install openssl`

2. **Generate SSL Certificates**:
   ```bash
   # After installing OpenSSL
   npm run generate-certs
   ```

3. **Start HTTPS Server**:
   ```bash
   npm run dev  # Uses HTTPS with certificates
   ```

4. **Update Facebook App Settings**:
   - Go to: https://developers.facebook.com/apps/****************/fb-login/settings/
   - Add Valid OAuth Redirect URI: `https://localhost:3003/api/facebook/callback`

## 🧪 COMPREHENSIVE TESTING SCENARIOS

### Scenario 1: Facebook Page Connection
**Generated OAuth URL**:
```
https://www.facebook.com/v20.0/dialog/oauth?client_id=****************&redirect_uri=https%3A%2F%2Flocalhost%3A3003%2Fapi%2Ffacebook%2Fcallback&scope=pages_show_list,pages_manage_posts,pages_read_engagement,business_management,email,public_profile&response_type=code&state=oauth_test_1752333501436
```

**Expected Flow**:
1. Click OAuth URL → Redirect to Facebook
2. Login with Facebook account
3. Grant permissions for Pages access
4. Redirect back to eWasl with authorization code
5. eWasl exchanges code for access token
6. Facebook Pages retrieved and displayed
7. Account stored in database

### Scenario 2: Instagram Business Account Connection
**Generated OAuth URL**:
```
https://www.facebook.com/v20.0/dialog/oauth?client_id=****************&redirect_uri=https%3A%2F%2Flocalhost%3A3003%2Fapi%2Ffacebook%2Fcallback&scope=instagram_basic,instagram_content_publish,pages_show_list,business_management&response_type=code&state=oauth_test_1752333501436
```

**Expected Flow**:
1. Click OAuth URL → Redirect to Facebook
2. Login and grant Instagram permissions
3. Instagram Business accounts retrieved
4. Account connection successful

### Scenario 3: Post Publishing Test
**After connecting accounts**:

1. **Create Test Post**:
   ```
   Content: "🚀 Testing eWasl social media platform! This is a real post test. #eWasl #Test"
   Platforms: Facebook Page + Instagram Business
   Schedule: Immediate
   ```

2. **Expected Results**:
   - ✅ Post appears on Facebook Page within 30 seconds
   - ✅ Post appears on Instagram within 30 seconds
   - ✅ Post URLs captured in eWasl database
   - ✅ Analytics data starts collecting

## 🔍 VERIFICATION CHECKLIST

### Database Verification
After successful OAuth:
```sql
-- Check connected accounts
SELECT * FROM social_accounts WHERE platform IN ('facebook', 'instagram');

-- Check OAuth logs
SELECT * FROM oauth_logs ORDER BY created_at DESC LIMIT 10;

-- Check posts after publishing
SELECT * FROM posts WHERE created_at > NOW() - INTERVAL '1 hour';
```

### Social Media Platform Verification
1. **Facebook Page**: Check for test post with #eWasl hashtag
2. **Instagram Account**: Verify image and caption posted correctly
3. **Analytics**: Check engagement metrics in eWasl dashboard

## 🚨 TROUBLESHOOTING

### Common Issues:

#### 1. "Invalid Redirect URI" Error
**Solution**: Update Facebook App settings:
- Go to: https://developers.facebook.com/apps/****************/fb-login/settings/
- Add: `https://localhost:3003/api/facebook/callback`

#### 2. SSL Certificate Warning
**Solution**: 
- Click "Advanced" in browser
- Click "Proceed to localhost (unsafe)"
- This is normal for self-signed certificates

#### 3. "App Not Approved" Error
**Solution**: 
- Use test users or submit app for review
- For testing, ensure you're logged in as app admin

#### 4. Token Expired Error
**Solution**: 
- Check token refresh mechanism
- Re-authenticate if needed

## 📊 SUCCESS METRICS

### OAuth Integration Success:
- [ ] Facebook OAuth completes without errors
- [ ] Instagram Business accounts retrieved
- [ ] Access tokens stored securely
- [ ] Account information displayed correctly

### Post Publishing Success:
- [ ] Test post appears on Facebook within 30 seconds
- [ ] Test post appears on Instagram within 30 seconds
- [ ] Post URLs captured and stored
- [ ] Analytics data collection starts

### Error Handling Success:
- [ ] Invalid content rejected with clear errors
- [ ] Network failures handled gracefully
- [ ] Token expiration triggers refresh
- [ ] Rate limiting prevents abuse

## 🎉 FINAL VERIFICATION

**Create this test post to verify everything works**:
```
Content: "🎉 eWasl social media platform is now live! Successfully posting to Facebook and Instagram simultaneously. #eWasl #SocialMedia #Success"
Image: Upload a test image (JPG/PNG, <10MB)
Platforms: Facebook + Instagram
Schedule: Immediate
```

**Expected Result**: Post appears on both platforms with image, proper formatting, and analytics tracking begins.

## 📞 NEXT STEPS

1. **If HTTPS setup is challenging**: 
   - Use ngrok for temporary HTTPS tunnel
   - Deploy to Vercel for production HTTPS testing

2. **If OAuth works but publishing fails**:
   - Check Facebook App permissions
   - Verify Instagram Business account setup
   - Review API rate limits

3. **If everything works**:
   - Test scheduled posting (5-minute delay)
   - Test bulk operations
   - Test analytics data collection
   - Prepare for production deployment

**🎯 Goal**: Successfully publish a real post to both Facebook and Instagram through the eWasl platform, proving end-to-end functionality.
