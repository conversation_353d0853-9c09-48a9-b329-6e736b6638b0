/**
 * Token Refresh Cron Job API Endpoint
 * This endpoint should be called periodically (e.g., daily) to refresh expiring tokens
 * Can be triggered by Vercel Cron Jobs or external cron services
 */

import { NextRequest, NextResponse } from 'next/server'
import { TokenRefreshService } from '@/lib/oauth/token-refresh-service'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Token refresh cron job started...')

    // Verify the request is authorized (optional security measure)
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      console.log('❌ Unauthorized cron job request')
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Initialize token refresh service
    const tokenRefreshService = new TokenRefreshService()

    // Check and refresh expiring tokens
    await tokenRefreshService.checkAndRefreshExpiredTokens()

    // Get summary of accounts that still need attention
    const accountsNeedingRefresh = await tokenRefreshService.getAccountsNeedingRefresh(7)

    const response = {
      success: true,
      message: 'Token refresh job completed successfully',
      timestamp: new Date().toISOString(),
      accountsNeedingRefresh: accountsNeedingRefresh.length,
      accounts: accountsNeedingRefresh.map(account => ({
        id: account.id,
        platform: account.platform,
        account_id: account.account_id,
        expires_at: account.expires_at,
        connection_status: account.connection_status
      }))
    }

    console.log('✅ Token refresh cron job completed:', response)
    return NextResponse.json(response)

  } catch (error) {
    console.error('❌ Token refresh cron job failed:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  // Allow POST requests as well for flexibility
  return GET(request)
}
