/**
 * OAuth Connection Initiation API
 * Handles OAuth connection requests for social media platforms
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { OAuthConnectionService } from '@/lib/oauth/connection-service';
import { getOAuthProvider, generateAuthUrl } from '@/lib/oauth/providers';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const { user } = await getAuthenticatedUser(request);
    
    const { platform } = await request.json();
    
    if (!platform) {
      return NextResponse.json(
        { error: 'Platform is required' },
        { status: 400 }
      );
    }

    // Get OAuth provider configuration
    const provider = getOAuthProvider(platform);
    if (!provider || !provider.enabled) {
      return NextResponse.json(
        { error: `OAuth provider not configured for platform: ${platform}` },
        { status: 400 }
      );
    }

    const connectionService = new OAuthConnectionService();
    
    // Generate state for CSRF protection
    const state = connectionService.generateState();
    
    // Generate PKCE for Twitter OAuth 2.0
    const pkce = platform.toLowerCase() === 'twitter' 
      ? connectionService.generatePKCE() 
      : undefined;
    
    // Construct redirect URI
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const redirectUri = `${baseUrl}/api/oauth/callback/${platform.toLowerCase()}`;
    
    // Store OAuth state
    await connectionService.storeOAuthState(
      user.id,
      platform,
      state,
      redirectUri,
      pkce?.codeVerifier
    );
    
    // Generate authorization URL
    const authUrl = generateAuthUrl(
      provider,
      state,
      redirectUri,
      pkce?.codeChallenge
    );
    
    return NextResponse.json({
      success: true,
      authUrl,
      platform: provider.name,
    });
    
  } catch (error: any) {
    console.error('OAuth connection error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to initiate OAuth connection' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const { user } = await getAuthenticatedUser(request);
    
    const connectionService = new OAuthConnectionService();
    const connections = await connectionService.getUserConnections(user.id);
    
    return NextResponse.json({
      success: true,
      connections: connections.map(conn => ({
        id: conn.id,
        platform: conn.platform,
        accountId: conn.accountId,
        accountName: conn.accountName,
        username: conn.username,
        avatar: conn.avatar,
        isActive: conn.isActive,
        createdAt: conn.createdAt,
        expiresAt: conn.expiresAt,
      })),
    });
    
  } catch (error: any) {
    console.error('Get connections error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to get connections' },
      { status: 500 }
    );
  }
}
