"use client";

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  MoreHorizontal, 
  RefreshCw, 
  Settings, 
  Unlink, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  TrendingUp,
  Calendar
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EnhancedSocialAccount, ConnectionStatus } from '@/types/social-enhanced';
import { cn } from '@/lib/utils';
// Note: date-fns imports will be handled by the component when needed

interface EnhancedAccountCardProps {
  account: EnhancedSocialAccount;
  isSelected: boolean;
  onSelect: (accountId: string, selected: boolean) => void;
  onRefresh: (accountId: string) => void;
  onDisconnect: (accountId: string) => void;
  onSettings: (accountId: string) => void;
  language: 'ar' | 'en';
}

const platformConfig = {
  FACEBOOK: { color: '#1877F2', icon: '📘' },
  INSTAGRAM: { color: '#E4405F', icon: '📷' },
  TWITTER: { color: '#1DA1F2', icon: '🐦' },
  LINKEDIN: { color: '#0A66C2', icon: '💼' },
  TIKTOK: { color: '#000000', icon: '🎵' },
  YOUTUBE: { color: '#FF0000', icon: '📺' },
  SNAPCHAT: { color: '#FFFC00', icon: '👻' },
  PINTEREST: { color: '#BD081C', icon: '📌' },
};

const statusConfig: Record<ConnectionStatus, { color: string; icon: React.ReactNode; label: { ar: string; en: string } }> = {
  connected: {
    color: 'text-green-600',
    icon: <CheckCircle className="h-4 w-4" />,
    label: { ar: 'متصل', en: 'Connected' }
  },
  expired: {
    color: 'text-yellow-600',
    icon: <Clock className="h-4 w-4" />,
    label: { ar: 'منتهي الصلاحية', en: 'Expired' }
  },
  error: {
    color: 'text-red-600',
    icon: <AlertTriangle className="h-4 w-4" />,
    label: { ar: 'خطأ', en: 'Error' }
  },
  reconnecting: {
    color: 'text-blue-600',
    icon: <RefreshCw className="h-4 w-4 animate-spin" />,
    label: { ar: 'إعادة الاتصال', en: 'Reconnecting' }
  },
  disconnected: {
    color: 'text-gray-600',
    icon: <Unlink className="h-4 w-4" />,
    label: { ar: 'غير متصل', en: 'Disconnected' }
  }
};

export function EnhancedAccountCard({
  account,
  isSelected,
  onSelect,
  onRefresh,
  onDisconnect,
  onSettings,
  language
}: EnhancedAccountCardProps) {
  const platform = platformConfig[account.platform];
  const status = statusConfig[account.connectionStatus];
  
  const formatNumber = (num?: number) => {
    if (!num) return '0';
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getApiUsagePercentage = () => {
    const apiLimit = account.rateLimits?.api;
    if (!apiLimit) return 0;
    return (apiLimit.current / apiLimit.limit) * 100;
  };

  return (
    <Card className={cn(
      "relative transition-all duration-200 hover:shadow-md",
      isSelected && "ring-2 ring-blue-500",
      account.connectionStatus === 'error' && "border-red-200 bg-red-50/50",
      account.connectionStatus === 'expired' && "border-yellow-200 bg-yellow-50/50"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Checkbox
              checked={isSelected}
              onCheckedChange={(checked) => onSelect(account.id, !!checked)}
              className="mt-1"
            />
            <div className="flex items-center gap-3">
              <div 
                className="w-3 h-3 rounded-full flex-shrink-0"
                style={{ backgroundColor: platform.color }}
              />
              <Avatar className="h-10 w-10">
                <AvatarImage src={account.profileImageUrl} />
                <AvatarFallback style={{ backgroundColor: platform.color + '20' }}>
                  {platform.icon}
                </AvatarFallback>
              </Avatar>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-sm truncate">
                  {account.accountName}
                </h3>
                {account.accountHandle && (
                  <p className="text-xs text-muted-foreground truncate">
                    @{account.accountHandle}
                  </p>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge 
              variant="outline" 
              className={cn("text-xs", status.color)}
            >
              {status.icon}
              <span className={cn("mr-1", language === 'ar' && "ml-1 mr-0")}>
                {status.label[language]}
              </span>
            </Badge>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onRefresh(account.id)}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'تحديث' : 'Refresh'}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onSettings(account.id)}>
                  <Settings className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'الإعدادات' : 'Settings'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => onDisconnect(account.id)}
                  className="text-red-600"
                >
                  <Unlink className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'قطع الاتصال' : 'Disconnect'}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Account Metrics */}
        {account.metrics && (
          <div className="grid grid-cols-2 gap-4 mb-4">
            {account.metrics.followerCount && (
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {formatNumber(account.metrics.followerCount)}
                </span>
                <span className="text-xs text-muted-foreground">
                  {language === 'ar' ? 'متابع' : 'followers'}
                </span>
              </div>
            )}
            
            {account.metrics.engagementRate && (
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {(account.metrics.engagementRate * 100).toFixed(1)}%
                </span>
                <span className="text-xs text-muted-foreground">
                  {language === 'ar' ? 'تفاعل' : 'engagement'}
                </span>
              </div>
            )}
          </div>
        )}

        {/* API Usage */}
        {account.rateLimits?.api && (
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-xs text-muted-foreground">
                {language === 'ar' ? 'استخدام API' : 'API Usage'}
              </span>
              <span className="text-xs font-medium">
                {account.rateLimits.api.current}/{account.rateLimits.api.limit}
              </span>
            </div>
            <Progress value={getApiUsagePercentage()} className="h-2" />
          </div>
        )}

        {/* Last Activity */}
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <Calendar className="h-3 w-3" />
          <span>
            {language === 'ar' ? 'آخر نشاط: ' : 'Last activity: '}
            {new Date(account.lastValidatedAt).toLocaleDateString()}
          </span>
        </div>

        {/* Account Groups */}
        {account.groups.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {account.groups.slice(0, 2).map((group) => (
              <Badge 
                key={group.id} 
                variant="secondary" 
                className="text-xs"
                style={{ backgroundColor: group.color + '20', color: group.color }}
              >
                {group.name}
              </Badge>
            ))}
            {account.groups.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{account.groups.length - 2}
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
