-- Fix database schema issues for eWasl publishing system
-- This addresses the "column social_accounts.status does not exist" error

-- Add connection_status column if it doesn't exist
ALTER TABLE social_accounts 
ADD COLUMN IF NOT EXISTS connection_status TEXT DEFAULT 'connected' 
CHECK (connection_status IN ('connected', 'expired', 'error', 'reconnecting', 'disconnected'));

-- Add status column if it doesn't exist (for account status)
ALTER TABLE social_accounts 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'ACTIVE' 
CHECK (status IN ('ACTIVE', 'INACTIVE', 'EXPIRED', 'ERROR'));

-- Update existing records to have proper connection status
UPDATE social_accounts 
SET connection_status = 'connected' 
WHERE connection_status IS NULL;

-- Update existing records to have proper status
UPDATE social_accounts 
SET status = 'ACTIVE' 
WHERE status IS NULL;

-- Add other missing columns that might be needed
ALTER TABLE social_accounts 
ADD COLUMN IF NOT EXISTS page_id TEXT,
ADD COLUMN IF NOT EXISTS page_access_token TEXT,
ADD COLUMN IF NOT EXISTS page_name TEXT,
ADD COLUMN IF NOT EXISTS instagram_business_account_id TEXT,
ADD COLUMN IF NOT EXISTS last_validated_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS permissions JSONB DEFAULT '[]';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_social_accounts_connection_status ON social_accounts(connection_status);
CREATE INDEX IF NOT EXISTS idx_social_accounts_status ON social_accounts(status);
CREATE INDEX IF NOT EXISTS idx_social_accounts_user_platform ON social_accounts(user_id, platform);

-- Add comments for documentation
COMMENT ON COLUMN social_accounts.connection_status IS 'OAuth connection status (connected, expired, error, etc.)';
COMMENT ON COLUMN social_accounts.status IS 'Account status (ACTIVE, INACTIVE, EXPIRED, ERROR)';
