#!/usr/bin/env node

/**
 * OAuth Integration Test Runner for eWasl
 * Comprehensive test execution with Arabic RTL validation
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com',
  testUser: {
    email: process.env.TEST_USER_EMAIL || '<EMAIL>',
    password: process.env.TEST_USER_PASSWORD || '111333Tt'
  },
  browsers: ['chromium', 'firefox', 'webkit'],
  headless: process.env.HEADLESS !== 'false',
  workers: process.env.CI ? 1 : 2
};

console.log('🚀 eWasl OAuth Integration Test Runner');
console.log('=====================================');

async function main() {
  try {
    // Validate environment
    await validateEnvironment();
    
    // Install Playwright browsers if needed
    await installBrowsers();
    
    // Run tests
    await runTests();
    
    // Generate reports
    await generateReports();
    
    console.log('✅ All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
    process.exit(1);
  }
}

/**
 * Validate test environment
 */
async function validateEnvironment() {
  console.log('🔍 Validating test environment...');
  
  const requiredEnvVars = [
    'NEXT_PUBLIC_APP_URL',
    'TEST_USER_EMAIL',
    'TEST_USER_PASSWORD'
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
  
  // Check if application is running
  try {
    const response = await fetch(TEST_CONFIG.baseUrl);
    if (!response.ok) {
      throw new Error(`Application not accessible at ${TEST_CONFIG.baseUrl}`);
    }
  } catch (error) {
    throw new Error(`Cannot reach application at ${TEST_CONFIG.baseUrl}: ${error.message}`);
  }
  
  console.log('✅ Environment validation passed');
}

/**
 * Install Playwright browsers
 */
async function installBrowsers() {
  console.log('📦 Installing Playwright browsers...');
  
  return new Promise((resolve, reject) => {
    const install = spawn('npx', ['playwright', 'install'], {
      stdio: 'inherit',
      shell: true
    });
    
    install.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Browsers installed successfully');
        resolve();
      } else {
        reject(new Error(`Browser installation failed with code ${code}`));
      }
    });
  });
}

/**
 * Run Playwright tests
 */
async function runTests() {
  console.log('🧪 Running OAuth integration tests...');
  
  const testArgs = [
    'playwright', 'test',
    '--config=playwright.config.ts',
    '--reporter=html,json,junit'
  ];
  
  // Add browser-specific arguments
  if (process.env.BROWSER) {
    testArgs.push(`--project=${process.env.BROWSER}`);
  }
  
  // Add headless mode
  if (TEST_CONFIG.headless) {
    testArgs.push('--headed=false');
  } else {
    testArgs.push('--headed=true');
  }
  
  // Add workers
  testArgs.push(`--workers=${TEST_CONFIG.workers}`);
  
  // Add specific test file if provided
  if (process.env.TEST_FILE) {
    testArgs.push(process.env.TEST_FILE);
  }
  
  return new Promise((resolve, reject) => {
    const test = spawn('npx', testArgs, {
      stdio: 'inherit',
      shell: true,
      env: {
        ...process.env,
        NEXT_PUBLIC_APP_URL: TEST_CONFIG.baseUrl,
        TEST_USER_EMAIL: TEST_CONFIG.testUser.email,
        TEST_USER_PASSWORD: TEST_CONFIG.testUser.password
      }
    });
    
    test.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Tests completed successfully');
        resolve();
      } else {
        console.warn(`⚠️ Tests completed with code ${code} (some tests may have failed)`);
        resolve(); // Don't fail the entire process for test failures
      }
    });
    
    test.on('error', (error) => {
      reject(new Error(`Test execution failed: ${error.message}`));
    });
  });
}

/**
 * Generate additional reports
 */
async function generateReports() {
  console.log('📊 Generating test reports...');
  
  const testResultsDir = path.join(process.cwd(), 'test-results');
  
  // Ensure test results directory exists
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
  }
  
  // Generate Arabic summary report
  await generateArabicSummary();
  
  // List generated artifacts
  listTestArtifacts();
  
  console.log('✅ Reports generated successfully');
}

/**
 * Generate Arabic summary report
 */
async function generateArabicSummary() {
  const testResultsDir = path.join(process.cwd(), 'test-results');
  const resultsFile = path.join(testResultsDir, 'results.json');
  
  if (!fs.existsSync(resultsFile)) {
    console.log('⚠️ No test results file found, skipping Arabic summary');
    return;
  }
  
  try {
    const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
    
    const summary = {
      timestamp: new Date().toISOString(),
      testRun: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0
      },
      categories: {
        oauth: 0,
        arabic: 0,
        performance: 0,
        mobile: 0
      },
      duration: results.stats?.duration || 0
    };
    
    // Process results
    results.suites?.forEach(suite => {
      suite.specs?.forEach(spec => {
        spec.tests?.forEach(test => {
          summary.testRun.total++;
          
          switch (test.status) {
            case 'passed':
              summary.testRun.passed++;
              break;
            case 'failed':
              summary.testRun.failed++;
              break;
            case 'skipped':
              summary.testRun.skipped++;
              break;
          }
          
          // Categorize tests
          const title = test.title?.toLowerCase() || '';
          if (title.includes('oauth') || title.includes('facebook')) {
            summary.categories.oauth++;
          }
          if (title.includes('arabic') || title.includes('rtl')) {
            summary.categories.arabic++;
          }
          if (title.includes('performance')) {
            summary.categories.performance++;
          }
          if (title.includes('mobile')) {
            summary.categories.mobile++;
          }
        });
      });
    });
    
    // Generate Arabic console output
    console.log('\n📋 ملخص نتائج الاختبارات:');
    console.log('========================');
    console.log(`📊 إجمالي الاختبارات: ${summary.testRun.total}`);
    console.log(`✅ اختبارات ناجحة: ${summary.testRun.passed}`);
    console.log(`❌ اختبارات فاشلة: ${summary.testRun.failed}`);
    console.log(`⏭️ اختبارات متجاهلة: ${summary.testRun.skipped}`);
    console.log(`⏱️ مدة التشغيل: ${(summary.duration / 1000).toFixed(2)} ثانية`);
    console.log(`📈 معدل النجاح: ${((summary.testRun.passed / summary.testRun.total) * 100).toFixed(1)}%`);
    
    console.log('\n🏷️ فئات الاختبارات:');
    console.log(`🔐 تكامل OAuth: ${summary.categories.oauth} اختبار`);
    console.log(`🔤 دعم العربية: ${summary.categories.arabic} اختبار`);
    console.log(`⚡ الأداء: ${summary.categories.performance} اختبار`);
    console.log(`📱 الجوال: ${summary.categories.mobile} اختبار`);
    
    // Save Arabic summary
    fs.writeFileSync(
      path.join(testResultsDir, 'arabic-summary.json'),
      JSON.stringify(summary, null, 2)
    );
    
  } catch (error) {
    console.warn('⚠️ Failed to generate Arabic summary:', error.message);
  }
}

/**
 * List test artifacts
 */
function listTestArtifacts() {
  const testResultsDir = path.join(process.cwd(), 'test-results');
  
  if (!fs.existsSync(testResultsDir)) {
    return;
  }
  
  const files = fs.readdirSync(testResultsDir);
  const artifacts = {
    reports: files.filter(f => f.endsWith('.html') || f.endsWith('.json')),
    screenshots: files.filter(f => f.endsWith('.png')),
    videos: files.filter(f => f.endsWith('.webm') || f.endsWith('.mp4')),
    traces: files.filter(f => f.endsWith('.zip'))
  };
  
  console.log('\n📁 ملفات الاختبار المُنتجة:');
  console.log('======================');
  
  if (artifacts.reports.length > 0) {
    console.log('📊 التقارير:');
    artifacts.reports.forEach(file => console.log(`   - ${file}`));
  }
  
  if (artifacts.screenshots.length > 0) {
    console.log('📸 لقطات الشاشة:');
    artifacts.screenshots.forEach(file => console.log(`   - ${file}`));
  }
  
  if (artifacts.videos.length > 0) {
    console.log('🎥 مقاطع الفيديو:');
    artifacts.videos.forEach(file => console.log(`   - ${file}`));
  }
  
  if (artifacts.traces.length > 0) {
    console.log('🔍 ملفات التتبع:');
    artifacts.traces.forEach(file => console.log(`   - ${file}`));
  }
  
  console.log(`\n📂 جميع الملفات في: ${testResultsDir}`);
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🚀 eWasl OAuth Integration Test Runner

Usage: node scripts/run-oauth-tests.js [options]

Environment Variables:
  NEXT_PUBLIC_APP_URL    Application URL (default: https://app.ewasl.com)
  TEST_USER_EMAIL        Test user email
  TEST_USER_PASSWORD     Test user password
  BROWSER               Specific browser to test (chromium, firefox, webkit)
  HEADLESS              Run in headless mode (default: true)
  TEST_FILE             Specific test file to run

Examples:
  node scripts/run-oauth-tests.js
  BROWSER=chromium node scripts/run-oauth-tests.js
  HEADLESS=false node scripts/run-oauth-tests.js
  TEST_FILE=oauth-integration.test.ts node scripts/run-oauth-tests.js
`);
  process.exit(0);
}

// Run the main function
main().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
