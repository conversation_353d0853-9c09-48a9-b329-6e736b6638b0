import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/logging/logger';
import { EnhancedError } from '@/lib/errors/error-codes';

/**
 * Instagram Token Refresh Endpoint
 * Refreshes expired Instagram access tokens
 * 
 * GET /api/oauth/instagram/refresh-token?accountId=123
 * POST /api/oauth/instagram/refresh-token { accountId: "123" }
 */
export async function GET(request: NextRequest) {
  return handleTokenRefresh(request);
}

export async function POST(request: NextRequest) {
  return handleTokenRefresh(request);
}

async function handleTokenRefresh(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    console.log('🔄 Instagram token refresh requested');

    // Get authenticated user
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      throw new EnhancedError('AUTH_REQUIRED');
    }

    // Get account ID from query params or request body
    let accountId: string | null = null;
    
    if (request.method === 'GET') {
      const { searchParams } = new URL(request.url);
      accountId = searchParams.get('accountId');
    } else {
      try {
        const body = await request.json();
        accountId = body.accountId;
      } catch (error) {
        // If no body, continue without accountId
      }
    }

    console.log('🔄 Token refresh request:', { userId: user.id, accountId });

    // If no specific account ID, refresh all expired Instagram accounts for user
    if (!accountId) {
      return await refreshAllExpiredTokens(user.id);
    }

    // Refresh specific account
    return await refreshSpecificToken(user.id, accountId);

  } catch (error) {
    console.error('❌ Instagram token refresh failed:', error);
    
    const errorMessage = error instanceof EnhancedError 
      ? error.arabicMessage 
      : 'فشل في تحديث رمز انستغرام';

    await logger.error('Instagram token refresh failed', error, {
      userId: request.headers.get('user-id'),
      duration: Date.now() - startTime
    });

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        message: 'Instagram token refresh failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Refresh all expired Instagram tokens for a user
 */
async function refreshAllExpiredTokens(userId: string) {
  try {
    console.log('🔄 Refreshing all expired Instagram tokens for user:', userId);

    const supabase = createClient();
    
    // Get all expired Instagram accounts for the user
    const { data: expiredAccounts, error: fetchError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', userId)
      .eq('platform', 'INSTAGRAM')
      .or('connection_status.eq.EXPIRED,expires_at.lt.' + new Date().toISOString());

    if (fetchError) {
      console.error('❌ Error fetching expired accounts:', fetchError);
      throw new Error('Failed to fetch expired accounts');
    }

    if (!expiredAccounts || expiredAccounts.length === 0) {
      console.log('✅ No expired Instagram accounts found');
      return NextResponse.json({
        success: true,
        message: 'No expired Instagram accounts found',
        arabicMessage: 'لا توجد حسابات انستغرام منتهية الصلاحية',
        refreshedAccounts: 0
      });
    }

    console.log(`🔄 Found ${expiredAccounts.length} expired Instagram accounts`);

    const refreshResults = [];
    let successCount = 0;
    let failureCount = 0;

    // Refresh each expired account
    for (const account of expiredAccounts) {
      try {
        const result = await refreshInstagramToken(account);
        refreshResults.push({
          accountId: account.id,
          platform_account_id: account.platform_account_id,
          success: result.success,
          message: result.message
        });

        if (result.success) {
          successCount++;
        } else {
          failureCount++;
        }
      } catch (error) {
        console.error(`❌ Failed to refresh token for account ${account.id}:`, error);
        refreshResults.push({
          accountId: account.id,
          platform_account_id: account.platform_account_id,
          success: false,
          message: error instanceof Error ? error.message : 'Unknown error'
        });
        failureCount++;
      }
    }

    console.log(`🔄 Token refresh completed: ${successCount} success, ${failureCount} failures`);

    return NextResponse.json({
      success: successCount > 0,
      message: `Refreshed ${successCount}/${expiredAccounts.length} Instagram tokens`,
      arabicMessage: `تم تحديث ${successCount}/${expiredAccounts.length} رمز انستغرام`,
      results: {
        totalAccounts: expiredAccounts.length,
        successCount,
        failureCount,
        details: refreshResults
      }
    });

  } catch (error) {
    console.error('❌ Error refreshing all expired tokens:', error);
    throw error;
  }
}

/**
 * Refresh a specific Instagram token
 */
async function refreshSpecificToken(userId: string, accountId: string) {
  try {
    console.log('🔄 Refreshing specific Instagram token:', { userId, accountId });

    const supabase = createClient();
    
    // Get the specific account
    const { data: account, error: fetchError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('user_id', userId)
      .eq('platform', 'INSTAGRAM')
      .single();

    if (fetchError || !account) {
      console.error('❌ Account not found:', fetchError);
      throw new EnhancedError('ACCOUNT_NOT_FOUND');
    }

    console.log('🔄 Found account:', { 
      id: account.id, 
      platform_account_id: account.platform_account_id,
      connection_status: account.connection_status 
    });

    // Refresh the token
    const result = await refreshInstagramToken(account);

    return NextResponse.json({
      success: result.success,
      message: result.message,
      arabicMessage: result.success 
        ? 'تم تحديث رمز انستغرام بنجاح' 
        : 'فشل في تحديث رمز انستغرام',
      account: {
        id: account.id,
        platform_account_id: account.platform_account_id,
        refreshed: result.success
      }
    });

  } catch (error) {
    console.error('❌ Error refreshing specific token:', error);
    throw error;
  }
}

/**
 * Refresh Instagram token using Facebook Graph API
 */
async function refreshInstagramToken(account: any) {
  try {
    console.log('🔄 Refreshing Instagram token for account:', account.platform_account_id);

    // Instagram Basic Display API token refresh
    const refreshUrl = `https://graph.instagram.com/refresh_access_token?grant_type=ig_refresh_token&access_token=${account.access_token}`;

    const response = await fetch(refreshUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('❌ Instagram API error:', data);
      throw new Error(data.error?.message || 'Instagram API error');
    }

    console.log('✅ Instagram token refreshed successfully');

    // Update the account with new token
    const supabase = createClient();
    const expiresAt = new Date(Date.now() + (data.expires_in * 1000));

    const { error: updateError } = await supabase
      .from('social_accounts')
      .update({
        access_token: data.access_token,
        expires_at: expiresAt.toISOString(),
        connection_status: 'CONNECTED',
        updated_at: new Date().toISOString()
      })
      .eq('id', account.id);

    if (updateError) {
      console.error('❌ Error updating account:', updateError);
      throw new Error('Failed to update account with new token');
    }

    console.log('✅ Account updated with new token');

    await logger.info('Instagram token refreshed successfully', {
      accountId: account.id,
      platform_account_id: account.platform_account_id,
      expiresAt: expiresAt.toISOString()
    });

    return {
      success: true,
      message: 'Instagram token refreshed successfully',
      newExpiresAt: expiresAt.toISOString()
    };

  } catch (error) {
    console.error('❌ Error refreshing Instagram token:', error);
    
    // Update account status to indicate refresh failure
    try {
      const supabase = createClient();
      await supabase
        .from('social_accounts')
        .update({
          connection_status: 'ERROR',
          error_message: error instanceof Error ? error.message : 'Token refresh failed',
          updated_at: new Date().toISOString()
        })
        .eq('id', account.id);
    } catch (updateError) {
      console.error('❌ Error updating account status:', updateError);
    }

    await logger.error('Instagram token refresh failed', error, {
      accountId: account.id,
      platform_account_id: account.platform_account_id
    });

    return {
      success: false,
      message: error instanceof Error ? error.message : 'Token refresh failed'
    };
  }
}
