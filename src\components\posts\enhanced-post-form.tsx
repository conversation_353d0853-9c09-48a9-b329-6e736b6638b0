"use client";

import { useState, useEffect } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { Calendar as CalendarIcon, Loader2, Upload, Image, Video, Send, Clock, Save } from "lucide-react";
import { format } from "date-fns";
import { arSA } from "date-fns/locale";

import { Button } from "@/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";

// Form validation schema
const formSchema = z.object({
  content: z.string().min(1, {
    message: "المحتوى مطلوب",
  }),
  mediaUrls: z.array(z.string().url()).optional().default([]),
  status: z.enum(["DRAFT", "SCHEDULED", "PUBLISHED"]),
  scheduledAt: z.date().optional(),
  platforms: z.array(z.string()).min(1, {
    message: "يرجى اختيار منصة واحدة على الأقل",
  }),
  timezone: z.string().default("Asia/Riyadh"),
});

interface PostFormProps {
  initialData?: any;
}

export function EnhancedPostForm({ initialData }: PostFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    initialData?.scheduledAt ? new Date(initialData.scheduledAt) : undefined
  );
  const [uploadedMedia, setUploadedMedia] = useState<any[]>([]);
  const [connectedAccounts, setConnectedAccounts] = useState<any[]>([]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content: initialData?.content || "",
      mediaUrls: initialData?.mediaUrls || [],
      status: initialData?.status || "DRAFT",
      scheduledAt: initialData?.scheduledAt ? new Date(initialData.scheduledAt) : undefined,
      platforms: initialData?.platforms || [],
      timezone: initialData?.timezone || "Asia/Riyadh",
    },
  });

  const status = form.watch("status");

  // Fetch connected accounts on component mount
  useEffect(() => {
    const fetchConnectedAccounts = async () => {
      try {
        console.log('🔄 Fetching connected accounts...');
        const response = await fetch('/api/social/accounts');
        if (response.ok) {
          const data = await response.json();
          console.log('✅ Connected accounts loaded:', data);
          setConnectedAccounts(data.data || []);
        } else {
          console.error('❌ Failed to fetch connected accounts:', response.status);
        }
      } catch (error) {
        console.error('❌ Error fetching connected accounts:', error);
      }
    };

    fetchConnectedAccounts();
  }, []);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Submitting post:', values);

      // Validate required fields
      if (!values.content.trim()) {
        throw new Error('يرجى إدخال محتوى المنشور');
      }

      if (values.platforms.length === 0) {
        throw new Error('يرجى اختيار منصة واحدة على الأقل');
      }

      if (values.status === 'SCHEDULED' && !values.scheduledAt) {
        throw new Error('يرجى تحديد وقت الجدولة');
      }

      // Prepare API payload
      const payload = {
        content: values.content,
        media_urls: values.mediaUrls || [],
        status: values.status,
        scheduled_at: values.scheduledAt ? values.scheduledAt.toISOString() : undefined,
        social_account_ids: values.platforms, // platforms field contains account IDs now
        timezone: values.timezone,
      };

      console.log('API Payload:', payload);

      // Submit to API
      const response = await fetch('/api/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حفظ المنشور');
      }

      const result = await response.json();
      console.log('Post created successfully:', result);

      // Show success message
      if (values.status === 'PUBLISHED') {
        toast.success('تم نشر المنشور بنجاح!');
      } else if (values.status === 'SCHEDULED') {
        toast.success('تم جدولة المنشور بنجاح!');
      } else {
        toast.success('تم حفظ المنشور كمسودة!');
      }

      // Redirect to posts page
      router.push("/posts");
    } catch (error: any) {
      console.error('Post submission error:', error);
      setError(error.message || "حدث خطأ أثناء حفظ المنشور");
      toast.error(error.message || "حدث خطأ أثناء حفظ المنشور");
    } finally {
      setIsLoading(false);
    }
  }

  // Platform labels for display
  const platformLabels: Record<string, string> = {
    FACEBOOK: "فيسبوك",
    INSTAGRAM: "انستغرام", 
    LINKEDIN: "لينكد إن",
    TWITTER: "تويتر",
    X: "تويتر",
    TIKTOK: "تيك توك",
    SNAPCHAT: "سناب شات"
  };

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8" dir="rtl">
        <Card>
          <CardContent className="pt-6">
            <Tabs defaultValue="content" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="content">المحتوى</TabsTrigger>
                <TabsTrigger value="media">الوسائط</TabsTrigger>
                <TabsTrigger value="platforms">المنصات</TabsTrigger>
                <TabsTrigger value="schedule">الجدولة</TabsTrigger>
              </TabsList>

              {/* Content Tab */}
              <TabsContent value="content" className="space-y-6">
                <FormField
                  control={form.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>محتوى المنشور</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="اكتب محتوى المنشور هنا..."
                          className="min-h-[200px] text-right"
                          dir="rtl"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        اكتب محتوى جذاب ومناسب لجمهورك
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              {/* Media Tab */}
              <TabsContent value="media" className="space-y-6">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-lg font-medium text-gray-900 mb-2">رفع الوسائط</p>
                  <p className="text-gray-500 mb-4">اسحب وأفلت الملفات هنا أو انقر للاختيار</p>
                  <Button type="button" variant="outline">
                    <Image className="w-4 h-4 mr-2" />
                    اختيار الملفات
                  </Button>
                </div>
              </TabsContent>

              {/* Platforms Tab */}
              <TabsContent value="platforms" className="space-y-6">
                <FormField
                  control={form.control}
                  name="platforms"
                  render={() => (
                    <FormItem>
                      <FormLabel>اختر الحسابات المتصلة</FormLabel>
                      {connectedAccounts.length === 0 ? (
                        <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                          <p className="text-gray-500 mb-4">لا توجد حسابات متصلة</p>
                          <p className="text-sm text-gray-400 mb-4">يرجى ربط حساب واحد على الأقل لتتمكن من النشر</p>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => window.open('/social', '_blank')}
                          >
                            ربط حساب جديد
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {connectedAccounts.map((account) => (
                            <FormField
                              key={account.id}
                              control={form.control}
                              name="platforms"
                              render={({ field }) => {
                                const isSelected = field.value?.includes(account.id);
                                const platformLabel = platformLabels[account.platform] || account.platform;
                                const statusColor = account.connection_status === 'connected' 
                                  ? 'text-green-600 bg-green-50' 
                                  : 'text-red-600 bg-red-50';
                                const statusText = account.connection_status === 'connected' ? 'متصل' : 'غير متصل';
                                
                                return (
                                  <FormItem key={account.id}>
                                    <FormControl>
                                      <div
                                        className={`flex items-center justify-between p-4 rounded-lg border-2 cursor-pointer transition-all ${
                                          isSelected
                                            ? "border-blue-500 bg-blue-50"
                                            : "border-gray-200 hover:border-gray-300"
                                        } ${account.connection_status !== 'connected' ? 'opacity-60' : ''}`}
                                        onClick={() => {
                                          if (account.connection_status === 'connected') {
                                            const currentValue = field.value || [];
                                            const newValue = isSelected
                                              ? currentValue.filter((val: string) => val !== account.id)
                                              : [...currentValue, account.id];
                                            field.onChange(newValue);
                                          }
                                        }}
                                      >
                                        <div className="flex items-center gap-3">
                                          <div className="flex flex-col">
                                            <span className="font-medium">{account.account_name}</span>
                                            <span className="text-sm text-gray-500">{platformLabel}</span>
                                          </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <span className={`px-2 py-1 rounded text-xs ${statusColor}`}>
                                            {statusText}
                                          </span>
                                          <Checkbox
                                            checked={isSelected}
                                            disabled={account.connection_status !== 'connected'}
                                            onChange={() => {}}
                                          />
                                        </div>
                                      </div>
                                    </FormControl>
                                  </FormItem>
                                );
                              }}
                            />
                          ))}
                        </div>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              {/* Schedule Tab */}
              <TabsContent value="schedule" className="space-y-6">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>حالة المنشور</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر حالة المنشور" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="DRAFT">مسودة</SelectItem>
                          <SelectItem value="PUBLISHED">نشر فوري</SelectItem>
                          <SelectItem value="SCHEDULED">جدولة</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {status === "SCHEDULED" && (
                  <FormField
                    control={form.control}
                    name="scheduledAt"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>تاريخ ووقت النشر</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-right font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP", { locale: arSA })
                                ) : (
                                  <span>اختر التاريخ</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date() || date < new Date("1900-01-01")
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <p className="text-red-600 text-center">{error}</p>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex gap-4 justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/posts")}
          >
            إلغاء
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="min-w-[120px]"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                جاري الحفظ...
              </>
            ) : status === "PUBLISHED" ? (
              <>
                <Send className="w-4 h-4 mr-2" />
                نشر الآن
              </>
            ) : status === "SCHEDULED" ? (
              <>
                <Clock className="w-4 h-4 mr-2" />
                جدولة
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                حفظ كمسودة
              </>
            )}
          </Button>
        </div>
      </form>
    </FormProvider>
  );
}