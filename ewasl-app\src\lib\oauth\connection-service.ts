/**
 * OAuth Connection Service
 * Handles OAuth connections, token storage, and management
 */

import { createClient } from '@/lib/supabase/client';
import { OAuthTokens, UserProfile, getOAuthProvider } from './providers';
import crypto from 'crypto';

export interface SocialConnection {
  id: string;
  userId: string;
  platform: string;
  accountId: string;
  accountName: string;
  username?: string;
  avatar?: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface OAuthState {
  state: string;
  platform: string;
  userId: string;
  redirectUri: string;
  codeVerifier?: string;
  expiresAt: Date;
}

export class OAuthConnectionService {
  private supabase = createClient();

  /**
   * Generate OAuth state for CSRF protection
   */
  generateState(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Generate PKCE code verifier and challenge for OAuth 2.0
   */
  generatePKCE(): { codeVerifier: string; codeChallenge: string } {
    const codeVerifier = crypto.randomBytes(32).toString('base64url');
    const codeChallenge = crypto
      .createHash('sha256')
      .update(codeVerifier)
      .digest('base64url');
    
    return { codeVerifier, codeChallenge };
  }

  /**
   * Store OAuth state for verification
   */
  async storeOAuthState(
    userId: string,
    platform: string,
    state: string,
    redirectUri: string,
    codeVerifier?: string
  ): Promise<void> {
    const { error } = await this.supabase
      .from('oauth_states')
      .insert({
        user_id: userId,
        platform: platform.toLowerCase(),
        state_token: state,
        redirect_uri: redirectUri,
        code_verifier: codeVerifier,
        expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes
      });

    if (error) {
      throw new Error(`Failed to store OAuth state: ${error.message}`);
    }
  }

  /**
   * Verify OAuth state and retrieve stored data
   */
  async verifyOAuthState(
    state: string,
    platform: string
  ): Promise<OAuthState | null> {
    const { data, error } = await this.supabase
      .from('oauth_states')
      .select('*')
      .eq('state_token', state)
      .eq('platform', platform.toLowerCase())
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !data) {
      return null;
    }

    // Clean up used state
    await this.supabase
      .from('oauth_states')
      .delete()
      .eq('state_token', state);

    return {
      state: data.state_token,
      platform: data.platform,
      userId: data.user_id,
      redirectUri: data.redirect_uri,
      codeVerifier: data.code_verifier,
      expiresAt: new Date(data.expires_at),
    };
  }

  /**
   * Store social media connection
   */
  async storeConnection(
    userId: string,
    platform: string,
    tokens: OAuthTokens,
    profile: UserProfile
  ): Promise<SocialConnection> {
    const connectionData = {
      user_id: userId,
      platform: platform.toUpperCase(),
      account_id: profile.id,
      account_name: profile.name,
      username: profile.username,
      avatar_url: profile.avatar,
      access_token: tokens.accessToken,
      refresh_token: tokens.refreshToken,
      expires_at: tokens.expiresAt?.toISOString(),
      is_active: true,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await this.supabase
      .from('social_accounts')
      .upsert(connectionData, {
        onConflict: 'user_id,platform,account_id',
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to store connection: ${error.message}`);
    }

    return this.mapToSocialConnection(data);
  }

  /**
   * Get user's social connections
   */
  async getUserConnections(userId: string): Promise<SocialConnection[]> {
    const { data, error } = await this.supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to get connections: ${error.message}`);
    }

    return data.map(this.mapToSocialConnection);
  }

  /**
   * Get specific connection
   */
  async getConnection(
    userId: string,
    platform: string,
    accountId?: string
  ): Promise<SocialConnection | null> {
    let query = this.supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', userId)
      .eq('platform', platform.toUpperCase())
      .eq('is_active', true);

    if (accountId) {
      query = query.eq('account_id', accountId);
    }

    const { data, error } = await query.single();

    if (error || !data) {
      return null;
    }

    return this.mapToSocialConnection(data);
  }

  /**
   * Disconnect social account
   */
  async disconnectAccount(
    userId: string,
    platform: string,
    accountId: string
  ): Promise<boolean> {
    const { error } = await this.supabase
      .from('social_accounts')
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('user_id', userId)
      .eq('platform', platform.toUpperCase())
      .eq('account_id', accountId);

    return !error;
  }

  /**
   * Refresh access token
   */
  async refreshToken(
    userId: string,
    platform: string,
    accountId: string
  ): Promise<boolean> {
    const connection = await this.getConnection(userId, platform, accountId);
    if (!connection || !connection.refreshToken) {
      return false;
    }

    const provider = getOAuthProvider(platform);
    if (!provider) {
      return false;
    }

    try {
      const response = await fetch(provider.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: new URLSearchParams({
          client_id: provider.clientId,
          client_secret: provider.clientSecret,
          refresh_token: connection.refreshToken,
          grant_type: 'refresh_token',
        }).toString(),
      });

      if (!response.ok) {
        return false;
      }

      const data = await response.json();
      
      const { error } = await this.supabase
        .from('social_accounts')
        .update({
          access_token: data.access_token,
          refresh_token: data.refresh_token || connection.refreshToken,
          expires_at: data.expires_in 
            ? new Date(Date.now() + data.expires_in * 1000).toISOString()
            : connection.expiresAt?.toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId)
        .eq('platform', platform.toUpperCase())
        .eq('account_id', accountId);

      return !error;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }

  /**
   * Check if token needs refresh
   */
  isTokenExpired(connection: SocialConnection): boolean {
    if (!connection.expiresAt) {
      return false; // No expiration set
    }
    
    // Consider token expired if it expires within 5 minutes
    const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);
    return connection.expiresAt <= fiveMinutesFromNow;
  }

  /**
   * Get valid access token (refresh if needed)
   */
  async getValidToken(
    userId: string,
    platform: string,
    accountId: string
  ): Promise<string | null> {
    const connection = await this.getConnection(userId, platform, accountId);
    if (!connection) {
      return null;
    }

    if (this.isTokenExpired(connection)) {
      const refreshed = await this.refreshToken(userId, platform, accountId);
      if (!refreshed) {
        return null;
      }
      
      // Get updated connection
      const updatedConnection = await this.getConnection(userId, platform, accountId);
      return updatedConnection?.accessToken || null;
    }

    return connection.accessToken;
  }

  /**
   * Map database record to SocialConnection
   */
  private mapToSocialConnection(data: any): SocialConnection {
    return {
      id: data.id,
      userId: data.user_id,
      platform: data.platform,
      accountId: data.account_id,
      accountName: data.account_name,
      username: data.username,
      avatar: data.avatar_url,
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      expiresAt: data.expires_at ? new Date(data.expires_at) : undefined,
      isActive: data.is_active,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }
}
