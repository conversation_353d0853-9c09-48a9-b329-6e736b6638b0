/**
 * OAuth States Debug Endpoint
 * Provides information about current OAuth states for debugging
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getAuthenticatedUser } from '@/lib/auth/api-auth'

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user from session
    const { user, supabase } = await getAuthenticatedUser(request);

    if (!user?.id) {
      return NextResponse.json(
        { error: 'المستخدم غير مصرح له' }, // User not authenticated in Arabic
        { status: 401 }
      )
    }

    const userId = user.id;

    // Get OAuth states for the current user
    const { data: states, error: statesError } = await supabase
      .from('oauth_states')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (statesError) {
      console.error('Error fetching OAuth states:', statesError)
      return NextResponse.json(
        { error: 'فشل في جلب حالات OAuth' }, // Failed to fetch OAuth states in Arabic
        { status: 500 }
      )
    }

    // Get social accounts for the current user
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('id, platform, account_name, connection_status, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (accountsError) {
      console.error('Error fetching social accounts:', accountsError)
    }

    // Clean up expired OAuth states
    const { error: cleanupError } = await supabase
      .from('oauth_states')
      .delete()
      .lt('expires_at', new Date().toISOString())

    if (cleanupError) {
      console.warn('Failed to cleanup expired OAuth states:', cleanupError)
    }

    return NextResponse.json({
      success: true,
      user: {
        id: userId,
        email: user.email
      },
      oauth_states: {
        total: states.length,
        active: states.filter(s => new Date(s.expires_at) > new Date()).length,
        expired: states.filter(s => new Date(s.expires_at) <= new Date()).length,
        states: states.map(s => ({
          id: s.id,
          platform: s.platform,
          created_at: s.created_at,
          expires_at: s.expires_at,
          expired: new Date(s.expires_at) <= new Date()
        }))
      },
      social_accounts: {
        total: accounts?.length || 0,
        by_platform: accounts?.reduce((acc, account) => {
          acc[account.platform] = (acc[account.platform] || 0) + 1;
          return acc;
        }, {}) || {},
        accounts: accounts?.map(a => ({
          id: a.id,
          platform: a.platform,
          account_name: a.account_name,
          connection_status: a.connection_status,
          created_at: a.created_at
        })) || []
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('OAuth states endpoint error:', error)
    
    return NextResponse.json(
      { 
        error: 'خطأ في الخادم الداخلي', // Internal server error in Arabic
        message: error instanceof Error ? error.message : 'خطأ غير معروف' // Unknown error in Arabic
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
