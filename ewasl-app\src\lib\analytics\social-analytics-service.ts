/**
 * Social Media Analytics Service
 * Fetches real-time analytics data from social media platforms
 */

import { OAuthConnectionService } from '@/lib/oauth/connection-service';

export interface AnalyticsMetrics {
  platform: string;
  accountId: string;
  accountName: string;
  metrics: {
    followers: number;
    following: number;
    posts: number;
    engagement: {
      likes: number;
      comments: number;
      shares: number;
      saves?: number;
      clicks?: number;
    };
    reach: {
      impressions: number;
      reach: number;
      profileViews?: number;
    };
    growth: {
      followersChange: number;
      engagementRate: number;
      averageLikes: number;
      averageComments: number;
    };
  };
  lastUpdated: Date;
}

export interface PostAnalytics {
  postId: string;
  platform: string;
  content: string;
  publishedAt: Date;
  metrics: {
    likes: number;
    comments: number;
    shares: number;
    saves?: number;
    clicks?: number;
    impressions: number;
    reach: number;
  };
  engagement: {
    rate: number;
    score: number;
  };
}

export interface PlatformInsights {
  platform: string;
  bestPostingTimes: Array<{
    day: string;
    hour: number;
    engagementRate: number;
  }>;
  topHashtags: Array<{
    hashtag: string;
    usage: number;
    engagement: number;
  }>;
  audienceInsights: {
    demographics: {
      ageGroups: Record<string, number>;
      gender: Record<string, number>;
      locations: Record<string, number>;
    };
    interests: Array<{
      category: string;
      percentage: number;
    }>;
  };
}

export class SocialAnalyticsService {
  private connectionService = new OAuthConnectionService();

  /**
   * Get analytics for all connected accounts
   */
  async getAllAccountsAnalytics(userId: string): Promise<AnalyticsMetrics[]> {
    const connections = await this.connectionService.getUserConnections(userId);
    const analytics: AnalyticsMetrics[] = [];

    for (const connection of connections) {
      try {
        const metrics = await this.getAccountAnalytics(
          userId,
          connection.platform,
          connection.accountId
        );
        if (metrics) {
          analytics.push(metrics);
        }
      } catch (error) {
        console.error(`Failed to get analytics for ${connection.platform}:`, error);
      }
    }

    return analytics;
  }

  /**
   * Get analytics for a specific account
   */
  async getAccountAnalytics(
    userId: string,
    platform: string,
    accountId: string
  ): Promise<AnalyticsMetrics | null> {
    const token = await this.connectionService.getValidToken(userId, platform, accountId);
    if (!token) {
      throw new Error('No valid token available');
    }

    switch (platform.toLowerCase()) {
      case 'facebook':
        return this.getFacebookAnalytics(token, accountId);
      case 'instagram':
        return this.getInstagramAnalytics(token, accountId);
      case 'twitter':
        return this.getTwitterAnalytics(token, accountId);
      case 'linkedin':
        return this.getLinkedInAnalytics(token, accountId);
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Get Facebook Page analytics
   */
  private async getFacebookAnalytics(
    accessToken: string,
    pageId: string
  ): Promise<AnalyticsMetrics> {
    const baseUrl = 'https://graph.facebook.com/v19.0';
    
    // Get page info
    const pageResponse = await fetch(
      `${baseUrl}/${pageId}?fields=name,followers_count,fan_count&access_token=${accessToken}`
    );
    const pageData = await pageResponse.json();

    // Get page insights
    const insightsResponse = await fetch(
      `${baseUrl}/${pageId}/insights?metric=page_impressions,page_reach,page_engaged_users,page_post_engagements&period=day&since=${this.getDateDaysAgo(30)}&access_token=${accessToken}`
    );
    const insightsData = await insightsResponse.json();

    // Get recent posts
    const postsResponse = await fetch(
      `${baseUrl}/${pageId}/posts?fields=id,message,created_time,likes.summary(true),comments.summary(true),shares&limit=10&access_token=${accessToken}`
    );
    const postsData = await postsResponse.json();

    // Calculate metrics
    const posts = postsData.data || [];
    const totalLikes = posts.reduce((sum: number, post: any) => sum + (post.likes?.summary?.total_count || 0), 0);
    const totalComments = posts.reduce((sum: number, post: any) => sum + (post.comments?.summary?.total_count || 0), 0);
    const totalShares = posts.reduce((sum: number, post: any) => sum + (post.shares?.count || 0), 0);

    const impressions = this.getInsightValue(insightsData.data, 'page_impressions');
    const reach = this.getInsightValue(insightsData.data, 'page_reach');
    const engagedUsers = this.getInsightValue(insightsData.data, 'page_engaged_users');

    return {
      platform: 'FACEBOOK',
      accountId: pageId,
      accountName: pageData.name,
      metrics: {
        followers: pageData.fan_count || 0,
        following: 0,
        posts: posts.length,
        engagement: {
          likes: totalLikes,
          comments: totalComments,
          shares: totalShares,
        },
        reach: {
          impressions,
          reach,
        },
        growth: {
          followersChange: 0, // Would need historical data
          engagementRate: engagedUsers > 0 ? (engagedUsers / reach) * 100 : 0,
          averageLikes: posts.length > 0 ? totalLikes / posts.length : 0,
          averageComments: posts.length > 0 ? totalComments / posts.length : 0,
        },
      },
      lastUpdated: new Date(),
    };
  }

  /**
   * Get Instagram Business Account analytics
   */
  private async getInstagramAnalytics(
    accessToken: string,
    accountId: string
  ): Promise<AnalyticsMetrics> {
    const baseUrl = 'https://graph.facebook.com/v19.0';
    
    // Get account info
    const accountResponse = await fetch(
      `${baseUrl}/${accountId}?fields=name,followers_count,follows_count,media_count&access_token=${accessToken}`
    );
    const accountData = await accountResponse.json();

    // Get account insights
    const insightsResponse = await fetch(
      `${baseUrl}/${accountId}/insights?metric=impressions,reach,profile_views&period=day&since=${this.getDateDaysAgo(30)}&access_token=${accessToken}`
    );
    const insightsData = await insightsResponse.json();

    // Get recent media
    const mediaResponse = await fetch(
      `${baseUrl}/${accountId}/media?fields=id,caption,timestamp,like_count,comments_count,media_type&limit=10&access_token=${accessToken}`
    );
    const mediaData = await mediaResponse.json();

    const media = mediaData.data || [];
    const totalLikes = media.reduce((sum: number, post: any) => sum + (post.like_count || 0), 0);
    const totalComments = media.reduce((sum: number, post: any) => sum + (post.comments_count || 0), 0);

    const impressions = this.getInsightValue(insightsData.data, 'impressions');
    const reach = this.getInsightValue(insightsData.data, 'reach');
    const profileViews = this.getInsightValue(insightsData.data, 'profile_views');

    return {
      platform: 'INSTAGRAM',
      accountId,
      accountName: accountData.name,
      metrics: {
        followers: accountData.followers_count || 0,
        following: accountData.follows_count || 0,
        posts: accountData.media_count || 0,
        engagement: {
          likes: totalLikes,
          comments: totalComments,
          shares: 0, // Instagram doesn't have shares
          saves: 0, // Would need additional API call
        },
        reach: {
          impressions,
          reach,
          profileViews,
        },
        growth: {
          followersChange: 0,
          engagementRate: reach > 0 ? ((totalLikes + totalComments) / reach) * 100 : 0,
          averageLikes: media.length > 0 ? totalLikes / media.length : 0,
          averageComments: media.length > 0 ? totalComments / media.length : 0,
        },
      },
      lastUpdated: new Date(),
    };
  }

  /**
   * Get Twitter analytics
   */
  private async getTwitterAnalytics(
    accessToken: string,
    userId: string
  ): Promise<AnalyticsMetrics> {
    const baseUrl = 'https://api.twitter.com/2';
    
    // Get user info
    const userResponse = await fetch(
      `${baseUrl}/users/${userId}?user.fields=public_metrics,name,username`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      }
    );
    const userData = await userResponse.json();

    // Get recent tweets
    const tweetsResponse = await fetch(
      `${baseUrl}/users/${userId}/tweets?tweet.fields=public_metrics,created_at&max_results=10`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      }
    );
    const tweetsData = await tweetsResponse.json();

    const tweets = tweetsData.data || [];
    const totalLikes = tweets.reduce((sum: number, tweet: any) => sum + (tweet.public_metrics?.like_count || 0), 0);
    const totalRetweets = tweets.reduce((sum: number, tweet: any) => sum + (tweet.public_metrics?.retweet_count || 0), 0);
    const totalReplies = tweets.reduce((sum: number, tweet: any) => sum + (tweet.public_metrics?.reply_count || 0), 0);
    const totalImpressions = tweets.reduce((sum: number, tweet: any) => sum + (tweet.public_metrics?.impression_count || 0), 0);

    const user = userData.data;
    const metrics = user.public_metrics;

    return {
      platform: 'TWITTER',
      accountId: userId,
      accountName: user.name,
      metrics: {
        followers: metrics.followers_count,
        following: metrics.following_count,
        posts: metrics.tweet_count,
        engagement: {
          likes: totalLikes,
          comments: totalReplies,
          shares: totalRetweets,
        },
        reach: {
          impressions: totalImpressions,
          reach: totalImpressions, // Twitter doesn't separate reach from impressions
        },
        growth: {
          followersChange: 0,
          engagementRate: totalImpressions > 0 ? ((totalLikes + totalRetweets + totalReplies) / totalImpressions) * 100 : 0,
          averageLikes: tweets.length > 0 ? totalLikes / tweets.length : 0,
          averageComments: tweets.length > 0 ? totalReplies / tweets.length : 0,
        },
      },
      lastUpdated: new Date(),
    };
  }

  /**
   * Get LinkedIn analytics
   */
  private async getLinkedInAnalytics(
    accessToken: string,
    personId: string
  ): Promise<AnalyticsMetrics> {
    // LinkedIn API is more complex and requires organization pages for detailed analytics
    // This is a simplified version for personal profiles
    
    const baseUrl = 'https://api.linkedin.com/v2';
    
    // Get person info
    const personResponse = await fetch(
      `${baseUrl}/people/${personId}?projection=(id,firstName,lastName,headline)`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      }
    );
    const personData = await personResponse.json();

    // LinkedIn personal profiles have limited analytics access
    // Most analytics require organization pages
    
    return {
      platform: 'LINKEDIN',
      accountId: personId,
      accountName: `${personData.firstName?.localized?.en_US || ''} ${personData.lastName?.localized?.en_US || ''}`.trim(),
      metrics: {
        followers: 0, // Requires additional permissions
        following: 0,
        posts: 0,
        engagement: {
          likes: 0,
          comments: 0,
          shares: 0,
        },
        reach: {
          impressions: 0,
          reach: 0,
        },
        growth: {
          followersChange: 0,
          engagementRate: 0,
          averageLikes: 0,
          averageComments: 0,
        },
      },
      lastUpdated: new Date(),
    };
  }

  /**
   * Helper function to get insight value from Facebook/Instagram insights
   */
  private getInsightValue(insights: any[], metric: string): number {
    const insight = insights?.find((i: any) => i.name === metric);
    if (!insight || !insight.values || insight.values.length === 0) {
      return 0;
    }
    
    // Get the most recent value
    const latestValue = insight.values[insight.values.length - 1];
    return latestValue.value || 0;
  }

  /**
   * Helper function to get date N days ago in ISO format
   */
  private getDateDaysAgo(days: number): string {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date.toISOString().split('T')[0];
  }

  /**
   * Get post-specific analytics
   */
  async getPostAnalytics(
    userId: string,
    platform: string,
    postId: string
  ): Promise<PostAnalytics | null> {
    const token = await this.connectionService.getValidToken(userId, platform, postId.split('_')[0]);
    if (!token) {
      return null;
    }

    switch (platform.toLowerCase()) {
      case 'facebook':
        return this.getFacebookPostAnalytics(token, postId);
      case 'instagram':
        return this.getInstagramPostAnalytics(token, postId);
      case 'twitter':
        return this.getTwitterPostAnalytics(token, postId);
      default:
        return null;
    }
  }

  /**
   * Get Facebook post analytics
   */
  private async getFacebookPostAnalytics(
    accessToken: string,
    postId: string
  ): Promise<PostAnalytics> {
    const baseUrl = 'https://graph.facebook.com/v19.0';
    
    const response = await fetch(
      `${baseUrl}/${postId}?fields=message,created_time,likes.summary(true),comments.summary(true),shares,insights.metric(post_impressions,post_reach)&access_token=${accessToken}`
    );
    const data = await response.json();

    const likes = data.likes?.summary?.total_count || 0;
    const comments = data.comments?.summary?.total_count || 0;
    const shares = data.shares?.count || 0;
    const impressions = this.getInsightValue(data.insights?.data, 'post_impressions');
    const reach = this.getInsightValue(data.insights?.data, 'post_reach');

    return {
      postId,
      platform: 'FACEBOOK',
      content: data.message || '',
      publishedAt: new Date(data.created_time),
      metrics: {
        likes,
        comments,
        shares,
        impressions,
        reach,
        clicks: 0,
      },
      engagement: {
        rate: reach > 0 ? ((likes + comments + shares) / reach) * 100 : 0,
        score: likes + comments * 2 + shares * 3,
      },
    };
  }

  /**
   * Get Instagram post analytics
   */
  private async getInstagramPostAnalytics(
    accessToken: string,
    mediaId: string
  ): Promise<PostAnalytics> {
    const baseUrl = 'https://graph.facebook.com/v19.0';
    
    const response = await fetch(
      `${baseUrl}/${mediaId}?fields=caption,timestamp,like_count,comments_count,insights.metric(impressions,reach,saves)&access_token=${accessToken}`
    );
    const data = await response.json();

    const likes = data.like_count || 0;
    const comments = data.comments_count || 0;
    const impressions = this.getInsightValue(data.insights?.data, 'impressions');
    const reach = this.getInsightValue(data.insights?.data, 'reach');
    const saves = this.getInsightValue(data.insights?.data, 'saves');

    return {
      postId: mediaId,
      platform: 'INSTAGRAM',
      content: data.caption || '',
      publishedAt: new Date(data.timestamp),
      metrics: {
        likes,
        comments,
        shares: 0,
        saves,
        impressions,
        reach,
        clicks: 0,
      },
      engagement: {
        rate: reach > 0 ? ((likes + comments + saves) / reach) * 100 : 0,
        score: likes + comments * 2 + saves * 1.5,
      },
    };
  }

  /**
   * Get Twitter post analytics
   */
  private async getTwitterPostAnalytics(
    accessToken: string,
    tweetId: string
  ): Promise<PostAnalytics> {
    const baseUrl = 'https://api.twitter.com/2';
    
    const response = await fetch(
      `${baseUrl}/tweets/${tweetId}?tweet.fields=public_metrics,created_at,text`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      }
    );
    const data = await response.json();

    const tweet = data.data;
    const metrics = tweet.public_metrics;

    return {
      postId: tweetId,
      platform: 'TWITTER',
      content: tweet.text,
      publishedAt: new Date(tweet.created_at),
      metrics: {
        likes: metrics.like_count,
        comments: metrics.reply_count,
        shares: metrics.retweet_count,
        impressions: metrics.impression_count,
        reach: metrics.impression_count,
        clicks: 0,
      },
      engagement: {
        rate: metrics.impression_count > 0 ? ((metrics.like_count + metrics.reply_count + metrics.retweet_count) / metrics.impression_count) * 100 : 0,
        score: metrics.like_count + metrics.reply_count * 2 + metrics.retweet_count * 3,
      },
    };
  }
}
