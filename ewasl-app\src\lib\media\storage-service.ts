/**
 * Media Storage Service
 * Handles file uploads, compression, and management with Supabase Storage
 */

import { createClient } from '@/lib/supabase/client';
import { v4 as uuidv4 } from 'uuid';

export interface MediaFile {
  id: string;
  fileName: string;
  originalName: string;
  fileType: string;
  fileSize: number;
  publicUrl: string;
  cdnUrl: string;
  userId: string;
  folder?: string;
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    compressed?: boolean;
    originalSize?: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface UploadOptions {
  folder?: string;
  compress?: boolean;
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  generateThumbnail?: boolean;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export class MediaStorageService {
  private supabase = createClient();
  private bucketName = 'media'; // Fixed: Use consistent bucket name

  /**
   * Initialize storage bucket if it doesn't exist
   */
  async initializeBucket(): Promise<void> {
    try {
      console.log('🪣 Initializing storage bucket...');
      const { data: buckets, error: listError } = await this.supabase.storage.listBuckets();

      if (listError) {
        console.error('❌ Failed to list buckets:', listError);
        throw new Error(`Failed to list storage buckets: ${listError.message}`);
      }

      // Check if our primary bucket exists
      const bucketExists = buckets?.some(bucket => bucket.name === this.bucketName);

      // Also check for legacy bucket name for backward compatibility
      const legacyBucketExists = buckets?.some(bucket => bucket.name === 'media-files');

      console.log('📊 Bucket status:', {
        primary: this.bucketName,
        exists: bucketExists,
        legacyExists: legacyBucketExists,
        allBuckets: buckets?.map(b => b.name).join(', ')
      });

      // If neither bucket exists, create the primary one
      if (!bucketExists && !legacyBucketExists) {
        console.log('🆕 Creating new storage bucket:', this.bucketName);
        const { error } = await this.supabase.storage.createBucket(this.bucketName, {
          public: true,
          allowedMimeTypes: [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'video/mp4',
            'video/mov',
            'video/avi',
            'video/quicktime',
          ],
          fileSizeLimit: 50 * 1024 * 1024, // 50MB
        });

        if (error) {
          console.error('❌ Failed to create storage bucket:', error);
          throw new Error(`Failed to initialize media storage: ${error.message}`);
        }

        console.log('✅ Storage bucket created successfully');
      } else {
        console.log('✅ Storage bucket already exists');
      }
    } catch (error) {
      console.error('❌ Bucket initialization error:', error);
      // Don't throw here - try to continue even if bucket initialization fails
      // The upload might still work if the bucket already exists
    }
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { isValid: boolean; error?: string } {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'video/mp4',
      'video/mov',
      'video/avi',
    ];

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `حجم الملف يتجاوز الحد الأقصى ${Math.round(maxSize / (1024 * 1024))}MB`,
      };
    }

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'نوع الملف غير مدعوم. يُسمح بالصور والفيديوهات فقط',
      };
    }

    return { isValid: true };
  }

  /**
   * Compress image file
   */
  async compressImage(
    file: File,
    options: {
      maxWidth?: number;
      maxHeight?: number;
      quality?: number;
    } = {}
  ): Promise<File> {
    const { maxWidth = 1920, maxHeight = 1080, quality = 0.8 } = options;

    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              reject(new Error('Image compression failed'));
            }
          },
          file.type,
          quality
        );
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Upload file to Supabase Storage
   */
  async uploadFile(
    file: File,
    userId: string,
    options: UploadOptions = {},
    onProgress?: (progress: UploadProgress) => void
  ): Promise<MediaFile> {
    // Validate file
    const validation = this.validateFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Initialize bucket
    await this.initializeBucket();

    let processedFile = file;
    const originalSize = file.size;

    // Compress image if requested
    if (options.compress && file.type.startsWith('image/')) {
      try {
        processedFile = await this.compressImage(file, {
          maxWidth: options.maxWidth,
          maxHeight: options.maxHeight,
          quality: options.quality,
        });
      } catch (error) {
        console.warn('Image compression failed, using original file:', error);
      }
    }

    // Generate unique file name
    const fileExtension = file.name.split('.').pop();
    const fileName = `${uuidv4()}.${fileExtension}`;
    const folder = options.folder || 'uploads';
    const filePath = `${userId}/${folder}/${fileName}`;

    // Upload to Supabase Storage
    console.log(`📤 Uploading file to ${this.bucketName}/${filePath}...`);

    try {
      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .upload(filePath, processedFile, {
          cacheControl: '3600',
          upsert: false,
        });

      if (error) {
        console.error('❌ Upload failed:', error);

        // Try fallback to legacy bucket if primary fails
        if (error.message.includes('not found') || error.message.includes('does not exist')) {
          console.log('🔄 Trying fallback to legacy bucket...');
          const { data: legacyData, error: legacyError } = await this.supabase.storage
            .from('media-files')
            .upload(filePath, processedFile, {
              cacheControl: '3600',
              upsert: false,
            });

          if (legacyError) {
            console.error('❌ Legacy upload also failed:', legacyError);
            throw new Error(`Upload failed on both buckets: ${error.message}, Legacy: ${legacyError.message}`);
          }

          console.log('✅ Legacy upload successful');
          // Update bucket name for this session
          this.bucketName = 'media-files';
        } else {
          throw new Error(`Upload failed: ${error.message}`);
        }
      } else {
        console.log('✅ Upload successful');
      }
    } catch (uploadError) {
      console.error('❌ Upload exception:', uploadError);
      throw new Error(`Upload exception: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
    }

    // Get public URL
    console.log('🔗 Getting public URL...');
    const { data: urlData } = this.supabase.storage
      .from(this.bucketName)
      .getPublicUrl(filePath);

    // Get file metadata
    const metadata: MediaFile['metadata'] = {
      compressed: options.compress && file.type.startsWith('image/'),
      originalSize,
    };

    // For images, get dimensions
    if (file.type.startsWith('image/')) {
      try {
        const dimensions = await this.getImageDimensions(processedFile);
        metadata.width = dimensions.width;
        metadata.height = dimensions.height;
      } catch (error) {
        console.warn('Failed to get image dimensions:', error);
      }
    }

    // Create media record
    const mediaFile: MediaFile = {
      id: uuidv4(),
      fileName,
      originalName: file.name,
      fileType: file.type,
      fileSize: processedFile.size,
      publicUrl: urlData.publicUrl,
      cdnUrl: urlData.publicUrl,
      userId,
      folder,
      metadata,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Store in database
    const { error: dbError } = await this.supabase
      .from('media_files')
      .insert({
        id: mediaFile.id,
        user_id: userId,
        file_name: fileName,
        original_name: file.name,
        file_type: file.type,
        file_size: processedFile.size,
        public_url: urlData.publicUrl,
        folder,
        metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (dbError) {
      // Clean up uploaded file if database insert fails
      await this.supabase.storage.from(this.bucketName).remove([filePath]);
      throw new Error(`Failed to save media record: ${dbError.message}`);
    }

    return mediaFile;
  }

  /**
   * Get image dimensions
   */
  private getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.naturalWidth, height: img.naturalHeight });
      };
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Get user's media files
   */
  async getUserMedia(
    userId: string,
    options: {
      folder?: string;
      type?: 'image' | 'video';
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<{ files: MediaFile[]; total: number }> {
    let query = this.supabase
      .from('media_files')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (options.folder) {
      query = query.eq('folder', options.folder);
    }

    if (options.type) {
      query = query.like('file_type', `${options.type}/%`);
    }

    if (options.limit) {
      query = query.limit(options.limit);
    }

    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to get media files: ${error.message}`);
    }

    const files = data?.map(this.mapToMediaFile) || [];

    return { files, total: count || 0 };
  }

  /**
   * Delete media file
   */
  async deleteFile(userId: string, fileId: string): Promise<boolean> {
    // Get file info
    const { data: fileData, error: fetchError } = await this.supabase
      .from('media_files')
      .select('*')
      .eq('id', fileId)
      .eq('user_id', userId)
      .single();

    if (fetchError || !fileData) {
      return false;
    }

    // Delete from storage
    const filePath = `${userId}/${fileData.folder}/${fileData.file_name}`;
    const { error: storageError } = await this.supabase.storage
      .from(this.bucketName)
      .remove([filePath]);

    if (storageError) {
      console.error('Failed to delete from storage:', storageError);
    }

    // Delete from database
    const { error: dbError } = await this.supabase
      .from('media_files')
      .delete()
      .eq('id', fileId)
      .eq('user_id', userId);

    return !dbError;
  }

  /**
   * Map database record to MediaFile
   */
  private mapToMediaFile(data: any): MediaFile {
    return {
      id: data.id,
      fileName: data.file_name,
      originalName: data.original_name,
      fileType: data.file_type,
      fileSize: data.file_size,
      publicUrl: data.public_url,
      cdnUrl: data.public_url,
      userId: data.user_id,
      folder: data.folder,
      metadata: data.metadata,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }
}
