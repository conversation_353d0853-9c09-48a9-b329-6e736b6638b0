import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Facebook API connectivity...');
    
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get Facebook accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'FACEBOOK');

    if (accountsError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch Facebook accounts',
        details: accountsError
      });
    }

    if (!accounts || accounts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No Facebook accounts found',
        message: 'Please connect a Facebook account first'
      });
    }

    const account = accounts[0];
    console.log('Testing Facebook account:', {
      id: account.id,
      account_name: account.account_name,
      platform: account.platform,
      hasAccessToken: !!account.access_token,
      hasPageId: !!account.page_id,
      hasPageToken: !!account.page_access_token,
      metadata: account.metadata
    });

    // Test 1: Validate access token
    console.log('🔑 Testing Facebook token validation...');
    const tokenResponse = await fetch(`https://graph.facebook.com/v19.0/me?access_token=${account.access_token}`);
    const tokenData = await tokenResponse.json();

    if (!tokenResponse.ok) {
      return NextResponse.json({
        success: false,
        step: 'token_validation',
        error: 'Facebook token validation failed',
        details: tokenData
      });
    }

    console.log('✅ Facebook token is valid:', tokenData);

    // Test 2: Get user's pages (if available)
    console.log('📄 Testing Facebook pages access...');
    const pagesResponse = await fetch(`https://graph.facebook.com/v19.0/me/accounts?access_token=${account.access_token}`);
    const pagesData = await pagesResponse.json();

    if (!pagesResponse.ok) {
      return NextResponse.json({
        success: false,
        step: 'pages_access',
        error: 'Failed to fetch Facebook pages',
        details: pagesData
      });
    }

    console.log('✅ Facebook pages data:', pagesData);

    // Test 3: Test posting capability (dry run)
    let postingTarget = 'me'; // Default to user feed
    let postingToken = account.access_token;

    if (account.page_id && account.page_access_token) {
      postingTarget = account.page_id;
      postingToken = account.page_access_token;
      console.log('📘 Will post to Facebook Page:', account.page_id);
    } else {
      console.log('📘 Will post to user feed (no page configured)');
    }

    // Test posting permissions (without actually posting)
    console.log('🔐 Testing posting permissions...');
    const permissionsResponse = await fetch(`https://graph.facebook.com/v19.0/${postingTarget}?fields=id,name&access_token=${postingToken}`);
    const permissionsData = await permissionsResponse.json();

    if (!permissionsResponse.ok) {
      return NextResponse.json({
        success: false,
        step: 'posting_permissions',
        error: 'Failed to verify posting permissions',
        details: permissionsData
      });
    }

    console.log('✅ Posting permissions verified:', permissionsData);

    return NextResponse.json({
      success: true,
      message: 'Facebook API connectivity test passed',
      results: {
        token_validation: {
          success: true,
          user_id: tokenData.id,
          user_name: tokenData.name
        },
        pages_access: {
          success: true,
          pages_count: pagesData.data?.length || 0,
          pages: pagesData.data?.map((p: any) => ({
            id: p.id,
            name: p.name,
            category: p.category
          })) || []
        },
        posting_permissions: {
          success: true,
          target_id: permissionsData.id,
          target_name: permissionsData.name,
          posting_target: postingTarget,
          using_page_token: !!account.page_access_token
        }
      },
      account_info: {
        id: account.id,
        account_name: account.account_name,
        account_id: account.account_id,
        has_page_access: !!account.page_access_token,
        metadata: account.metadata
      }
    });

  } catch (error: any) {
    console.error('❌ Facebook test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Facebook API test failed',
      details: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing Facebook post publishing...');
    
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { content = '🧪 Test post from eWasl platform - Facebook API test' } = body;

    // Get Facebook accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'FACEBOOK');

    if (accountsError || !accounts || accounts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No Facebook accounts found'
      });
    }

    const account = accounts[0];

    // Determine posting target
    let postingTarget = 'me';
    let postingToken = account.access_token;

    if (account.page_id && account.page_access_token) {
      postingTarget = account.page_id;
      postingToken = account.page_access_token;
    }

    console.log('📤 Publishing test post to Facebook...', {
      target: postingTarget,
      content: content.substring(0, 50) + '...'
    });

    // Publish the post
    const response = await fetch(`https://graph.facebook.com/v19.0/${postingTarget}/feed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: content,
        access_token: postingToken,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      console.error('❌ Facebook publishing failed:', result);
      return NextResponse.json({
        success: false,
        error: 'Facebook publishing failed',
        details: result,
        facebook_error: result.error
      });
    }

    console.log('✅ Facebook post published successfully:', result);

    return NextResponse.json({
      success: true,
      message: 'Facebook test post published successfully',
      post_id: result.id,
      post_url: `https://facebook.com/${result.id}`,
      platform_response: result
    });

  } catch (error: any) {
    console.error('❌ Facebook post test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Facebook post test failed',
      details: error.message
    }, { status: 500 });
  }
}
