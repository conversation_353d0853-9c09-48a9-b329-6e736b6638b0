"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import {
  MoreVertical,
  Heart,
  MessageCircle,
  Share,
  Eye,
  Calendar,
  Clock,
  Edit,
  Trash2,
  Copy,
  ExternalLink,
  TrendingUp,
  Users,
  Image as ImageIcon,
  Video,
  Play,
  Pause
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';

interface PostData {
  id: string;
  content: string;
  mediaUrls?: string[];
  platforms: string[];
  status: 'draft' | 'scheduled' | 'published';
  scheduledAt?: Date;
  publishedAt?: Date;
  analytics?: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
    engagement: number;
  };
  author: {
    name: string;
    avatar?: string;
  };
}

interface MobilePostCardProps {
  post: PostData;
  language?: 'ar' | 'en';
  onEdit?: (postId: string) => void;
  onDelete?: (postId: string) => void;
  onDuplicate?: (postId: string) => void;
  onView?: (postId: string) => void;
  className?: string;
}

export function MobilePostCard({
  post,
  language = 'ar',
  onEdit,
  onDelete,
  onDuplicate,
  onView,
  className
}: MobilePostCardProps) {
  const [isMediaExpanded, setIsMediaExpanded] = useState(false);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  // Translations
  const t = {
    ar: {
      status: {
        draft: 'مسودة',
        scheduled: 'مجدول',
        published: 'منشور'
      },
      actions: {
        edit: 'تعديل',
        delete: 'حذف',
        duplicate: 'نسخ',
        view: 'عرض',
        share: 'مشاركة',
        viewPost: 'عرض المنشور'
      },
      analytics: {
        views: 'مشاهدة',
        likes: 'إعجاب',
        comments: 'تعليق',
        shares: 'مشاركة',
        engagement: 'تفاعل'
      },
      time: {
        scheduledFor: 'مجدول لـ',
        publishedAt: 'نُشر في',
        createdAt: 'أُنشئ في'
      },
      media: {
        images: 'صور',
        videos: 'فيديوهات',
        viewAll: 'عرض الكل',
        collapse: 'طي'
      }
    },
    en: {
      status: {
        draft: 'Draft',
        scheduled: 'Scheduled',
        published: 'Published'
      },
      actions: {
        edit: 'Edit',
        delete: 'Delete',
        duplicate: 'Duplicate',
        view: 'View',
        share: 'Share',
        viewPost: 'View Post'
      },
      analytics: {
        views: 'Views',
        likes: 'Likes',
        comments: 'Comments',
        shares: 'Shares',
        engagement: 'Engagement'
      },
      time: {
        scheduledFor: 'Scheduled for',
        publishedAt: 'Published',
        createdAt: 'Created'
      },
      media: {
        images: 'Images',
        videos: 'Videos',
        viewAll: 'View All',
        collapse: 'Collapse'
      }
    }
  };

  const text = t[language];

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'scheduled':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Get platform icon
  const getPlatformIcon = (platform: string) => {
    const icons: Record<string, string> = {
      instagram: '📷',
      facebook: '👥',
      twitter: '🐦',
      linkedin: '💼',
      tiktok: '🎵',
      youtube: '📺'
    };
    return icons[platform.toLowerCase()] || '📱';
  };

  // Format number
  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  // Format date
  const formatDate = (date: Date): string => {
    return formatDistanceToNow(date, {
      addSuffix: true,
      locale: language === 'ar' ? ar : enUS
    });
  };

  // Render media preview
  const renderMediaPreview = () => {
    if (!post.mediaUrls || post.mediaUrls.length === 0) return null;

    const mediaCount = post.mediaUrls.length;
    const hasImages = post.mediaUrls.some(url => url.includes('image') || url.match(/\.(jpg|jpeg|png|gif|webp)$/i));
    const hasVideos = post.mediaUrls.some(url => url.includes('video') || url.match(/\.(mp4|webm|ogg|mov)$/i));

    return (
      <div className="mt-3">
        {/* Media Summary */}
        <div className={cn(
          "flex items-center gap-2 mb-2",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          {hasImages && (
            <div className={cn(
              "flex items-center gap-1 text-xs text-gray-600",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <ImageIcon className="w-3 h-3" />
              <span>{mediaCount} {text.media.images}</span>
            </div>
          )}
          {hasVideos && (
            <div className={cn(
              "flex items-center gap-1 text-xs text-gray-600",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <Video className="w-3 h-3" />
              <span>{text.media.videos}</span>
            </div>
          )}
        </div>

        {/* Media Grid */}
        <div className={cn(
          "grid gap-2",
          mediaCount === 1 ? "grid-cols-1" : 
          mediaCount === 2 ? "grid-cols-2" : 
          "grid-cols-2"
        )}>
          {post.mediaUrls.slice(0, isMediaExpanded ? undefined : 4).map((url, index) => (
            <div
              key={index}
              className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden"
            >
              {url.includes('video') || url.match(/\.(mp4|webm|ogg|mov)$/i) ? (
                <div className="relative w-full h-full bg-black flex items-center justify-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsVideoPlaying(!isVideoPlaying)}
                    className="absolute inset-0 w-full h-full bg-black/20 hover:bg-black/30"
                  >
                    {isVideoPlaying ? (
                      <Pause className="w-8 h-8 text-white" />
                    ) : (
                      <Play className="w-8 h-8 text-white" />
                    )}
                  </Button>
                  <Video className="w-6 h-6 text-white/60" />
                </div>
              ) : (
                <img
                  src={url}
                  alt={`Media ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              )}
              
              {/* More indicator */}
              {!isMediaExpanded && index === 3 && mediaCount > 4 && (
                <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                  <span className="text-white font-medium">
                    +{mediaCount - 4}
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Expand/Collapse Button */}
        {mediaCount > 4 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMediaExpanded(!isMediaExpanded)}
            className="w-full mt-2 text-xs"
          >
            {isMediaExpanded ? text.media.collapse : text.media.viewAll}
          </Button>
        )}
      </div>
    );
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className={cn(
          "flex items-start justify-between",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          {/* Author Info */}
          <div className={cn(
            "flex items-center gap-3 flex-1",
            language === 'ar' ? "flex-row-reverse" : ""
          )}>
            <Avatar className="w-8 h-8">
              <AvatarImage src={post.author.avatar} />
              <AvatarFallback className="text-xs">
                {post.author.name.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div className={cn("flex-1", language === 'ar' ? "text-right" : "")}>
              <p className="text-sm font-medium">{post.author.name}</p>
              <div className={cn(
                "flex items-center gap-2 mt-1",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <Badge 
                  variant="outline" 
                  className={cn("text-xs", getStatusColor(post.status))}
                >
                  {text.status[post.status as keyof typeof text.status]}
                </Badge>
                {post.status === 'scheduled' && post.scheduledAt && (
                  <span className="text-xs text-gray-500">
                    {formatDate(post.scheduledAt)}
                  </span>
                )}
                {post.status === 'published' && post.publishedAt && (
                  <span className="text-xs text-gray-500">
                    {formatDate(post.publishedAt)}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Actions Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                <MoreVertical className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align={language === 'ar' ? 'start' : 'end'}>
              <DropdownMenuItem onClick={() => onView?.(post.id)}>
                <Eye className="w-4 h-4 mr-2" />
                {text.actions.view}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit?.(post.id)}>
                <Edit className="w-4 h-4 mr-2" />
                {text.actions.edit}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDuplicate?.(post.id)}>
                <Copy className="w-4 h-4 mr-2" />
                {text.actions.duplicate}
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => onDelete?.(post.id)}
                className="text-red-600"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                {text.actions.delete}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Post Content */}
        <div className={language === 'ar' ? "text-right" : ""}>
          <p className="text-sm text-gray-900 line-clamp-3 leading-relaxed">
            {post.content}
          </p>
        </div>

        {/* Media Preview */}
        {renderMediaPreview()}

        {/* Platforms */}
        <div className={cn(
          "flex items-center gap-2 mt-3",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          <span className="text-xs text-gray-500">
            {language === 'ar' ? 'المنصات:' : 'Platforms:'}
          </span>
          {post.platforms.map((platform) => (
            <span key={platform} className="text-sm">
              {getPlatformIcon(platform)}
            </span>
          ))}
        </div>

        {/* Analytics (for published posts) */}
        {post.status === 'published' && post.analytics && (
          <div className="mt-4 pt-3 border-t border-gray-100">
            <div className="grid grid-cols-4 gap-4">
              <div className={cn("text-center", language === 'ar' ? "text-right" : "")}>
                <div className={cn(
                  "flex items-center justify-center gap-1",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <Eye className="w-3 h-3 text-gray-400" />
                  <span className="text-xs font-medium">
                    {formatNumber(post.analytics.views)}
                  </span>
                </div>
                <span className="text-xs text-gray-500">{text.analytics.views}</span>
              </div>

              <div className={cn("text-center", language === 'ar' ? "text-right" : "")}>
                <div className={cn(
                  "flex items-center justify-center gap-1",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <Heart className="w-3 h-3 text-red-400" />
                  <span className="text-xs font-medium">
                    {formatNumber(post.analytics.likes)}
                  </span>
                </div>
                <span className="text-xs text-gray-500">{text.analytics.likes}</span>
              </div>

              <div className={cn("text-center", language === 'ar' ? "text-right" : "")}>
                <div className={cn(
                  "flex items-center justify-center gap-1",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <MessageCircle className="w-3 h-3 text-blue-400" />
                  <span className="text-xs font-medium">
                    {formatNumber(post.analytics.comments)}
                  </span>
                </div>
                <span className="text-xs text-gray-500">{text.analytics.comments}</span>
              </div>

              <div className={cn("text-center", language === 'ar' ? "text-right" : "")}>
                <div className={cn(
                  "flex items-center justify-center gap-1",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <Share className="w-3 h-3 text-green-400" />
                  <span className="text-xs font-medium">
                    {formatNumber(post.analytics.shares)}
                  </span>
                </div>
                <span className="text-xs text-gray-500">{text.analytics.shares}</span>
              </div>
            </div>

            {/* Engagement Rate */}
            <div className={cn(
              "flex items-center justify-center gap-2 mt-3 pt-2 border-t border-gray-50",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <TrendingUp className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium text-blue-600">
                {post.analytics.engagement.toFixed(1)}% {text.analytics.engagement}
              </span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className={cn(
          "flex items-center gap-2 mt-4",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onView?.(post.id)}
            className="flex-1"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            {text.actions.viewPost}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit?.(post.id)}
          >
            <Edit className="w-4 h-4" />
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDuplicate?.(post.id)}
          >
            <Copy className="w-4 h-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
