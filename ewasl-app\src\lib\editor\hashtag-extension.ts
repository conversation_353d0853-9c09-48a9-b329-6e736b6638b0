/**
 * Custom Hashtag Extension for TipTap
 * Detects and highlights hashtags in the editor
 */

import { Mark, mergeAttributes } from '@tiptap/core';

export interface HashtagOptions {
  HTMLAttributes: Record<string, any>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    hashtag: {
      /**
       * Set a hashtag mark
       */
      setHashtag: (attributes?: { hashtag: string }) => ReturnType;
      /**
       * Toggle a hashtag mark
       */
      toggleHashtag: (attributes?: { hashtag: string }) => ReturnType;
      /**
       * Unset a hashtag mark
       */
      unsetHashtag: () => ReturnType;
    };
  }
}

export const Hashtag = Mark.create<HashtagOptions>({
  name: 'hashtag',

  addOptions() {
    return {
      HTMLAttributes: {
        class: 'hashtag text-blue-600 bg-blue-100 px-1 rounded font-medium',
      },
    };
  },

  addAttributes() {
    return {
      hashtag: {
        default: null,
        parseHTML: element => element.getAttribute('data-hashtag'),
        renderHTML: attributes => {
          if (!attributes.hashtag) {
            return {};
          }

          return {
            'data-hashtag': attributes.hashtag,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-hashtag]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'span',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
      0,
    ];
  },

  addCommands() {
    return {
      setHashtag:
        attributes =>
        ({ commands }) => {
          return commands.setMark(this.name, attributes);
        },
      toggleHashtag:
        attributes =>
        ({ commands }) => {
          return commands.toggleMark(this.name, attributes);
        },
      unsetHashtag:
        () =>
        ({ commands }) => {
          return commands.unsetMark(this.name);
        },
    };
  },
});

/**
 * Utility function to extract hashtags from text
 */
export function extractHashtags(text: string): string[] {
  const hashtagRegex = /#[\u0600-\u06FF\w]+/g;
  const matches = text.match(hashtagRegex);
  return matches ? matches.map(tag => tag.substring(1)) : [];
}

/**
 * Utility function to extract mentions from text
 */
export function extractMentions(text: string): string[] {
  const mentionRegex = /@[\u0600-\u06FF\w]+/g;
  const matches = text.match(mentionRegex);
  return matches ? matches.map(mention => mention.substring(1)) : [];
}

/**
 * Utility function to count characters for different platforms
 */
export function countCharactersForPlatform(
  text: string,
  platform: 'twitter' | 'facebook' | 'linkedin' | 'instagram'
): {
  characters: number;
  words: number;
  hashtags: string[];
  mentions: string[];
  links: string[];
} {
  // Remove HTML tags for accurate counting
  const plainText = text.replace(/<[^>]*>/g, '');
  
  // Extract links
  const linkRegex = /https?:\/\/[^\s]+/g;
  const links = plainText.match(linkRegex) || [];
  
  // For Twitter, links count as 23 characters regardless of actual length
  let adjustedText = plainText;
  if (platform === 'twitter') {
    adjustedText = plainText.replace(linkRegex, 'x'.repeat(23));
  }
  
  return {
    characters: adjustedText.length,
    words: plainText.split(/\s+/).filter(word => word.length > 0).length,
    hashtags: extractHashtags(plainText),
    mentions: extractMentions(plainText),
    links,
  };
}

/**
 * Validate content for platform-specific rules
 */
export function validateContentForPlatform(
  text: string,
  platform: 'twitter' | 'facebook' | 'linkedin' | 'instagram'
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const stats = countCharactersForPlatform(text, platform);
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Platform-specific limits
  const limits = {
    twitter: { characters: 280, hashtags: 2 },
    facebook: { characters: 63206, hashtags: 30 },
    linkedin: { characters: 3000, hashtags: 5 },
    instagram: { characters: 2200, hashtags: 30 },
  };
  
  const limit = limits[platform];
  
  // Character limit validation
  if (stats.characters > limit.characters) {
    errors.push(`المحتوى يتجاوز الحد الأقصى ${limit.characters} حرف`);
  }
  
  // Hashtag limit validation
  if (stats.hashtags.length > limit.hashtags) {
    errors.push(`عدد الهاشتاغات يتجاوز الحد الأقصى ${limit.hashtags}`);
  }
  
  // Platform-specific warnings
  if (platform === 'twitter') {
    if (stats.hashtags.length === 0) {
      warnings.push('إضافة هاشتاغ واحد على الأقل يحسن من الوصول');
    }
    if (stats.characters > 240) {
      warnings.push('المحتوى قريب من الحد الأقصى');
    }
  }
  
  if (platform === 'linkedin') {
    if (stats.words < 25) {
      warnings.push('المحتوى قصير جداً لـ LinkedIn، يُفضل 25 كلمة على الأقل');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
