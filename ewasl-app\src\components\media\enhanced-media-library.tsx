'use client';

import React, { useState, useEffect } from 'react';
import { Search, Filter, Grid, List, Trash2, Download, Eye, Image, Video, Calendar, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { MediaStorageService } from '@/lib/media/storage-service';
import { useSupabase } from '@/components/auth/supabase-provider';
import { toast } from 'sonner';

interface MediaFile {
  id: string;
  fileName: string;
  originalName: string;
  fileType: string;
  fileSize: number;
  publicUrl: string;
  cdnUrl: string;
  userId: string;
  folder?: string;
  metadata?: {
    width?: number;
    height?: number;
    compressed?: boolean;
    originalSize?: number;
  };
  createdAt: Date;
}

interface EnhancedMediaLibraryProps {
  onSelectMedia?: (media: MediaFile) => void;
  selectionMode?: boolean;
  selectedFiles?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
  folder?: string;
  className?: string;
}

export function EnhancedMediaLibrary({
  onSelectMedia,
  selectionMode = false,
  selectedFiles = [],
  onSelectionChange,
  folder,
  className = '',
}: EnhancedMediaLibraryProps) {
  const { user } = useSupabase();
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [filteredFiles, setFilteredFiles] = useState<MediaFile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'image' | 'video'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedPreview, setSelectedPreview] = useState<MediaFile | null>(null);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  
  const storageService = new MediaStorageService();
  const itemsPerPage = 20;

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const loadFiles = async (pageNum = 0, append = false) => {
    if (!user) return;

    try {
      setIsLoading(true);
      const result = await storageService.getUserMedia(user.id, {
        folder,
        type: typeFilter === 'all' ? undefined : typeFilter,
        limit: itemsPerPage,
        offset: pageNum * itemsPerPage,
      });

      if (append) {
        setFiles(prev => [...prev, ...result.files]);
      } else {
        setFiles(result.files);
      }
      
      setTotal(result.total);
      setHasMore(result.files.length === itemsPerPage);
    } catch (error: any) {
      console.error('Failed to load media files:', error);
      toast.error('فشل في تحميل الملفات');
    } finally {
      setIsLoading(false);
    }
  };

  const deleteFile = async (file: MediaFile) => {
    if (!user) return;

    try {
      const success = await storageService.deleteFile(user.id, file.id);
      if (success) {
        setFiles(prev => prev.filter(f => f.id !== file.id));
        toast.success(`تم حذف ${file.originalName} بنجاح`);
      } else {
        toast.error('فشل في حذف الملف');
      }
    } catch (error: any) {
      console.error('Delete error:', error);
      toast.error('فشل في حذف الملف');
    }
  };

  const downloadFile = (file: MediaFile) => {
    const link = document.createElement('a');
    link.href = file.publicUrl;
    link.download = file.originalName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleFileSelect = (file: MediaFile) => {
    if (selectionMode) {
      const newSelection = selectedFiles.includes(file.id)
        ? selectedFiles.filter(id => id !== file.id)
        : [...selectedFiles, file.id];
      onSelectionChange?.(newSelection);
    } else {
      onSelectMedia?.(file);
    }
  };

  const loadMore = () => {
    if (hasMore && !isLoading) {
      const nextPage = page + 1;
      setPage(nextPage);
      loadFiles(nextPage, true);
    }
  };

  // Filter files based on search query
  useEffect(() => {
    const filtered = files.filter(file =>
      file.originalName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      file.fileType.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredFiles(filtered);
  }, [files, searchQuery]);

  // Load files when filters change
  useEffect(() => {
    setPage(0);
    loadFiles(0, false);
  }, [user, typeFilter, folder]);

  const renderFileCard = (file: MediaFile) => {
    const isSelected = selectedFiles.includes(file.id);
    const isImage = file.fileType.startsWith('image/');
    const isVideo = file.fileType.startsWith('video/');

    return (
      <Card
        key={file.id}
        className={`cursor-pointer transition-all hover:shadow-md ${
          isSelected ? 'ring-2 ring-blue-500' : ''
        }`}
        onClick={() => handleFileSelect(file)}
      >
        <CardContent className="p-3">
          <div className="aspect-square relative mb-3 bg-gray-100 rounded-lg overflow-hidden">
            {isImage ? (
              <img
                src={file.publicUrl}
                alt={file.originalName}
                className="w-full h-full object-cover"
                loading="lazy"
              />
            ) : isVideo ? (
              <div className="w-full h-full flex items-center justify-center">
                <Video className="h-12 w-12 text-gray-400" />
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <FileText className="h-12 w-12 text-gray-400" />
              </div>
            )}
            
            {/* File type badge */}
            <Badge
              variant="secondary"
              className="absolute top-2 right-2 text-xs"
            >
              {isImage ? 'صورة' : isVideo ? 'فيديو' : 'ملف'}
            </Badge>
          </div>

          <div className="space-y-2">
            <p className="text-sm font-medium truncate" title={file.originalName}>
              {file.originalName}
            </p>
            
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>{formatFileSize(file.fileSize)}</span>
              {file.metadata?.compressed && (
                <Badge variant="outline" className="text-xs">
                  مضغوط
                </Badge>
              )}
            </div>
            
            <p className="text-xs text-gray-400">
              {formatDate(file.createdAt)}
            </p>

            {/* Action buttons */}
            <div className="flex gap-1 pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedPreview(file);
                }}
              >
                <Eye className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  downloadFile(file);
                }}
              >
                <Download className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  deleteFile(file);
                }}
              >
                <Trash2 className="h-3 w-3 text-red-500" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderListItem = (file: MediaFile) => {
    const isSelected = selectedFiles.includes(file.id);
    const isImage = file.fileType.startsWith('image/');
    const isVideo = file.fileType.startsWith('video/');

    return (
      <div
        key={file.id}
        className={`flex items-center gap-4 p-3 border rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
          isSelected ? 'ring-2 ring-blue-500' : ''
        }`}
        onClick={() => handleFileSelect(file)}
      >
        <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
          {isImage ? (
            <img
              src={file.publicUrl}
              alt={file.originalName}
              className="w-full h-full object-cover"
              loading="lazy"
            />
          ) : isVideo ? (
            <Video className="h-6 w-6 text-gray-400" />
          ) : (
            <FileText className="h-6 w-6 text-gray-400" />
          )}
        </div>

        <div className="flex-1 min-w-0">
          <p className="font-medium truncate">{file.originalName}</p>
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <span>{formatFileSize(file.fileSize)}</span>
            <span>•</span>
            <span>{formatDate(file.createdAt)}</span>
            {file.metadata?.compressed && (
              <>
                <span>•</span>
                <Badge variant="outline" className="text-xs">مضغوط</Badge>
              </>
            )}
          </div>
        </div>

        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setSelectedPreview(file);
            }}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              downloadFile(file);
            }}
          >
            <Download className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              deleteFile(file);
            }}
          >
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">مكتبة الوسائط</h3>
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <div className="flex-1">
          <Input
            placeholder="البحث في الملفات..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>
        <Select value={typeFilter} onValueChange={(value: any) => setTypeFilter(value)}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">جميع الملفات</SelectItem>
            <SelectItem value="image">الصور</SelectItem>
            <SelectItem value="video">الفيديوهات</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Stats */}
      <div className="flex items-center gap-4 text-sm text-gray-600">
        <span>المجموع: {total} ملف</span>
        {selectionMode && selectedFiles.length > 0 && (
          <span>المحدد: {selectedFiles.length} ملف</span>
        )}
      </div>

      {/* Files Grid/List */}
      {isLoading && files.length === 0 ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-500">جاري تحميل الملفات...</p>
        </div>
      ) : filteredFiles.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">لا توجد ملفات</p>
        </div>
      ) : (
        <>
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {filteredFiles.map(renderFileCard)}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredFiles.map(renderListItem)}
            </div>
          )}

          {/* Load More */}
          {hasMore && (
            <div className="text-center pt-4">
              <Button
                variant="outline"
                onClick={loadMore}
                disabled={isLoading}
              >
                {isLoading ? 'جاري التحميل...' : 'تحميل المزيد'}
              </Button>
            </div>
          )}
        </>
      )}

      {/* Preview Dialog */}
      <Dialog open={!!selectedPreview} onOpenChange={() => setSelectedPreview(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>{selectedPreview?.originalName}</DialogTitle>
          </DialogHeader>
          {selectedPreview && (
            <div className="space-y-4">
              <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                {selectedPreview.fileType.startsWith('image/') ? (
                  <img
                    src={selectedPreview.publicUrl}
                    alt={selectedPreview.originalName}
                    className="w-full h-full object-contain"
                  />
                ) : selectedPreview.fileType.startsWith('video/') ? (
                  <video
                    src={selectedPreview.publicUrl}
                    controls
                    className="w-full h-full"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <FileText className="h-24 w-24 text-gray-400" />
                  </div>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>الاسم:</strong> {selectedPreview.originalName}
                </div>
                <div>
                  <strong>الحجم:</strong> {formatFileSize(selectedPreview.fileSize)}
                </div>
                <div>
                  <strong>النوع:</strong> {selectedPreview.fileType}
                </div>
                <div>
                  <strong>تاريخ الرفع:</strong> {formatDate(selectedPreview.createdAt)}
                </div>
                {selectedPreview.metadata?.width && selectedPreview.metadata?.height && (
                  <div>
                    <strong>الأبعاد:</strong> {selectedPreview.metadata.width} × {selectedPreview.metadata.height}
                  </div>
                )}
                {selectedPreview.metadata?.compressed && (
                  <div>
                    <strong>مضغوط:</strong> نعم
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
