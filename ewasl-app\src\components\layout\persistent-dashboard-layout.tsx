"use client";

import { ReactNode, memo } from "react";
import EnhancedSidebar from "./enhanced-sidebar";
import EnhancedHeader from "./enhanced-header";
import { cn } from "@/lib/utils";
import { useNavigation } from "@/contexts/NavigationContext";

interface PersistentDashboardLayoutProps {
  children: ReactNode;
  title?: string;
}

const PersistentDashboardLayoutComponent = ({ children, title }: PersistentDashboardLayoutProps) => {
  const { 
    isSidebarOpen, 
    language, 
    setLanguage, 
    toggleSidebar, 
    closeSidebar 
  } = useNavigation();

  return (
    <div className={cn(
      "flex h-screen bg-gray-50",
      language === 'ar' ? "rtl" : "ltr"
    )}>
      {/* Enhanced Sidebar - Always rendered, controlled by CSS transforms */}
      <EnhancedSidebar
        isCollapsed={false}
        onToggle={toggleSidebar}
        language={language}
        onLanguageChange={setLanguage}
        isMobileMenuOpen={isSidebarOpen}
        className={cn(
          "transition-transform duration-300",
          // On desktop (lg+), always show sidebar
          "lg:translate-x-0",
          // On mobile/tablet, show/hide based on isSidebarOpen
          language === 'ar' ? (
            isSidebarOpen ? "translate-x-0" : "translate-x-full lg:translate-x-0"
          ) : (
            isSidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
          )
        )}
      />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Enhanced Header */}
        <EnhancedHeader
          language={language}
          title={title || (language === 'ar' ? "لوحة التحكم" : "Dashboard")}
          onMobileMenuToggle={toggleSidebar}
          onLanguageChange={setLanguage}
        />
        
        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-gray-50/30 via-white/50 to-blue-50/20">
          <div className="mx-auto max-w-7xl">
            {children}
          </div>
        </main>
      </div>
      
      {/* Mobile overlay */}
      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 lg:hidden z-30"
          onClick={closeSidebar}
        />
      )}
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const PersistentDashboardLayout = memo(PersistentDashboardLayoutComponent);

// Add display name for debugging
PersistentDashboardLayout.displayName = 'PersistentDashboardLayout';
