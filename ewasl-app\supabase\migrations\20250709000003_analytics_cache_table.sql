-- Analytics Cache Table
-- Stores cached analytics data from social media platforms

CREATE TABLE IF NOT EXISTS public.analytics_cache (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  platform TEXT NOT NULL,
  account_id TEXT NOT NULL,
  account_name TEXT,
  metrics JSONB NOT NULL DEFAULT '{}',
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique combination of user, platform, and account
  UNIQUE(user_id, platform, account_id)
);

-- Indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_analytics_cache_user_id ON public.analytics_cache(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_cache_platform ON public.analytics_cache(platform);
CREATE INDEX IF NOT EXISTS idx_analytics_cache_last_updated ON public.analytics_cache(last_updated DESC);
CREATE INDEX IF NOT EXISTS idx_analytics_cache_user_platform ON public.analytics_cache(user_id, platform);

-- Enable RLS
ALTER TABLE public.analytics_cache ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY IF NOT EXISTS "Users can view their own analytics cache" ON public.analytics_cache
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own analytics cache" ON public.analytics_cache
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own analytics cache" ON public.analytics_cache
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own analytics cache" ON public.analytics_cache
  FOR DELETE USING (auth.uid() = user_id);

-- Function to cleanup old analytics cache (older than 7 days)
CREATE OR REPLACE FUNCTION cleanup_old_analytics_cache()
RETURNS void AS $$
BEGIN
  DELETE FROM public.analytics_cache 
  WHERE last_updated < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION cleanup_old_analytics_cache() TO authenticated;

-- Post Analytics Table for detailed post-level analytics
CREATE TABLE IF NOT EXISTS public.post_analytics (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  platform TEXT NOT NULL,
  post_id TEXT NOT NULL,
  content TEXT,
  published_at TIMESTAMPTZ,
  metrics JSONB NOT NULL DEFAULT '{}',
  engagement JSONB NOT NULL DEFAULT '{}',
  last_updated TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique combination of user, platform, and post
  UNIQUE(user_id, platform, post_id)
);

-- Indexes for post analytics
CREATE INDEX IF NOT EXISTS idx_post_analytics_user_id ON public.post_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_post_analytics_platform ON public.post_analytics(platform);
CREATE INDEX IF NOT EXISTS idx_post_analytics_published_at ON public.post_analytics(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_post_analytics_user_platform ON public.post_analytics(user_id, platform);

-- Enable RLS for post analytics
ALTER TABLE public.post_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for post analytics
CREATE POLICY IF NOT EXISTS "Users can view their own post analytics" ON public.post_analytics
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own post analytics" ON public.post_analytics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own post analytics" ON public.post_analytics
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own post analytics" ON public.post_analytics
  FOR DELETE USING (auth.uid() = user_id);

-- Analytics Summary View for dashboard
CREATE OR REPLACE VIEW public.analytics_summary AS
SELECT 
  user_id,
  platform,
  COUNT(*) as accounts_count,
  SUM((metrics->>'followers')::int) as total_followers,
  SUM((metrics->'engagement'->>'likes')::int) as total_likes,
  SUM((metrics->'engagement'->>'comments')::int) as total_comments,
  SUM((metrics->'engagement'->>'shares')::int) as total_shares,
  SUM((metrics->'reach'->>'impressions')::int) as total_impressions,
  AVG((metrics->'growth'->>'engagementRate')::float) as avg_engagement_rate,
  MAX(last_updated) as last_updated
FROM public.analytics_cache
GROUP BY user_id, platform;

-- Grant access to the view
GRANT SELECT ON public.analytics_summary TO authenticated;

-- RLS for the view
CREATE POLICY IF NOT EXISTS "Users can view their own analytics summary" ON public.analytics_summary
  FOR SELECT USING (auth.uid() = user_id);

-- Function to get analytics insights
CREATE OR REPLACE FUNCTION get_analytics_insights(
  p_user_id UUID,
  p_days INTEGER DEFAULT 30
)
RETURNS TABLE (
  platform TEXT,
  followers_growth NUMERIC,
  engagement_trend NUMERIC,
  top_performing_metric TEXT,
  recommendation TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ac.platform,
    COALESCE((ac.metrics->'growth'->>'followersChange')::numeric, 0) as followers_growth,
    COALESCE((ac.metrics->'growth'->>'engagementRate')::numeric, 0) as engagement_trend,
    CASE 
      WHEN (ac.metrics->'engagement'->>'likes')::int > (ac.metrics->'engagement'->>'comments')::int 
        AND (ac.metrics->'engagement'->>'likes')::int > (ac.metrics->'engagement'->>'shares')::int 
      THEN 'likes'
      WHEN (ac.metrics->'engagement'->>'comments')::int > (ac.metrics->'engagement'->>'shares')::int 
      THEN 'comments'
      ELSE 'shares'
    END as top_performing_metric,
    CASE 
      WHEN (ac.metrics->'growth'->>'engagementRate')::numeric < 2.0 
      THEN 'يُنصح بزيادة التفاعل مع المتابعين'
      WHEN (ac.metrics->'growth'->>'followersChange')::numeric < 0 
      THEN 'يُنصح بتحسين جودة المحتوى'
      ELSE 'الأداء جيد، استمر في النشر المنتظم'
    END as recommendation
  FROM public.analytics_cache ac
  WHERE ac.user_id = p_user_id
    AND ac.last_updated > NOW() - INTERVAL '1 day' * p_days;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_analytics_insights(UUID, INTEGER) TO authenticated;

-- Create a scheduled job to cleanup old cache (if pg_cron is available)
-- SELECT cron.schedule('cleanup-analytics-cache', '0 2 * * *', 'SELECT cleanup_old_analytics_cache();');
