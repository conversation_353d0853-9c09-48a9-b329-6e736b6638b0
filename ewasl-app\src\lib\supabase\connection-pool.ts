/**
 * Supabase Connection Pool Manager
 * Optimizes database connections for production scalability
 * Supports Supavisor connection pooling and custom pool management
 */

import { logger } from '@/lib/monitoring/enhanced-logger';

export interface ConnectionPoolConfig {
  // Pool size configuration
  max: number;
  min: number;
  
  // Timeout configuration
  connectionTimeoutMillis: number;
  idleTimeoutMillis: number;
  maxLifetimeSeconds: number;
  
  // Query configuration
  statementTimeout: string;
  lockTimeout: string;
  idleInTransactionTimeout: string;
  
  // Retry configuration
  retryAttempts: number;
  retryDelay: number;
  retryBackoff: boolean;
  
  // Monitoring
  enableMetrics: boolean;
  metricsInterval: number;
}

export interface ConnectionPoolMetrics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingClients: number;
  totalQueries: number;
  averageQueryTime: number;
  errorRate: number;
  lastUpdated: Date;
}

/**
 * Default connection pool configuration
 * Optimized for Vercel and production environments
 */
export const DEFAULT_POOL_CONFIG: ConnectionPoolConfig = {
  // Pool size - conservative for serverless
  max: parseInt(process.env.SUPABASE_POOL_MAX || '20'),
  min: parseInt(process.env.SUPABASE_POOL_MIN || '2'),
  
  // Timeouts
  connectionTimeoutMillis: parseInt(process.env.SUPABASE_CONNECTION_TIMEOUT || '5000'),
  idleTimeoutMillis: parseInt(process.env.SUPABASE_IDLE_TIMEOUT || '30000'),
  maxLifetimeSeconds: parseInt(process.env.SUPABASE_MAX_LIFETIME || '3600'),
  
  // Query timeouts
  statementTimeout: process.env.SUPABASE_STATEMENT_TIMEOUT || '30s',
  lockTimeout: process.env.SUPABASE_LOCK_TIMEOUT || '10s',
  idleInTransactionTimeout: process.env.SUPABASE_IDLE_TRANSACTION_TIMEOUT || '60s',
  
  // Retry configuration
  retryAttempts: parseInt(process.env.SUPABASE_RETRY_ATTEMPTS || '3'),
  retryDelay: parseInt(process.env.SUPABASE_RETRY_DELAY || '1000'),
  retryBackoff: process.env.SUPABASE_RETRY_BACKOFF === 'true',
  
  // Monitoring
  enableMetrics: process.env.SUPABASE_ENABLE_METRICS === 'true',
  metricsInterval: parseInt(process.env.SUPABASE_METRICS_INTERVAL || '60000'),
};

/**
 * Connection Pool Manager
 * Handles connection lifecycle and monitoring
 */
export class ConnectionPoolManager {
  private config: ConnectionPoolConfig;
  private metrics: ConnectionPoolMetrics;
  private metricsTimer?: NodeJS.Timeout;
  
  constructor(config: Partial<ConnectionPoolConfig> = {}) {
    this.config = { ...DEFAULT_POOL_CONFIG, ...config };
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      waitingClients: 0,
      totalQueries: 0,
      averageQueryTime: 0,
      errorRate: 0,
      lastUpdated: new Date(),
    };
    
    if (this.config.enableMetrics) {
      this.startMetricsCollection();
    }
    
    logger.info('Connection pool manager initialized', {
      component: 'connection-pool',
      config: this.config
    });
  }
  
  /**
   * Get connection pool configuration for Supabase client
   */
  getClientConfig() {
    return {
      db: {
        schema: 'public',
      },
      auth: {
        autoRefreshToken: true,
        persistSession: true,
      },
      global: {
        headers: {
          'x-connection-pool': 'enabled',
          'x-pool-max': this.config.max.toString(),
          'x-pool-min': this.config.min.toString(),
          'x-connection-timeout': this.config.connectionTimeoutMillis.toString(),
          'x-statement-timeout': this.config.statementTimeout,
          'x-lock-timeout': this.config.lockTimeout,
        }
      }
    };
  }
  
  /**
   * Get service role client configuration
   */
  getServiceRoleConfig() {
    return {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      },
      db: {
        schema: 'public',
      },
      global: {
        headers: {
          'x-connection-pool': 'service-role',
          'x-pool-max': this.config.max.toString(),
          'x-pool-min': this.config.min.toString(),
          'x-connection-timeout': this.config.connectionTimeoutMillis.toString(),
          'x-statement-timeout': this.config.statementTimeout,
          'x-lock-timeout': this.config.lockTimeout,
          'x-idle-timeout': this.config.idleInTransactionTimeout,
        }
      }
    };
  }
  
  /**
   * Check if Supavisor pooling is available and configured
   */
  isSupavisorEnabled(): boolean {
    return process.env.SUPABASE_ENABLE_POOLING === 'true' && 
           !!process.env.SUPABASE_POOLING_PORT;
  }
  
  /**
   * Get pooled connection URL for Supavisor
   */
  getPooledUrl(baseUrl: string): string {
    if (!this.isSupavisorEnabled() || baseUrl.includes('placeholder')) {
      return baseUrl;
    }
    
    const poolingPort = process.env.SUPABASE_POOLING_PORT || '6543';
    const projectId = baseUrl.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1];
    
    if (projectId) {
      // For Supavisor, the pooled URL format is typically:
      // postgresql://postgres:[password]@[project].pooler.supabase.com:6543/postgres
      // But for HTTP clients, we still use the standard URL as pooling is handled at infrastructure level
      logger.info('Supavisor pooling enabled', {
        component: 'connection-pool',
        projectId,
        poolingPort
      });
    }
    
    return baseUrl;
  }
  
  /**
   * Start collecting connection pool metrics
   */
  private startMetricsCollection() {
    this.metricsTimer = setInterval(() => {
      this.collectMetrics();
    }, this.config.metricsInterval);
  }
  
  /**
   * Collect and log connection pool metrics
   */
  private collectMetrics() {
    // In a real implementation, this would query the actual pool statistics
    // For now, we'll log the configuration and basic metrics
    this.metrics.lastUpdated = new Date();
    
    logger.info('Connection pool metrics', {
      component: 'connection-pool',
      metrics: this.metrics,
      config: {
        max: this.config.max,
        min: this.config.min,
        timeout: this.config.connectionTimeoutMillis
      }
    });
  }
  
  /**
   * Update metrics (called by client implementations)
   */
  updateMetrics(update: Partial<ConnectionPoolMetrics>) {
    this.metrics = { ...this.metrics, ...update, lastUpdated: new Date() };
  }
  
  /**
   * Get current metrics
   */
  getMetrics(): ConnectionPoolMetrics {
    return { ...this.metrics };
  }
  
  /**
   * Cleanup resources
   */
  destroy() {
    if (this.metricsTimer) {
      clearInterval(this.metricsTimer);
      this.metricsTimer = undefined;
    }
    
    logger.info('Connection pool manager destroyed', {
      component: 'connection-pool'
    });
  }
}

// Global connection pool manager instance
let globalPoolManager: ConnectionPoolManager | undefined;

/**
 * Get or create the global connection pool manager
 */
export function getConnectionPoolManager(): ConnectionPoolManager {
  if (!globalPoolManager) {
    globalPoolManager = new ConnectionPoolManager();
  }
  return globalPoolManager;
}

/**
 * Initialize connection pool with custom configuration
 */
export function initializeConnectionPool(config?: Partial<ConnectionPoolConfig>): ConnectionPoolManager {
  if (globalPoolManager) {
    globalPoolManager.destroy();
  }
  globalPoolManager = new ConnectionPoolManager(config);
  return globalPoolManager;
}

/**
 * Health check for connection pool
 */
export async function checkConnectionPoolHealth(): Promise<{
  healthy: boolean;
  metrics: ConnectionPoolMetrics;
  config: ConnectionPoolConfig;
}> {
  const manager = getConnectionPoolManager();
  const metrics = manager.getMetrics();
  
  // Basic health checks
  const healthy = metrics.errorRate < 0.1 && // Less than 10% error rate
                  metrics.activeConnections <= manager['config'].max;
  
  return {
    healthy,
    metrics,
    config: manager['config']
  };
}
