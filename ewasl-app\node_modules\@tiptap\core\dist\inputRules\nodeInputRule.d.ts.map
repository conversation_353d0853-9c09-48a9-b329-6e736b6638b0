{"version": 3, "file": "nodeInputRule.d.ts", "sourceRoot": "", "sources": ["../../src/inputRules/nodeInputRule.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AAE3C,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAA;AAC5D,OAAO,EAAE,wBAAwB,EAAE,MAAM,aAAa,CAAA;AAGtD;;;;GAIG;AACH,wBAAgB,aAAa,CAAC,MAAM,EAAE;IACpC;;OAEG;IACH,IAAI,EAAE,eAAe,CAAA;IAErB;;OAEG;IACH,IAAI,EAAE,QAAQ,CAAA;IAEd;;;OAGG;IACH,aAAa,CAAC,EACV,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GACnB,CAAC,CAAC,KAAK,EAAE,wBAAwB,KAAK,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAC1D,KAAK,GACL,IAAI,CAAA;CACT,aAwCA"}