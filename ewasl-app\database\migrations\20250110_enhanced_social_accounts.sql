-- Enhanced Social Accounts Migration
-- This migration adds enhanced features for social account management

-- Create enum types if they don't exist
DO $$ BEGIN
    CREATE TYPE social_platform AS ENUM (
        'FACEBOOK', 'INSTAGRAM', 'TWITTER', 'LINKEDIN', 
        'TIKTOK', 'YOUTUBE', 'SNAPCHAT', 'PINTEREST'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE account_status AS ENUM (
        'connected', 'expired', 'error', 'reconnecting', 'disconnected'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create account groups table
CREATE TABLE IF NOT EXISTS account_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL, -- References users(id) for now, can be changed to organizations later
    name TEXT NOT NULL,
    description TEXT,
    color TEXT DEFAULT '#3B82F6',
    created_by UUID NOT NULL, -- References users(id)
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT account_groups_name_org_unique UNIQUE(organization_id, name)
);

-- Create account group memberships table
CREATE TABLE IF NOT EXISTS account_group_memberships (
    account_id UUID NOT NULL, -- References social_accounts(id)
    group_id UUID NOT NULL REFERENCES account_groups(id) ON DELETE CASCADE,
    added_by UUID NOT NULL, -- References users(id)
    added_at TIMESTAMPTZ DEFAULT NOW(),
    
    PRIMARY KEY (account_id, group_id)
);

-- Create rate limit tracking table
CREATE TABLE IF NOT EXISTS rate_limit_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL, -- References social_accounts(id)
    endpoint TEXT NOT NULL,
    requests_count INTEGER DEFAULT 0,
    window_start TIMESTAMPTZ NOT NULL,
    window_end TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT rate_limit_account_endpoint_window_unique 
    UNIQUE(account_id, endpoint, window_start)
);

-- Create audit logs table for tracking operations
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL, -- References users(id)
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_ids UUID[] DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_account_groups_organization_id ON account_groups(organization_id);
CREATE INDEX IF NOT EXISTS idx_account_groups_created_by ON account_groups(created_by);
CREATE INDEX IF NOT EXISTS idx_account_group_memberships_account_id ON account_group_memberships(account_id);
CREATE INDEX IF NOT EXISTS idx_account_group_memberships_group_id ON account_group_memberships(group_id);
CREATE INDEX IF NOT EXISTS idx_rate_limit_tracking_account_id ON rate_limit_tracking(account_id);
CREATE INDEX IF NOT EXISTS idx_rate_limit_tracking_window ON rate_limit_tracking(window_start, window_end);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);

-- Add enhanced columns to existing social_accounts table if they don't exist
DO $$ 
BEGIN
    -- Add platform column with enum type
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'social_accounts' AND column_name = 'platform_enum') THEN
        ALTER TABLE social_accounts ADD COLUMN platform_enum social_platform;
        
        -- Migrate existing platform data
        UPDATE social_accounts SET platform_enum = 
            CASE 
                WHEN UPPER(platform) = 'FACEBOOK' THEN 'FACEBOOK'::social_platform
                WHEN UPPER(platform) = 'INSTAGRAM' THEN 'INSTAGRAM'::social_platform
                WHEN UPPER(platform) = 'TWITTER' THEN 'TWITTER'::social_platform
                WHEN UPPER(platform) = 'LINKEDIN' THEN 'LINKEDIN'::social_platform
                WHEN UPPER(platform) = 'TIKTOK' THEN 'TIKTOK'::social_platform
                WHEN UPPER(platform) = 'YOUTUBE' THEN 'YOUTUBE'::social_platform
                WHEN UPPER(platform) = 'SNAPCHAT' THEN 'SNAPCHAT'::social_platform
                WHEN UPPER(platform) = 'PINTEREST' THEN 'PINTEREST'::social_platform
                ELSE 'FACEBOOK'::social_platform -- Default fallback
            END
        WHERE platform_enum IS NULL;
    END IF;
    
    -- Add connection status column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'social_accounts' AND column_name = 'connection_status') THEN
        ALTER TABLE social_accounts ADD COLUMN connection_status account_status DEFAULT 'connected';
    END IF;
    
    -- Add last validated timestamp
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'social_accounts' AND column_name = 'last_validated_at') THEN
        ALTER TABLE social_accounts ADD COLUMN last_validated_at TIMESTAMPTZ DEFAULT NOW();
    END IF;
    
    -- Add metadata column for storing additional account information
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'social_accounts' AND column_name = 'metadata') THEN
        ALTER TABLE social_accounts ADD COLUMN metadata JSONB DEFAULT '{}';
    END IF;
    
    -- Add profile image URL column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'social_accounts' AND column_name = 'profile_image_url') THEN
        ALTER TABLE social_accounts ADD COLUMN profile_image_url TEXT;
    END IF;
    
    -- Add account handle column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'social_accounts' AND column_name = 'account_handle') THEN
        ALTER TABLE social_accounts ADD COLUMN account_handle TEXT;
    END IF;
    
    -- Add permissions column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'social_accounts' AND column_name = 'permissions') THEN
        ALTER TABLE social_accounts ADD COLUMN permissions JSONB DEFAULT '["read", "write"]';
    END IF;
    
END $$;

-- Create foreign key constraints (will be ignored if they already exist)
DO $$ 
BEGIN
    -- Add foreign key for account_group_memberships -> social_accounts
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'fk_account_group_memberships_account_id') THEN
        ALTER TABLE account_group_memberships 
        ADD CONSTRAINT fk_account_group_memberships_account_id 
        FOREIGN KEY (account_id) REFERENCES social_accounts(id) ON DELETE CASCADE;
    END IF;
    
    -- Add foreign key for rate_limit_tracking -> social_accounts
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'fk_rate_limit_tracking_account_id') THEN
        ALTER TABLE rate_limit_tracking 
        ADD CONSTRAINT fk_rate_limit_tracking_account_id 
        FOREIGN KEY (account_id) REFERENCES social_accounts(id) ON DELETE CASCADE;
    END IF;
    
EXCEPTION
    WHEN others THEN
        -- Log the error but don't fail the migration
        RAISE NOTICE 'Warning: Could not create some foreign key constraints: %', SQLERRM;
END $$;

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
DO $$ 
BEGIN
    -- Trigger for account_groups
    IF NOT EXISTS (SELECT 1 FROM information_schema.triggers 
                   WHERE trigger_name = 'update_account_groups_updated_at') THEN
        CREATE TRIGGER update_account_groups_updated_at 
        BEFORE UPDATE ON account_groups 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    -- Trigger for social_accounts (if it doesn't already exist)
    IF NOT EXISTS (SELECT 1 FROM information_schema.triggers 
                   WHERE trigger_name = 'update_social_accounts_updated_at') THEN
        CREATE TRIGGER update_social_accounts_updated_at 
        BEFORE UPDATE ON social_accounts 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Warning: Could not create some triggers: %', SQLERRM;
END $$;

-- Insert some default account groups for demonstration
INSERT INTO account_groups (organization_id, name, description, color, created_by)
SELECT 
    id as organization_id,
    'Personal Accounts' as name,
    'Personal social media accounts' as description,
    '#3B82F6' as color,
    id as created_by
FROM (
    SELECT DISTINCT user_id as id 
    FROM social_accounts 
    WHERE user_id IS NOT NULL
    LIMIT 10 -- Limit to avoid too many inserts
) users
ON CONFLICT (organization_id, name) DO NOTHING;

INSERT INTO account_groups (organization_id, name, description, color, created_by)
SELECT 
    id as organization_id,
    'Business Accounts' as name,
    'Business and brand social media accounts' as description,
    '#10B981' as color,
    id as created_by
FROM (
    SELECT DISTINCT user_id as id 
    FROM social_accounts 
    WHERE user_id IS NOT NULL
    LIMIT 10 -- Limit to avoid too many inserts
) users
ON CONFLICT (organization_id, name) DO NOTHING;

-- Update existing social_accounts with enhanced data
UPDATE social_accounts 
SET 
    connection_status = 'connected',
    last_validated_at = COALESCE(updated_at, created_at, NOW()),
    metadata = COALESCE(metadata, '{}'),
    permissions = COALESCE(permissions, '["read", "write"]')
WHERE connection_status IS NULL OR last_validated_at IS NULL;

COMMIT;
