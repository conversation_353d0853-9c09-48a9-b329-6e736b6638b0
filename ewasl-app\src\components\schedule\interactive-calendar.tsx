'use client';

import React, { useState, useCallback } from 'react';
import { Calendar, momentLocalizer, View, Views } from 'react-big-calendar';
import moment from 'moment';
import 'moment/locale/ar-sa';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronLeft, 
  ChevronRight, 
  Calendar as CalendarIcon,
  Clock,
  Edit,
  Trash2
} from 'lucide-react';

// Configure moment for Arabic locale
moment.locale('ar-sa');
const localizer = momentLocalizer(moment);

interface ScheduledPost {
  id: string;
  title: string;
  content: string;
  start: Date;
  end: Date;
  status: 'DRAFT' | 'SCHEDULED' | 'PUBLISHED' | 'FAILED';
  platforms: string[];
  resource?: any;
}

interface InteractiveCalendarProps {
  posts: ScheduledPost[];
  onSelectEvent?: (event: ScheduledPost) => void;
  onSelectSlot?: (slotInfo: { start: Date; end: Date }) => void;
  onEventDrop?: (event: { event: ScheduledPost; start: Date; end: Date }) => void;
}

export function InteractiveCalendar({ 
  posts, 
  onSelectEvent, 
  onSelectSlot, 
  onEventDrop 
}: InteractiveCalendarProps) {
  const [view, setView] = useState<View>(Views.MONTH);
  const [date, setDate] = useState(new Date());

  // Custom event component
  const EventComponent = ({ event }: { event: ScheduledPost }) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'DRAFT': return 'bg-gray-500';
        case 'SCHEDULED': return 'bg-blue-500';
        case 'PUBLISHED': return 'bg-green-500';
        case 'FAILED': return 'bg-red-500';
        default: return 'bg-gray-500';
      }
    };

    const getPlatformIcon = (platform: string) => {
      switch (platform) {
        case 'TWITTER': return '𝕏';
        case 'FACEBOOK': return '📘';
        case 'INSTAGRAM': return '📷';
        case 'LINKEDIN': return '💼';
        case 'TIKTOK': return '🎵';
        default: return '📱';
      }
    };

    return (
      <div className={`p-1 rounded text-white text-xs ${getStatusColor(event.status)}`}>
        <div className="font-medium truncate">{event.title}</div>
        <div className="flex items-center gap-1 mt-1">
          <Clock className="w-3 h-3" />
          <span>{moment(event.start).format('HH:mm')}</span>
          <div className="flex ml-1">
            {event.platforms.slice(0, 2).map((platform, index) => (
              <span key={index} className="text-xs">
                {getPlatformIcon(platform)}
              </span>
            ))}
            {event.platforms.length > 2 && (
              <span className="text-xs">+{event.platforms.length - 2}</span>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Custom toolbar
  const CustomToolbar = ({ label, onNavigate, onView }: any) => {
    return (
      <div className="flex items-center justify-between mb-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onNavigate('PREV')}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onNavigate('TODAY')}
          >
            اليوم
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onNavigate('NEXT')}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
        </div>
        
        <h2 className="text-lg font-semibold">{label}</h2>
        
        <div className="flex items-center gap-2">
          <Button
            variant={view === Views.MONTH ? 'default' : 'outline'}
            size="sm"
            onClick={() => onView(Views.MONTH)}
          >
            شهر
          </Button>
          <Button
            variant={view === Views.WEEK ? 'default' : 'outline'}
            size="sm"
            onClick={() => onView(Views.WEEK)}
          >
            أسبوع
          </Button>
          <Button
            variant={view === Views.DAY ? 'default' : 'outline'}
            size="sm"
            onClick={() => onView(Views.DAY)}
          >
            يوم
          </Button>
        </div>
      </div>
    );
  };

  const handleSelectEvent = useCallback((event: ScheduledPost) => {
    onSelectEvent?.(event);
  }, [onSelectEvent]);

  const handleSelectSlot = useCallback((slotInfo: { start: Date; end: Date }) => {
    onSelectSlot?.(slotInfo);
  }, [onSelectSlot]);

  const handleEventDrop = useCallback(({ event, start, end }: any) => {
    onEventDrop?.({ event, start, end });
  }, [onEventDrop]);

  // Custom messages for Arabic
  const messages = {
    allDay: 'طوال اليوم',
    previous: 'السابق',
    next: 'التالي',
    today: 'اليوم',
    month: 'شهر',
    week: 'أسبوع',
    day: 'يوم',
    agenda: 'جدول الأعمال',
    date: 'التاريخ',
    time: 'الوقت',
    event: 'حدث',
    noEventsInRange: 'لا توجد أحداث في هذا النطاق',
    showMore: (total: number) => `+${total} أكثر`,
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CalendarIcon className="w-5 h-5" />
          التقويم التفاعلي
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div style={{ height: '600px' }} className="rtl-calendar">
          <Calendar
            localizer={localizer}
            events={posts}
            startAccessor="start"
            endAccessor="end"
            view={view}
            onView={setView}
            date={date}
            onNavigate={setDate}
            onSelectEvent={handleSelectEvent}
            onSelectSlot={handleSelectSlot}
            onEventDrop={handleEventDrop}
            selectable
            resizable
            popup
            components={{
              event: EventComponent,
              toolbar: CustomToolbar,
            }}
            messages={messages}
            formats={{
              monthHeaderFormat: 'MMMM YYYY',
              dayHeaderFormat: 'dddd DD/MM',
              dayRangeHeaderFormat: ({ start, end }) => 
                `${moment(start).format('DD/MM')} - ${moment(end).format('DD/MM')}`,
              timeGutterFormat: 'HH:mm',
              eventTimeRangeFormat: ({ start, end }) => 
                `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`,
            }}
            style={{
              direction: 'rtl',
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}
