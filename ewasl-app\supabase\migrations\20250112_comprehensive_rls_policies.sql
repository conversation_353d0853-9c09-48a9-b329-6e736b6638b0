-- Comprehensive Row-Level Security (RLS) Policies
-- Implements production-grade security for all eWasl tables
-- Part of Phase 1 Critical Fixes

-- Enable RLS on all core tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_social_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.media_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scheduled_posts_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.hashtag_suggestions ENABLE ROW LEVEL SECURITY;

-- Enable RLS on analytics tables
ALTER TABLE public.analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.account_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.hashtag_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.realtime_metrics ENABLE ROW LEVEL SECURITY;

-- Enable RLS on OAuth and security tables
ALTER TABLE public.oauth_states ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.oauth_logs ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can manage their own posts" ON public.posts;
DROP POLICY IF EXISTS "Users can manage their post social accounts" ON public.post_social_accounts;
DROP POLICY IF EXISTS "Users can manage their own social accounts" ON public.social_accounts;

-- =============================================================================
-- USERS TABLE POLICIES
-- =============================================================================

-- Users can view and update their own profile
CREATE POLICY "users_select_own" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "users_update_own" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- =============================================================================
-- SOCIAL ACCOUNTS TABLE POLICIES
-- =============================================================================

-- Users can manage their own social accounts
CREATE POLICY "social_accounts_all_own" ON public.social_accounts
    FOR ALL USING (auth.uid() = user_id);

-- =============================================================================
-- POSTS TABLE POLICIES
-- =============================================================================

-- Users can manage their own posts
CREATE POLICY "posts_all_own" ON public.posts
    FOR ALL USING (auth.uid() = user_id);

-- =============================================================================
-- POST SOCIAL ACCOUNTS TABLE POLICIES
-- =============================================================================

-- Users can manage post-social account relationships for their own posts
CREATE POLICY "post_social_accounts_all_own" ON public.post_social_accounts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.posts 
            WHERE posts.id = post_social_accounts.post_id 
            AND posts.user_id = auth.uid()
        )
    );

-- =============================================================================
-- MEDIA FILES TABLE POLICIES
-- =============================================================================

-- Users can manage their own media files
CREATE POLICY "media_files_all_own" ON public.media_files
    FOR ALL USING (auth.uid() = user_id);

-- =============================================================================
-- SCHEDULED POSTS QUEUE TABLE POLICIES
-- =============================================================================

-- Users can manage their own scheduled posts
CREATE POLICY "scheduled_posts_queue_all_own" ON public.scheduled_posts_queue
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.posts 
            WHERE posts.id = scheduled_posts_queue.post_id 
            AND posts.user_id = auth.uid()
        )
    );

-- =============================================================================
-- HASHTAG SUGGESTIONS TABLE POLICIES
-- =============================================================================

-- Users can view all hashtag suggestions (public data)
-- Users can only insert/update their own usage data
CREATE POLICY "hashtag_suggestions_select_all" ON public.hashtag_suggestions
    FOR SELECT USING (true);

CREATE POLICY "hashtag_suggestions_insert_own" ON public.hashtag_suggestions
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "hashtag_suggestions_update_own" ON public.hashtag_suggestions
    FOR UPDATE USING (auth.uid() = user_id OR user_id IS NULL);

-- =============================================================================
-- ANALYTICS EVENTS TABLE POLICIES
-- =============================================================================

-- Users can view and insert their own analytics events
CREATE POLICY "analytics_events_select_own" ON public.analytics_events
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "analytics_events_insert_own" ON public.analytics_events
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- =============================================================================
-- POST ANALYTICS TABLE POLICIES
-- =============================================================================

-- Users can view analytics for their own posts
CREATE POLICY "post_analytics_select_own" ON public.post_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.posts
            WHERE posts.id = post_analytics.post_id
            AND posts.user_id = auth.uid()
        )
    );

-- Service role can insert/update analytics data
CREATE POLICY "post_analytics_service_role" ON public.post_analytics
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================================================
-- ACCOUNT ANALYTICS TABLE POLICIES
-- =============================================================================

-- Users can view analytics for their own social accounts
CREATE POLICY "account_analytics_select_own" ON public.account_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.social_accounts
            WHERE social_accounts.id = account_analytics.social_account_id
            AND social_accounts.user_id = auth.uid()
        )
    );

-- Service role can insert/update analytics data
CREATE POLICY "account_analytics_service_role" ON public.account_analytics
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================================================
-- CONTENT INSIGHTS TABLE POLICIES
-- =============================================================================

-- Users can view insights for their own content
CREATE POLICY "content_insights_select_own" ON public.content_insights
    FOR SELECT USING (auth.uid() = user_id);

-- Service role can insert/update insights data
CREATE POLICY "content_insights_service_role" ON public.content_insights
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================================================
-- HASHTAG ANALYTICS TABLE POLICIES
-- =============================================================================

-- All users can view hashtag analytics (public trend data)
CREATE POLICY "hashtag_analytics_select_all" ON public.hashtag_analytics
    FOR SELECT USING (true);

-- Only service role can insert/update hashtag analytics
CREATE POLICY "hashtag_analytics_service_role" ON public.hashtag_analytics
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================================================
-- ANALYTICS REPORTS TABLE POLICIES
-- =============================================================================

-- Users can manage their own analytics reports
CREATE POLICY "analytics_reports_all_own" ON public.analytics_reports
    FOR ALL USING (auth.uid() = user_id);

-- =============================================================================
-- REALTIME METRICS TABLE POLICIES
-- =============================================================================

-- Users can view realtime metrics for their own content
CREATE POLICY "realtime_metrics_select_own" ON public.realtime_metrics
    FOR SELECT USING (auth.uid() = user_id);

-- Service role can insert/update realtime metrics
CREATE POLICY "realtime_metrics_service_role" ON public.realtime_metrics
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================================================
-- OAUTH STATES TABLE POLICIES
-- =============================================================================

-- Users can manage their own OAuth states
CREATE POLICY "oauth_states_all_own" ON public.oauth_states
    FOR ALL USING (auth.uid() = user_id);

-- =============================================================================
-- OAUTH LOGS TABLE POLICIES
-- =============================================================================

-- Users can view their own OAuth logs
CREATE POLICY "oauth_logs_select_own" ON public.oauth_logs
    FOR SELECT USING (auth.uid() = user_id);

-- Service role can insert OAuth logs
CREATE POLICY "oauth_logs_service_insert" ON public.oauth_logs
    FOR INSERT WITH CHECK (auth.role() = 'service_role' OR auth.uid() = user_id);

-- =============================================================================
-- SECURITY FUNCTIONS
-- =============================================================================

-- Function to check if user owns a post
CREATE OR REPLACE FUNCTION public.user_owns_post(post_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.posts 
        WHERE id = post_id AND user_id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user owns a social account
CREATE OR REPLACE FUNCTION public.user_owns_social_account(account_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.social_accounts 
        WHERE id = account_id AND user_id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- INDEXES FOR RLS PERFORMANCE
-- =============================================================================

-- Indexes to optimize RLS policy performance
CREATE INDEX IF NOT EXISTS idx_posts_user_id_rls ON public.posts(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_social_accounts_user_id_rls ON public.social_accounts(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_media_files_user_id_rls ON public.media_files(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id_rls ON public.analytics_events(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_oauth_states_user_id_rls ON public.oauth_states(user_id) WHERE user_id IS NOT NULL;

-- Composite indexes for junction tables
CREATE INDEX IF NOT EXISTS idx_post_social_accounts_post_id_rls ON public.post_social_accounts(post_id);
CREATE INDEX IF NOT EXISTS idx_scheduled_posts_queue_post_id_rls ON public.scheduled_posts_queue(post_id);

-- =============================================================================
-- GRANT PERMISSIONS
-- =============================================================================

-- Grant necessary permissions to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Grant permissions to service role for analytics
GRANT ALL ON public.post_analytics TO service_role;
GRANT ALL ON public.account_analytics TO service_role;
GRANT ALL ON public.hashtag_analytics TO service_role;
GRANT ALL ON public.realtime_metrics TO service_role;

-- =============================================================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================================================

COMMENT ON POLICY "users_select_own" ON public.users IS 'Users can only view their own profile data';
COMMENT ON POLICY "social_accounts_all_own" ON public.social_accounts IS 'Users can only manage their own social media accounts';
COMMENT ON POLICY "posts_all_own" ON public.posts IS 'Users can only manage their own posts';
COMMENT ON POLICY "post_social_accounts_all_own" ON public.post_social_accounts IS 'Users can only manage social account associations for their own posts';
COMMENT ON POLICY "media_files_all_own" ON public.media_files IS 'Users can only manage their own media files';

-- Log successful RLS setup
INSERT INTO public.oauth_logs (user_id, action, platform, details, created_at)
SELECT 
    auth.uid(),
    'rls_policies_applied',
    'system',
    jsonb_build_object(
        'migration', '20250112_comprehensive_rls_policies',
        'tables_secured', 17,
        'policies_created', 25
    ),
    NOW()
WHERE auth.uid() IS NOT NULL;
