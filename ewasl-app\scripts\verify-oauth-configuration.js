#!/usr/bin/env node

/**
 * OAuth Configuration Verification Script
 * Cross-checks platform console settings with our deployment configuration
 */

const https = require('https');

console.log('🔍 OAuth Configuration Verification');
console.log('===================================\n');

// Expected configuration based on your screenshots
const EXPECTED_CONFIG = {
  facebook: {
    clientId: '1366325774493759',
    clientSecret: '0634e2f1d4552a6bb2dfa98287894328',
    redirectUri: 'https://app.ewasl.com/api/facebook/callback',
    mode: 'LIVE_REQUIRED', // Currently in Development mode - CRITICAL ISSUE
    validRedirectUris: [
      'https://app.ewasl.com/api/facebook/callback'
    ],
    invalidRedirectUris: [
      'https://app.ewasl.com/api/social/callback/facebook',
      'https://app.ewasl.com/api/social/callback/instagram'
    ]
  },
  linkedin: {
    clientId: '787coegnsdocvq',
    clientSecret: 'WPL_AP1.F6yN8tH55DHmqsqP.ep1SGQ==',
    redirectUri: 'https://app.ewasl.com/api/linkedin/callback',
    validRedirectUris: [
      'https://app.ewasl.com/api/linkedin/callback'
    ],
    invalidRedirectUris: [
      'https://app.ewasl.com/api/social/callback/linkedin'
    ]
  },
  twitter: {
    clientId: 'RkFrLW9icHNaSVvaQUtrakljY6MTpjaQ',
    clientSecret: 'x76PHgEK-CMH_AV-CFsd5dkEabeoSFN-x-p5zv3HAQzliLYp5m',
    redirectUri: 'https://app.ewasl.com/api/x/callback'
  }
};

function verifyConfiguration() {
  console.log('📋 Configuration Verification Report');
  console.log('===================================\n');
  
  console.log('🔍 **FACEBOOK ANALYSIS**:');
  console.log(`   ✅ App ID: ${EXPECTED_CONFIG.facebook.clientId}`);
  console.log(`   ✅ App Secret: ${EXPECTED_CONFIG.facebook.clientSecret.slice(0, 8)}...`);
  console.log(`   ❌ App Mode: Development (MUST SWITCH TO LIVE)`);
  console.log(`   ✅ Correct Redirect: ${EXPECTED_CONFIG.facebook.redirectUri}`);
  console.log(`   ❌ Extra Redirects Found: ${EXPECTED_CONFIG.facebook.invalidRedirectUris.join(', ')}`);
  console.log('');
  
  console.log('🔍 **LINKEDIN ANALYSIS**:');
  console.log(`   ✅ Client ID: ${EXPECTED_CONFIG.linkedin.clientId}`);
  console.log(`   ✅ Client Secret: ${EXPECTED_CONFIG.linkedin.clientSecret.slice(0, 12)}...`);
  console.log(`   ⚠️  Multiple secrets active (use PRIMARY only)`);
  console.log(`   ✅ Correct Redirect: ${EXPECTED_CONFIG.linkedin.redirectUri}`);
  console.log(`   ❌ Extra Redirect Found: ${EXPECTED_CONFIG.linkedin.invalidRedirectUris.join(', ')}`);
  console.log('');
  
  console.log('🔍 **TWITTER/X ANALYSIS**:');
  console.log(`   ✅ Client ID: ${EXPECTED_CONFIG.twitter.clientId}`);
  console.log(`   ✅ Client Secret: ${EXPECTED_CONFIG.twitter.clientSecret.slice(0, 8)}...`);
  console.log(`   ✅ Redirect URI: ${EXPECTED_CONFIG.twitter.redirectUri}`);
  console.log('');
  
  console.log('🚨 **CRITICAL FIXES NEEDED**:');
  console.log('============================');
  console.log('1. 🔴 FACEBOOK: Switch from Development to Live mode');
  console.log('2. 🟡 FACEBOOK: Remove extra redirect URIs');
  console.log('3. 🟡 LINKEDIN: Remove extra redirect URI');
  console.log('4. 🟡 LINKEDIN: Ensure using PRIMARY client secret only');
  console.log('');
  
  console.log('📝 **EXPECTED vs ACTUAL COMPARISON**:');
  console.log('=====================================');
  console.log('');
  console.log('**Digital Ocean Environment Variables Should Be**:');
  console.log('```');
  console.log(`FACEBOOK_CLIENT_ID="${EXPECTED_CONFIG.facebook.clientId}"`);
  console.log(`FACEBOOK_CLIENT_SECRET="${EXPECTED_CONFIG.facebook.clientSecret}"`);
  console.log(`LINKEDIN_CLIENT_ID="${EXPECTED_CONFIG.linkedin.clientId}"`);
  console.log(`LINKEDIN_CLIENT_SECRET="${EXPECTED_CONFIG.linkedin.clientSecret}"`);
  console.log(`X_CLIENT_ID="${EXPECTED_CONFIG.twitter.clientId}"`);
  console.log(`X_CLIENT_SECRET="${EXPECTED_CONFIG.twitter.clientSecret}"`);
  console.log('```');
  console.log('');
  
  console.log('**Platform Console Redirect URIs Should Be**:');
  console.log('Facebook: https://app.ewasl.com/api/facebook/callback');
  console.log('LinkedIn: https://app.ewasl.com/api/linkedin/callback');
  console.log('Twitter/X: https://app.ewasl.com/api/x/callback');
  console.log('');
}

async function main() {
  verifyConfiguration();
  
  console.log('🎯 **NEXT STEPS**:');
  console.log('==================');
  console.log('1. Fix Facebook app mode (Development → Live)');
  console.log('2. Clean up redirect URIs in both platforms');
  console.log('3. Verify Digital Ocean env vars match exactly');
  console.log('4. Test OAuth flows after each fix');
  console.log('');
  console.log('Once these are fixed, all OAuth errors should resolve! 🚀');
}

if (require.main === module) {
  main();
}

module.exports = { verifyConfiguration, EXPECTED_CONFIG }; 