/**
 * OAuth Disconnect API
 * Handles disconnecting social media accounts
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { OAuthConnectionService } from '@/lib/oauth/connection-service';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const { user } = await getAuthenticatedUser(request);
    
    const { platform, accountId } = await request.json();
    
    if (!platform || !accountId) {
      return NextResponse.json(
        { error: 'Platform and accountId are required' },
        { status: 400 }
      );
    }

    const connectionService = new OAuthConnectionService();
    
    // Disconnect the account
    const success = await connectionService.disconnectAccount(
      user.id,
      platform,
      accountId
    );
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to disconnect account' },
        { status: 500 }
      );
    }
    
    console.log(`Account disconnected:`, {
      userId: user.id,
      platform,
      accountId,
    });
    
    return NextResponse.json({
      success: true,
      message: 'Account disconnected successfully',
    });
    
  } catch (error: any) {
    console.error('Disconnect error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to disconnect account' },
      { status: 500 }
    );
  }
}
