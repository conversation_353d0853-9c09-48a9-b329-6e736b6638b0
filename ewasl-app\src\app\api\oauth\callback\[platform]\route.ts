/**
 * OAuth Callback Handler
 * Handles OAuth callbacks from social media platforms
 */

import { NextRequest, NextResponse } from 'next/server';
import { OAuthConnectionService } from '@/lib/oauth/connection-service';
import { getOAuthProvider, exchangeCodeForToken, getUserProfile } from '@/lib/oauth/providers';

export async function GET(
  request: NextRequest,
  { params }: { params: { platform: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const platform = params.platform;

    // Handle OAuth errors
    if (error) {
      console.error(`OAuth error for ${platform}:`, error);
      const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/social?error=oauth_error&platform=${platform}&message=${encodeURIComponent(error)}`;
      return NextResponse.redirect(redirectUrl);
    }

    // Validate required parameters
    if (!code || !state) {
      const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/social?error=missing_params&platform=${platform}`;
      return NextResponse.redirect(redirectUrl);
    }

    // Get OAuth provider configuration
    const provider = getOAuthProvider(platform);
    if (!provider || !provider.enabled) {
      const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/social?error=provider_not_configured&platform=${platform}`;
      return NextResponse.redirect(redirectUrl);
    }

    const connectionService = new OAuthConnectionService();
    
    // Verify OAuth state
    const oauthState = await connectionService.verifyOAuthState(state, platform);
    if (!oauthState) {
      const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/social?error=invalid_state&platform=${platform}`;
      return NextResponse.redirect(redirectUrl);
    }

    try {
      // Exchange authorization code for access token
      const tokens = await exchangeCodeForToken(
        provider,
        code,
        oauthState.redirectUri,
        oauthState.codeVerifier
      );

      // Get user profile information
      const profile = await getUserProfile(provider, tokens.accessToken);

      // Store the connection
      const connection = await connectionService.storeConnection(
        oauthState.userId,
        platform,
        tokens,
        profile
      );

      console.log(`OAuth connection successful for ${platform}:`, {
        userId: oauthState.userId,
        platform,
        accountId: profile.id,
        accountName: profile.name,
      });

      // Redirect to social page with success message
      const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/social?success=connected&platform=${platform}&account=${encodeURIComponent(profile.name)}`;
      return NextResponse.redirect(redirectUrl);

    } catch (tokenError: any) {
      console.error(`Token exchange failed for ${platform}:`, tokenError);
      const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/social?error=token_exchange_failed&platform=${platform}&message=${encodeURIComponent(tokenError.message)}`;
      return NextResponse.redirect(redirectUrl);
    }

  } catch (error: any) {
    console.error(`OAuth callback error for ${params.platform}:`, error);
    const redirectUrl = `${process.env.NEXT_PUBLIC_APP_URL}/social?error=callback_error&platform=${params.platform}&message=${encodeURIComponent(error.message)}`;
    return NextResponse.redirect(redirectUrl);
  }
}
