/**
 * Token Refresh Service
 * Handles automatic token refresh for social media platforms
 * Focuses on Facebook/Instagram token management
 */

import { createClient } from '@/lib/supabase/client'
import { FacebookOAuthService } from './facebook'
import crypto from 'crypto'

export interface TokenRefreshResult {
  success: boolean
  newAccessToken?: string
  newPageAccessToken?: string
  expiresAt?: Date
  error?: string
}

export interface SocialAccountToken {
  id: string
  platform: string
  account_id: string
  access_token: string
  page_access_token?: string
  page_id?: string
  expires_at?: string
  token_expires_at?: string
  connection_status: string
}

export class TokenRefreshService {
  private supabase = createClient()
  private facebookService: FacebookOAuthService

  constructor() {
    this.facebookService = new FacebookOAuthService({
      appId: process.env.FACEBOOK_APP_ID!,
      appSecret: process.env.FACEBOOK_APP_SECRET!,
      businessId: process.env.FACEBOOK_BUSINESS_ID!,
      redirectUri: `${process.env.NEXT_PUBLIC_APP_URL}/api/oauth/facebook/callback`
    })
  }

  /**
   * Check and refresh tokens that are about to expire
   */
  async checkAndRefreshExpiredTokens(): Promise<void> {
    console.log('🔄 Starting token refresh check...')

    try {
      // Get tokens that expire within the next 7 days
      const expirationThreshold = new Date()
      expirationThreshold.setDate(expirationThreshold.getDate() + 7)

      const { data: accounts, error } = await this.supabase
        .from('social_accounts')
        .select('*')
        .in('platform', ['FACEBOOK', 'INSTAGRAM'])
        .or(`expires_at.lt.${expirationThreshold.toISOString()},token_expires_at.lt.${expirationThreshold.toISOString()}`)
        .eq('connection_status', 'connected')

      if (error) {
        console.error('❌ Error fetching accounts for token refresh:', error)
        return
      }

      if (!accounts || accounts.length === 0) {
        console.log('✅ No tokens need refreshing')
        return
      }

      console.log(`🔄 Found ${accounts.length} accounts with tokens needing refresh`)

      // Refresh tokens for each account
      for (const account of accounts) {
        await this.refreshAccountToken(account as SocialAccountToken)
      }

    } catch (error) {
      console.error('❌ Error in token refresh check:', error)
    }
  }

  /**
   * Refresh token for a specific account
   */
  async refreshAccountToken(account: SocialAccountToken): Promise<TokenRefreshResult> {
    console.log(`🔄 Refreshing token for account: ${account.account_id} (${account.platform})`)

    try {
      switch (account.platform) {
        case 'FACEBOOK':
          return await this.refreshFacebookToken(account)
        case 'INSTAGRAM':
          return await this.refreshInstagramToken(account)
        default:
          return {
            success: false,
            error: `Token refresh not supported for platform: ${account.platform}`
          }
      }
    } catch (error) {
      console.error(`❌ Error refreshing token for ${account.account_id}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Refresh Facebook user and page tokens
   */
  private async refreshFacebookToken(account: SocialAccountToken): Promise<TokenRefreshResult> {
    try {
      console.log(`📘 Refreshing Facebook token for: ${account.account_id}`)

      // Step 1: Refresh the user access token
      const refreshedUserToken = await this.facebookService.getLongLivedToken(account.access_token)
      
      if (!refreshedUserToken.access_token) {
        throw new Error('Failed to refresh Facebook user token')
      }

      console.log('✅ Facebook user token refreshed successfully')

      // Step 2: Get updated page information with new page tokens
      const updatedAccounts = await this.facebookService.getUserAccounts(refreshedUserToken.access_token)
      const matchingAccount = updatedAccounts.find(acc => acc.pageId === account.page_id)

      if (!matchingAccount) {
        throw new Error('Could not find matching page after token refresh')
      }

      // Step 3: Calculate new expiration date (Facebook long-lived tokens last ~60 days)
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 55) // Refresh 5 days before actual expiration

      // Step 4: Update database with new tokens
      const { error: updateError } = await this.supabase
        .from('social_accounts')
        .update({
          access_token: refreshedUserToken.access_token,
          page_access_token: matchingAccount.pageAccessToken,
          expires_at: expiresAt.toISOString(),
          token_expires_at: expiresAt.toISOString(),
          connection_status: 'connected',
          last_validated_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', account.id)

      if (updateError) {
        throw new Error(`Failed to update database: ${updateError.message}`)
      }

      console.log(`✅ Facebook token refresh completed for: ${account.account_id}`)

      return {
        success: true,
        newAccessToken: refreshedUserToken.access_token,
        newPageAccessToken: matchingAccount.pageAccessToken,
        expiresAt: expiresAt
      }

    } catch (error) {
      console.error(`❌ Facebook token refresh failed for ${account.account_id}:`, error)

      // Mark account as having token issues
      await this.markAccountAsExpired(account.id, error instanceof Error ? error.message : 'Token refresh failed')

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Refresh Instagram token (uses Facebook token refresh since Instagram uses Facebook Graph API)
   */
  private async refreshInstagramToken(account: SocialAccountToken): Promise<TokenRefreshResult> {
    // Instagram Business accounts use Facebook page tokens
    // So we refresh the associated Facebook page token
    return await this.refreshFacebookToken(account)
  }

  /**
   * Mark account as expired and requiring re-authentication
   */
  private async markAccountAsExpired(accountId: string, reason: string): Promise<void> {
    try {
      await this.supabase
        .from('social_accounts')
        .update({
          connection_status: 'expired',
          metadata: {
            error_reason: reason,
            expired_at: new Date().toISOString()
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', accountId)

      console.log(`⚠️  Marked account ${accountId} as expired: ${reason}`)
    } catch (error) {
      console.error(`❌ Failed to mark account as expired:`, error)
    }
  }

  /**
   * Validate token by making a test API call
   */
  async validateToken(account: SocialAccountToken): Promise<boolean> {
    try {
      const accessToken = account.page_access_token || account.access_token
      const appSecretProof = this.generateAppSecretProof(accessToken)

      const response = await fetch(
        `https://graph.facebook.com/v18.0/me?access_token=${accessToken}&appsecret_proof=${appSecretProof}`
      )

      if (response.ok) {
        console.log(`✅ Token validation successful for: ${account.account_id}`)
        return true
      } else {
        const error = await response.json()
        console.log(`❌ Token validation failed for ${account.account_id}:`, error.error?.message)
        return false
      }
    } catch (error) {
      console.error(`❌ Token validation error for ${account.account_id}:`, error)
      return false
    }
  }

  /**
   * Generate appsecret_proof for secure API calls
   */
  private generateAppSecretProof(accessToken: string): string {
    const appSecret = process.env.FACEBOOK_APP_SECRET
    if (!appSecret) {
      throw new Error('FACEBOOK_APP_SECRET environment variable is required')
    }
    return crypto.createHmac('sha256', appSecret).update(accessToken).digest('hex')
  }

  /**
   * Get accounts that need token refresh soon
   */
  async getAccountsNeedingRefresh(daysAhead: number = 7): Promise<SocialAccountToken[]> {
    const threshold = new Date()
    threshold.setDate(threshold.getDate() + daysAhead)

    const { data: accounts, error } = await this.supabase
      .from('social_accounts')
      .select('*')
      .in('platform', ['FACEBOOK', 'INSTAGRAM'])
      .or(`expires_at.lt.${threshold.toISOString()},token_expires_at.lt.${threshold.toISOString()}`)
      .eq('connection_status', 'connected')

    if (error) {
      console.error('Error fetching accounts needing refresh:', error)
      return []
    }

    return (accounts || []) as SocialAccountToken[]
  }
}
