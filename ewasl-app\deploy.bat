@echo off
echo ========================================
echo eWasl Social Media Platform Deployment
echo ========================================
echo.

echo 🔍 Checking prerequisites...
if not exist package.json (
    echo ❌ package.json not found. Make sure you're in the ewasl-app directory.
    pause
    exit /b 1
)

echo ✅ Prerequisites check completed
echo.

echo 📦 Installing Vercel CLI...
call npm install -g vercel
if %errorlevel% neq 0 (
    echo ⚠️  Vercel CLI installation failed, trying with npx...
)

echo.
echo 🚀 Starting deployment...
echo.

echo 🔐 Please login to Vercel when prompted...
call npx vercel login

echo.
echo 📤 Deploying to production...
call npx vercel --prod

if %errorlevel% equ 0 (
    echo.
    echo 🎉 Deployment completed successfully!
    echo.
    echo 📋 Next steps:
    echo 1. Note your deployment URL from the output above
    echo 2. Go to https://vercel.com/dashboard
    echo 3. Select your project and go to Settings ^> Environment Variables
    echo 4. Add the environment variables from PRODUCTION_DEPLOYMENT_GUIDE.md
    echo 5. Update Facebook app settings with your new domain
    echo 6. Redeploy: npx vercel --prod
    echo.
    echo 🧪 Test your deployment:
    echo - Visit your deployment URL
    echo - Test post creation at /posts/new
    echo - Use debug tools at /debug-post
    echo.
) else (
    echo.
    echo ❌ Deployment failed. Please check the error messages above.
    echo.
    echo 🔧 Manual deployment steps:
    echo 1. Install Vercel CLI: npm install -g vercel
    echo 2. Login: vercel login
    echo 3. Deploy: vercel --prod
    echo.
    echo Or use the Vercel Dashboard to deploy from GitHub.
)

echo.
echo 📖 For detailed instructions, see PRODUCTION_DEPLOYMENT_GUIDE.md
echo.
pause
