const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ajpcbugydftdyhlbddpl.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.oEzL8RGuwDyLOupfw2TQLbpViDujaFnTGfDl6sBMFkc';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function verifyDatabase() {
  console.log('🔍 Verifying eWasl Database Schema...');
  console.log('====================================');
  
  const tablesToCheck = [
    'users',
    'social_accounts', 
    'posts',
    'post_social_accounts',
    'activities',
    'subscriptions'
  ];
  
  let allTablesExist = true;
  
  for (const tableName of tablesToCheck) {
    try {
      console.log(`\n📊 Checking table: ${tableName}`);
      
      // Try to query the table structure
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(0);
      
      if (error) {
        console.log(`❌ Table ${tableName} not found or not accessible`);
        console.log(`   Error: ${error.message}`);
        allTablesExist = false;
      } else {
        console.log(`✅ Table ${tableName} exists and is accessible`);
      }
      
    } catch (error) {
      console.log(`❌ Error checking table ${tableName}:`, error.message);
      allTablesExist = false;
    }
  }
  
  console.log('\n🔐 Checking Row Level Security...');
  
  try {
    // Test RLS by trying to access data without authentication
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .limit(1);
    
    if (error && error.message.includes('RLS')) {
      console.log('✅ Row Level Security is properly configured');
    } else {
      console.log('⚠️  Row Level Security may not be properly configured');
    }
  } catch (error) {
    console.log('✅ Row Level Security is active (expected error)');
  }
  
  console.log('\n📈 Database Statistics:');
  
  try {
    // Get table information from information_schema
    const { data: tableInfo, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', tablesToCheck);
    
    if (!tableError && tableInfo) {
      console.log(`📊 Found ${tableInfo.length} out of ${tablesToCheck.length} expected tables`);
      tableInfo.forEach(table => {
        console.log(`   ✓ ${table.table_name}`);
      });
    }
  } catch (error) {
    console.log('⚠️  Could not retrieve table statistics');
  }
  
  console.log('\n🎉 Database Verification Complete!');
  console.log('==================================');
  
  if (allTablesExist) {
    console.log('✅ All required tables are present');
    console.log('✅ Database schema deployment successful');
    console.log('✅ eWasl is ready for production use');
    console.log('');
    console.log('🚀 Next steps:');
    console.log('   1. Test user registration and authentication');
    console.log('   2. Connect social media accounts');
    console.log('   3. Test Stripe payment integration');
    console.log('   4. Create and publish posts');
  } else {
    console.log('❌ Some tables are missing');
    console.log('💡 Please run the database schema deployment:');
    console.log('   1. Open: https://supabase.com/dashboard/project/ajpcbugydftdyhlbddpl/sql/new');
    console.log('   2. Copy the schema from DEPLOYMENT-GUIDE.md');
    console.log('   3. Click "Run" to execute the schema');
  }
}

// Run verification
verifyDatabase().catch(console.error);
