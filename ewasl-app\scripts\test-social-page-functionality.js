#!/usr/bin/env node

/**
 * Social Page Functionality Testing Script
 * Tests all aspects of the social page to ensure everything works properly
 */

require('dotenv').config({ path: '.env.local' });

console.log('🚀 SOCIAL PAGE FUNCTIONALITY TESTING');
console.log('='.repeat(50));

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

async function makeRequest(endpoint, options = {}) {
  try {
    const url = endpoint.startsWith('http') ? endpoint : `${BASE_URL}${endpoint}`;
    console.log(`📡 Testing: ${endpoint}`);
    
    const response = await fetch(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'eWasl-Social-Tester/1.0',
        ...options.headers
      },
      body: options.body ? JSON.stringify(options.body) : undefined
    });

    const contentType = response.headers.get('content-type');
    let data;
    
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    return {
      status: response.status,
      statusText: response.statusText,
      data,
      headers: Object.fromEntries(response.headers.entries())
    };
  } catch (error) {
    return {
      error: error.message,
      status: 0
    };
  }
}

async function testSocialConnectAPI() {
  console.log('\n🔗 TESTING SOCIAL CONNECT API...\n');
  
  const platforms = ['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN'];
  const results = [];
  
  for (const platform of platforms) {
    const result = await makeRequest('/api/social/connect', {
      method: 'POST',
      body: { 
        platform,
        userId: '3ddaeb03-2d95-4fff-abad-2a2c7dd25037' // Demo user
      }
    });
    
    console.log(`  ${platform}:`);
    if (result.status === 200) {
      console.log(`    ✅ OAuth URL generated successfully`);
      console.log(`    🔗 URL: ${result.data.authUrl?.substring(0, 50)}...`);
    } else if (result.status === 401) {
      console.log(`    ⚠️  Authentication required (expected for some platforms)`);
    } else if (result.error) {
      console.log(`    ❌ Error: ${result.error}`);
    } else {
      console.log(`    ❌ Failed: ${result.status} - ${result.data?.error || result.statusText}`);
    }
    
    results.push({ platform, status: result.status, success: result.status === 200 });
  }
  
  return results;
}

async function testSocialAccountsAPI() {
  console.log('\n👥 TESTING SOCIAL ACCOUNTS API...\n');
  
  const result = await makeRequest('/api/social/accounts?userId=3ddaeb03-2d95-4fff-abad-2a2c7dd25037');
  
  console.log(`  Status: ${result.status}`);
  if (result.status === 200) {
    console.log(`  ✅ Accounts API working`);
    const accounts = result.data.accounts || [];
    console.log(`  📊 Connected accounts: ${accounts.length}`);
    
    accounts.forEach(account => {
      console.log(`    • ${account.platform}: ${account.account_name} (${account.is_active ? 'Active' : 'Inactive'})`);
    });
    
    return { success: true, accountCount: accounts.length };
  } else {
    console.log(`  ❌ Failed: ${result.data?.error || result.statusText}`);
    return { success: false, accountCount: 0 };
  }
}

async function testAnalyticsAPI() {
  console.log('\n📊 TESTING ANALYTICS API...\n');
  
  const result = await makeRequest('/api/analytics/advanced?userId=3ddaeb03-2d95-4fff-abad-2a2c7dd25037&period=30d');
  
  console.log(`  Status: ${result.status}`);
  if (result.status === 200) {
    console.log(`  ✅ Analytics API working`);
    if (result.data.data) {
      console.log(`  📈 Analytics data available`);
    } else {
      console.log(`  📊 No analytics data (expected for new accounts)`);
    }
    return { success: true };
  } else {
    console.log(`  ❌ Failed: ${result.data?.error || result.statusText}`);
    return { success: false };
  }
}

async function testHealthEndpoints() {
  console.log('\n🏥 TESTING HEALTH ENDPOINTS...\n');
  
  const healthEndpoints = [
    '/api/health',
    '/api/social/health',
    '/api/system/health'
  ];
  
  const results = [];
  
  for (const endpoint of healthEndpoints) {
    const result = await makeRequest(endpoint);
    
    console.log(`  ${endpoint}:`);
    if (result.status === 200) {
      console.log(`    ✅ Healthy`);
      results.push({ endpoint, healthy: true });
    } else if (result.status === 404) {
      console.log(`    ⚠️  Endpoint not found (may not be implemented)`);
      results.push({ endpoint, healthy: false, reason: 'not_found' });
    } else {
      console.log(`    ❌ Unhealthy: ${result.status}`);
      results.push({ endpoint, healthy: false, reason: 'error' });
    }
  }
  
  return results;
}

async function testComponentAPIs() {
  console.log('\n🧩 TESTING COMPONENT-SPECIFIC APIS...\n');
  
  const componentTests = [
    {
      name: 'Business Accounts',
      endpoint: '/api/social/business-accounts',
      method: 'POST',
      body: { platform: 'FACEBOOK', accessToken: 'test-token' }
    },
    {
      name: 'Enhanced Testing',
      endpoint: '/api/social/enhanced/test',
      method: 'GET'
    },
    {
      name: 'Real-time Analytics',
      endpoint: '/api/analytics/real-time',
      method: 'GET'
    }
  ];
  
  const results = [];
  
  for (const test of componentTests) {
    const result = await makeRequest(test.endpoint, {
      method: test.method,
      body: test.body
    });
    
    console.log(`  ${test.name}:`);
    if (result.status === 200) {
      console.log(`    ✅ Working`);
      results.push({ ...test, success: true });
    } else if (result.status === 401) {
      console.log(`    ⚠️  Authentication required (expected)`);
      results.push({ ...test, success: true, note: 'auth_required' });
    } else if (result.status === 404) {
      console.log(`    ⚠️  Endpoint not found`);
      results.push({ ...test, success: false, reason: 'not_found' });
    } else {
      console.log(`    ❌ Failed: ${result.status}`);
      results.push({ ...test, success: false, reason: 'error' });
    }
  }
  
  return results;
}

async function testDatabaseConnectivity() {
  console.log('\n🗄️ TESTING DATABASE CONNECTIVITY...\n');
  
  try {
    // Test if we can reach a simple database endpoint
    const result = await makeRequest('/api/test/database-connection');
    
    if (result.status === 200) {
      console.log(`  ✅ Database connection working`);
      return { success: true };
    } else {
      console.log(`  ⚠️  Database test endpoint not available (status: ${result.status})`);
      
      // Try alternative test via accounts endpoint
      const accountsTest = await makeRequest('/api/social/accounts?userId=test');
      if (accountsTest.status === 200 || accountsTest.status === 401) {
        console.log(`  ✅ Database connectivity confirmed via accounts endpoint`);
        return { success: true };
      } else {
        console.log(`  ❌ Database connectivity issues`);
        return { success: false };
      }
    }
  } catch (error) {
    console.log(`  ❌ Database test failed: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runSocialPageTests() {
  console.log(`Testing against: ${BASE_URL}\n`);
  
  try {
    // Run all tests
    const connectResults = await testSocialConnectAPI();
    const accountsResult = await testSocialAccountsAPI();
    const analyticsResult = await testAnalyticsAPI();
    const healthResults = await testHealthEndpoints();
    const componentResults = await testComponentAPIs();
    const databaseResult = await testDatabaseConnectivity();
    
    // Calculate overall results
    const totalTests = connectResults.length + 5; // 4 connect + 1 accounts + 1 analytics + 1 database + health/component tests
    let passedTests = 0;
    
    // Count passes
    passedTests += connectResults.filter(r => r.success).length;
    if (accountsResult.success) passedTests++;
    if (analyticsResult.success) passedTests++;
    if (databaseResult.success) passedTests++;
    
    // Generate summary
    console.log('\n📊 TEST SUMMARY');
    console.log('='.repeat(30));
    console.log(`Total core tests: ${totalTests}`);
    console.log(`Passed tests: ${passedTests}`);
    console.log(`Success rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    console.log('\n🔍 DETAILED RESULTS:');
    console.log(`  📱 Platform connections: ${connectResults.filter(r => r.success).length}/${connectResults.length} working`);
    console.log(`  👥 Accounts API: ${accountsResult.success ? '✅' : '❌'}`);
    console.log(`  📊 Analytics API: ${analyticsResult.success ? '✅' : '❌'}`);
    console.log(`  🗄️  Database: ${databaseResult.success ? '✅' : '❌'}`);
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    
    if (passedTests === totalTests) {
      console.log('🎉 Excellent! All core social page functionality is working properly.');
      console.log('✅ The social page is ready for production use.');
    } else if (passedTests >= totalTests * 0.8) {
      console.log('👍 Good! Most functionality is working with minor issues.');
      console.log('🔧 Consider addressing the failing tests for optimal performance.');
    } else {
      console.log('⚠️  Several issues detected. Review the failing tests above.');
      console.log('🛠️  Focus on fixing core connectivity and database issues first.');
    }
    
    console.log('\n🚀 NEXT STEPS:');
    console.log('1. 🌐 Start the development server: npm run dev');
    console.log('2. 🔗 Visit: http://localhost:3000/social');
    console.log('3. 🧪 Test social connections manually');
    console.log('4. 📊 Check analytics dashboard');
    console.log('5. 🔧 Run API testing page: http://localhost:3000/api-testing');
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
    console.log('\n🛠️  Troubleshooting:');
    console.log('• Ensure the development server is running');
    console.log('• Check environment variables are configured');
    console.log('• Verify network connectivity');
  }
}

// Run the tests
runSocialPageTests(); 