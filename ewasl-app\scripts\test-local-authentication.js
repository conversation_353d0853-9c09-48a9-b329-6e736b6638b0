#!/usr/bin/env node

/**
 * Test Local vs Production Authentication
 * This script helps diagnose authentication issues by testing both environments
 */

const fetch = require('node-fetch');

console.log('🔍 Authentication Environment Test');
console.log('==================================\n');

// Test both local and production environments
const environments = [
  {
    name: 'Local Development',
    baseUrl: 'http://localhost:3000',
    description: 'Testing local development server'
  },
  {
    name: 'Production',
    baseUrl: 'https://app.ewasl.com',
    description: 'Testing production deployment'
  }
];

async function testEnvironment(env) {
  console.log(`🧪 Testing: ${env.name}`);
  console.log(`📍 ${env.description}`);
  console.log(`🌐 Base URL: ${env.baseUrl}\n`);

  const endpoints = [
    '/api/social/accounts',
    '/social',
    '/auth/signin',
    '/api/system/health'
  ];

  for (const endpoint of endpoints) {
    try {
      const url = `${env.baseUrl}${endpoint}`;
      console.log(`   Testing: ${endpoint}`);
      
      const response = await fetch(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'eWasl-Auth-Test/1.0'
        }
      });
      
      const status = response.status;
      const statusText = response.statusText;
      
      if (status === 200) {
        console.log(`   ✅ ${endpoint}: ${status} ${statusText}`);
      } else if (status === 401) {
        console.log(`   🔐 ${endpoint}: ${status} ${statusText} (Authentication required - EXPECTED)`);
      } else if (status === 307 || status === 302) {
        console.log(`   🔄 ${endpoint}: ${status} ${statusText} (Redirect - check auth middleware)`);
      } else if (status >= 400) {
        console.log(`   ❌ ${endpoint}: ${status} ${statusText}`);
      } else {
        console.log(`   ✅ ${endpoint}: ${status} ${statusText}`);
      }
      
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`   💤 ${endpoint}: Server not running`);
      } else {
        console.log(`   ⚠️  ${endpoint}: ${error.message}`);
      }
    }
  }
  console.log('');
}

async function runTests() {
  for (const env of environments) {
    await testEnvironment(env);
  }
  
  console.log('🎯 EXPECTED BEHAVIOR:');
  console.log('=====================');
  console.log('✅ /api/system/health should return 200');
  console.log('✅ /auth/signin should return 200');
  console.log('🔐 /api/social/accounts should return 401 (unauthenticated)');
  console.log('🔄 /social should redirect to auth or show auth prompt');
  console.log('');
  
  console.log('🚨 TROUBLESHOOTING:');
  console.log('===================');
  console.log('1. If local server is not running: npm run dev');
  console.log('2. If production shows 500 errors: Check deployment logs');
  console.log('3. If all endpoints show 401: Check Supabase configuration');
  console.log('4. If redirects don\'t work: Check middleware configuration');
  console.log('');
  
  console.log('🔧 QUICK FIXES:');
  console.log('===============');
  console.log('1. Start local dev server: npm run dev');
  console.log('2. Test in browser: http://localhost:3000/social');
  console.log('3. Check console logs for detailed error messages');
  console.log('4. Verify Supabase credentials in .env.local');
  console.log('');
}

// Run the tests
runTests().catch(console.error); 