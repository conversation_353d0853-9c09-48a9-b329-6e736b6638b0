"use client";

import React, { useState, useEffect } from 'react';
import { EnhancedContentCalendar } from '@/components/calendar/enhanced-content-calendar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Calendar,
  ArrowLeft,
  Sparkles,
  MousePointer,
  Move,
  Clock,
  Filter,
  Search,
  BarChart3,
  Globe,
  Info,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { useRTL } from '@/lib/i18n/arabic-rtl-optimizer';
import { toast } from 'sonner';

interface EnhancedCalendarPageProps {
  searchParams?: {
    lang?: string;
  };
}

export default function EnhancedCalendarPage({ searchParams }: EnhancedCalendarPageProps) {
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const router = useRouter();
  const rtl = useRTL(language);

  useEffect(() => {
    // Set language from search params
    if (searchParams?.lang === 'en') {
      setLanguage('en');
    }

    // Apply RTL to document
    document.documentElement.dir = rtl.direction;
    document.documentElement.lang = language;
  }, [searchParams, language, rtl.direction]);

  const handleLanguageChange = (newLanguage: 'ar' | 'en') => {
    setLanguage(newLanguage);
    
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('lang', newLanguage);
    window.history.replaceState({}, '', url.toString());
  };

  // Calendar features
  const calendarFeatures = [
    {
      icon: MousePointer,
      title: language === 'ar' ? 'السحب والإفلات' : 'Drag & Drop',
      description: language === 'ar' 
        ? 'اسحب المنشورات وأفلتها لإعادة جدولتها بسهولة'
        : 'Drag and drop posts to reschedule them easily',
      color: 'blue'
    },
    {
      icon: Move,
      title: language === 'ar' ? 'تغيير الحجم' : 'Resize Events',
      description: language === 'ar'
        ? 'قم بتغيير مدة الأحداث بسحب الحواف'
        : 'Change event duration by dragging the edges',
      color: 'green'
    },
    {
      icon: Clock,
      title: language === 'ar' ? 'عروض متعددة' : 'Multiple Views',
      description: language === 'ar'
        ? 'شاهد التقويم بعرض الشهر أو الأسبوع أو اليوم'
        : 'View calendar in month, week, or day view',
      color: 'purple'
    },
    {
      icon: Filter,
      title: language === 'ar' ? 'تصفية متقدمة' : 'Advanced Filtering',
      description: language === 'ar'
        ? 'صفي المنشورات حسب الحالة والمنصة والمحتوى'
        : 'Filter posts by status, platform, and content',
      color: 'orange'
    },
    {
      icon: Search,
      title: language === 'ar' ? 'بحث سريع' : 'Quick Search',
      description: language === 'ar'
        ? 'ابحث في المنشورات بسرعة باستخدام الكلمات المفتاحية'
        : 'Quickly search through posts using keywords',
      color: 'red'
    },
    {
      icon: BarChart3,
      title: language === 'ar' ? 'إحصائيات مدمجة' : 'Built-in Analytics',
      description: language === 'ar'
        ? 'شاهد إحصائيات الأداء مباشرة في التقويم'
        : 'View performance analytics directly in the calendar',
      color: 'indigo'
    }
  ];

  // Key benefits
  const keyBenefits = [
    {
      metric: language === 'ar' ? 'سهولة الاستخدام' : 'Ease of Use',
      value: '95%',
      improvement: '+40%'
    },
    {
      metric: language === 'ar' ? 'سرعة الجدولة' : 'Scheduling Speed',
      value: '3x',
      improvement: 'faster'
    },
    {
      metric: language === 'ar' ? 'دقة التنظيم' : 'Organization Accuracy',
      value: '98%',
      improvement: '+25%'
    },
    {
      metric: language === 'ar' ? 'توفير الوقت' : 'Time Saved',
      value: '60%',
      improvement: 'daily'
    }
  ];

  // Handle calendar events
  const handlePostCreate = (date: Date) => {
    toast.success(language === 'ar' ? 'فتح نموذج إنشاء منشور جديد' : 'Opening new post creation form');
    console.log('Create post for date:', date);
  };

  const handlePostEdit = (post: any) => {
    toast.success(language === 'ar' ? 'فتح نموذج تعديل المنشور' : 'Opening post edit form');
    console.log('Edit post:', post);
  };

  const handlePostDelete = (postId: string) => {
    toast.success(language === 'ar' ? 'تم حذف المنشور بنجاح' : 'Post deleted successfully');
    console.log('Delete post:', postId);
  };

  const handlePostDuplicate = (post: any) => {
    toast.success(language === 'ar' ? 'تم نسخ المنشور بنجاح' : 'Post duplicated successfully');
    console.log('Duplicate post:', post);
  };

  const handlePostMove = async (postId: string, newDate: Date) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('Move post:', postId, 'to:', newDate);
  };

  return (
    <div className={cn(
      "min-h-screen bg-gray-50",
      language === 'ar' ? "rtl" : "ltr"
    )} dir={rtl.direction}>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className={rtl.cn(
          "flex items-center justify-between",
          rtl.flex()
        )}>
          <div className={rtl.textAlign()}>
            <div className={rtl.cn(
              "flex items-center gap-3 mb-2",
              rtl.flex()
            )}>
              <h1 className="text-4xl font-bold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                {language === 'ar' ? 'تقويم المحتوى المتقدم' : 'Enhanced Content Calendar'}
              </h1>
              <Badge variant="secondary" className="text-xs">
                <Sparkles className="w-3 h-3 mr-1" />
                {language === 'ar' ? 'متقدم' : 'Enhanced'}
              </Badge>
            </div>
            <p className="text-xl text-gray-600" style={{ fontFamily: rtl.getFontFamily('primary') }}>
              {language === 'ar'
                ? 'جدولة وإدارة المحتوى مع السحب والإفلات والميزات التفاعلية المتقدمة'
                : 'Schedule and manage content with drag-and-drop and advanced interactive features'
              }
            </p>
          </div>
          
          <div className={rtl.cn(
            "flex items-center gap-4",
            rtl.flex()
          )}>
            <Button
              variant="outline"
              onClick={() => handleLanguageChange(language === 'ar' ? 'en' : 'ar')}
              className={rtl.cn(
                "flex items-center gap-2",
                rtl.flex()
              )}
            >
              <Globe className="w-4 h-4" />
              {language === 'ar' ? 'English' : 'العربية'}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className={rtl.cn(
                "flex items-center gap-2",
                rtl.flex()
              )}
            >
              <ArrowLeft className={cn("w-4 h-4", language === 'ar' && "rotate-180")} />
              {language === 'ar' ? 'العودة للوحة التحكم' : 'Back to Dashboard'}
            </Button>
          </div>
        </div>

        {/* Enhanced Features Alert */}
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertTitle className={rtl.cn(
            "text-green-900",
            rtl.textAlign()
          )}>
            {language === 'ar' ? 'ميزات متقدمة نشطة' : 'Enhanced Features Active'}
          </AlertTitle>
          <AlertDescription className={rtl.cn(
            "text-green-800",
            rtl.textAlign()
          )}>
            {language === 'ar'
              ? 'استخدم السحب والإفلات لإعادة جدولة المنشورات، والبحث والتصفية المتقدمة، والعروض المتعددة للتقويم.'
              : 'Use drag-and-drop to reschedule posts, advanced search and filtering, and multiple calendar views.'
            }
          </AlertDescription>
        </Alert>

        {/* Key Benefits */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {keyBenefits.map((benefit, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6 text-center">
                <div className="space-y-2">
                  <p className="text-3xl font-bold text-blue-600">{benefit.value}</p>
                  <p className="text-sm font-medium text-gray-900">{benefit.metric}</p>
                  <p className="text-xs text-green-600">{benefit.improvement}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Calendar Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {calendarFeatures.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className={rtl.cn(
                    "flex items-center gap-3",
                    rtl.flex()
                  )}>
                    <div className={`w-10 h-10 bg-${feature.color}-100 rounded-lg flex items-center justify-center`}>
                      <Icon className={`w-5 h-5 text-${feature.color}-600`} />
                    </div>
                    <CardTitle className="text-lg" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                      {feature.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className={rtl.textAlign()} style={{ fontFamily: rtl.getFontFamily('primary') }}>
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Enhanced Content Calendar */}
        <EnhancedContentCalendar 
          language={language}
          onPostCreate={handlePostCreate}
          onPostEdit={handlePostEdit}
          onPostDelete={handlePostDelete}
          onPostDuplicate={handlePostDuplicate}
          onPostMove={handlePostMove}
        />

        {/* Footer */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-6">
            <div className={rtl.cn(
              "flex items-center justify-center gap-4",
              rtl.flex()
            )}>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Calendar className="w-6 h-6 text-white" />
              </div>
              <div className={rtl.textAlign()}>
                <h4 className="font-semibold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                  {language === 'ar' ? 'تقويم المحتوى المتقدم جاهز' : 'Enhanced Content Calendar Ready'}
                </h4>
                <p className="text-sm text-gray-600" style={{ fontFamily: rtl.getFontFamily('primary') }}>
                  {language === 'ar'
                    ? 'جدولة وإدارة المحتوى بكفاءة مع ميزات السحب والإفلات والتفاعل المتقدم'
                    : 'Efficiently schedule and manage content with drag-and-drop and advanced interaction features'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
