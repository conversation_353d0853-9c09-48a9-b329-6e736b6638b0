import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface TokenValidationResult {
  account_id: string;
  platform: string;
  account_name: string;
  token_valid: boolean;
  token_expires_at?: string;
  error_message?: string;
  permissions?: string[];
  user_id?: string;
  page_id?: string;
}

async function validateFacebookToken(accessToken: string): Promise<{
  valid: boolean;
  error?: string;
  user_id?: string;
  permissions?: string[];
  expires_at?: number;
}> {
  try {
    // Check token validity and get user info
    const tokenResponse = await fetch(`https://graph.facebook.com/me?access_token=${accessToken}`);
    const tokenData = await tokenResponse.json();

    if (!tokenResponse.ok) {
      return {
        valid: false,
        error: tokenData.error?.message || 'رمز الوصول غير صالح'
      };
    }

    // Get token permissions
    const permissionsResponse = await fetch(`https://graph.facebook.com/me/permissions?access_token=${accessToken}`);
    const permissionsData = await permissionsResponse.json();

    const permissions = permissionsData.data?.map((p: any) => p.permission) || [];

    // Get token expiration info
    const debugResponse = await fetch(`https://graph.facebook.com/debug_token?input_token=${accessToken}&access_token=${accessToken}`);
    const debugData = await debugResponse.json();

    return {
      valid: true,
      user_id: tokenData.id,
      permissions,
      expires_at: debugData.data?.expires_at
    };
  } catch (error) {
    return {
      valid: false,
      error: `خطأ في التحقق من الرمز: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
    };
  }
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('🔍 [OAuth Token Test] Starting OAuth token validation...');

    // Get current user from Supabase auth
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    const { data: { user }, error: userError } = await supabaseClient.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({
        success: false,
        message: 'المستخدم غير مصرح له',
        error: 'Authentication required',
        response_time_ms: Date.now() - startTime
      }, { status: 401 });
    }

    // Fetch all social accounts for the authenticated user
    const { data: socialAccounts, error: fetchError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id);

    if (fetchError) {
      console.error('❌ [OAuth Token Test] Database fetch error:', fetchError);
      return NextResponse.json({
        success: false,
        message: 'خطأ في قاعدة البيانات',
        error: fetchError.message,
        response_time_ms: Date.now() - startTime
      }, { status: 500 });
    }

    console.log(`📊 [OAuth Token Test] Found ${socialAccounts?.length || 0} social accounts`);

    if (!socialAccounts || socialAccounts.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'لا توجد حسابات اجتماعية متصلة',
        accounts: [],
        total_accounts: 0,
        response_time_ms: Date.now() - startTime
      });
    }

    // Validate each token
    const validationResults: TokenValidationResult[] = [];

    for (const account of socialAccounts) {
      console.log(`🔍 [OAuth Token Test] Validating token for account: ${account.account_name || account.account_id} (${account.platform})`);

      let validation;

      // Validate based on platform
      if (account.platform.toUpperCase() === 'FACEBOOK' || account.platform.toUpperCase() === 'INSTAGRAM') {
        validation = await validateFacebookToken(account.access_token);
      } else {
        validation = {
          valid: false,
          error: `Platform ${account.platform} not supported for token validation`
        };
      }

      const result: TokenValidationResult = {
        account_id: account.account_id,
        platform: account.platform,
        account_name: account.account_name || 'غير محدد',
        token_valid: validation.valid,
        user_id: validation.user_id,
        permissions: validation.permissions,
        token_expires_at: validation.expires_at ? new Date(validation.expires_at * 1000).toISOString() : undefined,
        error_message: validation.error
      };

      if (account.page_id) {
        result.page_id = account.page_id;
      }

      validationResults.push(result);

      console.log(`${validation.valid ? '✅' : '❌'} [OAuth Token Test] Account ${account.account_name} (${account.platform}): ${validation.valid ? 'صالح' : validation.error}`);
    }

    // Summary statistics
    const validTokens = validationResults.filter(r => r.token_valid).length;
    const invalidTokens = validationResults.length - validTokens;

    const instagramAccounts = validationResults.filter(r => r.platform.toUpperCase() === 'INSTAGRAM').length;
    const facebookAccounts = validationResults.filter(r => r.platform.toUpperCase() === 'FACEBOOK').length;

    const response = {
      success: true,
      message: `تم فحص ${validationResults.length} حساب اجتماعي`,
      summary: {
        total_accounts: validationResults.length,
        valid_tokens: validTokens,
        invalid_tokens: invalidTokens,
        instagram_accounts: instagramAccounts,
        facebook_accounts: facebookAccounts,
        validation_rate: validationResults.length > 0 ? `${Math.round((validTokens / validationResults.length) * 100)}%` : '0%'
      },
      accounts: validationResults,
      recommendations: invalidTokens > 0 ? [
        'يجب إعادة ربط الحسابات ذات الرموز المنتهية الصلاحية',
        'تحقق من صلاحيات التطبيق في Facebook Developer Console',
        'قم بتحديث رموز الوصول باستخدام آلية التحديث التلقائي'
      ] : validationResults.length > 0 ? [
        'جميع رموز الوصول صالحة ✅',
        'يمكن المتابعة مع اختبار الصلاحيات والنشر'
      ] : [
        'لا توجد حسابات اجتماعية متصلة',
        'يجب ربط حساب Instagram أو Facebook أولاً'
      ],
      response_time_ms: Date.now() - startTime,
      timestamp: new Date().toISOString()
    };

    console.log(`✅ [OAuth Token Test] Completed: ${validTokens}/${validationResults.length} tokens valid`);
    
    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ [OAuth Token Test] Unexpected error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'خطأ في خادم اختبار رموز OAuth',
      error: error instanceof Error ? error.message : 'خطأ غير معروف',
      response_time_ms: Date.now() - startTime,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
