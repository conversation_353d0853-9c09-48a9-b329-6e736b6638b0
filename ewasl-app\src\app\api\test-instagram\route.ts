import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Instagram API connectivity...');
    
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get Instagram accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'INSTAGRAM');

    if (accountsError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch Instagram accounts',
        details: accountsError
      });
    }

    if (!accounts || accounts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No Instagram accounts found',
        message: 'Please connect an Instagram Business account first'
      });
    }

    const account = accounts[0];
    console.log('Testing Instagram account:', {
      id: account.id,
      account_name: account.account_name,
      platform: account.platform,
      hasAccessToken: !!account.access_token,
      hasInstagramBusinessId: !!account.instagram_business_account_id,
      metadata: account.metadata
    });

    // Test 1: Validate access token (using Facebook Graph API)
    console.log('🔑 Testing Instagram token validation...');
    const tokenResponse = await fetch(`https://graph.facebook.com/v19.0/me?access_token=${account.access_token}`);
    const tokenData = await tokenResponse.json();

    if (!tokenResponse.ok) {
      return NextResponse.json({
        success: false,
        step: 'token_validation',
        error: 'Instagram token validation failed',
        details: tokenData
      });
    }

    console.log('✅ Instagram token is valid:', tokenData);

    // Test 2: Check Instagram Business Account ID
    let instagramBusinessId = account.instagram_business_account_id || account.account_id;
    
    if (!instagramBusinessId) {
      return NextResponse.json({
        success: false,
        step: 'business_account_check',
        error: 'Instagram Business Account ID not found',
        message: 'Please ensure you have connected an Instagram Business account'
      });
    }

    // Test 3: Verify Instagram Business Account access
    console.log('📸 Testing Instagram Business Account access...');
    const instagramResponse = await fetch(
      `https://graph.facebook.com/v19.0/${instagramBusinessId}?fields=id,username,name,profile_picture_url,followers_count&access_token=${account.access_token}`
    );
    const instagramData = await instagramResponse.json();

    if (!instagramResponse.ok) {
      return NextResponse.json({
        success: false,
        step: 'instagram_account_access',
        error: 'Failed to access Instagram Business Account',
        details: instagramData
      });
    }

    console.log('✅ Instagram Business Account data:', instagramData);

    // Test 4: Check media publishing permissions
    console.log('🔐 Testing Instagram media publishing permissions...');
    const permissionsResponse = await fetch(
      `https://graph.facebook.com/v19.0/${instagramBusinessId}?fields=id,username&access_token=${account.access_token}`
    );
    const permissionsData = await permissionsResponse.json();

    if (!permissionsResponse.ok) {
      return NextResponse.json({
        success: false,
        step: 'publishing_permissions',
        error: 'Failed to verify Instagram publishing permissions',
        details: permissionsData
      });
    }

    console.log('✅ Instagram publishing permissions verified:', permissionsData);

    return NextResponse.json({
      success: true,
      message: 'Instagram API connectivity test passed',
      results: {
        token_validation: {
          success: true,
          user_id: tokenData.id,
          user_name: tokenData.name
        },
        instagram_account_access: {
          success: true,
          instagram_id: instagramData.id,
          username: instagramData.username,
          name: instagramData.name,
          followers_count: instagramData.followers_count
        },
        publishing_permissions: {
          success: true,
          account_id: permissionsData.id,
          username: permissionsData.username
        }
      },
      account_info: {
        id: account.id,
        account_name: account.account_name,
        account_id: account.account_id,
        instagram_business_id: instagramBusinessId,
        metadata: account.metadata
      }
    });

  } catch (error: any) {
    console.error('❌ Instagram test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Instagram API test failed',
      details: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing Instagram post publishing...');
    
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      content = '🧪 Test post from eWasl platform - Instagram API test #ewasl #test',
      mediaUrl = 'https://picsum.photos/800/600' // Default test image
    } = body;

    // Get Instagram accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'INSTAGRAM');

    if (accountsError || !accounts || accounts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No Instagram accounts found'
      });
    }

    const account = accounts[0];
    const instagramBusinessId = account.instagram_business_account_id || account.account_id;

    if (!instagramBusinessId) {
      return NextResponse.json({
        success: false,
        error: 'Instagram Business Account ID not found'
      });
    }

    console.log('📤 Publishing test post to Instagram...', {
      instagram_id: instagramBusinessId,
      content: content.substring(0, 50) + '...',
      media_url: mediaUrl
    });

    // Step 1: Create media container
    const containerResponse = await fetch(
      `https://graph.facebook.com/v19.0/${instagramBusinessId}/media`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image_url: mediaUrl,
          caption: content,
          access_token: account.access_token,
        }),
      }
    );

    const containerData = await containerResponse.json();

    if (!containerResponse.ok) {
      console.error('❌ Instagram container creation failed:', containerData);
      return NextResponse.json({
        success: false,
        step: 'container_creation',
        error: 'Failed to create Instagram media container',
        details: containerData,
        instagram_error: containerData.error
      });
    }

    const containerId = containerData.id;
    console.log('✅ Instagram media container created:', containerId);

    // Step 2: Publish the media container
    const publishResponse = await fetch(
      `https://graph.facebook.com/v19.0/${instagramBusinessId}/media_publish`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          creation_id: containerId,
          access_token: account.access_token,
        }),
      }
    );

    const publishData = await publishResponse.json();

    if (!publishResponse.ok) {
      console.error('❌ Instagram publish failed:', publishData);
      return NextResponse.json({
        success: false,
        step: 'media_publish',
        error: 'Failed to publish Instagram post',
        details: publishData,
        instagram_error: publishData.error
      });
    }

    const postId = publishData.id;
    console.log('✅ Instagram post published successfully:', postId);

    return NextResponse.json({
      success: true,
      message: 'Instagram test post published successfully',
      post_id: postId,
      post_url: `https://instagram.com/p/${postId}`,
      container_id: containerId,
      platform_response: publishData
    });

  } catch (error: any) {
    console.error('❌ Instagram post test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Instagram post test failed',
      details: error.message
    }, { status: 500 });
  }
}
