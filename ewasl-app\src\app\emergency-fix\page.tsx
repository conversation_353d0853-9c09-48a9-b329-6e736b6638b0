'use client';

import { useState } from 'react';

export default function EmergencyFixPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const executeEmergencyFix = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      console.log('🚨 Executing emergency schema fix...');
      
      const response = await fetch('/api/emergency-schema-fix', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      console.log('Emergency fix result:', data);
      setResult(data);
      
      if (data.success) {
        // Test the fix immediately
        setTimeout(async () => {
          try {
            const testResponse = await fetch('/api/test-oauth-tokens');
            const testData = await testResponse.json();
            setResult(prev => ({
              ...prev,
              verification: {
                oauth_test: testData,
                timestamp: new Date().toISOString()
              }
            }));
          } catch (error) {
            console.error('Verification test failed:', error);
          }
        }, 2000);
      }
      
    } catch (error) {
      console.error('Emergency fix failed:', error);
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  const testOAuthTokens = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-oauth-tokens');
      const data = await response.json();
      setResult({
        test: 'oauth-tokens-verification',
        ...data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testPublishing = async () => {
    setLoading(true);
    try {
      // Navigate to post creation to test publishing
      window.location.href = '/posts/new';
    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-red-50 py-8" dir="rtl">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6 border-l-4 border-red-500">
          <div className="flex items-center mb-6">
            <div className="text-3xl ml-3">🚨</div>
            <div>
              <h1 className="text-2xl font-bold text-red-900 mb-2">
                إصلاح طارئ لمخطط قاعدة البيانات
              </h1>
              <p className="text-red-700">
                تنفيذ إصلاح حرج لحل مشكلة "column social_accounts.status does not exist"
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <button
              onClick={executeEmergencyFix}
              disabled={loading}
              className="bg-red-600 hover:bg-red-700 text-white px-6 py-4 rounded-lg font-bold disabled:opacity-50 transition-colors"
            >
              {loading ? '🔄 جاري التنفيذ...' : '🚨 تنفيذ الإصلاح الطارئ'}
            </button>
            
            <button
              onClick={testOAuthTokens}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-4 rounded-lg font-medium disabled:opacity-50 transition-colors"
            >
              {loading ? '🔄 جاري الاختبار...' : '🔍 اختبار OAuth Tokens'}
            </button>
            
            <button
              onClick={testPublishing}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-4 rounded-lg font-medium disabled:opacity-50 transition-colors"
            >
              📝 اختبار النشر
            </button>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <h3 className="font-bold text-yellow-800 mb-2">📋 خطوات الإصلاح:</h3>
            <ol className="list-decimal list-inside text-yellow-700 space-y-1">
              <li>إضافة عمود connection_status</li>
              <li>إضافة عمود status</li>
              <li>إضافة أعمدة Facebook (page_id, page_access_token, page_name)</li>
              <li>تحديث السجلات الموجودة بالقيم الافتراضية</li>
              <li>التحقق من المخطط النهائي</li>
            </ol>
          </div>

          {result && (
            <div className="bg-gray-100 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <div className="text-xl ml-2">
                  {result.success ? '✅' : '❌'}
                </div>
                <h2 className="text-lg font-semibold">
                  {result.success ? 'نجح الإصلاح!' : 'فشل الإصلاح'}
                </h2>
              </div>
              <pre className="text-sm overflow-auto max-h-96 bg-white p-4 rounded border font-mono">
                {JSON.stringify(result, null, 2)}
              </pre>
              
              {result.success && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded">
                  <p className="text-green-800 font-medium">
                    🎉 تم إصلاح قاعدة البيانات بنجاح! يمكنك الآن اختبار نظام النشر.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
