/* RTL Calendar Styles */
.rtl-calendar {
  direction: rtl;
}

.rtl-calendar .rbc-calendar {
  direction: rtl;
}

.rtl-calendar .rbc-header {
  text-align: center;
  font-weight: 600;
  padding: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.rtl-calendar .rbc-month-view {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.rtl-calendar .rbc-date-cell {
  text-align: center;
  padding: 4px;
  font-size: 14px;
}

.rtl-calendar .rbc-date-cell.rbc-off-range {
  color: #9ca3af;
}

.rtl-calendar .rbc-date-cell.rbc-now {
  font-weight: bold;
  color: #2563eb;
}

.rtl-calendar .rbc-event {
  border-radius: 4px;
  padding: 2px 4px;
  margin: 1px;
  font-size: 11px;
  line-height: 1.2;
  cursor: pointer;
  border: none;
}

.rtl-calendar .rbc-event:hover {
  opacity: 0.8;
}

.rtl-calendar .rbc-event.rbc-selected {
  box-shadow: 0 0 0 2px #2563eb;
}

.rtl-calendar .rbc-day-slot {
  border-right: 1px solid #e5e7eb;
}

.rtl-calendar .rbc-time-slot {
  border-bottom: 1px solid #f3f4f6;
}

.rtl-calendar .rbc-time-header {
  border-bottom: 1px solid #e5e7eb;
}

.rtl-calendar .rbc-time-content {
  border-right: 1px solid #e5e7eb;
}

.rtl-calendar .rbc-time-gutter {
  text-align: center;
  font-size: 12px;
  color: #6b7280;
}

.rtl-calendar .rbc-allday-cell {
  text-align: center;
  padding: 4px;
  font-size: 12px;
  font-weight: 500;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.rtl-calendar .rbc-week-view,
.rtl-calendar .rbc-day-view {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.rtl-calendar .rbc-today {
  background-color: #eff6ff;
}

.rtl-calendar .rbc-current-time-indicator {
  background-color: #dc2626;
  height: 2px;
  z-index: 10;
}

/* Custom event colors */
.rtl-calendar .rbc-event.status-draft {
  background-color: #6b7280;
  color: white;
}

.rtl-calendar .rbc-event.status-scheduled {
  background-color: #2563eb;
  color: white;
}

.rtl-calendar .rbc-event.status-published {
  background-color: #059669;
  color: white;
}

.rtl-calendar .rbc-event.status-failed {
  background-color: #dc2626;
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rtl-calendar .rbc-toolbar {
    flex-direction: column;
    gap: 8px;
  }
  
  .rtl-calendar .rbc-toolbar-label {
    order: -1;
    margin-bottom: 8px;
  }
  
  .rtl-calendar .rbc-btn-group {
    display: flex;
    justify-content: center;
  }
  
  .rtl-calendar .rbc-event {
    font-size: 10px;
    padding: 1px 2px;
  }
}

/* Popup styles */
.rtl-calendar .rbc-overlay {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 8px;
  z-index: 1000;
}

.rtl-calendar .rbc-overlay-header {
  font-weight: 600;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e5e7eb;
}

/* Drag and drop styles */
.rtl-calendar .rbc-addons-dnd .rbc-event {
  cursor: move;
}

.rtl-calendar .rbc-addons-dnd .rbc-event:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

.rtl-calendar .rbc-addons-dnd-drag-preview {
  opacity: 0.7;
  transform: rotate(5deg);
}

.rtl-calendar .rbc-addons-dnd-drop-preview {
  background-color: #dbeafe;
  border: 2px dashed #2563eb;
  border-radius: 4px;
}

/* Custom scrollbar for time views */
.rtl-calendar .rbc-time-content::-webkit-scrollbar {
  width: 6px;
}

.rtl-calendar .rbc-time-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.rtl-calendar .rbc-time-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.rtl-calendar .rbc-time-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
