"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  ArrowLeft,
  ArrowRight,
  Save,
  Send,
  Clock,
  Eye,
  CheckCircle,
  AlertCircle,
  Sparkles,
  Zap,
  Target,
  Calendar,
  Image,
  Type,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { RichTextEditor } from '@/components/editor/rich-text-editor';
import { EnhancedMediaUpload } from '@/components/media/enhanced-media-upload';
import { PlatformSelector } from '@/components/posts/platform-selector';
import { PostPreview } from '@/components/posts/post-preview';
import { PostScheduler } from '@/components/posts/post-scheduler';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

// Form validation schema
const formSchema = z.object({
  content: z.string().min(1, "المحتوى مطلوب"),
  mediaUrls: z.array(z.string().url()).optional().default([]),
  status: z.enum(["DRAFT", "SCHEDULED", "PUBLISHED"]),
  scheduledAt: z.date().optional(),
  platforms: z.array(z.string()).min(1, "يرجى اختيار منصة واحدة على الأقل"),
  timezone: z.string().default("Asia/Riyadh"),
});

type FormData = z.infer<typeof formSchema>;

interface Step {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  isCompleted: boolean;
  isActive: boolean;
  isOptional?: boolean;
}

interface ImprovedPostFormProps {
  initialData?: Partial<FormData>;
  language?: 'ar' | 'en';
  onSave?: (data: FormData) => void;
  onPublish?: (data: FormData) => void;
  className?: string;
}

export function ImprovedPostForm({
  initialData,
  language = 'ar',
  onSave,
  onPublish,
  className
}: ImprovedPostFormProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'saved' | 'saving' | 'error' | null>(null);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const router = useRouter();

  // Translations
  const t = {
    ar: {
      title: 'إنشاء منشور جديد',
      subtitle: 'أنشئ محتوى جذاب لوسائل التواصل الاجتماعي',
      steps: {
        content: { title: 'المحتوى', description: 'اكتب محتوى المنشور' },
        media: { title: 'الوسائط', description: 'أضف صور أو فيديوهات' },
        platforms: { title: 'المنصات', description: 'اختر منصات النشر' },
        schedule: { title: 'الجدولة', description: 'حدد وقت النشر' },
        preview: { title: 'المعاينة', description: 'راجع المنشور قبل النشر' }
      },
      actions: {
        next: 'التالي',
        previous: 'السابق',
        save: 'حفظ كمسودة',
        publish: 'نشر الآن',
        schedule: 'جدولة النشر'
      },
      status: {
        autoSaved: 'تم الحفظ التلقائي',
        saving: 'جاري الحفظ...',
        error: 'خطأ في الحفظ',
        lastSaved: 'آخر حفظ'
      },
      validation: {
        contentRequired: 'المحتوى مطلوب',
        platformRequired: 'يرجى اختيار منصة واحدة على الأقل',
        scheduleRequired: 'يرجى تحديد وقت الجدولة'
      },
      tips: {
        content: 'استخدم الهاشتاغات والإشارات لزيادة التفاعل',
        media: 'الصور والفيديوهات تزيد من معدل التفاعل بنسبة 650%',
        platforms: 'كل منصة لها جمهور مختلف، اختر بحكمة',
        schedule: 'أفضل أوقات النشر: 9-11 صباحاً و 7-9 مساءً',
        preview: 'تأكد من أن المنشور يبدو رائعاً على جميع المنصات'
      }
    },
    en: {
      title: 'Create New Post',
      subtitle: 'Create engaging content for social media',
      steps: {
        content: { title: 'Content', description: 'Write your post content' },
        media: { title: 'Media', description: 'Add images or videos' },
        platforms: { title: 'Platforms', description: 'Choose publishing platforms' },
        schedule: { title: 'Schedule', description: 'Set publishing time' },
        preview: { title: 'Preview', description: 'Review before publishing' }
      },
      actions: {
        next: 'Next',
        previous: 'Previous',
        save: 'Save as Draft',
        publish: 'Publish Now',
        schedule: 'Schedule Post'
      },
      status: {
        autoSaved: 'Auto-saved',
        saving: 'Saving...',
        error: 'Save error',
        lastSaved: 'Last saved'
      },
      validation: {
        contentRequired: 'Content is required',
        platformRequired: 'Please select at least one platform',
        scheduleRequired: 'Please set schedule time'
      },
      tips: {
        content: 'Use hashtags and mentions to increase engagement',
        media: 'Images and videos increase engagement by 650%',
        platforms: 'Each platform has different audience, choose wisely',
        schedule: 'Best posting times: 9-11 AM and 7-9 PM',
        preview: 'Make sure your post looks great on all platforms'
      }
    }
  };

  const text = t[language];

  // Form setup
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content: initialData?.content || '',
      mediaUrls: initialData?.mediaUrls || [],
      status: initialData?.status || 'DRAFT',
      scheduledAt: initialData?.scheduledAt,
      platforms: initialData?.platforms || [],
      timezone: initialData?.timezone || 'Asia/Riyadh',
    },
  });

  // Steps configuration
  const steps: Step[] = [
    {
      id: 'content',
      title: text.steps.content.title,
      description: text.steps.content.description,
      icon: Type,
      isCompleted: !!form.watch('content'),
      isActive: currentStep === 0,
    },
    {
      id: 'media',
      title: text.steps.media.title,
      description: text.steps.media.description,
      icon: Image,
      isCompleted: (form.watch('mediaUrls') || []).length > 0,
      isActive: currentStep === 1,
      isOptional: true,
    },
    {
      id: 'platforms',
      title: text.steps.platforms.title,
      description: text.steps.platforms.description,
      icon: Target,
      isCompleted: (form.watch('platforms') || []).length > 0,
      isActive: currentStep === 2,
    },
    {
      id: 'schedule',
      title: text.steps.schedule.title,
      description: text.steps.schedule.description,
      icon: Calendar,
      isCompleted: form.watch('status') === 'PUBLISHED' || !!form.watch('scheduledAt'),
      isActive: currentStep === 3,
      isOptional: true,
    },
    {
      id: 'preview',
      title: text.steps.preview.title,
      description: text.steps.preview.description,
      icon: Eye,
      isCompleted: false,
      isActive: currentStep === 4,
    },
  ];

  // Auto-save functionality
  const autoSave = useCallback(async () => {
    const values = form.getValues();
    if (!values.content.trim()) return;

    setAutoSaveStatus('saving');
    try {
      // Simulate auto-save API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setAutoSaveStatus('saved');
      setLastSaved(new Date());
      
      // Call onSave if provided
      onSave?.(values);
    } catch (error) {
      setAutoSaveStatus('error');
      console.error('Auto-save failed:', error);
    }
  }, [form, onSave]);

  // Auto-save effect
  useEffect(() => {
    const subscription = form.watch(() => {
      const timer = setTimeout(autoSave, 2000); // Auto-save after 2 seconds of inactivity
      return () => clearTimeout(timer);
    });

    return () => subscription.unsubscribe();
  }, [form, autoSave]);

  // Navigation functions
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex);
  };

  // Form submission
  const onSubmit = async (values: FormData) => {
    setIsLoading(true);
    try {
      if (values.status === 'PUBLISHED') {
        await onPublish?.(values);
        toast.success(language === 'ar' ? 'تم نشر المنشور بنجاح!' : 'Post published successfully!');
      } else {
        await onSave?.(values);
        toast.success(language === 'ar' ? 'تم حفظ المنشور كمسودة!' : 'Post saved as draft!');
      }
      router.push('/posts');
    } catch (error) {
      toast.error(language === 'ar' ? 'حدث خطأ أثناء النشر' : 'Error occurred while publishing');
      console.error('Form submission error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate progress
  const completedSteps = steps.filter(step => step.isCompleted).length;
  const progress = (completedSteps / steps.length) * 100;

  return (
    <div className={cn("max-w-6xl mx-auto space-y-6", className)}>
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        language === 'ar' ? "flex-row-reverse" : ""
      )}>
        <div className={language === 'ar' ? "text-right" : ""}>
          <h1 className="text-3xl font-bold text-gray-900">{text.title}</h1>
          <p className="text-gray-600 mt-1">{text.subtitle}</p>
        </div>
        
        {/* Auto-save status */}
        <div className={cn(
          "flex items-center gap-2 text-sm",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          {autoSaveStatus === 'saving' && (
            <>
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
              <span className="text-yellow-600">{text.status.saving}</span>
            </>
          )}
          {autoSaveStatus === 'saved' && (
            <>
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-green-600">{text.status.autoSaved}</span>
              {lastSaved && (
                <span className="text-gray-500">
                  {text.status.lastSaved}: {lastSaved.toLocaleTimeString()}
                </span>
              )}
            </>
          )}
          {autoSaveStatus === 'error' && (
            <>
              <AlertCircle className="w-4 h-4 text-red-500" />
              <span className="text-red-600">{text.status.error}</span>
            </>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className={cn(
              "flex items-center justify-between",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <span className="text-sm font-medium text-gray-700">
                {language === 'ar' ? 'التقدم' : 'Progress'}: {completedSteps}/{steps.length}
              </span>
              <Badge variant="secondary" className="text-xs">
                {Math.round(progress)}%
              </Badge>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Step Navigation */}
      <Card>
        <CardContent className="pt-6">
          <div className={cn(
            "flex items-center justify-between",
            language === 'ar' ? "flex-row-reverse" : ""
          )}>
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={cn(
                  "flex items-center cursor-pointer transition-all",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}
                onClick={() => goToStep(index)}
              >
                <div className={cn(
                  "flex items-center gap-3",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  {/* Step Icon */}
                  <div className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center transition-all",
                    step.isActive
                      ? "bg-blue-500 text-white"
                      : step.isCompleted
                        ? "bg-green-500 text-white"
                        : "bg-gray-200 text-gray-500"
                  )}>
                    {step.isCompleted ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <step.icon className="w-5 h-5" />
                    )}
                  </div>

                  {/* Step Info */}
                  <div className={cn(
                    "hidden md:block",
                    language === 'ar' ? "text-right" : ""
                  )}>
                    <p className={cn(
                      "text-sm font-medium",
                      step.isActive ? "text-blue-600" : "text-gray-700"
                    )}>
                      {step.title}
                      {step.isOptional && (
                        <Badge variant="outline" className="ml-2 text-xs">
                          {language === 'ar' ? 'اختياري' : 'Optional'}
                        </Badge>
                      )}
                    </p>
                    <p className="text-xs text-gray-500">{step.description}</p>
                  </div>
                </div>

                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className={cn(
                    "w-12 h-0.5 mx-4",
                    step.isCompleted ? "bg-green-300" : "bg-gray-200"
                  )}></div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Form Content */}
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse text-right" : ""
              )}>
                <steps[currentStep].icon className="w-6 h-6 text-blue-500" />
                {steps[currentStep].title}
              </CardTitle>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {steps[currentStep].description}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Step Content */}
              {currentStep === 0 && (
                <div className="space-y-6">
                  {/* Content Editor */}
                  <FormField
                    control={form.control}
                    name="content"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-lg font-semibold">
                          {language === 'ar' ? 'محتوى المنشور' : 'Post Content'}
                        </FormLabel>
                        <FormControl>
                          <RichTextEditor
                            content={field.value}
                            placeholder={language === 'ar'
                              ? "اكتب محتوى المنشور هنا... 🚀"
                              : "Write your post content here... 🚀"
                            }
                            platform="facebook"
                            onChange={field.onChange}
                            dir={language === 'ar' ? 'rtl' : 'ltr'}
                            autoSave={true}
                            className="min-h-[300px]"
                          />
                        </FormControl>
                        <FormDescription>
                          {text.tips.content}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Content Tips */}
                  <Alert>
                    <Sparkles className="h-4 w-4" />
                    <AlertDescription className={language === 'ar' ? "text-right" : ""}>
                      {text.tips.content}
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {currentStep === 1 && (
                <div className="space-y-6">
                  {/* Media Upload */}
                  <FormField
                    control={form.control}
                    name="mediaUrls"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-lg font-semibold">
                          {language === 'ar' ? 'الوسائط' : 'Media'}
                        </FormLabel>
                        <FormControl>
                          <EnhancedMediaUpload
                            onUploadComplete={(file) => {
                              const currentUrls = field.value || [];
                              field.onChange([...currentUrls, file.url]);
                            }}
                            acceptedTypes={['image/*', 'video/*']}
                            maxSize={50 * 1024 * 1024} // 50MB
                            className="min-h-[200px]"
                          />
                        </FormControl>
                        <FormDescription>
                          {language === 'ar'
                            ? 'أضف صور أو فيديوهات لجعل منشورك أكثر جاذبية'
                            : 'Add images or videos to make your post more engaging'
                          }
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Media Tips */}
                  <Alert>
                    <Zap className="h-4 w-4" />
                    <AlertDescription className={language === 'ar' ? "text-right" : ""}>
                      {text.tips.media}
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-6">
                  {/* Platform Selection */}
                  <FormField
                    control={form.control}
                    name="platforms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-lg font-semibold">
                          {language === 'ar' ? 'منصات النشر' : 'Publishing Platforms'}
                        </FormLabel>
                        <FormControl>
                          <PlatformSelector
                            selectedPlatforms={field.value}
                            onSelectionChange={field.onChange}
                            language={language}
                          />
                        </FormControl>
                        <FormDescription>
                          {language === 'ar'
                            ? 'اختر المنصات التي تريد النشر عليها'
                            : 'Choose platforms where you want to publish'
                          }
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Platform Tips */}
                  <Alert>
                    <Target className="h-4 w-4" />
                    <AlertDescription className={language === 'ar' ? "text-right" : ""}>
                      {text.tips.platforms}
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-6">
                  {/* Scheduling */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-lg font-semibold">
                            {language === 'ar' ? 'نوع النشر' : 'Publishing Type'}
                          </FormLabel>
                          <FormControl>
                            <div className="space-y-3">
                              <div className={cn(
                                "flex items-center gap-3 p-4 border rounded-lg cursor-pointer transition-all",
                                field.value === 'PUBLISHED' ? "border-blue-500 bg-blue-50" : "border-gray-200",
                                language === 'ar' ? "flex-row-reverse" : ""
                              )}
                              onClick={() => field.onChange('PUBLISHED')}
                              >
                                <Send className="w-5 h-5 text-blue-500" />
                                <div className={language === 'ar' ? "text-right" : ""}>
                                  <p className="font-medium">{text.actions.publish}</p>
                                  <p className="text-sm text-gray-500">
                                    {language === 'ar' ? 'نشر فوري للمنشور' : 'Publish immediately'}
                                  </p>
                                </div>
                              </div>

                              <div className={cn(
                                "flex items-center gap-3 p-4 border rounded-lg cursor-pointer transition-all",
                                field.value === 'SCHEDULED' ? "border-blue-500 bg-blue-50" : "border-gray-200",
                                language === 'ar' ? "flex-row-reverse" : ""
                              )}
                              onClick={() => field.onChange('SCHEDULED')}
                              >
                                <Clock className="w-5 h-5 text-orange-500" />
                                <div className={language === 'ar' ? "text-right" : ""}>
                                  <p className="font-medium">{text.actions.schedule}</p>
                                  <p className="text-sm text-gray-500">
                                    {language === 'ar' ? 'جدولة النشر لوقت لاحق' : 'Schedule for later'}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch('status') === 'SCHEDULED' && (
                      <FormField
                        control={form.control}
                        name="scheduledAt"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-lg font-semibold">
                              {language === 'ar' ? 'وقت النشر' : 'Publishing Time'}
                            </FormLabel>
                            <FormControl>
                              <PostScheduler
                                selectedDate={field.value}
                                onDateChange={field.onChange}
                                timezone={form.watch('timezone')}
                                language={language}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>

                  {/* Schedule Tips */}
                  <Alert>
                    <Calendar className="h-4 w-4" />
                    <AlertDescription className={language === 'ar' ? "text-right" : ""}>
                      {text.tips.schedule}
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {currentStep === 4 && (
                <div className="space-y-6">
                  {/* Post Preview */}
                  <div>
                    <h3 className={cn(
                      "text-lg font-semibold mb-4",
                      language === 'ar' ? "text-right" : ""
                    )}>
                      {language === 'ar' ? 'معاينة المنشور' : 'Post Preview'}
                    </h3>
                    <PostPreview
                      content={form.watch('content')}
                      mediaUrls={form.watch('mediaUrls') || []}
                      platforms={form.watch('platforms')}
                      scheduledAt={form.watch('scheduledAt')}
                      language={language}
                    />
                  </div>

                  {/* Preview Tips */}
                  <Alert>
                    <Eye className="h-4 w-4" />
                    <AlertDescription className={language === 'ar' ? "text-right" : ""}>
                      {text.tips.preview}
                    </AlertDescription>
                  </Alert>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Navigation and Actions */}
          <Card>
            <CardContent className="pt-6">
              <div className={cn(
                "flex items-center justify-between",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                {/* Previous Button */}
                <Button
                  type="button"
                  variant="outline"
                  onClick={previousStep}
                  disabled={currentStep === 0}
                  className={cn(
                    "flex items-center gap-2",
                    language === 'ar' ? "flex-row-reverse" : ""
                  )}
                >
                  {language === 'ar' ? (
                    <>
                      <ArrowRight className="w-4 h-4" />
                      {text.actions.previous}
                    </>
                  ) : (
                    <>
                      <ArrowLeft className="w-4 h-4" />
                      {text.actions.previous}
                    </>
                  )}
                </Button>

                {/* Action Buttons */}
                <div className={cn(
                  "flex items-center gap-3",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  {/* Save Draft */}
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      const values = form.getValues();
                      values.status = 'DRAFT';
                      onSubmit(values);
                    }}
                    disabled={isLoading}
                    className={cn(
                      "flex items-center gap-2",
                      language === 'ar' ? "flex-row-reverse" : ""
                    )}
                  >
                    <Save className="w-4 h-4" />
                    {text.actions.save}
                  </Button>

                  {/* Next/Publish Button */}
                  {currentStep < steps.length - 1 ? (
                    <Button
                      type="button"
                      onClick={nextStep}
                      disabled={!steps[currentStep].isCompleted && !steps[currentStep].isOptional}
                      className={cn(
                        "flex items-center gap-2",
                        language === 'ar' ? "flex-row-reverse" : ""
                      )}
                    >
                      {language === 'ar' ? (
                        <>
                          {text.actions.next}
                          <ArrowLeft className="w-4 h-4" />
                        </>
                      ) : (
                        <>
                          {text.actions.next}
                          <ArrowRight className="w-4 h-4" />
                        </>
                      )}
                    </Button>
                  ) : (
                    <div className={cn(
                      "flex items-center gap-2",
                      language === 'ar' ? "flex-row-reverse" : ""
                    )}>
                      {form.watch('status') === 'SCHEDULED' ? (
                        <Button
                          type="submit"
                          disabled={isLoading || !form.formState.isValid}
                          className={cn(
                            "flex items-center gap-2 bg-orange-500 hover:bg-orange-600",
                            language === 'ar' ? "flex-row-reverse" : ""
                          )}
                        >
                          <Clock className="w-4 h-4" />
                          {isLoading ? (
                            language === 'ar' ? 'جاري الجدولة...' : 'Scheduling...'
                          ) : (
                            text.actions.schedule
                          )}
                        </Button>
                      ) : (
                        <Button
                          type="submit"
                          disabled={isLoading || !form.formState.isValid}
                          className={cn(
                            "flex items-center gap-2 bg-green-500 hover:bg-green-600",
                            language === 'ar' ? "flex-row-reverse" : ""
                          )}
                        >
                          <Send className="w-4 h-4" />
                          {isLoading ? (
                            language === 'ar' ? 'جاري النشر...' : 'Publishing...'
                          ) : (
                            text.actions.publish
                          )}
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Form Validation Summary */}
              {!form.formState.isValid && currentStep === steps.length - 1 && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className={cn(
                    "flex items-center gap-2 text-red-700",
                    language === 'ar' ? "flex-row-reverse" : ""
                  )}>
                    <AlertCircle className="w-4 h-4" />
                    <span className="font-medium">
                      {language === 'ar' ? 'يرجى إصلاح الأخطاء التالية:' : 'Please fix the following errors:'}
                    </span>
                  </div>
                  <ul className={cn(
                    "mt-2 space-y-1 text-sm text-red-600",
                    language === 'ar' ? "text-right" : ""
                  )}>
                    {Object.entries(form.formState.errors).map(([field, error]) => (
                      <li key={field}>• {error?.message}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Progress Indicator */}
              <div className="mt-4 pt-4 border-t">
                <div className={cn(
                  "flex items-center justify-center gap-2 text-sm text-gray-500",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <span>
                    {language === 'ar' ? 'الخطوة' : 'Step'} {currentStep + 1} {language === 'ar' ? 'من' : 'of'} {steps.length}
                  </span>
                  <Separator orientation="vertical" className="h-4" />
                  <span>
                    {Math.round(progress)}% {language === 'ar' ? 'مكتمل' : 'complete'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </form>
      </FormProvider>

      {/* Keyboard Shortcuts Help */}
      <Card className="bg-gray-50">
        <CardContent className="pt-6">
          <div className={cn(
            "flex items-center justify-between text-sm text-gray-600",
            language === 'ar' ? "flex-row-reverse" : ""
          )}>
            <div className={cn(
              "flex items-center gap-4",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <span className="font-medium">
                {language === 'ar' ? 'اختصارات لوحة المفاتيح:' : 'Keyboard shortcuts:'}
              </span>
              <div className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <Badge variant="outline" className="text-xs">Ctrl + S</Badge>
                <span>{language === 'ar' ? 'حفظ' : 'Save'}</span>
              </div>
              <div className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <Badge variant="outline" className="text-xs">Ctrl + Enter</Badge>
                <span>{language === 'ar' ? 'نشر' : 'Publish'}</span>
              </div>
            </div>

            <div className={cn(
              "flex items-center gap-2",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <Settings className="w-4 h-4" />
              <span>{language === 'ar' ? 'إعدادات متقدمة' : 'Advanced settings'}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
