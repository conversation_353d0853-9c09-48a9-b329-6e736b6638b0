#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create sample posts for testing the posts page functionality
 * This script adds sample posts directly to the database for the demo user
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const DEMO_USER_EMAIL = '<EMAIL>';

async function createSamplePosts() {
  try {
    console.log('🔍 Finding demo user...');

    // Get demo user from auth
    const { data: user, error: userError } = await supabase.auth.admin.listUsers();

    if (userError) {
      console.error('❌ Error fetching users:', userError);
      return;
    }

    const demoUser = user.users.find(u => u.email === DEMO_USER_EMAIL);

    if (!demoUser) {
      console.error(`❌ Demo user not found: ${DEMO_USER_EMAIL}`);
      return;
    }

    console.log(`✅ Found demo user: ${demoUser.id}`);

    // Check if user exists in users table, if not create it
    console.log('🔍 Checking users table...');
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('id')
      .eq('id', demoUser.id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = not found
      console.error('❌ Error checking users table:', checkError);
      return;
    }

    if (!existingUser) {
      console.log('📝 Creating user record in users table...');
      const { error: insertUserError } = await supabase
        .from('users')
        .insert({
          id: demoUser.id,
          email: demoUser.email,
          name: 'Demo User',
          email_verified: demoUser.email_confirmed_at,
          role: 'USER',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (insertUserError) {
        console.error('❌ Error creating user record:', insertUserError);
        return;
      }
      console.log('✅ User record created successfully');
    } else {
      console.log('✅ User record already exists');
    }

    // Sample posts data
    const samplePosts = [
      {
        user_id: demoUser.id,
        content: 'مرحباً بكم في منصة eWasl لإدارة وسائل التواصل الاجتماعي! 🚀\n\nنحن متحمسون لمساعدتكم في إدارة حساباتكم على جميع منصات التواصل الاجتماعي بطريقة احترافية وفعالة.\n\n#eWasl #SocialMedia #إدارة_المحتوى',
        status: 'PUBLISHED',
        published_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        user_id: demoUser.id,
        content: 'نصائح لإنشاء محتوى جذاب على وسائل التواصل الاجتماعي:\n\n✨ استخدم الصور والفيديوهات عالية الجودة\n📝 اكتب نصوص واضحة ومفيدة\n⏰ انشر في الأوقات المناسبة لجمهورك\n💬 تفاعل مع التعليقات والرسائل\n📊 راقب الإحصائيات وحلل الأداء\n\n#نصائح_التسويق #المحتوى_الرقمي',
        status: 'PUBLISHED',
        published_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        user_id: demoUser.id,
        content: 'هل تعلم أن منصة eWasl تدعم جدولة المنشورات على جميع منصات التواصل الاجتماعي؟ 📅\n\nيمكنك الآن:\n• جدولة منشوراتك مسبقاً\n• إدارة عدة حسابات من مكان واحد\n• متابعة الإحصائيات والتحليلات\n• توفير الوقت والجهد\n\nجرب المنصة الآن واكتشف المزيد من المميزات! 🎯',
        status: 'SCHEDULED',
        scheduled_at: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
        created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
        updated_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
      },
      {
        user_id: demoUser.id,
        content: 'مسودة منشور حول أهمية التسويق الرقمي في العصر الحديث...\n\nالتسويق الرقمي أصبح ضرورة لا غنى عنها لأي عمل تجاري يريد النجاح والنمو.\n\n[يحتاج إلى مراجعة وإضافة المزيد من التفاصيل]',
        status: 'DRAFT',
        created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
        updated_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString() // 1 hour ago
      },
      {
        user_id: demoUser.id,
        content: 'شكراً لجميع متابعينا الأعزاء! 🙏\n\nوصلنا إلى 1000 متابع على منصاتنا المختلفة، وهذا بفضل دعمكم المستمر وثقتكم في خدماتنا.\n\nنعدكم بتقديم المزيد من المحتوى المفيد والخدمات المتميزة.\n\n#شكراً #1000_متابع #eWasl_community',
        status: 'FAILED',
        created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
        updated_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
      }
    ];

    console.log('📝 Creating sample posts...');

    // Insert sample posts
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .insert(samplePosts)
      .select();

    if (postsError) {
      console.error('❌ Error creating posts:', postsError);
      return;
    }

    console.log(`✅ Created ${posts.length} sample posts successfully!`);
    
    // Display created posts
    posts.forEach((post, index) => {
      console.log(`\n📄 Post ${index + 1}:`);
      console.log(`   ID: ${post.id}`);
      console.log(`   Status: ${post.status}`);
      console.log(`   Content: ${post.content.substring(0, 50)}...`);
      console.log(`   Created: ${post.created_at}`);
    });

    console.log('\n🎉 Sample posts created successfully!');
    console.log('You can now test the posts page at http://localhost:3000/posts');

  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Run the script
createSamplePosts();
