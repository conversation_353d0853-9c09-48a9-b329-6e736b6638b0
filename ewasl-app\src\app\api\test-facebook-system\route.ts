import { NextRequest, NextResponse } from 'next/server';

interface DiagnosticResult {
  test_name: string;
  success: boolean;
  message: string;
  details?: any;
  recommendations?: string[];
}

async function runDiagnosticTest(endpoint: string, baseUrl: string): Promise<DiagnosticResult> {
  try {
    const response = await fetch(`${baseUrl}${endpoint}`);
    const data = await response.json();
    
    return {
      test_name: endpoint.replace('/api/', ''),
      success: response.ok && data.success,
      message: data.message || `${endpoint} completed`,
      details: data,
      recommendations: data.recommendations || data.global_recommendations
    };
  } catch (error) {
    return {
      test_name: endpoint.replace('/api/', ''),
      success: false,
      message: `فشل في تشغيل اختبار ${endpoint}`,
      details: { error: error instanceof Error ? error.message : 'خطأ غير معروف' },
      recommendations: [`إصلاح مطلوب لاختبار ${endpoint}`]
    };
  }
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  
  try {
    console.log(`🔍 [${timestamp}] Starting comprehensive Facebook system diagnostics...`);

    const baseUrl = new URL(request.url).origin;
    
    // Run all diagnostic tests
    const diagnosticEndpoints = [
      '/api/test-oauth-tokens',
      '/api/test-facebook-permissions', 
      '/api/test-account-mapping'
    ];

    console.log(`📊 [${timestamp}] Running ${diagnosticEndpoints.length} diagnostic tests...`);

    const diagnosticResults: DiagnosticResult[] = [];
    
    for (const endpoint of diagnosticEndpoints) {
      console.log(`🔍 [${timestamp}] Running diagnostic: ${endpoint}`);
      const result = await runDiagnosticTest(endpoint, baseUrl);
      diagnosticResults.push(result);
      console.log(`${result.success ? '✅' : '❌'} [${timestamp}] ${endpoint}: ${result.success ? 'نجح' : 'فشل'}`);
    }

    // Analyze results
    const passedTests = diagnosticResults.filter(r => r.success).length;
    const failedTests = diagnosticResults.length - passedTests;
    const overallSuccess = failedTests === 0;

    // Generate comprehensive recommendations
    const allRecommendations: string[] = [];
    const criticalIssues: string[] = [];
    
    diagnosticResults.forEach(result => {
      if (!result.success) {
        criticalIssues.push(`❌ ${result.test_name}: ${result.message}`);
      }
      if (result.recommendations) {
        allRecommendations.push(...result.recommendations);
      }
    });

    // Add system-level recommendations
    if (failedTests > 0) {
      allRecommendations.unshift('🔧 إجراءات مطلوبة لحل مشاكل النظام:');
    } else {
      allRecommendations.unshift('✅ جميع الاختبارات نجحت - النظام جاهز للنشر');
    }

    // Determine root cause of 500 errors
    let rootCauseAnalysis = 'تحليل السبب الجذري للخطأ 500:';
    let publishingReadiness = false;

    const tokenTest = diagnosticResults.find(r => r.test_name === 'test-oauth-tokens');
    const permissionsTest = diagnosticResults.find(r => r.test_name === 'test-facebook-permissions');
    const mappingTest = diagnosticResults.find(r => r.test_name === 'test-account-mapping');

    if (!tokenTest?.success) {
      rootCauseAnalysis += '\n• مشكلة في رموز OAuth - السبب الأكثر احتمالاً للخطأ 500';
    } else if (!permissionsTest?.success) {
      rootCauseAnalysis += '\n• مشكلة في صلاحيات فيسبوك - قد تسبب خطأ 500 عند النشر';
    } else if (!mappingTest?.success) {
      rootCauseAnalysis += '\n• مشكلة في ربط الحسابات - قد تسبب خطأ 500 في اختيار المنصة';
    } else {
      rootCauseAnalysis += '\n• جميع الاختبارات الأساسية نجحت - المشكلة قد تكون في مكان آخر';
      publishingReadiness = true;
    }

    // Test publishing readiness
    let publishingTest = null;
    if (publishingReadiness) {
      try {
        console.log(`🚀 [${timestamp}] Testing publishing readiness...`);
        
        // Get account details from mapping test
        const accountDetails = mappingTest?.details?.platforms?.find((p: any) => 
          p.platform === 'FACEBOOK' && p.available_for_posting > 0
        );

        if (accountDetails && accountDetails.accounts.length > 0) {
          const readyAccount = accountDetails.accounts.find((acc: any) => acc.can_post);
          
          publishingTest = {
            ready_accounts: accountDetails.available_for_posting,
            total_accounts: accountDetails.total_accounts,
            sample_account: readyAccount ? {
              name: readyAccount.account_name,
              can_post: readyAccount.can_post,
              issues: readyAccount.issues
            } : null,
            recommendation: readyAccount ? 
              'النظام جاهز لاختبار النشر الفعلي' : 
              'لا توجد حسابات جاهزة للنشر'
          };
        }
      } catch (error) {
        publishingTest = {
          error: 'فشل في اختبار جاهزية النشر',
          details: error instanceof Error ? error.message : 'خطأ غير معروف'
        };
      }
    }

    const response = {
      success: overallSuccess,
      message: overallSuccess ? 
        'جميع اختبارات نظام فيسبوك نجحت ✅' : 
        `فشل ${failedTests} من ${diagnosticResults.length} اختبارات ❌`,
      
      summary: {
        total_tests: diagnosticResults.length,
        passed_tests: passedTests,
        failed_tests: failedTests,
        success_rate: `${Math.round((passedTests / diagnosticResults.length) * 100)}%`,
        publishing_ready: publishingReadiness
      },

      diagnostic_results: diagnosticResults,
      
      root_cause_analysis: rootCauseAnalysis,
      
      critical_issues: criticalIssues,
      
      publishing_readiness: publishingTest,
      
      recommendations: allRecommendations,
      
      next_steps: overallSuccess ? [
        '1. اختبر النشر الفعلي باستخدام /posts/new',
        '2. راقب سجلات الخادم للتأكد من عدم وجود أخطاء',
        '3. تحقق من ظهور المنشور على فيسبوك فعلياً'
      ] : [
        '1. أصلح المشاكل المحددة في الاختبارات الفاشلة',
        '2. أعد تشغيل هذا الاختبار للتأكد من الإصلاح',
        '3. بعد نجاح جميع الاختبارات، اختبر النشر الفعلي'
      ],

      debug_info: {
        timestamp,
        response_time_ms: Date.now() - startTime,
        base_url: baseUrl,
        user_agent: request.headers.get('user-agent'),
        facebook_app_id: '1366325774493759'
      }
    };

    console.log(`${overallSuccess ? '✅' : '❌'} [${timestamp}] Facebook system diagnostics completed: ${passedTests}/${diagnosticResults.length} tests passed`);
    
    return NextResponse.json(response);

  } catch (error) {
    const errorTimestamp = new Date().toISOString();
    console.error(`❌ [${errorTimestamp}] Facebook system diagnostics failed:`, error);
    
    return NextResponse.json({
      success: false,
      message: 'فشل في تشغيل اختبارات نظام فيسبوك',
      error: error instanceof Error ? error.message : 'خطأ غير معروف',
      timestamp: errorTimestamp,
      response_time_ms: Date.now() - startTime
    }, { status: 500 });
  }
}
