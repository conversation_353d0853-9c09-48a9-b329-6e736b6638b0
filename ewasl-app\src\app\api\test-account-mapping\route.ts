import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface AccountMappingResult {
  platform: string;
  total_accounts: number;
  connected_accounts: number;
  available_for_posting: number;
  accounts: {
    account_id: string;
    account_name: string;
    status: string;
    user_id?: string;
    page_id?: string;
    page_name?: string;
    can_post: boolean;
    issues: string[];
  }[];
}

interface PlatformSelectionTest {
  platform: string;
  selected_by_ui: boolean;
  mapped_accounts: number;
  ready_for_publishing: boolean;
  blocking_issues: string[];
}

async function testAccountAvailability(account: any): Promise<{
  can_post: boolean;
  issues: string[];
}> {
  const issues: string[] = [];
  
  // Check basic requirements
  if (!account.access_token) {
    issues.push('رمز الوصول مفقود');
  }
  
  if (account.status !== 'CONNECTED') {
    issues.push(`حالة الحساب: ${account.status}`);
  }
  
  if (!account.account_id) {
    issues.push('معرف الحساب مفقود');
  }

  // For Facebook, check if we have page info when needed
  if (account.platform === 'FACEBOOK' && account.page_id && !account.page_name) {
    issues.push('معلومات الصفحة غير مكتملة');
  }

  // Test token validity (simplified check)
  if (account.access_token && account.platform === 'FACEBOOK') {
    try {
      const response = await fetch(`https://graph.facebook.com/me?access_token=${account.access_token}`);
      if (!response.ok) {
        issues.push('رمز الوصول منتهي الصلاحية أو غير صالح');
      }
    } catch (error) {
      issues.push('خطأ في التحقق من رمز الوصول');
    }
  }

  return {
    can_post: issues.length === 0,
    issues
  };
}

async function simulatePlatformSelection(platforms: string[]): Promise<PlatformSelectionTest[]> {
  const results: PlatformSelectionTest[] = [];

  for (const platform of platforms) {
    console.log(`🔍 [Account Mapping Test] Testing platform selection: ${platform}`);

    // Get accounts for this platform
    const { data: accounts, error } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('platform', platform.toUpperCase());

    if (error) {
      results.push({
        platform,
        selected_by_ui: true,
        mapped_accounts: 0,
        ready_for_publishing: false,
        blocking_issues: [`خطأ في قاعدة البيانات: ${error.message}`]
      });
      continue;
    }

    const connectedAccounts = accounts?.filter(acc => acc.status === 'CONNECTED') || [];
    const blockingIssues: string[] = [];

    if (connectedAccounts.length === 0) {
      blockingIssues.push('لا توجد حسابات متصلة لهذه المنصة');
    }

    // Test each connected account
    let readyAccounts = 0;
    for (const account of connectedAccounts) {
      const availability = await testAccountAvailability(account);
      if (availability.can_post) {
        readyAccounts++;
      } else {
        blockingIssues.push(`${account.account_name || account.account_id}: ${availability.issues.join(', ')}`);
      }
    }

    if (readyAccounts === 0 && connectedAccounts.length > 0) {
      blockingIssues.push('جميع الحسابات المتصلة بها مشاكل');
    }

    results.push({
      platform,
      selected_by_ui: true,
      mapped_accounts: connectedAccounts.length,
      ready_for_publishing: readyAccounts > 0,
      blocking_issues: blockingIssues
    });
  }

  return results;
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    console.log('🔍 [Account Mapping Test] Starting account mapping validation...');

    const { searchParams } = new URL(request.url);
    const testPlatforms = searchParams.get('platforms')?.split(',') || ['facebook', 'instagram'];

    // Get all social accounts grouped by platform
    const { data: allAccounts, error: fetchError } = await supabase
      .from('social_accounts')
      .select('*')
      .order('platform', { ascending: true })
      .order('created_at', { ascending: false });

    if (fetchError) {
      console.error('❌ [Account Mapping Test] Database fetch error:', fetchError);
      return NextResponse.json({
        success: false,
        message: 'خطأ في قاعدة البيانات',
        error: fetchError.message,
        response_time_ms: Date.now() - startTime
      }, { status: 500 });
    }

    console.log(`📊 [Account Mapping Test] Found ${allAccounts?.length || 0} total social accounts`);

    // Group accounts by platform
    const platformGroups = new Map<string, any[]>();
    (allAccounts || []).forEach(account => {
      const platform = account.platform.toLowerCase();
      if (!platformGroups.has(platform)) {
        platformGroups.set(platform, []);
      }
      platformGroups.get(platform)!.push(account);
    });

    // Analyze each platform
    const platformResults: AccountMappingResult[] = [];

    for (const [platform, accounts] of platformGroups) {
      console.log(`🔍 [Account Mapping Test] Analyzing platform: ${platform}`);
      
      const connectedAccounts = accounts.filter(acc => acc.status === 'CONNECTED');
      const accountDetails = [];
      let availableForPosting = 0;

      for (const account of accounts) {
        const availability = await testAccountAvailability(account);
        
        if (availability.can_post) {
          availableForPosting++;
        }

        accountDetails.push({
          account_id: account.account_id,
          account_name: account.account_name || 'غير محدد',
          status: account.status,
          user_id: account.user_id,
          page_id: account.page_id,
          page_name: account.page_name,
          can_post: availability.can_post,
          issues: availability.issues
        });
      }

      platformResults.push({
        platform: platform.toUpperCase(),
        total_accounts: accounts.length,
        connected_accounts: connectedAccounts.length,
        available_for_posting: availableForPosting,
        accounts: accountDetails
      });
    }

    // Test platform selection simulation
    console.log('🔍 [Account Mapping Test] Simulating platform selection...');
    const selectionTests = await simulatePlatformSelection(testPlatforms);

    // Generate recommendations
    const totalAvailable = platformResults.reduce((sum, p) => sum + p.available_for_posting, 0);
    const totalConnected = platformResults.reduce((sum, p) => sum + p.connected_accounts, 0);

    const recommendations: string[] = [];
    
    if (totalAvailable === 0) {
      recommendations.push('⚠️ لا توجد حسابات جاهزة للنشر - يجب حل المشاكل أولاً');
    } else if (totalAvailable < totalConnected) {
      recommendations.push(`⚠️ ${totalConnected - totalAvailable} حساب متصل لكن غير جاهز للنشر`);
    } else {
      recommendations.push('✅ جميع الحسابات المتصلة جاهزة للنشر');
    }

    // Check for common issues
    const commonIssues = new Map<string, number>();
    platformResults.forEach(platform => {
      platform.accounts.forEach(account => {
        account.issues.forEach(issue => {
          commonIssues.set(issue, (commonIssues.get(issue) || 0) + 1);
        });
      });
    });

    if (commonIssues.size > 0) {
      recommendations.push('المشاكل الشائعة:');
      Array.from(commonIssues.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3)
        .forEach(([issue, count]) => {
          recommendations.push(`  • ${issue} (${count} حساب)`);
        });
    }

    const response = {
      success: true,
      message: `تم فحص ربط ${platformResults.reduce((sum, p) => sum + p.total_accounts, 0)} حساب عبر ${platformResults.length} منصة`,
      summary: {
        total_platforms: platformResults.length,
        total_accounts: platformResults.reduce((sum, p) => sum + p.total_accounts, 0),
        connected_accounts: totalConnected,
        available_for_posting: totalAvailable,
        readiness_rate: totalConnected > 0 ? `${Math.round((totalAvailable / totalConnected) * 100)}%` : '0%'
      },
      platforms: platformResults,
      platform_selection_tests: selectionTests,
      ui_integration: {
        post_form_platforms: testPlatforms,
        mapping_success: selectionTests.every(test => test.ready_for_publishing),
        blocking_platforms: selectionTests.filter(test => !test.ready_for_publishing).map(test => test.platform)
      },
      recommendations,
      common_issues: Array.from(commonIssues.entries()).map(([issue, count]) => ({
        issue,
        affected_accounts: count
      })),
      response_time_ms: Date.now() - startTime,
      timestamp: new Date().toISOString()
    };

    console.log(`✅ [Account Mapping Test] Completed: ${totalAvailable}/${totalConnected} accounts ready for posting`);
    
    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ [Account Mapping Test] Unexpected error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'خطأ في خادم اختبار ربط الحسابات',
      error: error instanceof Error ? error.message : 'خطأ غير معروف',
      response_time_ms: Date.now() - startTime,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
