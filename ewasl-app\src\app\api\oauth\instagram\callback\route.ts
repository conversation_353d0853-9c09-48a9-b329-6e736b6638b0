/**
 * Instagram OAuth Callback Handler
 * Handles Instagram Business API OAuth callback through Facebook Graph API
 * Updated: Force rebuild to clear any cached import errors
 */

import { NextRequest, NextResponse } from 'next/server'
import { createFacebookOAuthService } from '@/lib/oauth/facebook'
import { createClient } from '@/lib/supabase/server'
import { createClient as createServiceClient } from '@supabase/supabase-js'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')
    const errorDescription = searchParams.get('error_description')

    // Handle OAuth errors
    if (error) {
      console.error('Instagram OAuth error:', error, errorDescription)
      return NextResponse.redirect(
        new URL(`/social?error=${encodeURIComponent(error)}&description=${encodeURIComponent(errorDescription || '')}`, request.url)
      )
    }

    if (!code || !state) {
      console.error('Missing code or state parameter')
      return NextResponse.redirect(
        new URL('/social?error=missing_parameters', request.url)
      )
    }

    // Validate OAuth state and get user ID
    if (!state) {
      return NextResponse.redirect(
        new URL('/social?error=missing_state&message=OAuth state parameter missing', request.url)
      )
    }

    // Extract the actual state token (remove _instagram suffix if present)
    const actualState = state.replace('_instagram', '');

    const supabase = createClient()
    
    // Create service role client for database operations (bypasses RLS)
    const supabaseService = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Validate state parameter in database
    const { data: stateRecord, error: stateError } = await supabase
      .from('oauth_states')
      .select('*')
      .eq('state_token', actualState)
      .eq('platform', 'INSTAGRAM')
      .single()

    if (stateError || !stateRecord) {
      console.error('Invalid or expired state:', stateError)
      return NextResponse.redirect(
        new URL('/social?error=invalid_state&message=OAuth state validation failed', request.url)
      )
    }

    // Check if state is expired (10 minutes)
    const stateAge = Date.now() - new Date(stateRecord.created_at).getTime()
    if (stateAge > 10 * 60 * 1000) {
      console.error('State expired:', stateAge)
      return NextResponse.redirect(
        new URL('/social?error=state_expired&message=OAuth state has expired', request.url)
      )
    }

    const userId = stateRecord.user_id
    console.log('Instagram OAuth callback - User ID:', userId)

    // Clean up the used state
    await supabase
      .from('oauth_states')
      .delete()
      .eq('state_token', actualState)

    // Initialize Facebook OAuth service (Instagram uses Facebook Graph API)
    const facebookService = createFacebookOAuthService()

    // Exchange code for access token
    console.log('Exchanging Instagram authorization code for access token...')
    const tokenResponse = await facebookService.exchangeCodeForToken(code)
    
    // Get long-lived token
    console.log('Getting long-lived access token for Instagram...')
    const longLivedToken = await facebookService.getLongLivedToken(tokenResponse.access_token)
    
    // Get user's Instagram Business accounts through Facebook Graph API
    console.log('Fetching Instagram Business accounts...')
    const accounts = await facebookService.getUserAccounts(longLivedToken.access_token)
    
    // Filter for Instagram accounts only
    const instagramAccounts = accounts.filter(account => 
      account.platform === 'instagram' || 
      account.accountType === 'instagram_business'
    )

    console.log(`Found ${instagramAccounts.length} Instagram Business accounts`)

    let savedCount = 0
    for (const account of instagramAccounts) {
      try {
        // Prepare account data for database
        const accountData = {
          account_id: account.id,
          user_id: userId,
          platform: 'INSTAGRAM',
          account_name: account.accountName,
          access_token: account.accessToken || longLivedToken.access_token,
          account_handle: account.accountHandle || null,
          connection_status: account.connectionStatus || 'connected',
          refresh_token: account.refreshToken || null,
          expires_at: account.expiresAt ? new Date(account.expiresAt).toISOString() : null,
          last_validated_at: new Date().toISOString(),
          metadata: {
            ...account.metadata,
            instagram_business_account: true,
            connected_via: 'facebook_graph_api'
          },
          // Add page-specific fields for Instagram Business accounts
          page_id: account.page_id || null,
          page_access_token: account.page_access_token || null,
          page_name: account.page_name || account.accountName,
          account_data: {
            id: account.id,
            name: account.accountName,
            platform: 'instagram',
            access_token: account.accessToken ? account.accessToken.substring(0, 20) + '...' : null,
            refresh_token: account.refreshToken ? account.refreshToken.substring(0, 20) + '...' : null,
            expires_at: account.expiresAt,
            permissions: account.permissions || [],
            account_type: account.accountType || 'instagram_business',
            page_id: account.page_id,
            page_access_token: account.page_access_token ? account.page_access_token.substring(0, 20) + '...' : null,
            page_name: account.page_name
          },
          is_active: true,
          last_sync_at: new Date().toISOString(),
          // Additional debug fields to track page connection
          debug_info: {
            original_account_type: account.accountType,
            has_page_connection: !!account.pageId,
            page_connection_method: account.pageId ? 'facebook_page_link' : 'direct_instagram',
            raw_permissions: account.permissions,
            connection_timestamp: new Date().toISOString()
          },
          profile_image_url: account.profileImageUrl || null,
          permissions: account.permissions || [],
          // Instagram page-specific fields (for Facebook page connection)
          page_id: account.pageId || null,
          page_access_token: account.pageAccessToken || null,
          page_name: account.pageName || null
        }

        // 🔍 DEBUG: Log Instagram account object to see if page fields are present
        console.log(`🔍 DEBUGGING: Instagram Account object for ${account.id}:`, {
          id: account.id,
          platform: account.platform,
          accountName: account.accountName,
          hasPageId: !!account.pageId,
          hasPageAccessToken: !!account.pageAccessToken,
          hasPageName: !!account.pageName,
          pageId: account.pageId,
          pageAccessToken: account.pageAccessToken ? account.pageAccessToken.substring(0, 20) + '...' : null,
          pageName: account.pageName,
          metadataConnectedFacebookPageId: account.metadata?.connectedFacebookPageId
        })

        // 🔍 DEBUG: Log Instagram accountData object to see what's being inserted
        console.log(`🔍 DEBUGGING: Instagram AccountData being inserted:`, {
          account_id: accountData.account_id,
          platform: accountData.platform,
          account_name: accountData.account_name,
          page_id: accountData.page_id,
          page_access_token: accountData.page_access_token ? accountData.page_access_token.substring(0, 20) + '...' : null,
          page_name: accountData.page_name
        });

        // Insert or update account in database using service client
        const { error: dbError } = await supabaseService
          .from('social_accounts')
          .upsert(accountData, {
            onConflict: 'user_id,platform,account_id'
          });

        if (dbError) {
          console.error(`Database error saving Instagram account ${account.id}:`, dbError)
        } else {
          console.log(`Successfully saved Instagram account: ${account.accountName}`)
          savedCount++
        }

      } catch (accountError) {
        console.error(`Error processing Instagram account ${account.id}:`, accountError)
      }
    }

    // Log the OAuth completion
    await supabase
      .from('oauth_logs')
      .insert({
        user_id: userId,
        platform: 'instagram',
        action: 'callback_completed',
        success: savedCount > 0,
        details: {
          accounts_found: instagramAccounts.length,
          accounts_saved: savedCount,
          state: actualState
        }
      })

    console.log(`Instagram OAuth completed. Saved ${savedCount}/${instagramAccounts.length} accounts`)

    // Redirect back to social page with success message
    const successMessage = savedCount > 0 
      ? `Successfully connected ${savedCount} Instagram Business account(s)!`
      : 'Connected to Instagram, but no business accounts were found.'
    
    return NextResponse.redirect(
      new URL(`/social?success=${encodeURIComponent(successMessage)}`, request.url)
    )

  } catch (error) {
    console.error('Instagram OAuth callback error:', error)
    
    // Log the error
    try {
      const supabase = createClient()
      const { data: { user } } = await supabase.auth.getUser()
      
      if (user) {
        await supabase
          .from('oauth_logs')
          .insert({
            user_id: user.id,
            platform: 'instagram',
            action: 'callback_error',
            success: false,
            details: {
              error: error instanceof Error ? error.message : 'Unknown error',
              stack: error instanceof Error ? error.stack : undefined
            }
          })
      }
    } catch (logError) {
      console.error('Failed to log OAuth error:', logError)
    }
    
    return NextResponse.redirect(
      new URL('/social?error=callback_failed', request.url)
    )
  }
}
