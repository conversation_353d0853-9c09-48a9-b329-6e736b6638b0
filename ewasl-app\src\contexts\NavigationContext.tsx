"use client";

import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from 'react';

interface NavigationContextType {
  isSidebarOpen: boolean;
  language: 'ar' | 'en';
  isMobile: boolean;
  setLanguage: (lang: 'ar' | 'en') => void;
  toggleSidebar: () => void;
  closeSidebar: () => void;
  openSidebar: () => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

interface NavigationProviderProps {
  children: ReactNode;
}

export function NavigationProvider({ children }: NavigationProviderProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [language, setLanguageState] = useState<'ar' | 'en'>('ar');
  const [isMobile, setIsMobile] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize navigation state only once
  useEffect(() => {
    if (typeof window === 'undefined' || isInitialized) return;

    const checkMobile = () => {
      const mobile = window.innerWidth < 1024;
      setIsMobile(mobile);
      
      // On desktop, sidebar should be visible by default
      // On mobile, sidebar should be closed by default
      setIsSidebarOpen(!mobile);
    };

    // Load saved language preference
    const savedLanguage = localStorage.getItem('preferred-language') as 'ar' | 'en';
    if (savedLanguage && (savedLanguage === 'ar' || savedLanguage === 'en')) {
      setLanguageState(savedLanguage);
      document.documentElement.dir = savedLanguage === 'ar' ? 'rtl' : 'ltr';
      document.documentElement.lang = savedLanguage;
    } else {
      document.documentElement.dir = 'rtl';
      document.documentElement.lang = 'ar';
    }

    checkMobile();
    setIsInitialized(true);

    // Add resize listener
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [isInitialized]);

  const toggleSidebar = useCallback(() => {
    setIsSidebarOpen(prev => !prev);
  }, []);

  const closeSidebar = useCallback(() => {
    setIsSidebarOpen(false);
  }, []);

  const openSidebar = useCallback(() => {
    setIsSidebarOpen(true);
  }, []);

  const setLanguage = useCallback((newLanguage: 'ar' | 'en') => {
    if (typeof window === 'undefined') return;
    
    setLanguageState(newLanguage);

    // Update document direction and language
    const direction = newLanguage === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.dir = direction;
    document.documentElement.lang = newLanguage;

    // Store preference in localStorage
    localStorage.setItem('preferred-language', newLanguage);

    // Close sidebar on mobile when language changes for better UX
    if (isMobile && isSidebarOpen) {
      setIsSidebarOpen(false);
    }
  }, [isMobile, isSidebarOpen]);

  const value: NavigationContextType = {
    isSidebarOpen,
    language,
    isMobile,
    setLanguage,
    toggleSidebar,
    closeSidebar,
    openSidebar,
  };

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  );
}

export function useNavigation() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
}
