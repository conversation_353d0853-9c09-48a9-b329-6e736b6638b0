import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/logging/logger';
import { EnhancedError } from '@/lib/errors/error-codes';

/**
 * Phase 1 Implementation Test Endpoint
 * Tests all Phase 1 features: token refresh, monitoring, error handling
 * 
 * GET /api/test-phase1?test=all|tokens|health|metrics|logging
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const { searchParams } = new URL(request.url);
  const testType = searchParams.get('test') || 'all';

  console.log('🧪 Phase 1 testing started:', { testType });

  try {
    // Get authenticated user
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      throw new EnhancedError('AUTH_REQUIRED');
    }

    const testResults = {
      timestamp: new Date().toISOString(),
      testType,
      user: { id: user.id, email: user.email },
      results: {} as any,
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        duration: 0
      }
    };

    // Test token management
    if (testType === 'all' || testType === 'tokens') {
      console.log('🔄 Testing token management...');
      testResults.results.tokenManagement = await testTokenManagement(user.id);
      testResults.summary.total++;
      if (testResults.results.tokenManagement.success) {
        testResults.summary.passed++;
      } else {
        testResults.summary.failed++;
      }
    }

    // Test health monitoring
    if (testType === 'all' || testType === 'health') {
      console.log('🏥 Testing health monitoring...');
      testResults.results.healthMonitoring = await testHealthMonitoring();
      testResults.summary.total++;
      if (testResults.results.healthMonitoring.success) {
        testResults.summary.passed++;
      } else {
        testResults.summary.failed++;
      }
    }

    // Test metrics collection
    if (testType === 'all' || testType === 'metrics') {
      console.log('📊 Testing metrics collection...');
      testResults.results.metricsCollection = await testMetricsCollection(user.id);
      testResults.summary.total++;
      if (testResults.results.metricsCollection.success) {
        testResults.summary.passed++;
      } else {
        testResults.summary.failed++;
      }
    }

    // Test logging system
    if (testType === 'all' || testType === 'logging') {
      console.log('📝 Testing logging system...');
      testResults.results.loggingSystem = await testLoggingSystem(user.id);
      testResults.summary.total++;
      if (testResults.results.loggingSystem.success) {
        testResults.summary.passed++;
      } else {
        testResults.summary.failed++;
      }
    }

    // Calculate duration
    testResults.summary.duration = Date.now() - startTime;

    console.log('🧪 Phase 1 testing completed:', testResults.summary);

    return NextResponse.json({
      success: true,
      message: 'Phase 1 testing completed',
      arabicMessage: 'تم إكمال اختبار المرحلة الأولى بنجاح',
      ...testResults
    });

  } catch (error) {
    console.error('❌ Phase 1 testing failed:', error);
    
    const errorMessage = error instanceof EnhancedError 
      ? error.arabicMessage 
      : 'فشل في اختبار المرحلة الأولى';

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        message: 'Phase 1 testing failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      },
      { status: 500 }
    );
  }
}

/**
 * Test token management functionality
 */
async function testTokenManagement(userId: string) {
  const results = {
    success: false,
    message: '',
    tests: {} as any
  };

  try {
    // Test 1: Check if token refresh endpoint exists
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/oauth/instagram/refresh-token`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      results.tests.refreshEndpointExists = {
        success: response.status !== 404,
        message: response.status !== 404 ? 'Token refresh endpoint exists' : 'Token refresh endpoint not found'
      };
    } catch (error) {
      results.tests.refreshEndpointExists = {
        success: false,
        message: 'Failed to test token refresh endpoint'
      };
    }

    // Test 2: Check token manager functionality
    try {
      const { tokenManager } = await import('@/lib/social/token-manager');
      
      // Test getting accounts needing refresh
      const accountsNeedingRefresh = await tokenManager.getAccountsNeedingRefresh(userId);
      
      results.tests.tokenManagerFunctionality = {
        success: true,
        message: `Token manager working, found ${accountsNeedingRefresh.length} accounts needing refresh`,
        accountsCount: accountsNeedingRefresh.length
      };
    } catch (error) {
      results.tests.tokenManagerFunctionality = {
        success: false,
        message: error instanceof Error ? error.message : 'Token manager test failed'
      };
    }

    // Test 3: Check database token queries
    try {
      const supabase = createClient();
      const { data: accounts, error } = await supabase
        .from('social_accounts')
        .select('id, platform, expires_at, connection_status')
        .eq('user_id', userId)
        .eq('platform', 'INSTAGRAM');

      results.tests.databaseTokenQueries = {
        success: !error,
        message: error ? error.message : `Found ${accounts?.length || 0} Instagram accounts`,
        accountsFound: accounts?.length || 0
      };
    } catch (error) {
      results.tests.databaseTokenQueries = {
        success: false,
        message: error instanceof Error ? error.message : 'Database query failed'
      };
    }

    // Determine overall success
    const testCount = Object.keys(results.tests).length;
    const successCount = Object.values(results.tests).filter((test: any) => test.success).length;
    
    results.success = successCount > 0;
    results.message = `Token management tests: ${successCount}/${testCount} passed`;

    return results;
  } catch (error) {
    results.success = false;
    results.message = error instanceof Error ? error.message : 'Token management test failed';
    return results;
  }
}

/**
 * Test health monitoring functionality
 */
async function testHealthMonitoring() {
  const results = {
    success: false,
    message: '',
    tests: {} as any
  };

  try {
    // Test health endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/health`);
    const healthData = await response.json();

    results.tests.healthEndpoint = {
      success: response.ok,
      message: response.ok ? 'Health endpoint working' : 'Health endpoint failed',
      status: healthData.status,
      components: Object.keys(healthData.components || {})
    };

    // Test enhanced health monitoring
    const detailedResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/health?detailed=true`);
    const detailedHealthData = await detailedResponse.json();

    results.tests.enhancedHealthMonitoring = {
      success: detailedResponse.ok && (detailedHealthData.components?.tokens || detailedHealthData.components?.queue),
      message: detailedResponse.ok ? 'Enhanced health monitoring available' : 'Enhanced health monitoring not available',
      hasTokenMonitoring: !!detailedHealthData.components?.tokens,
      hasQueueMonitoring: !!detailedHealthData.components?.queue
    };

    const testCount = Object.keys(results.tests).length;
    const successCount = Object.values(results.tests).filter((test: any) => test.success).length;

    results.success = successCount > 0;
    results.message = `Health monitoring tests: ${successCount}/${testCount} passed`;

    return results;
  } catch (error) {
    results.success = false;
    results.message = error instanceof Error ? error.message : 'Health monitoring test failed';
    return results;
  }
}

/**
 * Test metrics collection functionality
 */
async function testMetricsCollection(userId: string) {
  const results = {
    success: false,
    message: '',
    tests: {} as any
  };

  try {
    // Test metrics endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/metrics?userId=${userId}`);
    const metricsData = await response.json();

    results.tests.metricsEndpoint = {
      success: response.ok,
      message: response.ok ? 'Metrics endpoint working' : 'Metrics endpoint failed',
      hasSystemMetrics: !!metricsData.system,
      hasPostMetrics: !!metricsData.posts,
      hasPublishingMetrics: !!metricsData.publishing
    };

    // Test detailed metrics
    const detailedResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/metrics?detailed=true&userId=${userId}`);
    const detailedMetricsData = await detailedResponse.json();

    results.tests.detailedMetrics = {
      success: detailedResponse.ok,
      message: detailedResponse.ok ? 'Detailed metrics available' : 'Detailed metrics not available',
      hasPlatformMetrics: !!detailedMetricsData.detailed?.platforms,
      hasErrorMetrics: !!detailedMetricsData.detailed?.errors,
      hasQueueMetrics: !!detailedMetricsData.detailed?.queue
    };

    const testCount = Object.keys(results.tests).length;
    const successCount = Object.values(results.tests).filter((test: any) => test.success).length;

    results.success = successCount > 0;
    results.message = `Metrics collection tests: ${successCount}/${testCount} passed`;

    return results;
  } catch (error) {
    results.success = false;
    results.message = error instanceof Error ? error.message : 'Metrics collection test failed';
    return results;
  }
}

/**
 * Test logging system functionality
 */
async function testLoggingSystem(userId: string) {
  const results = {
    success: false,
    message: '',
    tests: {} as any
  };

  try {
    // Test logger import and basic functionality
    await logger.info('Phase 1 test log entry', { userId, testType: 'logging' });
    await logger.error('Phase 1 test error log', new Error('Test error'), { userId });

    results.tests.loggerFunctionality = {
      success: true,
      message: 'Logger functionality working'
    };

    // Test Arabic error messages
    const { arabicErrors } = await import('@/lib/logging/logger');

    results.tests.arabicErrors = {
      success: Object.keys(arabicErrors).length > 0,
      message: `Arabic error messages available: ${Object.keys(arabicErrors).length} messages`
    };

    // Test enhanced error handling
    results.tests.enhancedErrorHandling = await testEnhancedErrorHandling();

    const testCount = Object.keys(results.tests).length;
    const successCount = Object.values(results.tests).filter((test: any) => test.success).length;

    results.success = successCount > 0;
    results.message = `Logging system tests: ${successCount}/${testCount} passed`;

    return results;
  } catch (error) {
    results.success = false;
    results.message = error instanceof Error ? error.message : 'Logging system test failed';
    return results;
  }
}

/**
 * Test enhanced error handling
 */
async function testEnhancedErrorHandling() {
  try {
    // Test error codes import
    const { ERROR_CODES, createErrorResponse, EnhancedError } = await import('@/lib/errors/error-codes');

    const results = {
      success: false,
      message: '',
      tests: {} as any
    };

    results.tests.errorCodesAvailable = {
      success: Object.keys(ERROR_CODES).length > 0,
      message: `Error codes available: ${Object.keys(ERROR_CODES).length} codes`
    };

    // Test enhanced error creation
    const testError = new EnhancedError('AUTH_REQUIRED');

    results.tests.enhancedErrorCreation = {
      success: testError.arabicMessage.includes('المصادقة'),
      message: 'Enhanced error with Arabic messages working'
    };

    // Test error response creation
    const errorResponse = createErrorResponse('INVALID_REQUEST', 'Test error');

    results.tests.errorResponseCreation = {
      success: errorResponse.arabicMessage && errorResponse.message,
      message: 'Error response creation working'
    };

    const testCount = Object.keys(results.tests).length;
    const successCount = Object.values(results.tests).filter((test: any) => test.success).length;

    results.success = successCount > 0;
    results.message = `Enhanced error handling tests: ${successCount}/${testCount} passed`;

    return results;
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Enhanced error handling test failed'
    };
  }
}
