'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Users, 
  Plus, 
  RefreshCw, 
  Search, 
  Grid3X3, 
  List, 
  MoreVertical,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Settings,
  Trash2,
  Eye
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { ConnectAccountButton } from './connect-account-button';
import { EnhancedAccountCard } from './enhanced-account-card';

// Types
interface EnhancedSocialAccount {
  id: string;
  userId: string;
  platform: string;
  accountId: string;
  accountName: string;
  profileImageUrl?: string;
  accessToken: string;
  refreshToken?: string;
  permissions: string[];
  connectionStatus: 'connected' | 'expired' | 'error' | 'pending';
  lastValidatedAt: string;
  metadata: Record<string, any>;
  rateLimits: {
    api: { current: number; limit: number; resetTime: string; percentage: number };
    posts: { current: number; limit: number; resetTime: string; percentage: number };
  };
  metrics: {
    followerCount: number;
    engagementRate: number;
    postsCount: number;
  };
  groups: string[];
  createdAt: string;
  updatedAt: string;
}

interface SocialAccountsFilter {
  platform?: string;
  status?: string;
  search?: string;
}

interface BulkOperation {
  type: 'refresh' | 'disconnect' | 'validate';
  accountIds: string[];
}

interface EnhancedAccountsDashboardProps {
  language: 'ar' | 'en';
}

const PLATFORM_COLORS = {
  FACEBOOK: 'bg-blue-500',
  INSTAGRAM: 'bg-pink-500',
  TWITTER: 'bg-sky-500',
  LINKEDIN: 'bg-blue-700',
  TIKTOK: 'bg-black',
  SNAPCHAT: 'bg-yellow-400'
};

// Enhanced Accounts Dashboard Component - Fixed UI data loading issues
export function EnhancedAccountsDashboard({ language }: EnhancedAccountsDashboardProps) {
  const [accounts, setAccounts] = useState<EnhancedSocialAccount[]>([]);
  const [selectedAccounts, setSelectedAccounts] = useState<string[]>([]);
  const [filter, setFilter] = useState<SocialAccountsFilter>({});
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoading, setIsLoading] = useState(true);
  const [bulkOperation, setBulkOperation] = useState<BulkOperation | null>(null);

  // Load accounts data
  useEffect(() => {
    loadAccounts();
  }, []);

  const loadAccounts = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/social/accounts/enhanced');
      const data = await response.json();
      
      if (response.ok) {
        setAccounts(data.accounts || []);
      } else {
        toast.error(language === 'ar' ? 'فشل في تحميل الحسابات' : 'Failed to load accounts');
      }
    } catch (error) {
      console.error('Error loading accounts:', error);
      toast.error(language === 'ar' ? 'خطأ في تحميل الحسابات' : 'Error loading accounts');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter accounts based on current filters
  const filteredAccounts = useMemo(() => {
    return accounts.filter(account => {
      if (filter.platform && account.platform !== filter.platform) return false;
      if (filter.status && account.connectionStatus !== filter.status) return false;
      if (filter.search) {
        const searchLower = filter.search.toLowerCase();
        return (
          account.accountName.toLowerCase().includes(searchLower) ||
          account.platform.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  }, [accounts, filter]);

  // Handle account selection
  const handleAccountSelect = (accountId: string, selected: boolean) => {
    setSelectedAccounts(prev => 
      selected 
        ? [...prev, accountId]
        : prev.filter(id => id !== accountId)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedAccounts(selected ? filteredAccounts.map(a => a.id) : []);
  };

  // Handle bulk operations
  const handleBulkOperation = async (operation: BulkOperation) => {
    if (selectedAccounts.length === 0) {
      toast.error(language === 'ar' ? 'لم يتم تحديد أي حسابات' : 'No accounts selected');
      return;
    }

    setBulkOperation(operation);
    
    try {
      const response = await fetch('/api/social/accounts/bulk-operation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...operation,
          accountIds: selectedAccounts
        })
      });

      if (response.ok) {
        toast.success(
          language === 'ar' 
            ? `تم تنفيذ العملية على ${selectedAccounts.length} حساب`
            : `Operation completed on ${selectedAccounts.length} accounts`
        );
        loadAccounts();
        setSelectedAccounts([]);
      } else {
        throw new Error('Bulk operation failed');
      }
    } catch (error) {
      toast.error(
        language === 'ar' 
          ? 'فشل في تنفيذ العملية'
          : 'Failed to execute operation'
      );
    } finally {
      setBulkOperation(null);
    }
  };

  // Account actions
  const handleRefresh = () => {
    loadAccounts();
  };

  const handleRefreshAccount = async (accountId: string) => {
    try {
      const response = await fetch(`/api/social/accounts/${accountId}/refresh`, {
        method: 'POST'
      });

      if (response.ok) {
        toast.success(language === 'ar' ? 'تم تحديث الرمز المميز' : 'Token refreshed');
        loadAccounts();
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      toast.error(language === 'ar' ? 'فشل في تحديث الرمز المميز' : 'Failed to refresh token');
    }
  };

  const handleDisconnectAccount = async (accountId: string) => {
    try {
      const response = await fetch(`/api/social/accounts/${accountId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success(language === 'ar' ? 'تم قطع الاتصال' : 'Account disconnected');
        loadAccounts();
      } else {
        throw new Error('Disconnect failed');
      }
    } catch (error) {
      toast.error(language === 'ar' ? 'فشل في قطع الاتصال' : 'Failed to disconnect');
    }
  };

  const handleAccountSettings = (accountId: string) => {
    // Navigate to account settings
    window.location.href = `/social/accounts/${accountId}/settings`;
  };

  // Statistics
  const stats = useMemo(() => {
    const total = accounts.length;
    const connected = accounts.filter(a => a.connectionStatus === 'connected').length;
    const expired = accounts.filter(a => a.connectionStatus === 'expired').length;
    const errors = accounts.filter(a => a.connectionStatus === 'error').length;
    
    return { total, connected, expired, errors };
  }, [accounts]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">
            {language === 'ar' ? 'إدارة الحسابات الاجتماعية' : 'Social Accounts Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إدارة وتنظيم حساباتك على منصات التواصل الاجتماعي' 
              : 'Manage and organize your social media accounts'
            }
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
          
          <ConnectAccountButton
            onAccountConnected={handleRefresh}
          />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'إجمالي الحسابات' : 'Total Accounts'}
                </p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'متصل' : 'Connected'}
                </p>
                <p className="text-2xl font-bold text-green-600">{stats.connected}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'منتهي الصلاحية' : 'Expired'}
                </p>
                <p className="text-2xl font-bold text-yellow-600">{stats.expired}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'أخطاء' : 'Errors'}
                </p>
                <p className="text-2xl font-bold text-red-600">{stats.errors}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="flex gap-2 items-center">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder={language === 'ar' ? 'البحث في الحسابات...' : 'Search accounts...'}
              value={filter.search || ''}
              onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10 w-64"
            />
          </div>
          
          <Select value={filter.platform || 'all'} onValueChange={(value) => 
            setFilter(prev => ({ ...prev, platform: value === 'all' ? undefined : value }))
          }>
            <SelectTrigger className="w-40">
              <SelectValue placeholder={language === 'ar' ? 'جميع المنصات' : 'All Platforms'} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{language === 'ar' ? 'جميع المنصات' : 'All Platforms'}</SelectItem>
              <SelectItem value="FACEBOOK">Facebook</SelectItem>
              <SelectItem value="INSTAGRAM">Instagram</SelectItem>
              <SelectItem value="TWITTER">Twitter</SelectItem>
              <SelectItem value="LINKEDIN">LinkedIn</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={filter.status || 'all'} onValueChange={(value) => 
            setFilter(prev => ({ ...prev, status: value === 'all' ? undefined : value }))
          }>
            <SelectTrigger className="w-40">
              <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All Statuses'} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{language === 'ar' ? 'جميع الحالات' : 'All Statuses'}</SelectItem>
              <SelectItem value="connected">{language === 'ar' ? 'متصل' : 'Connected'}</SelectItem>
              <SelectItem value="expired">{language === 'ar' ? 'منتهي الصلاحية' : 'Expired'}</SelectItem>
              <SelectItem value="error">{language === 'ar' ? 'خطأ' : 'Error'}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex gap-2 items-center">
          {selectedAccounts.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  {language === 'ar' ? `عمليات جماعية (${selectedAccounts.length})` : `Bulk Actions (${selectedAccounts.length})`}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleBulkOperation({ type: 'refresh', accountIds: selectedAccounts })}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'تحديث الرموز المميزة' : 'Refresh Tokens'}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkOperation({ type: 'validate', accountIds: selectedAccounts })}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'التحقق من الاتصال' : 'Validate Connection'}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => handleBulkOperation({ type: 'disconnect', accountIds: selectedAccounts })}
                  className="text-red-600"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'قطع الاتصال' : 'Disconnect'}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Accounts List */}
      <div>
        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : filteredAccounts.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {language === 'ar' ? 'لا توجد حسابات متصلة' : 'No Connected Accounts'}
              </h3>
              <p className="text-muted-foreground mb-4">
                {language === 'ar' 
                  ? 'ابدأ بربط حساباتك على منصات التواصل الاجتماعي'
                  : 'Start by connecting your social media accounts'
                }
              </p>
              <ConnectAccountButton
                onAccountConnected={handleRefresh}
              />
            </CardContent>
          </Card>
        ) : (
          <div className={cn(
            viewMode === 'grid' 
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
              : "space-y-4"
          )}>
            {filteredAccounts.map((account) => (
              <EnhancedAccountCard
                key={account.id}
                account={account}
                isSelected={selectedAccounts.includes(account.id)}
                onSelect={handleAccountSelect}
                onRefresh={handleRefreshAccount}
                onDisconnect={handleDisconnectAccount}
                onSettings={handleAccountSettings}
                language={language}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}