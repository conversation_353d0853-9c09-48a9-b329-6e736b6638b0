import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * OAuth Token Validation and Refresh Diagnostic Endpoint
 * Tests Facebook OAuth tokens and performs automatic refresh if needed
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Get all connected social accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('platform', 'facebook')
      .eq('is_active', true);

    if (accountsError) {
      return NextResponse.json({
        success: false,
        error: 'فشل في جلب الحسابات المتصلة',
        details: accountsError.message,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    if (!accounts || accounts.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'لا توجد حسابات فيسبوك متصلة',
        accounts: [],
        timestamp: new Date().toISOString()
      });
    }

    const results = [];
    let refreshedCount = 0;
    let errorCount = 0;

    for (const account of accounts) {
      const result = {
        account_id: account.id,
        platform_account_id: account.platform_account_id,
        account_name: account.account_name,
        token_status: 'unknown',
        expires_at: account.expires_at,
        needs_refresh: false,
        refresh_attempted: false,
        refresh_success: false,
        error: null
      };

      try {
        // Check if token is expired or expires soon (within 1 hour)
        const expiresAt = new Date(account.expires_at);
        const now = new Date();
        const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);

        if (expiresAt <= oneHourFromNow) {
          result.needs_refresh = true;
          result.token_status = 'expired_or_expiring';

          // Attempt token refresh
          result.refresh_attempted = true;
          
          const refreshResponse = await fetch(`https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token&client_id=${process.env.FACEBOOK_APP_ID}&client_secret=${process.env.FACEBOOK_APP_SECRET}&fb_exchange_token=${account.access_token}`);
          
          if (refreshResponse.ok) {
            const refreshData = await refreshResponse.json();
            
            // Update token in database
            const newExpiresAt = new Date(now.getTime() + (refreshData.expires_in * 1000));
            
            const { error: updateError } = await supabase
              .from('social_accounts')
              .update({
                access_token: refreshData.access_token,
                expires_at: newExpiresAt.toISOString(),
                updated_at: now.toISOString()
              })
              .eq('id', account.id);

            if (!updateError) {
              result.refresh_success = true;
              result.token_status = 'refreshed';
              result.expires_at = newExpiresAt.toISOString();
              refreshedCount++;
            } else {
              result.error = `فشل في تحديث التوكن: ${updateError.message}`;
              errorCount++;
            }
          } else {
            const errorData = await refreshResponse.json();
            result.error = `فشل في تجديد التوكن: ${errorData.error?.message || 'خطأ غير معروف'}`;
            errorCount++;
          }
        } else {
          result.token_status = 'valid';
          
          // Test token validity with a simple API call
          const testResponse = await fetch(`https://graph.facebook.com/me?access_token=${account.access_token}`);
          
          if (!testResponse.ok) {
            result.token_status = 'invalid';
            result.error = 'التوكن غير صالح';
            errorCount++;
          }
        }
      } catch (error) {
        result.error = `خطأ في معالجة التوكن: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`;
        errorCount++;
      }

      results.push(result);
    }

    return NextResponse.json({
      success: true,
      message: 'تم فحص توكنات OAuth بنجاح',
      summary: {
        total_accounts: accounts.length,
        tokens_refreshed: refreshedCount,
        errors: errorCount,
        healthy_tokens: accounts.length - errorCount
      },
      accounts: results,
      recommendations: errorCount > 0 ? [
        'يوجد مشاكل في بعض التوكنات - يرجى إعادة ربط الحسابات المتأثرة',
        'تحقق من صحة FACEBOOK_APP_SECRET في متغيرات البيئة',
        'تأكد من أن التطبيق لديه الصلاحيات المطلوبة'
      ] : [
        'جميع التوكنات صحيحة وتعمل بشكل طبيعي'
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ OAuth tokens test error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم الداخلي أثناء فحص التوكنات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}