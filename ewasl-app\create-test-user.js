#!/usr/bin/env node

/**
 * Create Test User Script
 * Creates a test user for local testing
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');

async function createTestUser() {
  console.log('🔧 Creating test user for eWasl application...\n');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('   Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
    return;
  }
  
  try {
    // Create Supabase client with service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test user credentials
    const testUser = {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Test User',
      role: 'USER'
    };
    
    console.log('👤 Test User Credentials:');
    console.log(`   Email: ${testUser.email}`);
    console.log(`   Password: ${testUser.password}`);
    console.log(`   Name: ${testUser.name}`);
    console.log('');
    
    // Check if user already exists
    console.log('1. Checking if test user already exists...');
    const { data: existingUser } = await supabase
      .from('users')
      .select('*')
      .eq('email', testUser.email)
      .single();
    
    if (existingUser) {
      console.log('   ✅ Test user already exists');
      console.log(`   User ID: ${existingUser.id}`);
      console.log(`   Created: ${existingUser.created_at}`);
      console.log('\n🎉 You can use the existing test user credentials above!');
      return;
    }
    
    // Hash the password
    console.log('2. Hashing password...');
    const hashedPassword = await bcrypt.hash(testUser.password, 12);
    console.log('   ✅ Password hashed successfully');
    
    // Create the user in the database
    console.log('\n3. Creating test user in database...');
    const { data: newUser, error: createError } = await supabase
      .from('users')
      .insert({
        email: testUser.email,
        name: testUser.name,
        password: hashedPassword,
        role: testUser.role,
        email_verified: new Date().toISOString() // Mark as verified for testing
      })
      .select()
      .single();
    
    if (createError) {
      console.error('   ❌ Failed to create user:', createError.message);
      return;
    }
    
    console.log('   ✅ Test user created successfully!');
    console.log(`   User ID: ${newUser.id}`);
    console.log(`   Email: ${newUser.email}`);
    console.log(`   Name: ${newUser.name}`);
    
    // Create a sample social account for testing
    console.log('\n4. Creating sample social media account...');
    const { data: socialAccount, error: socialError } = await supabase
      .from('social_accounts')
      .insert({
        user_id: newUser.id,
        platform: 'TWITTER',
        account_id: 'test_twitter_account',
        account_name: '@testuser_ewasl',
        access_token: 'sample_access_token_for_testing'
      })
      .select()
      .single();
    
    if (socialError) {
      console.log('   ⚠️ Could not create sample social account:', socialError.message);
    } else {
      console.log('   ✅ Sample Twitter account created');
      console.log(`   Platform: ${socialAccount.platform}`);
      console.log(`   Account: ${socialAccount.account_name}`);
    }
    
    // Create a sample post for testing
    console.log('\n5. Creating sample post...');
    const { data: samplePost, error: postError } = await supabase
      .from('posts')
      .insert({
        user_id: newUser.id,
        content: 'Welcome to eWasl! 🚀 This is a sample post to test the social media management platform. #eWasl #SocialMedia #Testing',
        status: 'DRAFT'
      })
      .select()
      .single();
    
    if (postError) {
      console.log('   ⚠️ Could not create sample post:', postError.message);
    } else {
      console.log('   ✅ Sample post created');
      console.log(`   Content: "${samplePost.content.substring(0, 50)}..."`);
      console.log(`   Status: ${samplePost.status}`);
    }
    
    // Log activity
    console.log('\n6. Logging user registration activity...');
    const { error: activityError } = await supabase
      .from('activities')
      .insert({
        user_id: newUser.id,
        action: 'REGISTER',
        details: 'Test user account created for local testing'
      });
    
    if (activityError) {
      console.log('   ⚠️ Could not log activity:', activityError.message);
    } else {
      console.log('   ✅ Registration activity logged');
    }
    
    console.log('\n🎉 Test user setup complete!');
    console.log('\n📋 Login Instructions:');
    console.log('   1. Open your browser to: http://localhost:3000');
    console.log('   2. Click "Sign In" or go to: http://localhost:3000/auth/signin');
    console.log(`   3. Use email: ${testUser.email}`);
    console.log(`   4. Use password: ${testUser.password}`);
    console.log('\n✨ You should now be able to access the dashboard and test all features!');
    
  } catch (error) {
    console.error('❌ Failed to create test user:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Make sure the development server is running');
    console.error('   2. Check that all environment variables are set');
    console.error('   3. Verify Supabase connection is working');
  }
}

// Run the script
createTestUser();
