"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DatePickerWithRange } from '@/components/ui/date-range-picker';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  AreaChart,
  Area,
  Pie<PERSON>hart,
  Pie,
  Cell,
  Composed<PERSON>hart,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON>atter<PERSON>hart,
  <PERSON>atter
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart as PieChartIcon,
  LineChart as LineChartIcon,
  Download,
  Filter,
  Calendar,
  Users,
  Eye,
  Heart,
  MessageCircle,
  Share,
  Target,
  Zap,
  Clock,
  Globe,
  RefreshCw,
  Settings,
  Info,
  CheckCircle,
  AlertCircle,
  Lightbulb,
  Star,
  Award,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRTL } from '@/lib/i18n/arabic-rtl-optimizer';
import { toast } from 'sonner';
import { DateRange } from 'react-day-picker';
import { addDays, format, subDays } from 'date-fns';

interface AdvancedAnalyticsData {
  summary: {
    totalPosts: number;
    totalEngagement: number;
    totalReach: number;
    totalImpressions: number;
    engagementRate: number;
    reachRate: number;
    clickThroughRate: number;
    conversionRate: number;
  };
  trends: {
    postsGrowth: number;
    engagementGrowth: number;
    reachGrowth: number;
    impressionsGrowth: number;
  };
  platformComparison: Array<{
    platform: string;
    posts: number;
    engagement: number;
    reach: number;
    impressions: number;
    engagementRate: number;
    color: string;
  }>;
  timeSeriesData: Array<{
    date: string;
    posts: number;
    engagement: number;
    reach: number;
    impressions: number;
    clicks: number;
    conversions: number;
  }>;
  contentAnalysis: {
    topPerformingPosts: Array<{
      id: string;
      content: string;
      platform: string;
      engagement: number;
      reach: number;
      score: number;
    }>;
    contentTypes: Array<{
      type: string;
      count: number;
      avgEngagement: number;
      performance: number;
    }>;
    hashtagAnalysis: Array<{
      hashtag: string;
      usage: number;
      avgEngagement: number;
      trend: 'up' | 'down' | 'stable';
    }>;
  };
  audienceInsights: {
    demographics: Array<{
      segment: string;
      percentage: number;
      engagement: number;
    }>;
    bestTimes: Array<{
      hour: number;
      day: string;
      engagement: number;
      reach: number;
    }>;
    locations: Array<{
      country: string;
      percentage: number;
      engagement: number;
    }>;
  };
  competitorAnalysis: Array<{
    competitor: string;
    followers: number;
    engagement: number;
    postFrequency: number;
    performance: number;
  }>;
  insights: Array<{
    id: string;
    type: 'success' | 'warning' | 'info' | 'recommendation';
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    actionable: boolean;
  }>;
  predictions: {
    nextWeekEngagement: number;
    nextWeekReach: number;
    optimalPostingTimes: Array<{
      day: string;
      hour: number;
      score: number;
    }>;
    contentRecommendations: Array<{
      type: string;
      reason: string;
      expectedImprovement: number;
    }>;
  };
}

interface AdvancedAnalyticsDashboardProps {
  language?: 'ar' | 'en';
  className?: string;
}

export function AdvancedAnalyticsDashboard({ 
  language = 'ar', 
  className 
}: AdvancedAnalyticsDashboardProps) {
  const [data, setData] = useState<AdvancedAnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState('overview');
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 30),
    to: new Date(),
  });
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(['all']);
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(['engagement', 'reach', 'impressions']);
  const [chartType, setChartType] = useState<'line' | 'bar' | 'area'>('line');
  const [comparisonMode, setComparisonMode] = useState<'period' | 'platform' | 'content'>('period');

  const rtl = useRTL(language);

  // Translations
  const t = {
    ar: {
      title: 'لوحة التحليلات المتقدمة',
      subtitle: 'تحليلات شاملة مع تقارير مخصصة ورؤى عميقة',
      tabs: {
        overview: 'نظرة عامة',
        comparison: 'المقارنات',
        content: 'تحليل المحتوى',
        audience: 'الجمهور',
        insights: 'الرؤى',
        reports: 'التقارير'
      },
      metrics: {
        totalPosts: 'إجمالي المنشورات',
        totalEngagement: 'إجمالي التفاعل',
        totalReach: 'إجمالي الوصول',
        totalImpressions: 'إجمالي المشاهدات',
        engagementRate: 'معدل التفاعل',
        reachRate: 'معدل الوصول',
        clickThroughRate: 'معدل النقر',
        conversionRate: 'معدل التحويل'
      },
      actions: {
        refresh: 'تحديث',
        export: 'تصدير',
        filter: 'تصفية',
        compare: 'مقارنة',
        customize: 'تخصيص'
      },
      insights: {
        recommendations: 'التوصيات',
        opportunities: 'الفرص',
        warnings: 'التحذيرات',
        achievements: 'الإنجازات'
      }
    },
    en: {
      title: 'Advanced Analytics Dashboard',
      subtitle: 'Comprehensive analytics with custom reports and deep insights',
      tabs: {
        overview: 'Overview',
        comparison: 'Comparisons',
        content: 'Content Analysis',
        audience: 'Audience',
        insights: 'Insights',
        reports: 'Reports'
      },
      metrics: {
        totalPosts: 'Total Posts',
        totalEngagement: 'Total Engagement',
        totalReach: 'Total Reach',
        totalImpressions: 'Total Impressions',
        engagementRate: 'Engagement Rate',
        reachRate: 'Reach Rate',
        clickThroughRate: 'Click Through Rate',
        conversionRate: 'Conversion Rate'
      },
      actions: {
        refresh: 'Refresh',
        export: 'Export',
        filter: 'Filter',
        compare: 'Compare',
        customize: 'Customize'
      },
      insights: {
        recommendations: 'Recommendations',
        opportunities: 'Opportunities',
        warnings: 'Warnings',
        achievements: 'Achievements'
      }
    }
  };

  const text = t[language];

  // Fetch analytics data
  const fetchAnalyticsData = useCallback(async () => {
    setIsRefreshing(true);
    try {
      const response = await fetch('/api/analytics/advanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dateRange,
          platforms: selectedPlatforms,
          metrics: selectedMetrics,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setData(result.data);
      } else {
        // Fallback to demo data
        setData(generateDemoData());
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
      setData(generateDemoData());
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [dateRange, selectedPlatforms, selectedMetrics]);

  // Generate demo data
  const generateDemoData = (): AdvancedAnalyticsData => {
    const days = 30;
    const timeSeriesData = Array.from({ length: days }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (days - 1 - i));
      return {
        date: date.toISOString().split('T')[0],
        posts: Math.floor(Math.random() * 10) + 1,
        engagement: Math.floor(Math.random() * 1000) + 500,
        reach: Math.floor(Math.random() * 5000) + 2000,
        impressions: Math.floor(Math.random() * 8000) + 4000,
        clicks: Math.floor(Math.random() * 200) + 50,
        conversions: Math.floor(Math.random() * 20) + 5,
      };
    });

    return {
      summary: {
        totalPosts: 156,
        totalEngagement: 28947,
        totalReach: 125634,
        totalImpressions: 234567,
        engagementRate: 8.2,
        reachRate: 53.6,
        clickThroughRate: 2.4,
        conversionRate: 1.8,
      },
      trends: {
        postsGrowth: 12.5,
        engagementGrowth: 18.3,
        reachGrowth: 15.7,
        impressionsGrowth: 22.1,
      },
      platformComparison: [
        { platform: 'Instagram', posts: 45, engagement: 8234, reach: 35678, impressions: 67890, engagementRate: 9.2, color: '#E4405F' },
        { platform: 'Facebook', posts: 38, engagement: 6892, reach: 28456, impressions: 54321, engagementRate: 7.8, color: '#1877F2' },
        { platform: 'Twitter', posts: 42, engagement: 5456, reach: 22134, impressions: 43210, engagementRate: 6.9, color: '#1DA1F2' },
        { platform: 'LinkedIn', posts: 31, engagement: 3265, reach: 15678, impressions: 29876, engagementRate: 5.4, color: '#0A66C2' },
      ],
      timeSeriesData,
      contentAnalysis: {
        topPerformingPosts: [
          {
            id: '1',
            content: language === 'ar' ? 'أفضل استراتيجيات التسويق الرقمي لعام 2024' : 'Best Digital Marketing Strategies for 2024',
            platform: 'Instagram',
            engagement: 1234,
            reach: 5678,
            score: 95,
          },
          {
            id: '2',
            content: language === 'ar' ? 'نصائح لزيادة التفاعل على وسائل التواصل' : 'Tips to Increase Social Media Engagement',
            platform: 'Facebook',
            engagement: 987,
            reach: 4321,
            score: 89,
          },
        ],
        contentTypes: [
          { type: language === 'ar' ? 'صور' : 'Images', count: 45, avgEngagement: 856, performance: 92 },
          { type: language === 'ar' ? 'فيديوهات' : 'Videos', count: 23, avgEngagement: 1234, performance: 98 },
          { type: language === 'ar' ? 'نصوص' : 'Text', count: 67, avgEngagement: 432, performance: 76 },
          { type: language === 'ar' ? 'روابط' : 'Links', count: 21, avgEngagement: 567, performance: 84 },
        ],
        hashtagAnalysis: [
          { hashtag: '#تسويق_رقمي', usage: 23, avgEngagement: 890, trend: 'up' as const },
          { hashtag: '#وسائل_التواصل', usage: 18, avgEngagement: 756, trend: 'stable' as const },
          { hashtag: '#محتوى_إبداعي', usage: 15, avgEngagement: 1023, trend: 'up' as const },
        ],
      },
      audienceInsights: {
        demographics: [
          { segment: language === 'ar' ? '18-24 سنة' : '18-24 years', percentage: 35, engagement: 8.9 },
          { segment: language === 'ar' ? '25-34 سنة' : '25-34 years', percentage: 42, engagement: 9.2 },
          { segment: language === 'ar' ? '35-44 سنة' : '35-44 years', percentage: 18, engagement: 7.8 },
          { segment: language === 'ar' ? '45+ سنة' : '45+ years', percentage: 5, engagement: 6.4 },
        ],
        bestTimes: [
          { hour: 9, day: language === 'ar' ? 'الأحد' : 'Sunday', engagement: 9.2, reach: 5678 },
          { hour: 14, day: language === 'ar' ? 'الثلاثاء' : 'Tuesday', engagement: 8.8, reach: 5234 },
          { hour: 19, day: language === 'ar' ? 'الخميس' : 'Thursday', engagement: 9.5, reach: 6123 },
        ],
        locations: [
          { country: language === 'ar' ? 'السعودية' : 'Saudi Arabia', percentage: 45, engagement: 9.1 },
          { country: language === 'ar' ? 'الإمارات' : 'UAE', percentage: 25, engagement: 8.7 },
          { country: language === 'ar' ? 'مصر' : 'Egypt', percentage: 20, engagement: 8.3 },
          { country: language === 'ar' ? 'أخرى' : 'Others', percentage: 10, engagement: 7.9 },
        ],
      },
      competitorAnalysis: [
        { competitor: language === 'ar' ? 'منافس أ' : 'Competitor A', followers: 125000, engagement: 7.8, postFrequency: 12, performance: 85 },
        { competitor: language === 'ar' ? 'منافس ب' : 'Competitor B', followers: 98000, engagement: 6.9, postFrequency: 8, performance: 78 },
        { competitor: language === 'ar' ? 'منافس ج' : 'Competitor C', followers: 156000, engagement: 8.9, postFrequency: 15, performance: 92 },
      ],
      insights: [
        {
          id: '1',
          type: 'success',
          title: language === 'ar' ? 'أداء ممتاز' : 'Excellent Performance',
          description: language === 'ar' ? 'معدل التفاعل أعلى من المتوسط بنسبة 23%' : 'Engagement rate is 23% above average',
          impact: 'high',
          actionable: false,
        },
        {
          id: '2',
          type: 'recommendation',
          title: language === 'ar' ? 'توصية للتحسين' : 'Improvement Recommendation',
          description: language === 'ar' ? 'انشر المزيد من الفيديوهات لزيادة التفاعل' : 'Post more videos to increase engagement',
          impact: 'medium',
          actionable: true,
        },
      ],
      predictions: {
        nextWeekEngagement: 15.2,
        nextWeekReach: 67890,
        optimalPostingTimes: [
          { day: language === 'ar' ? 'الأحد' : 'Sunday', hour: 9, score: 95 },
          { day: language === 'ar' ? 'الثلاثاء' : 'Tuesday', hour: 14, score: 89 },
          { day: language === 'ar' ? 'الخميس' : 'Thursday', hour: 19, score: 92 },
        ],
        contentRecommendations: [
          {
            type: language === 'ar' ? 'فيديوهات قصيرة' : 'Short Videos',
            reason: language === 'ar' ? 'تحقق تفاعل أعلى بنسبة 45%' : 'Achieve 45% higher engagement',
            expectedImprovement: 45,
          },
        ],
      },
    };
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData]);

  // Format numbers
  const formatNumber = (num: number): string => {
    return rtl.formatCompactNumber(num);
  };

  // Format percentage
  const formatPercentage = (num: number): string => {
    return rtl.formatPercentage(num);
  };

  // Get trend display
  const getTrendDisplay = (value: number) => {
    const isPositive = value > 0;
    return {
      icon: isPositive ? TrendingUp : TrendingDown,
      color: isPositive ? 'text-green-600' : 'text-red-600',
      bgColor: isPositive ? 'bg-green-50' : 'bg-red-50',
    };
  };

  if (isLoading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!data) return null;

  return (
    <div className={cn(
      "space-y-6",
      language === 'ar' ? "rtl" : "ltr",
      className
    )} dir={rtl.direction}>
      {/* Header */}
      <div className={rtl.cn(
        "flex items-center justify-between",
        rtl.flex()
      )}>
        <div className={rtl.textAlign()}>
          <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
            {text.title}
          </h1>
          <p className="text-gray-600 mt-1" style={{ fontFamily: rtl.getFontFamily('primary') }}>
            {text.subtitle}
          </p>
        </div>

        <div className={rtl.cn(
          "flex items-center gap-4",
          rtl.flex()
        )}>
          <Button
            variant="outline"
            onClick={fetchAnalyticsData}
            disabled={isRefreshing}
            className={rtl.cn(
              "flex items-center gap-2",
              rtl.flex()
            )}
          >
            <RefreshCw className={cn("w-4 h-4", isRefreshing && "animate-spin")} />
            {text.actions.refresh}
          </Button>

          <Button
            variant="outline"
            className={rtl.cn(
              "flex items-center gap-2",
              rtl.flex()
            )}
          >
            <Download className="w-4 h-4" />
            {text.actions.export}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className={rtl.textAlign()}>
            {text.actions.filter}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Date Range */}
            <div>
              <Label className={rtl.textAlign()}>
                {language === 'ar' ? 'نطاق التاريخ' : 'Date Range'}
              </Label>
              <DatePickerWithRange
                date={dateRange}
                onDateChange={setDateRange}
                className="w-full"
              />
            </div>

            {/* Platform Filter */}
            <div>
              <Label className={rtl.textAlign()}>
                {language === 'ar' ? 'المنصات' : 'Platforms'}
              </Label>
              <Select value={selectedPlatforms[0]} onValueChange={(value) => setSelectedPlatforms([value])}>
                <SelectTrigger>
                  <SelectValue placeholder={language === 'ar' ? 'اختر المنصة' : 'Select Platform'} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{language === 'ar' ? 'جميع المنصات' : 'All Platforms'}</SelectItem>
                  <SelectItem value="instagram">Instagram</SelectItem>
                  <SelectItem value="facebook">Facebook</SelectItem>
                  <SelectItem value="twitter">Twitter</SelectItem>
                  <SelectItem value="linkedin">LinkedIn</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Chart Type */}
            <div>
              <Label className={rtl.textAlign()}>
                {language === 'ar' ? 'نوع الرسم البياني' : 'Chart Type'}
              </Label>
              <Select value={chartType} onValueChange={(value: 'line' | 'bar' | 'area') => setChartType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="line">{language === 'ar' ? 'خطي' : 'Line'}</SelectItem>
                  <SelectItem value="bar">{language === 'ar' ? 'أعمدة' : 'Bar'}</SelectItem>
                  <SelectItem value="area">{language === 'ar' ? 'منطقة' : 'Area'}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Comparison Mode */}
            <div>
              <Label className={rtl.textAlign()}>
                {language === 'ar' ? 'نوع المقارنة' : 'Comparison Mode'}
              </Label>
              <Select value={comparisonMode} onValueChange={(value: 'period' | 'platform' | 'content') => setComparisonMode(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="period">{language === 'ar' ? 'فترة زمنية' : 'Time Period'}</SelectItem>
                  <SelectItem value="platform">{language === 'ar' ? 'منصة' : 'Platform'}</SelectItem>
                  <SelectItem value="content">{language === 'ar' ? 'محتوى' : 'Content'}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[
          { key: 'totalPosts', label: text.metrics.totalPosts, value: data.summary.totalPosts, trend: data.trends.postsGrowth, icon: BarChart3, color: 'blue' },
          { key: 'totalEngagement', label: text.metrics.totalEngagement, value: data.summary.totalEngagement, trend: data.trends.engagementGrowth, icon: Heart, color: 'red' },
          { key: 'totalReach', label: text.metrics.totalReach, value: data.summary.totalReach, trend: data.trends.reachGrowth, icon: Users, color: 'green' },
          { key: 'totalImpressions', label: text.metrics.totalImpressions, value: data.summary.totalImpressions, trend: data.trends.impressionsGrowth, icon: Eye, color: 'purple' },
        ].map((metric) => {
          const Icon = metric.icon;
          const trend = getTrendDisplay(metric.trend);
          const TrendIcon = trend.icon;

          return (
            <Card key={metric.key}>
              <CardContent className="p-6">
                <div className={rtl.cn(
                  "flex items-center justify-between",
                  rtl.flex()
                )}>
                  <div className={rtl.textAlign()}>
                    <p className="text-2xl font-bold">{formatNumber(metric.value)}</p>
                    <p className="text-sm text-gray-600">{metric.label}</p>
                  </div>
                  <div className="space-y-2">
                    <div className={`w-12 h-12 bg-${metric.color}-100 rounded-lg flex items-center justify-center`}>
                      <Icon className={`w-6 h-6 text-${metric.color}-600`} />
                    </div>
                    <div className={rtl.cn(
                      "flex items-center gap-1",
                      rtl.flex()
                    )}>
                      <TrendIcon className={cn("w-3 h-3", trend.color)} />
                      <span className={cn("text-xs", trend.color)}>
                        {formatPercentage(Math.abs(metric.trend))}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Advanced Analytics Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">{text.tabs.overview}</TabsTrigger>
          <TabsTrigger value="comparison">{text.tabs.comparison}</TabsTrigger>
          <TabsTrigger value="content">{text.tabs.content}</TabsTrigger>
          <TabsTrigger value="audience">{text.tabs.audience}</TabsTrigger>
          <TabsTrigger value="insights">{text.tabs.insights}</TabsTrigger>
          <TabsTrigger value="reports">{text.tabs.reports}</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Time Series Chart */}
            <Card>
              <CardHeader>
                <CardTitle className={rtl.textAlign()}>
                  {language === 'ar' ? 'اتجاهات الأداء' : 'Performance Trends'}
                </CardTitle>
                <CardDescription className={rtl.textAlign()}>
                  {language === 'ar' ? 'تطور المقاييس عبر الزمن' : 'Metrics evolution over time'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  {chartType === 'line' && (
                    <LineChart data={data.timeSeriesData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="engagement" stroke="#8884d8" name={language === 'ar' ? 'التفاعل' : 'Engagement'} />
                      <Line type="monotone" dataKey="reach" stroke="#82ca9d" name={language === 'ar' ? 'الوصول' : 'Reach'} />
                      <Line type="monotone" dataKey="impressions" stroke="#ffc658" name={language === 'ar' ? 'المشاهدات' : 'Impressions'} />
                    </LineChart>
                  )}
                  {chartType === 'bar' && (
                    <BarChart data={data.timeSeriesData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="engagement" fill="#8884d8" name={language === 'ar' ? 'التفاعل' : 'Engagement'} />
                      <Bar dataKey="reach" fill="#82ca9d" name={language === 'ar' ? 'الوصول' : 'Reach'} />
                      <Bar dataKey="impressions" fill="#ffc658" name={language === 'ar' ? 'المشاهدات' : 'Impressions'} />
                    </BarChart>
                  )}
                  {chartType === 'area' && (
                    <AreaChart data={data.timeSeriesData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area type="monotone" dataKey="engagement" stackId="1" stroke="#8884d8" fill="#8884d8" name={language === 'ar' ? 'التفاعل' : 'Engagement'} />
                      <Area type="monotone" dataKey="reach" stackId="1" stroke="#82ca9d" fill="#82ca9d" name={language === 'ar' ? 'الوصول' : 'Reach'} />
                      <Area type="monotone" dataKey="impressions" stackId="1" stroke="#ffc658" fill="#ffc658" name={language === 'ar' ? 'المشاهدات' : 'Impressions'} />
                    </AreaChart>
                  )}
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Platform Performance */}
            <Card>
              <CardHeader>
                <CardTitle className={rtl.textAlign()}>
                  {language === 'ar' ? 'أداء المنصات' : 'Platform Performance'}
                </CardTitle>
                <CardDescription className={rtl.textAlign()}>
                  {language === 'ar' ? 'مقارنة الأداء بين المنصات' : 'Performance comparison across platforms'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={data.platformComparison}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="platform" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="engagementRate" fill="#8884d8" name={language === 'ar' ? 'معدل التفاعل %' : 'Engagement Rate %'} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Performance Rates */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[
              { label: text.metrics.engagementRate, value: data.summary.engagementRate, color: 'blue' },
              { label: text.metrics.reachRate, value: data.summary.reachRate, color: 'green' },
              { label: text.metrics.clickThroughRate, value: data.summary.clickThroughRate, color: 'orange' },
              { label: text.metrics.conversionRate, value: data.summary.conversionRate, color: 'purple' },
            ].map((rate, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className={rtl.textAlign()}>
                    <p className="text-lg font-semibold">{formatPercentage(rate.value)}</p>
                    <p className="text-sm text-gray-600">{rate.label}</p>
                    <Progress value={rate.value} className="mt-2" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Comparison Tab */}
        <TabsContent value="comparison" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Platform Comparison */}
            <Card>
              <CardHeader>
                <CardTitle className={rtl.textAlign()}>
                  {language === 'ar' ? 'مقارنة المنصات' : 'Platform Comparison'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.platformComparison.map((platform, index) => (
                    <div key={index} className={rtl.cn(
                      "flex items-center justify-between p-3 rounded-lg border",
                      rtl.flex()
                    )}>
                      <div className={rtl.cn(
                        "flex items-center gap-3",
                        rtl.flex()
                      )}>
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: platform.color }}
                        ></div>
                        <span className="font-medium">{platform.platform}</span>
                      </div>
                      <div className={rtl.cn(
                        "flex items-center gap-4",
                        rtl.flex()
                      )}>
                        <div className={rtl.textAlign()}>
                          <p className="text-sm text-gray-600">{language === 'ar' ? 'التفاعل' : 'Engagement'}</p>
                          <p className="font-semibold">{formatNumber(platform.engagement)}</p>
                        </div>
                        <div className={rtl.textAlign()}>
                          <p className="text-sm text-gray-600">{language === 'ar' ? 'المعدل' : 'Rate'}</p>
                          <p className="font-semibold">{formatPercentage(platform.engagementRate)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Competitor Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className={rtl.textAlign()}>
                  {language === 'ar' ? 'تحليل المنافسين' : 'Competitor Analysis'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.competitorAnalysis.map((competitor, index) => (
                    <div key={index} className={rtl.cn(
                      "flex items-center justify-between p-3 rounded-lg bg-gray-50",
                      rtl.flex()
                    )}>
                      <div className={rtl.textAlign()}>
                        <p className="font-medium">{competitor.competitor}</p>
                        <p className="text-sm text-gray-600">
                          {formatNumber(competitor.followers)} {language === 'ar' ? 'متابع' : 'followers'}
                        </p>
                      </div>
                      <div className={rtl.cn(
                        "flex items-center gap-2",
                        rtl.flex()
                      )}>
                        <Badge variant={competitor.performance > 85 ? 'default' : 'secondary'}>
                          {competitor.performance}%
                        </Badge>
                        <div className={rtl.textAlign()}>
                          <p className="text-xs text-gray-500">{language === 'ar' ? 'الأداء' : 'Performance'}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Content Analysis Tab */}
        <TabsContent value="content" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Performing Posts */}
            <Card>
              <CardHeader>
                <CardTitle className={rtl.textAlign()}>
                  {language === 'ar' ? 'أفضل المنشورات أداءً' : 'Top Performing Posts'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.contentAnalysis.topPerformingPosts.map((post, index) => (
                    <div key={post.id} className="p-4 border rounded-lg">
                      <div className={rtl.cn(
                        "flex items-start justify-between",
                        rtl.flex()
                      )}>
                        <div className={rtl.textAlign()}>
                          <p className="font-medium line-clamp-2">{post.content}</p>
                          <p className="text-sm text-gray-600 mt-1">{post.platform}</p>
                        </div>
                        <div className={rtl.cn(
                          "flex items-center gap-2",
                          rtl.flex()
                        )}>
                          <Star className="w-4 h-4 text-yellow-500" />
                          <span className="font-semibold">{post.score}</span>
                        </div>
                      </div>
                      <div className={rtl.cn(
                        "flex items-center gap-4 mt-3",
                        rtl.flex()
                      )}>
                        <div className={rtl.textAlign()}>
                          <p className="text-xs text-gray-500">{language === 'ar' ? 'التفاعل' : 'Engagement'}</p>
                          <p className="font-medium">{formatNumber(post.engagement)}</p>
                        </div>
                        <div className={rtl.textAlign()}>
                          <p className="text-xs text-gray-500">{language === 'ar' ? 'الوصول' : 'Reach'}</p>
                          <p className="font-medium">{formatNumber(post.reach)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Content Type Performance */}
            <Card>
              <CardHeader>
                <CardTitle className={rtl.textAlign()}>
                  {language === 'ar' ? 'أداء أنواع المحتوى' : 'Content Type Performance'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={data.contentAnalysis.contentTypes}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ type, performance }) => `${type}: ${performance}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="performance"
                    >
                      {data.contentAnalysis.contentTypes.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={['#8884d8', '#82ca9d', '#ffc658', '#ff8042'][index % 4]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Hashtag Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {language === 'ar' ? 'تحليل الهاشتاغات' : 'Hashtag Analysis'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {data.contentAnalysis.hashtagAnalysis.map((hashtag, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className={rtl.cn(
                      "flex items-center justify-between",
                      rtl.flex()
                    )}>
                      <span className="font-medium">{hashtag.hashtag}</span>
                      <div className={rtl.cn(
                        "flex items-center gap-1",
                        rtl.flex()
                      )}>
                        {hashtag.trend === 'up' && <TrendingUp className="w-4 h-4 text-green-500" />}
                        {hashtag.trend === 'down' && <TrendingDown className="w-4 h-4 text-red-500" />}
                        {hashtag.trend === 'stable' && <Activity className="w-4 h-4 text-gray-500" />}
                      </div>
                    </div>
                    <div className={rtl.cn(
                      "flex items-center justify-between mt-2",
                      rtl.flex()
                    )}>
                      <div className={rtl.textAlign()}>
                        <p className="text-xs text-gray-500">{language === 'ar' ? 'الاستخدام' : 'Usage'}</p>
                        <p className="font-medium">{hashtag.usage}</p>
                      </div>
                      <div className={rtl.textAlign()}>
                        <p className="text-xs text-gray-500">{language === 'ar' ? 'التفاعل' : 'Engagement'}</p>
                        <p className="font-medium">{formatNumber(hashtag.avgEngagement)}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Audience Tab */}
        <TabsContent value="audience" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Demographics */}
            <Card>
              <CardHeader>
                <CardTitle className={rtl.textAlign()}>
                  {language === 'ar' ? 'التركيبة السكانية' : 'Demographics'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={data.audienceInsights.demographics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="segment" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="percentage" fill="#8884d8" name={language === 'ar' ? 'النسبة %' : 'Percentage %'} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Geographic Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className={rtl.textAlign()}>
                  {language === 'ar' ? 'التوزيع الجغرافي' : 'Geographic Distribution'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={data.audienceInsights.locations}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ country, percentage }) => `${country}: ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="percentage"
                    >
                      {data.audienceInsights.locations.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={['#8884d8', '#82ca9d', '#ffc658', '#ff8042'][index % 4]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Best Times to Post */}
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {language === 'ar' ? 'أفضل أوقات النشر' : 'Best Times to Post'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {data.audienceInsights.bestTimes.map((time, index) => (
                  <div key={index} className="p-4 border rounded-lg text-center">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                      <Clock className="w-6 h-6 text-blue-600" />
                    </div>
                    <p className="font-semibold">{time.day}</p>
                    <p className="text-2xl font-bold text-blue-600">{time.hour}:00</p>
                    <p className="text-sm text-gray-600">
                      {formatPercentage(time.engagement)} {language === 'ar' ? 'تفاعل' : 'engagement'}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* AI Insights */}
            <Card>
              <CardHeader>
                <CardTitle className={rtl.cn(
                  "flex items-center gap-2",
                  rtl.flex(),
                  rtl.textAlign()
                )}>
                  <Lightbulb className="w-5 h-5 text-yellow-500" />
                  {language === 'ar' ? 'رؤى ذكية' : 'AI Insights'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.insights.map((insight) => (
                    <Alert key={insight.id} className={cn(
                      insight.type === 'success' && 'border-green-200 bg-green-50',
                      insight.type === 'warning' && 'border-yellow-200 bg-yellow-50',
                      insight.type === 'info' && 'border-blue-200 bg-blue-50',
                      insight.type === 'recommendation' && 'border-purple-200 bg-purple-50'
                    )}>
                      <div className={rtl.cn(
                        "flex items-start gap-3",
                        rtl.flex()
                      )}>
                        {insight.type === 'success' && <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />}
                        {insight.type === 'warning' && <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />}
                        {insight.type === 'info' && <Info className="h-4 w-4 text-blue-600 mt-0.5" />}
                        {insight.type === 'recommendation' && <Lightbulb className="h-4 w-4 text-purple-600 mt-0.5" />}

                        <div className={rtl.textAlign()}>
                          <AlertTitle className={cn(
                            insight.type === 'success' && 'text-green-900',
                            insight.type === 'warning' && 'text-yellow-900',
                            insight.type === 'info' && 'text-blue-900',
                            insight.type === 'recommendation' && 'text-purple-900'
                          )}>
                            {insight.title}
                          </AlertTitle>
                          <AlertDescription className={cn(
                            insight.type === 'success' && 'text-green-800',
                            insight.type === 'warning' && 'text-yellow-800',
                            insight.type === 'info' && 'text-blue-800',
                            insight.type === 'recommendation' && 'text-purple-800'
                          )}>
                            {insight.description}
                          </AlertDescription>
                          <div className={rtl.cn(
                            "flex items-center gap-2 mt-2",
                            rtl.flex()
                          )}>
                            <Badge variant={insight.impact === 'high' ? 'default' : insight.impact === 'medium' ? 'secondary' : 'outline'}>
                              {language === 'ar' ?
                                (insight.impact === 'high' ? 'تأثير عالي' : insight.impact === 'medium' ? 'تأثير متوسط' : 'تأثير منخفض') :
                                `${insight.impact} impact`
                              }
                            </Badge>
                            {insight.actionable && (
                              <Button size="sm" variant="outline">
                                {language === 'ar' ? 'اتخاذ إجراء' : 'Take Action'}
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Predictions */}
            <Card>
              <CardHeader>
                <CardTitle className={rtl.cn(
                  "flex items-center gap-2",
                  rtl.flex(),
                  rtl.textAlign()
                )}>
                  <Target className="w-5 h-5 text-blue-500" />
                  {language === 'ar' ? 'التوقعات والتنبؤات' : 'Predictions & Forecasts'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Next Week Predictions */}
                  <div>
                    <h4 className={rtl.cn("font-semibold mb-3", rtl.textAlign())}>
                      {language === 'ar' ? 'توقعات الأسبوع القادم' : 'Next Week Predictions'}
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm text-blue-600">{language === 'ar' ? 'التفاعل المتوقع' : 'Expected Engagement'}</p>
                        <p className="text-xl font-bold text-blue-900">+{formatPercentage(data.predictions.nextWeekEngagement)}</p>
                      </div>
                      <div className="p-3 bg-green-50 rounded-lg">
                        <p className="text-sm text-green-600">{language === 'ar' ? 'الوصول المتوقع' : 'Expected Reach'}</p>
                        <p className="text-xl font-bold text-green-900">{formatNumber(data.predictions.nextWeekReach)}</p>
                      </div>
                    </div>
                  </div>

                  {/* Optimal Posting Times */}
                  <div>
                    <h4 className={rtl.cn("font-semibold mb-3", rtl.textAlign())}>
                      {language === 'ar' ? 'أفضل أوقات النشر' : 'Optimal Posting Times'}
                    </h4>
                    <div className="space-y-2">
                      {data.predictions.optimalPostingTimes.map((time, index) => (
                        <div key={index} className={rtl.cn(
                          "flex items-center justify-between p-2 bg-gray-50 rounded",
                          rtl.flex()
                        )}>
                          <span>{time.day} - {time.hour}:00</span>
                          <div className={rtl.cn(
                            "flex items-center gap-2",
                            rtl.flex()
                          )}>
                            <Progress value={time.score} className="w-16" />
                            <span className="text-sm font-medium">{time.score}%</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Content Recommendations */}
                  <div>
                    <h4 className={rtl.cn("font-semibold mb-3", rtl.textAlign())}>
                      {language === 'ar' ? 'توصيات المحتوى' : 'Content Recommendations'}
                    </h4>
                    <div className="space-y-3">
                      {data.predictions.contentRecommendations.map((rec, index) => (
                        <div key={index} className="p-3 border rounded-lg">
                          <div className={rtl.cn(
                            "flex items-center justify-between",
                            rtl.flex()
                          )}>
                            <div className={rtl.textAlign()}>
                              <p className="font-medium">{rec.type}</p>
                              <p className="text-sm text-gray-600">{rec.reason}</p>
                            </div>
                            <Badge variant="outline">
                              +{rec.expectedImprovement}%
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Reports Tab */}
        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {language === 'ar' ? 'تقارير مخصصة' : 'Custom Reports'}
              </CardTitle>
              <CardDescription className={rtl.textAlign()}>
                {language === 'ar' ? 'إنشاء وتصدير تقارير مخصصة' : 'Create and export custom reports'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Report Builder */}
                <div className="space-y-4">
                  <h4 className={rtl.cn("font-semibold", rtl.textAlign())}>
                    {language === 'ar' ? 'منشئ التقارير' : 'Report Builder'}
                  </h4>

                  <div className="space-y-3">
                    <div>
                      <Label className={rtl.textAlign()}>
                        {language === 'ar' ? 'نوع التقرير' : 'Report Type'}
                      </Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder={language === 'ar' ? 'اختر نوع التقرير' : 'Select report type'} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="performance">{language === 'ar' ? 'تقرير الأداء' : 'Performance Report'}</SelectItem>
                          <SelectItem value="engagement">{language === 'ar' ? 'تقرير التفاعل' : 'Engagement Report'}</SelectItem>
                          <SelectItem value="audience">{language === 'ar' ? 'تقرير الجمهور' : 'Audience Report'}</SelectItem>
                          <SelectItem value="competitor">{language === 'ar' ? 'تقرير المنافسين' : 'Competitor Report'}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className={rtl.textAlign()}>
                        {language === 'ar' ? 'المقاييس المطلوبة' : 'Required Metrics'}
                      </Label>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        {['engagement', 'reach', 'impressions', 'clicks', 'conversions', 'followers'].map((metric) => (
                          <div key={metric} className={rtl.cn(
                            "flex items-center space-x-2",
                            rtl.flex()
                          )}>
                            <Checkbox
                              id={metric}
                              checked={selectedMetrics.includes(metric)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedMetrics([...selectedMetrics, metric]);
                                } else {
                                  setSelectedMetrics(selectedMetrics.filter(m => m !== metric));
                                }
                              }}
                            />
                            <Label htmlFor={metric} className="text-sm">
                              {language === 'ar' ?
                                (metric === 'engagement' ? 'التفاعل' :
                                 metric === 'reach' ? 'الوصول' :
                                 metric === 'impressions' ? 'المشاهدات' :
                                 metric === 'clicks' ? 'النقرات' :
                                 metric === 'conversions' ? 'التحويلات' : 'المتابعون') :
                                metric.charAt(0).toUpperCase() + metric.slice(1)
                              }
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label className={rtl.textAlign()}>
                        {language === 'ar' ? 'تنسيق التصدير' : 'Export Format'}
                      </Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder={language === 'ar' ? 'اختر التنسيق' : 'Select format'} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pdf">PDF</SelectItem>
                          <SelectItem value="excel">Excel</SelectItem>
                          <SelectItem value="csv">CSV</SelectItem>
                          <SelectItem value="json">JSON</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <Button className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      {language === 'ar' ? 'إنشاء التقرير' : 'Generate Report'}
                    </Button>
                  </div>
                </div>

                {/* Scheduled Reports */}
                <div className="space-y-4">
                  <h4 className={rtl.cn("font-semibold", rtl.textAlign())}>
                    {language === 'ar' ? 'التقارير المجدولة' : 'Scheduled Reports'}
                  </h4>

                  <div className="space-y-3">
                    {[
                      { name: language === 'ar' ? 'تقرير أسبوعي' : 'Weekly Report', frequency: language === 'ar' ? 'كل أسبوع' : 'Every week', next: language === 'ar' ? 'الأحد القادم' : 'Next Sunday' },
                      { name: language === 'ar' ? 'تقرير شهري' : 'Monthly Report', frequency: language === 'ar' ? 'كل شهر' : 'Every month', next: language === 'ar' ? '1 يناير' : 'Jan 1st' },
                    ].map((report, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className={rtl.cn(
                          "flex items-center justify-between",
                          rtl.flex()
                        )}>
                          <div className={rtl.textAlign()}>
                            <p className="font-medium">{report.name}</p>
                            <p className="text-sm text-gray-600">{report.frequency}</p>
                          </div>
                          <div className={rtl.textAlign()}>
                            <p className="text-sm text-gray-500">{language === 'ar' ? 'التالي:' : 'Next:'}</p>
                            <p className="text-sm font-medium">{report.next}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <Button variant="outline" className="w-full">
                    <Calendar className="w-4 h-4 mr-2" />
                    {language === 'ar' ? 'جدولة تقرير جديد' : 'Schedule New Report'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
