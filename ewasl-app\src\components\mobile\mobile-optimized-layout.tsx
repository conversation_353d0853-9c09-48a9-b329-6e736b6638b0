"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Menu,
  X,
  Home,
  PlusCircle,
  BarChart3,
  Calendar,
  Users,
  Settings,
  Search,
  Bell,
  User,
  ChevronDown,
  ChevronUp,
  Smartphone,
  Tablet,
  Monitor,
  Zap,
  TouchIcon as Touch
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter, usePathname } from 'next/navigation';

interface MobileOptimizedLayoutProps {
  children: React.ReactNode;
  language?: 'ar' | 'en';
  className?: string;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  href: string;
  badge?: string;
  isActive?: boolean;
}

export function MobileOptimizedLayout({ 
  children, 
  language = 'ar', 
  className 
}: MobileOptimizedLayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('mobile');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // Translations
  const t = {
    ar: {
      navigation: {
        dashboard: 'لوحة التحكم',
        posts: 'المنشورات',
        analytics: 'التحليلات',
        schedule: 'الجدولة',
        social: 'الحسابات',
        settings: 'الإعدادات'
      },
      actions: {
        menu: 'القائمة',
        close: 'إغلاق',
        search: 'البحث',
        notifications: 'الإشعارات',
        profile: 'الملف الشخصي',
        newPost: 'منشور جديد'
      },
      mobile: {
        optimized: 'محسن للجوال',
        touchFriendly: 'سهل اللمس',
        responsive: 'متجاوب',
        fastLoading: 'تحميل سريع'
      }
    },
    en: {
      navigation: {
        dashboard: 'Dashboard',
        posts: 'Posts',
        analytics: 'Analytics',
        schedule: 'Schedule',
        social: 'Social',
        settings: 'Settings'
      },
      actions: {
        menu: 'Menu',
        close: 'Close',
        search: 'Search',
        notifications: 'Notifications',
        profile: 'Profile',
        newPost: 'New Post'
      },
      mobile: {
        optimized: 'Mobile Optimized',
        touchFriendly: 'Touch Friendly',
        responsive: 'Responsive',
        fastLoading: 'Fast Loading'
      }
    }
  };

  const text = t[language];

  // Navigation items
  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: text.navigation.dashboard,
      icon: Home,
      href: '/dashboard',
      isActive: pathname === '/dashboard'
    },
    {
      id: 'posts',
      label: text.navigation.posts,
      icon: PlusCircle,
      href: '/posts',
      badge: '12',
      isActive: pathname.startsWith('/posts')
    },
    {
      id: 'analytics',
      label: text.navigation.analytics,
      icon: BarChart3,
      href: '/analytics',
      isActive: pathname.startsWith('/analytics')
    },
    {
      id: 'schedule',
      label: text.navigation.schedule,
      icon: Calendar,
      href: '/schedule',
      badge: '3',
      isActive: pathname.startsWith('/schedule')
    },
    {
      id: 'social',
      label: text.navigation.social,
      icon: Users,
      href: '/social',
      isActive: pathname.startsWith('/social')
    },
    {
      id: 'settings',
      label: text.navigation.settings,
      icon: Settings,
      href: '/settings',
      isActive: pathname.startsWith('/settings')
    }
  ];

  // Detect device type
  useEffect(() => {
    const checkDeviceType = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setDeviceType('mobile');
      } else if (width < 1024) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    checkDeviceType();
    window.addEventListener('resize', checkDeviceType);
    return () => window.removeEventListener('resize', checkDeviceType);
  }, []);

  // Handle navigation
  const handleNavigation = (href: string) => {
    router.push(href);
    setIsMobileMenuOpen(false);
  };

  // Mobile Navigation Component
  const MobileNavigation = () => (
    <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "md:hidden",
            language === 'ar' ? "ml-auto" : "mr-auto"
          )}
        >
          <Menu className="w-5 h-5" />
        </Button>
      </SheetTrigger>
      <SheetContent 
        side={language === 'ar' ? 'right' : 'left'} 
        className="w-80 p-0"
      >
        <SheetHeader className="p-6 pb-4">
          <SheetTitle className={cn(
            "flex items-center gap-3",
            language === 'ar' ? "flex-row-reverse text-right" : ""
          )}>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <Zap className="w-4 h-4 text-white" />
            </div>
            eWasl
          </SheetTitle>
          <SheetDescription className={language === 'ar' ? "text-right" : ""}>
            {text.mobile.optimized}
          </SheetDescription>
        </SheetHeader>
        
        <ScrollArea className="flex-1 px-6">
          <div className="space-y-2">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <Button
                  key={item.id}
                  variant={item.isActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start h-12 text-base",
                    language === 'ar' ? "flex-row-reverse" : "",
                    item.isActive && "bg-blue-50 text-blue-700 border-blue-200"
                  )}
                  onClick={() => handleNavigation(item.href)}
                >
                  <Icon className="w-5 h-5" />
                  <span className="flex-1">{item.label}</span>
                  {item.badge && (
                    <Badge variant="secondary" className="text-xs">
                      {item.badge}
                    </Badge>
                  )}
                </Button>
              );
            })}
          </div>
          
          <Separator className="my-6" />
          
          {/* Mobile Features */}
          <div className="space-y-4">
            <h4 className={cn(
              "text-sm font-medium text-gray-700",
              language === 'ar' ? "text-right" : ""
            )}>
              {language === 'ar' ? 'ميزات الجوال' : 'Mobile Features'}
            </h4>
            
            <div className="grid grid-cols-2 gap-3">
              <Card className="p-3">
                <div className={cn(
                  "flex items-center gap-2",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <Touch className="w-4 h-4 text-blue-500" />
                  <span className="text-xs font-medium">{text.mobile.touchFriendly}</span>
                </div>
              </Card>
              
              <Card className="p-3">
                <div className={cn(
                  "flex items-center gap-2",
                  language === 'ar' ? "flex-row-reverse" : ""
                )}>
                  <Smartphone className="w-4 h-4 text-green-500" />
                  <span className="text-xs font-medium">{text.mobile.responsive}</span>
                </div>
              </Card>
            </div>
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );

  // Desktop Sidebar Component
  const DesktopSidebar = () => (
    <div className={cn(
      "hidden md:flex flex-col w-64 bg-white border-r border-gray-200 transition-all duration-300",
      isCollapsed && "w-16",
      language === 'ar' ? "border-l border-r-0" : ""
    )}>
      {/* Sidebar Header */}
      <div className="p-6 border-b border-gray-200">
        <div className={cn(
          "flex items-center gap-3",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <Zap className="w-4 h-4 text-white" />
          </div>
          {!isCollapsed && (
            <div className={language === 'ar' ? "text-right" : ""}>
              <h2 className="text-lg font-semibold">eWasl</h2>
              <p className="text-xs text-gray-500">{text.mobile.optimized}</p>
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className={cn(
              "ml-auto",
              language === 'ar' ? "mr-auto ml-0" : ""
            )}
          >
            {isCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {/* Navigation */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <Button
                key={item.id}
                variant={item.isActive ? "secondary" : "ghost"}
                className={cn(
                  "w-full h-11",
                  isCollapsed ? "justify-center px-2" : "justify-start",
                  language === 'ar' && !isCollapsed ? "flex-row-reverse" : "",
                  item.isActive && "bg-blue-50 text-blue-700 border-blue-200"
                )}
                onClick={() => handleNavigation(item.href)}
              >
                <Icon className="w-5 h-5" />
                {!isCollapsed && (
                  <>
                    <span className="flex-1">{item.label}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </>
                )}
              </Button>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );

  // Mobile Header Component
  const MobileHeader = () => (
    <div className="md:hidden bg-white border-b border-gray-200 px-4 py-3">
      <div className={cn(
        "flex items-center justify-between",
        language === 'ar' ? "flex-row-reverse" : ""
      )}>
        <div className={cn(
          "flex items-center gap-3",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          <MobileNavigation />
          <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center">
            <Zap className="w-3 h-3 text-white" />
          </div>
          <h1 className="text-lg font-semibold">eWasl</h1>
        </div>

        <div className={cn(
          "flex items-center gap-2",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          <Button variant="ghost" size="sm">
            <Search className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="w-4 h-4" />
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
          </Button>
          <Button variant="ghost" size="sm">
            <User className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );

  // Device Type Indicator
  const DeviceTypeIndicator = () => {
    const icons = {
      mobile: Smartphone,
      tablet: Tablet,
      desktop: Monitor
    };
    const Icon = icons[deviceType];

    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Card className="p-2">
          <div className={cn(
            "flex items-center gap-2 text-xs",
            language === 'ar' ? "flex-row-reverse" : ""
          )}>
            <Icon className="w-4 h-4 text-blue-500" />
            <span className="capitalize">{deviceType}</span>
          </div>
        </Card>
      </div>
    );
  };

  return (
    <div className={cn(
      "min-h-screen bg-gray-50 flex",
      language === 'ar' ? "rtl" : "ltr",
      className
    )}>
      {/* Desktop Sidebar */}
      <DesktopSidebar />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Mobile Header */}
        <MobileHeader />

        {/* Content Area */}
        <main className="flex-1 overflow-auto">
          <div className="p-4 md:p-6 lg:p-8">
            {children}
          </div>
        </main>
      </div>

      {/* Device Type Indicator (Development Only) */}
      {process.env.NODE_ENV === 'development' && <DeviceTypeIndicator />}
    </div>
  );
}
