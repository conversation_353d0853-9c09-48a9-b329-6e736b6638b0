import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  // Allow in production for debugging Facebook OAuth
  const isDev = process.env.NODE_ENV === 'development'
  
  return NextResponse.json({
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    url: request.url,
    envVars: {
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'undefined',
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'undefined',
      NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'undefined',
      NODE_ENV: process.env.NODE_ENV || 'undefined',
      VERCEL_URL: process.env.VERCEL_URL || 'undefined',
      VERCEL_ENV: process.env.VERCEL_ENV || 'undefined',
      // Facebook OAuth variables (showing only presence, not values)
      FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID ? 'SET' : 'undefined',
      FACEBOOK_APP_SECRET: process.env.FACEBOOK_APP_SECRET ? 'SET' : 'undefined',
      FACEBOOK_BUSINESS_ID: process.env.FACEBOOK_BUSINESS_ID ? 'SET' : 'undefined',
    },
    headers: {
      host: request.headers.get('host'),
      'x-forwarded-host': request.headers.get('x-forwarded-host'),
      'x-forwarded-proto': request.headers.get('x-forwarded-proto'),
    }
  })
}
