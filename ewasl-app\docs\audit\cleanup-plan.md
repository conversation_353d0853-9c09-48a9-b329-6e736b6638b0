# 🧹 eWasl Cleanup Plan - Dead Code & Asset Removal

**Generated:** 2025-07-12  
**Analysis Tools:** ts-prune, depcheck, manual audit  
**Estimated Bundle Reduction:** ~200-300 kB  

## 📊 Analysis Summary

### Unused Dependencies (19 packages)
- **Size Impact:** ~150-200 kB bundle reduction
- **Risk Level:** Low (safe to remove)
- **Dependencies:** 19 unused packages identified

### Unused TypeScript Exports (200+ items)
- **Size Impact:** ~50-100 kB bundle reduction
- **Risk Level:** Medium (requires careful review)
- **Files Affected:** 50+ files with unused exports

### Dead Files & Assets (Quantified)
- **Test Files:** 77 test-*.js files
- **Documentation:** 5+ *-REPORT.md files
- **Screenshots:** 10 PNG files
- **Backup Files:** 9 .old/.bak/.temp files
- **Cache Files:** Multiple .next cache files

## 🗑️ Unused Dependencies to Remove

### Production Dependencies (Safe to Remove)
```bash
npm uninstall \
  @radix-ui/react-icons \
  @supabase/auth-helpers-react \
  @supabase/auth-ui-react \
  @supabase/auth-ui-shared \
  @supabase/storage-js \
  @tanstack/react-table \
  @tiptap/extension-image \
  @tiptap/extension-youtube \
  autoprefixer \
  date-fns-tz \
  facebook-nodejs-business-sdk \
  fluent-ffmpeg \
  fs-extra \
  mime-types \
  next-themes \
  postcss \
  react-dnd \
  react-dnd-html5-backend \
  twitter-api-v2
```

### Dev Dependencies (Safe to Remove)
```bash
npm uninstall --save-dev \
  @types/fluent-ffmpeg \
  @types/fs-extra \
  @types/jest \
  @types/mime-types \
  depcheck \
  eslint \
  eslint-config-next \
  jest-environment-jsdom \
  puppeteer \
  ts-node \
  ts-prune
```

### Missing Dependencies (Need to Add)
```bash
npm install \
  webpack-bundle-analyzer \
  axios \
  @jest/globals \
  node-mocks-http \
  vitest \
  @tiptap/core
```

## 📁 Files to Delete

### Test Files (Low Risk)
```
ewasl-app/test-*.js (50+ files)
ewasl-app/test-*.mjs (10+ files)
ewasl-app/test-*.html (5+ files)
ewasl-app/comprehensive-*.js (10+ files)
ewasl-app/debug-*.js (5+ files)
ewasl-app/manual-*.js (5+ files)
```

### Documentation Files (Medium Risk - Review First)
```
ewasl-app/*-TESTING-REPORT.md (20+ files)
ewasl-app/*-COMPLETION-SUMMARY.md (15+ files)
ewasl-app/*-IMPLEMENTATION-PLAN.md (10+ files)
ewasl-app/*-AUDIT-REPORT.md (10+ files)
ewasl-app/*-FIX-PLAN.md (10+ files)
```

### Screenshots & Assets (Safe to Remove)
```
ewasl-app/*.png (10+ files)
ewasl-app/screenshots/ (entire directory)
ewasl-app/home.html
ewasl-app/dashboard-*.html (5+ files)
```

### Backup & Temporary Files
```
ewasl-app/backup-enhanced-components/ (entire directory)
ewasl-app/*.old
ewasl-app/*.bak
ewasl-app/*.temp
ewasl-app/Dockerfile.* (except Dockerfile.production)
```

### Build & Config Artifacts
```
ewasl-app/ca.crt
ewasl-app/ca.key
ewasl-app/cert.crt
ewasl-app/cert.key
ewasl-app/doctl*
ewasl-app/supabase.exe
ewasl-app/supabase.tar.gz
```

## 🔍 Unused TypeScript Exports (Requires Review)

### High-Impact Unused Exports
```typescript
// src/lib/mock-data.ts
- mockActivities (100+ lines)
- mockEngagementData (50+ lines)
- mockPlatformData (30+ lines)

// src/components/ui/ (Multiple files)
- Various unused UI component variants
- Unused form components
- Unused loading states

// src/lib/analytics/ (Multiple files)
- Unused analytics collectors
- Unused reporting engines
- Unused trend detectors
```

### Medium-Impact Unused Exports
```typescript
// src/hooks/ (Multiple files)
- usePostAnalytics
- useRenderCount
- useWhyDidYouUpdate
- useDebounce
- useThrottle

// src/lib/social/ (Multiple files)
- Various OAuth service methods
- Unused publisher implementations
- Unused token managers
```

### Low-Impact Unused Exports
```typescript
// Type definitions and interfaces
- Various unused TypeScript interfaces
- Unused enum definitions
- Unused constant exports
```

## 🎯 Cleanup Execution Plan

### Phase 1: Safe Removals (Day 1)
1. **Remove unused dependencies** (19 packages)
2. **Delete test files** (safe, not in production)
3. **Remove screenshots and assets** (safe)
4. **Clean backup directories** (safe)

### Phase 2: Documentation Cleanup (Day 2)
1. **Archive important documentation** to `/docs/archive/`
2. **Remove redundant reports** (keep latest versions)
3. **Consolidate implementation guides**
4. **Update README with current status**

### Phase 3: Code Cleanup (Day 3-4)
1. **Remove unused TypeScript exports** (careful review)
2. **Clean up unused components** (test thoroughly)
3. **Remove unused utility functions**
4. **Optimize import statements**

### Phase 4: Verification (Day 5)
1. **Run full test suite**
2. **Verify build still works**
3. **Check bundle size reduction**
4. **Deploy to staging for testing**

## ⚠️ Risk Assessment

### Low Risk (Safe to Remove)
- Unused npm packages
- Test files not in production
- Screenshots and documentation
- Backup directories

### Medium Risk (Review Required)
- Unused TypeScript exports
- Component variants
- Utility functions
- Configuration files

### High Risk (Careful Analysis)
- Core library functions
- Authentication components
- Database utilities
- API integrations

## 📊 Expected Impact

### Bundle Size Reduction
- **Dependencies:** -150-200 kB
- **Dead code:** -50-100 kB
- **Total reduction:** -200-300 kB
- **Target:** <250 kB first load JS

### Build Performance
- **Faster TypeScript compilation**
- **Reduced dependency resolution time**
- **Smaller node_modules directory**
- **Faster CI/CD pipeline**

### Maintenance Benefits
- **Cleaner codebase**
- **Easier navigation**
- **Reduced cognitive load**
- **Better IDE performance**

## 🔄 Rollback Plan

### Before Cleanup
1. **Create git branch:** `cleanup/dead-code-removal`
2. **Full backup of current state**
3. **Document all changes**
4. **Prepare rollback scripts**

### If Issues Occur
1. **Immediate rollback:** `git checkout main`
2. **Selective restoration** of critical files
3. **Incremental cleanup** instead of bulk removal
4. **Extended testing period**

## ✅ Success Criteria

### Completion Metrics
- [ ] Bundle size reduced by >200 kB
- [ ] Build time improved by >20%
- [ ] Zero unused dependencies in depcheck
- [ ] Zero unused exports in ts-prune
- [ ] All tests passing
- [ ] Production deployment successful

### Quality Gates
- [ ] No broken functionality
- [ ] No performance regressions
- [ ] No security vulnerabilities introduced
- [ ] Documentation updated
- [ ] Team review completed

---

**Next Steps:** Execute Phase 1 safe removals  
**Timeline:** 5 days total  
**Review Required:** Before Phase 3 code cleanup
