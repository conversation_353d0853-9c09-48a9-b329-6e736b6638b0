"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Calendar, momentLocalizer, Views, View } from 'react-big-calendar';
import withDragAndDrop from 'react-big-calendar/lib/addons/dragAndDrop';
import moment from 'moment';
import 'moment/locale/ar-sa';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Calendar as CalendarIcon,
  Clock,
  Plus,
  Edit,
  Trash2,
  Copy,
  MoreHorizontal,
  Filter,
  Search,
  RefreshCw,
  Download,
  Upload,
  Settings,
  Eye,
  Users,
  Heart,
  MessageCircle,
  Share,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle,
  Zap,
  Target,
  Globe
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRTL } from '@/lib/i18n/arabic-rtl-optimizer';
import { toast } from 'sonner';

// Configure moment for Arabic locale
moment.locale('ar-sa');
const localizer = momentLocalizer(moment);
const DragAndDropCalendar = withDragAndDrop(Calendar);

interface ScheduledPost {
  id: string;
  title: string;
  content: string;
  start: Date;
  end: Date;
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  platforms: string[];
  mediaUrls?: string[];
  analytics?: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
    engagement: number;
  };
  author: {
    name: string;
    avatar?: string;
  };
  priority: 'low' | 'medium' | 'high';
  tags: string[];
  isRecurring?: boolean;
  recurringPattern?: string;
  parentId?: string;
}

interface EnhancedContentCalendarProps {
  language?: 'ar' | 'en';
  className?: string;
  onPostCreate?: (date: Date) => void;
  onPostEdit?: (post: ScheduledPost) => void;
  onPostDelete?: (postId: string) => void;
  onPostDuplicate?: (post: ScheduledPost) => void;
  onPostMove?: (postId: string, newDate: Date) => Promise<void>;
}

export function EnhancedContentCalendar({
  language = 'ar',
  className,
  onPostCreate,
  onPostEdit,
  onPostDelete,
  onPostDuplicate,
  onPostMove
}: EnhancedContentCalendarProps) {
  const [posts, setPosts] = useState<ScheduledPost[]>([]);
  const [view, setView] = useState<View>(Views.MONTH);
  const [date, setDate] = useState(new Date());
  const [selectedPost, setSelectedPost] = useState<ScheduledPost | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [platformFilter, setPlatformFilter] = useState<string>('all');
  const [showQuickCreate, setShowQuickCreate] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState<{ start: Date; end: Date } | null>(null);

  const rtl = useRTL(language);

  // Translations
  const t = {
    ar: {
      title: 'تقويم المحتوى المتقدم',
      subtitle: 'جدولة وإدارة المحتوى مع السحب والإفلات',
      views: {
        month: 'شهر',
        week: 'أسبوع',
        day: 'يوم',
        agenda: 'جدول الأعمال'
      },
      actions: {
        create: 'إنشاء منشور',
        edit: 'تعديل',
        delete: 'حذف',
        duplicate: 'نسخ',
        search: 'بحث',
        filter: 'تصفية',
        refresh: 'تحديث',
        export: 'تصدير',
        import: 'استيراد'
      },
      status: {
        all: 'الكل',
        draft: 'مسودة',
        scheduled: 'مجدول',
        published: 'منشور',
        failed: 'فشل'
      },
      platforms: {
        all: 'جميع المنصات',
        instagram: 'إنستغرام',
        facebook: 'فيسبوك',
        twitter: 'تويتر',
        linkedin: 'لينكد إن'
      },
      quickCreate: {
        title: 'إنشاء سريع',
        content: 'محتوى المنشور',
        platform: 'المنصة',
        time: 'الوقت',
        create: 'إنشاء'
      },
      analytics: {
        views: 'مشاهدات',
        likes: 'إعجابات',
        comments: 'تعليقات',
        shares: 'مشاركات',
        engagement: 'تفاعل'
      }
    },
    en: {
      title: 'Enhanced Content Calendar',
      subtitle: 'Schedule and manage content with drag-and-drop functionality',
      views: {
        month: 'Month',
        week: 'Week',
        day: 'Day',
        agenda: 'Agenda'
      },
      actions: {
        create: 'Create Post',
        edit: 'Edit',
        delete: 'Delete',
        duplicate: 'Duplicate',
        search: 'Search',
        filter: 'Filter',
        refresh: 'Refresh',
        export: 'Export',
        import: 'Import'
      },
      status: {
        all: 'All',
        draft: 'Draft',
        scheduled: 'Scheduled',
        published: 'Published',
        failed: 'Failed'
      },
      platforms: {
        all: 'All Platforms',
        instagram: 'Instagram',
        facebook: 'Facebook',
        twitter: 'Twitter',
        linkedin: 'LinkedIn'
      },
      quickCreate: {
        title: 'Quick Create',
        content: 'Post Content',
        platform: 'Platform',
        time: 'Time',
        create: 'Create'
      },
      analytics: {
        views: 'Views',
        likes: 'Likes',
        comments: 'Comments',
        shares: 'Shares',
        engagement: 'Engagement'
      }
    }
  };

  const text = t[language];

  // Generate demo data
  useEffect(() => {
    const demoData = generateDemoData();
    setPosts(demoData);
  }, []);

  // Filter posts based on search and filters
  const filteredPosts = useMemo(() => {
    return posts.filter(post => {
      const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           post.content.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || post.status === statusFilter;
      const matchesPlatform = platformFilter === 'all' || post.platforms.includes(platformFilter);
      
      return matchesSearch && matchesStatus && matchesPlatform;
    });
  }, [posts, searchTerm, statusFilter, platformFilter]);

  // Handle drag and drop
  const handleEventDrop = useCallback(async ({ event, start, end }: any) => {
    try {
      setIsLoading(true);
      
      // Update local state immediately for better UX
      setPosts(prevPosts => 
        prevPosts.map(post => 
          post.id === event.id 
            ? { ...post, start, end }
            : post
        )
      );

      // Call external handler if provided
      if (onPostMove) {
        await onPostMove(event.id, start);
      }

      toast.success(language === 'ar' ? 'تم تحديث موعد المنشور بنجاح' : 'Post rescheduled successfully');
    } catch (error) {
      console.error('Failed to move post:', error);
      toast.error(language === 'ar' ? 'فشل في تحديث موعد المنشور' : 'Failed to reschedule post');
      
      // Revert the change
      setPosts(prevPosts => 
        prevPosts.map(post => 
          post.id === event.id 
            ? { ...post, start: event.start, end: event.end }
            : post
        )
      );
    } finally {
      setIsLoading(false);
    }
  }, [onPostMove, language]);

  // Handle event resize
  const handleEventResize = useCallback(async ({ event, start, end }: any) => {
    try {
      setPosts(prevPosts => 
        prevPosts.map(post => 
          post.id === event.id 
            ? { ...post, start, end }
            : post
        )
      );

      if (onPostMove) {
        await onPostMove(event.id, start);
      }

      toast.success(language === 'ar' ? 'تم تحديث مدة المنشور بنجاح' : 'Post duration updated successfully');
    } catch (error) {
      console.error('Failed to resize post:', error);
      toast.error(language === 'ar' ? 'فشل في تحديث مدة المنشور' : 'Failed to update post duration');
    }
  }, [onPostMove, language]);

  // Handle slot selection
  const handleSelectSlot = useCallback(({ start, end }: { start: Date; end: Date }) => {
    setSelectedSlot({ start, end });
    setShowQuickCreate(true);
  }, []);

  // Handle event selection
  const handleSelectEvent = useCallback((event: ScheduledPost) => {
    setSelectedPost(event);
  }, []);

  // Custom event component
  const EventComponent = ({ event }: { event: ScheduledPost }) => {
    const statusColors = {
      draft: 'bg-gray-100 border-gray-300 text-gray-700',
      scheduled: 'bg-blue-100 border-blue-300 text-blue-700',
      published: 'bg-green-100 border-green-300 text-green-700',
      failed: 'bg-red-100 border-red-300 text-red-700'
    };

    const priorityIndicators = {
      low: '🟢',
      medium: '🟡',
      high: '🔴'
    };

    return (
      <div className={cn(
        "p-1 rounded border-l-2 text-xs",
        statusColors[event.status],
        language === 'ar' ? "text-right" : "text-left"
      )}>
        <div className={rtl.cn(
          "flex items-center justify-between",
          rtl.flex()
        )}>
          <span className="font-medium truncate">{event.title}</span>
          <span>{priorityIndicators[event.priority]}</span>
        </div>
        
        <div className={rtl.cn(
          "flex items-center gap-1 mt-1",
          rtl.flex()
        )}>
          {event.platforms.map((platform, index) => (
            <span key={index} className="text-xs">
              {platform === 'instagram' && '📷'}
              {platform === 'facebook' && '👥'}
              {platform === 'twitter' && '🐦'}
              {platform === 'linkedin' && '💼'}
            </span>
          ))}
        </div>

        {event.status === 'published' && event.analytics && (
          <div className={rtl.cn(
            "flex items-center gap-1 mt-1 text-xs opacity-75",
            rtl.flex()
          )}>
            <span>👁 {rtl.formatCompactNumber(event.analytics.views)}</span>
            <span>❤️ {rtl.formatCompactNumber(event.analytics.likes)}</span>
          </div>
        )}
      </div>
    );
  };

  // Event style getter
  const eventStyleGetter = (event: ScheduledPost) => {
    const statusStyles = {
      draft: { backgroundColor: '#f3f4f6', borderColor: '#d1d5db' },
      scheduled: { backgroundColor: '#dbeafe', borderColor: '#93c5fd' },
      published: { backgroundColor: '#dcfce7', borderColor: '#86efac' },
      failed: { backgroundColor: '#fee2e2', borderColor: '#fca5a5' }
    };

    return {
      style: {
        ...statusStyles[event.status],
        border: `1px solid ${statusStyles[event.status].borderColor}`,
        borderRadius: '4px',
        opacity: event.status === 'draft' ? 0.7 : 1
      }
    };
  };

  // Custom toolbar
  const CustomToolbar = ({ label, onNavigate, onView }: any) => (
    <div className={rtl.cn(
      "flex items-center justify-between mb-4 p-4 bg-white rounded-lg border",
      rtl.flex()
    )}>
      <div className={rtl.cn(
        "flex items-center gap-2",
        rtl.flex()
      )}>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onNavigate('PREV')}
        >
          {language === 'ar' ? '→' : '←'}
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onNavigate('TODAY')}
        >
          {language === 'ar' ? 'اليوم' : 'Today'}
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onNavigate('NEXT')}
        >
          {language === 'ar' ? '←' : '→'}
        </Button>
      </div>

      <h2 className="text-lg font-semibold">{label}</h2>

      <div className={rtl.cn(
        "flex items-center gap-2",
        rtl.flex()
      )}>
        {Object.entries(text.views).map(([key, value]) => (
          <Button
            key={key}
            variant={view === Views[key.toUpperCase() as keyof typeof Views] ? "default" : "outline"}
            size="sm"
            onClick={() => onView(Views[key.toUpperCase() as keyof typeof Views])}
          >
            {value}
          </Button>
        ))}
      </div>
    </div>
  );

  // Generate demo data
  const generateDemoData = (): ScheduledPost[] => {
    const now = new Date();
    const posts: ScheduledPost[] = [];

    for (let i = 0; i < 20; i++) {
      const start = new Date(now.getTime() + (i - 10) * 24 * 60 * 60 * 1000 + Math.random() * 24 * 60 * 60 * 1000);
      const end = new Date(start.getTime() + 60 * 60 * 1000); // 1 hour duration

      const statuses: Array<'draft' | 'scheduled' | 'published' | 'failed'> = ['draft', 'scheduled', 'published', 'failed'];
      const platforms = ['instagram', 'facebook', 'twitter', 'linkedin'];
      const priorities: Array<'low' | 'medium' | 'high'> = ['low', 'medium', 'high'];

      posts.push({
        id: `post-${i}`,
        title: language === 'ar' 
          ? `منشور تجريبي ${i + 1}`
          : `Sample Post ${i + 1}`,
        content: language === 'ar'
          ? `هذا محتوى تجريبي للمنشور رقم ${i + 1}. يحتوي على نص ووسائط متنوعة.`
          : `This is sample content for post ${i + 1}. It contains text and various media.`,
        start,
        end,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        platforms: platforms.slice(0, Math.floor(Math.random() * 3) + 1),
        priority: priorities[Math.floor(Math.random() * priorities.length)],
        tags: [`tag${i}`, `category${Math.floor(i / 3)}`],
        author: {
          name: language === 'ar' ? `المؤلف ${i + 1}` : `Author ${i + 1}`,
          avatar: `https://via.placeholder.com/32x32/3b82f6/ffffff?text=A${i + 1}`
        },
        analytics: Math.random() > 0.5 ? {
          views: Math.floor(Math.random() * 10000),
          likes: Math.floor(Math.random() * 1000),
          comments: Math.floor(Math.random() * 100),
          shares: Math.floor(Math.random() * 50),
          engagement: Math.random() * 10
        } : undefined
      });
    }

    return posts;
  };

  return (
    <div className={cn(
      "space-y-6",
      language === 'ar' ? "rtl" : "ltr",
      className
    )} dir={rtl.direction}>
      {/* Header */}
      <div className={rtl.cn(
        "flex items-center justify-between",
        rtl.flex()
      )}>
        <div className={rtl.textAlign()}>
          <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
            {text.title}
          </h1>
          <p className="text-gray-600 mt-1" style={{ fontFamily: rtl.getFontFamily('primary') }}>
            {text.subtitle}
          </p>
        </div>

        <div className={rtl.cn(
          "flex items-center gap-4",
          rtl.flex()
        )}>
          <Button
            onClick={() => onPostCreate?.(new Date())}
            className={rtl.cn(
              "flex items-center gap-2",
              rtl.flex()
            )}
          >
            <Plus className="w-4 h-4" />
            {text.actions.create}
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>
                <Download className="w-4 h-4 mr-2" />
                {text.actions.export}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="w-4 h-4 mr-2" />
                {text.actions.import}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'الإعدادات' : 'Settings'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className={rtl.cn(
            "flex items-center gap-2",
            rtl.flex(),
            rtl.textAlign()
          )}>
            <Filter className="w-5 h-5" />
            {text.actions.filter}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <Label className={rtl.textAlign()}>
                {text.actions.search}
              </Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في المنشورات...' : 'Search posts...'}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={cn("pl-10", rtl.textAlign())}
                />
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <Label className={rtl.textAlign()}>
                {language === 'ar' ? 'الحالة' : 'Status'}
              </Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(text.status).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Platform Filter */}
            <div>
              <Label className={rtl.textAlign()}>
                {language === 'ar' ? 'المنصة' : 'Platform'}
              </Label>
              <Select value={platformFilter} onValueChange={setPlatformFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(text.platforms).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Actions */}
            <div className={rtl.cn(
              "flex items-end gap-2",
              rtl.flex()
            )}>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setPlatformFilter('all');
                }}
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Calendar Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[
          {
            label: language === 'ar' ? 'إجمالي المنشورات' : 'Total Posts',
            value: filteredPosts.length,
            icon: CalendarIcon,
            color: 'blue'
          },
          {
            label: language === 'ar' ? 'مجدولة' : 'Scheduled',
            value: filteredPosts.filter(p => p.status === 'scheduled').length,
            icon: Clock,
            color: 'orange'
          },
          {
            label: language === 'ar' ? 'منشورة' : 'Published',
            value: filteredPosts.filter(p => p.status === 'published').length,
            icon: CheckCircle,
            color: 'green'
          },
          {
            label: language === 'ar' ? 'مسودات' : 'Drafts',
            value: filteredPosts.filter(p => p.status === 'draft').length,
            icon: Edit,
            color: 'gray'
          }
        ].map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <CardContent className="p-4">
                <div className={rtl.cn(
                  "flex items-center justify-between",
                  rtl.flex()
                )}>
                  <div className={rtl.textAlign()}>
                    <p className="text-2xl font-bold">{stat.value}</p>
                    <p className="text-sm text-gray-600">{stat.label}</p>
                  </div>
                  <div className={`w-10 h-10 bg-${stat.color}-100 rounded-lg flex items-center justify-center`}>
                    <Icon className={`w-5 h-5 text-${stat.color}-600`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Calendar */}
      <Card>
        <CardContent className="p-0">
          <div style={{ height: '700px' }} className="p-4">
            <DragAndDropCalendar
              localizer={localizer}
              events={filteredPosts}
              startAccessor="start"
              endAccessor="end"
              view={view}
              onView={setView}
              date={date}
              onNavigate={setDate}
              onSelectEvent={handleSelectEvent}
              onSelectSlot={handleSelectSlot}
              onEventDrop={handleEventDrop}
              onEventResize={handleEventResize}
              selectable
              resizable
              popup
              step={30}
              timeslots={2}
              eventPropGetter={eventStyleGetter}
              components={{
                event: EventComponent,
                toolbar: CustomToolbar,
              }}
              messages={{
                allDay: language === 'ar' ? 'طوال اليوم' : 'All Day',
                previous: language === 'ar' ? 'السابق' : 'Previous',
                next: language === 'ar' ? 'التالي' : 'Next',
                today: language === 'ar' ? 'اليوم' : 'Today',
                month: text.views.month,
                week: text.views.week,
                day: text.views.day,
                agenda: text.views.agenda,
                date: language === 'ar' ? 'التاريخ' : 'Date',
                time: language === 'ar' ? 'الوقت' : 'Time',
                event: language === 'ar' ? 'الحدث' : 'Event',
                noEventsInRange: language === 'ar' ? 'لا توجد أحداث في هذا النطاق' : 'No events in this range',
                showMore: (total: number) => language === 'ar' ? `+${total} أكثر` : `+${total} more`
              }}
              culture={language === 'ar' ? 'ar-SA' : 'en-US'}
              rtl={language === 'ar'}
            />
          </div>
        </CardContent>
      </Card>

      {/* Quick Create Dialog */}
      <Dialog open={showQuickCreate} onOpenChange={setShowQuickCreate}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className={rtl.textAlign()}>
              {text.quickCreate.title}
            </DialogTitle>
            <DialogDescription className={rtl.textAlign()}>
              {language === 'ar'
                ? 'إنشاء منشور جديد بسرعة للتاريخ والوقت المحددين'
                : 'Quickly create a new post for the selected date and time'
              }
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label className={rtl.textAlign()}>
                {text.quickCreate.content}
              </Label>
              <Input
                placeholder={language === 'ar' ? 'اكتب محتوى المنشور...' : 'Write your post content...'}
                className={rtl.textAlign()}
              />
            </div>

            <div>
              <Label className={rtl.textAlign()}>
                {text.quickCreate.platform}
              </Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder={language === 'ar' ? 'اختر المنصة' : 'Select platform'} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="instagram">Instagram</SelectItem>
                  <SelectItem value="facebook">Facebook</SelectItem>
                  <SelectItem value="twitter">Twitter</SelectItem>
                  <SelectItem value="linkedin">LinkedIn</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {selectedSlot && (
              <div>
                <Label className={rtl.textAlign()}>
                  {text.quickCreate.time}
                </Label>
                <Input
                  type="datetime-local"
                  value={selectedSlot.start.toISOString().slice(0, 16)}
                  className={rtl.textAlign()}
                />
              </div>
            )}

            <div className={rtl.cn(
              "flex justify-end gap-2",
              rtl.flex()
            )}>
              <Button variant="outline" onClick={() => setShowQuickCreate(false)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={() => {
                // Handle quick create
                setShowQuickCreate(false);
                toast.success(language === 'ar' ? 'تم إنشاء المنشور بنجاح' : 'Post created successfully');
              }}>
                {text.quickCreate.create}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Post Details Dialog */}
      {selectedPost && (
        <Dialog open={!!selectedPost} onOpenChange={() => setSelectedPost(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className={rtl.cn(
                "flex items-center justify-between",
                rtl.flex(),
                rtl.textAlign()
              )}>
                <span>{selectedPost.title}</span>
                <Badge variant={
                  selectedPost.status === 'published' ? 'default' :
                  selectedPost.status === 'scheduled' ? 'secondary' :
                  selectedPost.status === 'failed' ? 'destructive' : 'outline'
                }>
                  {text.status[selectedPost.status]}
                </Badge>
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div className={rtl.textAlign()}>
                <Label>{language === 'ar' ? 'المحتوى' : 'Content'}</Label>
                <p className="text-sm text-gray-600 mt-1">{selectedPost.content}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className={rtl.textAlign()}>
                  <Label>{language === 'ar' ? 'التاريخ والوقت' : 'Date & Time'}</Label>
                  <p className="text-sm text-gray-600 mt-1">
                    {rtl.formatDateTime(selectedPost.start)}
                  </p>
                </div>

                <div className={rtl.textAlign()}>
                  <Label>{language === 'ar' ? 'المنصات' : 'Platforms'}</Label>
                  <div className={rtl.cn(
                    "flex gap-2 mt-1",
                    rtl.flex()
                  )}>
                    {selectedPost.platforms.map((platform, index) => (
                      <Badge key={index} variant="outline">
                        {text.platforms[platform as keyof typeof text.platforms]}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {selectedPost.analytics && (
                <div>
                  <Label>{text.analytics.engagement}</Label>
                  <div className="grid grid-cols-4 gap-4 mt-2">
                    <div className="text-center">
                      <p className="text-lg font-bold">{rtl.formatCompactNumber(selectedPost.analytics.views)}</p>
                      <p className="text-xs text-gray-600">{text.analytics.views}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-bold">{rtl.formatCompactNumber(selectedPost.analytics.likes)}</p>
                      <p className="text-xs text-gray-600">{text.analytics.likes}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-bold">{rtl.formatCompactNumber(selectedPost.analytics.comments)}</p>
                      <p className="text-xs text-gray-600">{text.analytics.comments}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-bold">{rtl.formatCompactNumber(selectedPost.analytics.shares)}</p>
                      <p className="text-xs text-gray-600">{text.analytics.shares}</p>
                    </div>
                  </div>
                </div>
              )}

              <div className={rtl.cn(
                "flex justify-end gap-2",
                rtl.flex()
              )}>
                <Button
                  variant="outline"
                  onClick={() => {
                    onPostEdit?.(selectedPost);
                    setSelectedPost(null);
                  }}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  {text.actions.edit}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    onPostDuplicate?.(selectedPost);
                    setSelectedPost(null);
                  }}
                >
                  <Copy className="w-4 h-4 mr-2" />
                  {text.actions.duplicate}
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    onPostDelete?.(selectedPost.id);
                    setSelectedPost(null);
                  }}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  {text.actions.delete}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg flex items-center gap-3">
            <RefreshCw className="w-5 h-5 animate-spin" />
            <span>{language === 'ar' ? 'جاري التحديث...' : 'Updating...'}</span>
          </div>
        </div>
      )}
    </div>
  );
}
