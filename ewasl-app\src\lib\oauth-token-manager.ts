import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const FACEBOOK_APP_ID = process.env.FACEBOOK_APP_ID || '1366325774493759';
const FACEBOOK_APP_SECRET = process.env.FACEBOOK_APP_SECRET;

interface TokenRefreshResult {
  success: boolean;
  new_token?: string;
  expires_at?: Date;
  error?: string;
  error_code?: string;
}

interface TokenValidationResult {
  valid: boolean;
  expires_at?: Date;
  user_id?: string;
  error?: string;
  needs_refresh?: boolean;
}

/**
 * Validate Facebook access token and check expiration
 */
export async function validateFacebookToken(accessToken: string): Promise<TokenValidationResult> {
  try {
    console.log('🔍 [Token Manager] Validating Facebook token...');

    // Use debug_token endpoint to get detailed token info
    const debugResponse = await fetch(
      `https://graph.facebook.com/debug_token?input_token=${accessToken}&access_token=${accessToken}`
    );
    
    const debugData = await debugResponse.json();

    if (!debugResponse.ok || debugData.error) {
      console.error('❌ [Token Manager] Token validation failed:', debugData.error);
      return {
        valid: false,
        error: debugData.error?.message || 'رمز الوصول غير صالح',
        needs_refresh: true
      };
    }

    const tokenInfo = debugData.data;
    const expiresAt = tokenInfo.expires_at ? new Date(tokenInfo.expires_at * 1000) : undefined;
    const now = new Date();

    // Check if token is expired or expires soon (within 1 hour)
    const needsRefresh = expiresAt && (expiresAt.getTime() - now.getTime()) < (60 * 60 * 1000);

    console.log(`✅ [Token Manager] Token valid, expires: ${expiresAt?.toISOString() || 'never'}, needs refresh: ${needsRefresh}`);

    return {
      valid: tokenInfo.is_valid,
      expires_at: expiresAt,
      user_id: tokenInfo.user_id,
      needs_refresh: needsRefresh || false
    };
  } catch (error) {
    console.error('❌ [Token Manager] Token validation error:', error);
    return {
      valid: false,
      error: `خطأ في التحقق من الرمز: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`,
      needs_refresh: true
    };
  }
}

/**
 * Refresh Facebook access token using long-lived token exchange
 */
export async function refreshFacebookToken(currentToken: string): Promise<TokenRefreshResult> {
  try {
    console.log('🔄 [Token Manager] Attempting to refresh Facebook token...');

    if (!FACEBOOK_APP_SECRET) {
      console.error('❌ [Token Manager] Facebook App Secret not configured');
      return {
        success: false,
        error: 'إعدادات التطبيق غير مكتملة - App Secret مفقود',
        error_code: 'MISSING_APP_SECRET'
      };
    }

    // Exchange short-lived token for long-lived token
    const refreshUrl = new URL('https://graph.facebook.com/oauth/access_token');
    refreshUrl.searchParams.set('grant_type', 'fb_exchange_token');
    refreshUrl.searchParams.set('client_id', FACEBOOK_APP_ID);
    refreshUrl.searchParams.set('client_secret', FACEBOOK_APP_SECRET);
    refreshUrl.searchParams.set('fb_exchange_token', currentToken);

    const response = await fetch(refreshUrl.toString());
    const data = await response.json();

    if (!response.ok || data.error) {
      console.error('❌ [Token Manager] Token refresh failed:', data.error);
      return {
        success: false,
        error: data.error?.message || 'فشل في تحديث رمز الوصول',
        error_code: data.error?.code || 'REFRESH_FAILED'
      };
    }

    // Calculate expiration time (Facebook long-lived tokens typically last 60 days)
    const expiresIn = data.expires_in || (60 * 24 * 60 * 60); // 60 days in seconds
    const expiresAt = new Date(Date.now() + (expiresIn * 1000));

    console.log(`✅ [Token Manager] Token refreshed successfully, expires: ${expiresAt.toISOString()}`);

    return {
      success: true,
      new_token: data.access_token,
      expires_at: expiresAt
    };
  } catch (error) {
    console.error('❌ [Token Manager] Token refresh error:', error);
    return {
      success: false,
      error: `خطأ في تحديث الرمز: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`,
      error_code: 'REFRESH_ERROR'
    };
  }
}

/**
 * Update social account with new token information
 */
export async function updateAccountToken(
  accountId: string,
  newToken: string,
  expiresAt?: Date
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`🔄 [Token Manager] Updating token for account: ${accountId}`);

    const updateData: any = {
      access_token: newToken,
      updated_at: new Date().toISOString(),
      status: 'CONNECTED'
    };

    if (expiresAt) {
      updateData.token_expires_at = expiresAt.toISOString();
    }

    const { error } = await supabase
      .from('social_accounts')
      .update(updateData)
      .eq('account_id', accountId);

    if (error) {
      console.error('❌ [Token Manager] Database update failed:', error);
      return {
        success: false,
        error: `خطأ في تحديث قاعدة البيانات: ${error.message}`
      };
    }

    console.log(`✅ [Token Manager] Token updated successfully for account: ${accountId}`);
    return { success: true };
  } catch (error) {
    console.error('❌ [Token Manager] Update error:', error);
    return {
      success: false,
      error: `خطأ في تحديث الرمز: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
    };
  }
}

/**
 * Comprehensive token management for a social account
 * Validates token, refreshes if needed, and updates database
 */
export async function ensureValidToken(accountId: string, currentToken: string): Promise<{
  success: boolean;
  token: string;
  refreshed: boolean;
  error?: string;
}> {
  try {
    console.log(`🔍 [Token Manager] Ensuring valid token for account: ${accountId}`);

    // First, validate the current token
    const validation = await validateFacebookToken(currentToken);

    if (validation.valid && !validation.needs_refresh) {
      console.log(`✅ [Token Manager] Current token is valid and fresh`);
      return {
        success: true,
        token: currentToken,
        refreshed: false
      };
    }

    // Token needs refresh or is invalid
    console.log(`🔄 [Token Manager] Token needs refresh: valid=${validation.valid}, needs_refresh=${validation.needs_refresh}`);

    const refreshResult = await refreshFacebookToken(currentToken);

    if (!refreshResult.success) {
      console.error(`❌ [Token Manager] Failed to refresh token: ${refreshResult.error}`);
      
      // Mark account as having token issues
      await supabase
        .from('social_accounts')
        .update({
          status: 'TOKEN_EXPIRED',
          updated_at: new Date().toISOString()
        })
        .eq('account_id', accountId);

      return {
        success: false,
        token: currentToken,
        refreshed: false,
        error: refreshResult.error
      };
    }

    // Update database with new token
    const updateResult = await updateAccountToken(
      accountId,
      refreshResult.new_token!,
      refreshResult.expires_at
    );

    if (!updateResult.success) {
      console.error(`❌ [Token Manager] Failed to update database: ${updateResult.error}`);
      return {
        success: false,
        token: currentToken,
        refreshed: false,
        error: updateResult.error
      };
    }

    console.log(`✅ [Token Manager] Token successfully refreshed and updated for account: ${accountId}`);

    return {
      success: true,
      token: refreshResult.new_token!,
      refreshed: true
    };
  } catch (error) {
    console.error('❌ [Token Manager] Unexpected error in ensureValidToken:', error);
    return {
      success: false,
      token: currentToken,
      refreshed: false,
      error: `خطأ غير متوقع: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
    };
  }
}

/**
 * Batch token validation and refresh for multiple accounts
 */
export async function refreshAllExpiredTokens(): Promise<{
  success: boolean;
  processed: number;
  refreshed: number;
  failed: number;
  results: Array<{
    account_id: string;
    account_name: string;
    success: boolean;
    refreshed: boolean;
    error?: string;
  }>;
}> {
  try {
    console.log('🔄 [Token Manager] Starting batch token refresh...');

    // Get all Facebook accounts
    const { data: accounts, error } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('platform', 'FACEBOOK')
      .in('status', ['CONNECTED', 'TOKEN_EXPIRED']);

    if (error) {
      console.error('❌ [Token Manager] Failed to fetch accounts:', error);
      return {
        success: false,
        processed: 0,
        refreshed: 0,
        failed: 0,
        results: []
      };
    }

    const results = [];
    let refreshed = 0;
    let failed = 0;

    for (const account of accounts || []) {
      console.log(`🔄 [Token Manager] Processing account: ${account.account_name || account.account_id}`);

      const result = await ensureValidToken(account.account_id, account.access_token);
      
      results.push({
        account_id: account.account_id,
        account_name: account.account_name || 'غير محدد',
        success: result.success,
        refreshed: result.refreshed,
        error: result.error
      });

      if (result.success && result.refreshed) {
        refreshed++;
      } else if (!result.success) {
        failed++;
      }
    }

    console.log(`✅ [Token Manager] Batch refresh completed: ${refreshed} refreshed, ${failed} failed`);

    return {
      success: true,
      processed: accounts?.length || 0,
      refreshed,
      failed,
      results
    };
  } catch (error) {
    console.error('❌ [Token Manager] Batch refresh error:', error);
    return {
      success: false,
      processed: 0,
      refreshed: 0,
      failed: 0,
      results: []
    };
  }
}
