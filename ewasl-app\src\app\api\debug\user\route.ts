import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

export async function GET(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    
    // Get social accounts for this user
    const { data: socialAccounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id);
    
    // Get social accounts with platform filter
    const { data: facebookAccounts, error: fbError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'FACEBOOK');
    
    // Test posts table structure
    const { data: testPost, error: testPostError } = await supabase
      .from('posts')
      .select('*')
      .limit(1);

    // Test post_social_accounts table structure
    const { data: testPostSocialAccounts, error: testPostSocialAccountsError } = await supabase
      .from('post_social_accounts')
      .select('*')
      .limit(1);

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email
      },
      socialAccounts: socialAccounts || [],
      facebookAccounts: facebookAccounts || [],
      accountsError,
      fbError,
      testPost: testPost || [],
      testPostError,
      testPostSocialAccounts: testPostSocialAccounts || [],
      testPostSocialAccountsError,
      debug: {
        totalAccounts: socialAccounts?.length || 0,
        activeFacebookAccounts: facebookAccounts?.length || 0,
        postsTableTest: testPostError ? 'ERROR' : 'OK',
        postSocialAccountsTableTest: testPostSocialAccountsError ? 'ERROR' : 'OK'
      }
    });
    
  } catch (error) {
    console.error('Debug API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
