import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

/**
 * Account Mapping Verification Diagnostic Endpoint
 * Verifies that platform selection maps to actual connected accounts
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Get all connected social accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('is_active', true)
      .order('platform', { ascending: true });

    if (accountsError) {
      return NextResponse.json({
        success: false,
        error: 'فشل في جلب الحسابات المتصلة',
        details: accountsError.message,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }

    if (!accounts || accounts.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'لا توجد حسابات متصلة للفحص',
        accounts: [],
        mapping_status: 'no_accounts',
        timestamp: new Date().toISOString()
      });
    }

    const results = [];
    const platformSummary = {
      facebook: { count: 0, working: 0, issues: 0 },
      instagram: { count: 0, working: 0, issues: 0 },
      total_issues: 0
    };

    for (const account of accounts) {
      const result = {
        account_id: account.id,
        platform: account.platform,
        platform_account_id: account.platform_account_id,
        account_name: account.account_name,
        account_type: account.account_type,
        mapping_status: 'unknown',
        api_accessible: false,
        pages_accessible: false,
        instagram_accessible: false,
        can_publish: false,
        issues: [],
        error: null
      };

      // Update platform summary
      if (platformSummary[account.platform as keyof typeof platformSummary]) {
        platformSummary[account.platform as keyof typeof platformSummary].count++;
      }

      try {
        if (account.platform === 'facebook') {
          // Test Facebook account accessibility
          const meResponse = await fetch(
            `https://graph.facebook.com/me?access_token=${account.access_token}`
          );

          if (meResponse.ok) {
            const meData = await meResponse.json();
            result.api_accessible = true;
            
            // Verify account ID matches
            if (meData.id !== account.platform_account_id) {
              result.issues.push('معرف الحساب لا يطابق البيانات المحفوظة');
              platformSummary.facebook.issues++;
              platformSummary.total_issues++;
            }

            // Test Pages access
            const pagesResponse = await fetch(
              `https://graph.facebook.com/me/accounts?access_token=${account.access_token}`
            );

            if (pagesResponse.ok) {
              const pagesData = await pagesResponse.json();
              result.pages_accessible = pagesData.data && pagesData.data.length > 0;
              
              if (!result.pages_accessible) {
                result.issues.push('لا يمكن الوصول إلى صفحات فيسبوك');
              }

              // Test Instagram access for each page
              if (pagesData.data && pagesData.data.length > 0) {
                for (const page of pagesData.data) {
                  try {
                    const instagramResponse = await fetch(
                      `https://graph.facebook.com/${page.id}?fields=instagram_business_account&access_token=${page.access_token}`
                    );
                    
                    if (instagramResponse.ok) {
                      const instagramData = await instagramResponse.json();
                      if (instagramData.instagram_business_account) {
                        result.instagram_accessible = true;
                        break;
                      }
                    }
                  } catch (instagramError) {
                    // Continue checking other pages
                  }
                }
              }

              // Test publishing capability
              result.can_publish = result.pages_accessible && result.api_accessible;
              
            } else {
              result.issues.push('فشل في الوصول إلى صفحات فيسبوك');
              platformSummary.facebook.issues++;
              platformSummary.total_issues++;
            }

          } else {
            const errorData = await meResponse.json();
            result.error = `فشل في الوصول إلى حساب فيسبوك: ${errorData.error?.message || 'خطأ غير معروف'}`;
            result.issues.push('لا يمكن الوصول إلى حساب فيسبوك');
            platformSummary.facebook.issues++;
            platformSummary.total_issues++;
          }

          // Determine overall status
          if (result.issues.length === 0 && result.api_accessible && result.can_publish) {
            result.mapping_status = 'working';
            platformSummary.facebook.working++;
          } else {
            result.mapping_status = 'issues';
          }

        } else if (account.platform === 'instagram') {
          // Test Instagram account accessibility
          // Note: Instagram accounts are typically accessed through Facebook Pages
          result.issues.push('فحص إنستغرام يتطلب ربط صفحة فيسبوك');
          result.mapping_status = 'requires_facebook_page';
          platformSummary.instagram.issues++;
          platformSummary.total_issues++;
        }

      } catch (error) {
        result.error = `خطأ في فحص الحساب: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`;
        result.mapping_status = 'error';
        result.issues.push('خطأ في الاتصال بالمنصة');
        
        if (account.platform === 'facebook') {
          platformSummary.facebook.issues++;
        } else if (account.platform === 'instagram') {
          platformSummary.instagram.issues++;
        }
        platformSummary.total_issues++;
      }

      results.push(result);
    }

    // Generate recommendations
    const recommendations = [];
    if (platformSummary.total_issues > 0) {
      recommendations.push('يوجد مشاكل في ربط بعض الحسابات');
      
      if (platformSummary.facebook.issues > 0) {
        recommendations.push('يرجى إعادة ربط حسابات فيسبوك المتأثرة');
        recommendations.push('تأكد من منح جميع الصلاحيات المطلوبة عند الربط');
      }
      
      if (platformSummary.instagram.issues > 0) {
        recommendations.push('تأكد من ربط حسابات إنستغرام من خلال صفحات فيسبوك');
        recommendations.push('يجب أن تكون حسابات إنستغرام من نوع Business Account');
      }
      
      recommendations.push('راجع إعدادات التطبيق في Facebook Developer Console');
    } else {
      recommendations.push('جميع الحسابات مربوطة بشكل صحيح وتعمل بشكل طبيعي');
    }

    return NextResponse.json({
      success: true,
      message: 'تم فحص ربط الحسابات بنجاح',
      summary: {
        total_accounts: accounts.length,
        working_accounts: platformSummary.facebook.working + platformSummary.instagram.working,
        accounts_with_issues: platformSummary.total_issues,
        platform_breakdown: platformSummary
      },
      accounts: results,
      recommendations,
      ui_mapping_check: {
        facebook_accounts_available: platformSummary.facebook.working > 0,
        instagram_accounts_available: platformSummary.instagram.working > 0,
        can_create_posts: platformSummary.facebook.working > 0 || platformSummary.instagram.working > 0
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Account mapping test error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم الداخلي أثناء فحص ربط الحسابات',
      details: error instanceof Error ? error.message : 'خطأ غير معروف',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}