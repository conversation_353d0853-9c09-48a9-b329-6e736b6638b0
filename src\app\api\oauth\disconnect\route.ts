import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { FacebookOAuthService } from '@/lib/oauth/facebook';

/**
 * Enhanced OAuth Account Disconnection API
 * Handles secure disconnection with platform-specific token revocation
 */

export async function POST(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    const body = await request.json();
    const { platform, accountId, userId = user.id, revokeToken = true } = body;

    if (!platform || !accountId) {
      return NextResponse.json(
        { error: 'Platform and accountId are required' },
        { status: 400 }
      );
    }

    console.log(`🔌 Disconnecting ${platform} account ${accountId} for user ${userId}`);

    // Get account details
    const { data: account, error: accountError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('user_id', userId)
      .eq('platform', platform.toUpperCase())
      .single();

    if (accountError || !account) {
      return NextResponse.json(
        { error: 'لم يتم العثور على الحساب أو ليس لديك صلاحية للوصول إليه' },
        { status: 404 }
      );
    }

    // Revoke token with platform if requested
    let tokenRevoked = false;
    if (revokeToken && account.access_token) {
      tokenRevoked = await revokeTokenWithPlatform(platform.toUpperCase(), account.access_token);
    }

    // Update account status in database
    const { error: updateError } = await supabase
      .from('social_accounts')
      .update({
        connection_status: 'disconnected',
        is_active: false,
        access_token: null, // Clear sensitive data
        refresh_token: null,
        expires_at: null,
        last_validated_at: null,
        updated_at: new Date().toISOString(),
        metadata: {
          ...account.metadata,
          disconnected_at: new Date().toISOString(),
          token_revoked: tokenRevoked
        }
      })
      .eq('id', accountId);

    if (updateError) {
      console.error('Error updating account status:', updateError);
      return NextResponse.json(
        { error: 'فشل في تحديث حالة الحساب' },
        { status: 500 }
      );
    }

    // Log disconnection activity
    await supabase
      .from('activities')
      .insert({
        user_id: userId,
        action: 'ACCOUNT_DISCONNECTED',
        details: `Disconnected ${platform} account: ${account.account_name}`,
        metadata: {
          platform,
          account_id: accountId,
          account_name: account.account_name,
          token_revoked: tokenRevoked
        },
        created_at: new Date().toISOString()
      });

    // Remove related data (posts, scheduled content, etc.)
    await cleanupAccountData(supabase, accountId, userId);

    console.log(`✅ Successfully disconnected ${platform} account ${accountId}`);

    return NextResponse.json({
      success: true,
      message: 'تم قطع اتصال الحساب بنجاح',
      platform,
      accountName: account.account_name,
      tokenRevoked,
      disconnectedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error disconnecting account:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في قطع اتصال الحساب',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET - Get disconnection status and options
 */
export async function GET(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');
    const platform = searchParams.get('platform');

    if (!accountId || !platform) {
      return NextResponse.json(
        { error: 'accountId and platform are required' },
        { status: 400 }
      );
    }

    // Get account details
    const { data: account, error: accountError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('user_id', user.id)
      .eq('platform', platform.toUpperCase())
      .single();

    if (accountError || !account) {
      return NextResponse.json(
        { error: 'لم يتم العثور على الحساب' },
        { status: 404 }
      );
    }

    // Check for dependent data
    const dependentData = await checkDependentData(supabase, accountId, user.id);

    return NextResponse.json({
      success: true,
      account: {
        id: account.id,
        platform: account.platform,
        accountName: account.account_name,
        connectionStatus: account.connection_status,
        isActive: account.is_active,
        lastValidatedAt: account.last_validated_at
      },
      dependentData,
      disconnectionOptions: {
        revokeToken: true,
        cleanupData: true,
        preserveHistory: false
      },
      warnings: generateDisconnectionWarnings(dependentData)
    });

  } catch (error) {
    console.error('❌ Error getting disconnection info:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في الحصول على معلومات قطع الاتصال',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Revoke token with the respective platform
 */
async function revokeTokenWithPlatform(platform: string, accessToken: string): Promise<boolean> {
  try {
    switch (platform) {
      case 'FACEBOOK':
      case 'INSTAGRAM':
        const facebookService = new FacebookOAuthService({
          appId: process.env.FACEBOOK_APP_ID!,
          appSecret: process.env.FACEBOOK_APP_SECRET!,
          businessId: process.env.FACEBOOK_BUSINESS_ID!,
          redirectUri: `${process.env.NEXT_PUBLIC_APP_URL}/api/facebook/callback`
        });
        return await facebookService.revokeToken(accessToken);

      case 'LINKEDIN':
        // LinkedIn token revocation
        const response = await fetch('https://www.linkedin.com/oauth/v2/revoke', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            token: accessToken,
            client_id: process.env.LINKEDIN_CLIENT_ID!,
            client_secret: process.env.LINKEDIN_CLIENT_SECRET!,
          }),
        });
        return response.ok;

      case 'TWITTER':
      case 'X':
        // Twitter/X token revocation
        const twitterResponse = await fetch('https://api.twitter.com/2/oauth2/revoke', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${Buffer.from(`${process.env.TWITTER_CLIENT_ID}:${process.env.TWITTER_CLIENT_SECRET}`).toString('base64')}`,
          },
          body: new URLSearchParams({
            token: accessToken,
            client_id: process.env.TWITTER_CLIENT_ID!,
          }),
        });
        return twitterResponse.ok;

      default:
        console.warn(`Token revocation not implemented for platform: ${platform}`);
        return false;
    }
  } catch (error) {
    console.error(`Error revoking ${platform} token:`, error);
    return false;
  }
}

/**
 * Check for data that depends on this account
 */
async function checkDependentData(supabase: any, accountId: string, userId: string) {
  try {
    // Check for posts
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .select('id, content, status, created_at')
      .eq('user_id', userId)
      .contains('social_accounts', [accountId])
      .limit(10);

    // Check for scheduled posts
    const { data: scheduledPosts, error: scheduledError } = await supabase
      .from('scheduled_posts_queue')
      .select('id, post_id, scheduled_at, status')
      .eq('user_id', userId)
      .eq('social_account_id', accountId)
      .limit(10);

    return {
      posts: posts || [],
      scheduledPosts: scheduledPosts || [],
      totalPosts: posts?.length || 0,
      totalScheduledPosts: scheduledPosts?.length || 0
    };
  } catch (error) {
    console.error('Error checking dependent data:', error);
    return {
      posts: [],
      scheduledPosts: [],
      totalPosts: 0,
      totalScheduledPosts: 0
    };
  }
}

/**
 * Generate warnings for disconnection
 */
function generateDisconnectionWarnings(dependentData: any): string[] {
  const warnings: string[] = [];

  if (dependentData.totalPosts > 0) {
    warnings.push(`سيتم حذف ${dependentData.totalPosts} منشور مرتبط بهذا الحساب`);
  }

  if (dependentData.totalScheduledPosts > 0) {
    warnings.push(`سيتم إلغاء ${dependentData.totalScheduledPosts} منشور مجدول`);
  }

  if (warnings.length === 0) {
    warnings.push('لا توجد بيانات مرتبطة بهذا الحساب');
  }

  return warnings;
}

/**
 * Clean up account-related data
 */
async function cleanupAccountData(supabase: any, accountId: string, userId: string) {
  try {
    // Cancel scheduled posts
    await supabase
      .from('scheduled_posts_queue')
      .update({
        status: 'CANCELLED',
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('social_account_id', accountId);

    // Update posts to remove this social account
    const { data: posts } = await supabase
      .from('posts')
      .select('id, social_accounts')
      .eq('user_id', userId)
      .contains('social_accounts', [accountId]);

    for (const post of posts || []) {
      const updatedSocialAccounts = (post.social_accounts || []).filter((id: string) => id !== accountId);

      await supabase
        .from('posts')
        .update({
          social_accounts: updatedSocialAccounts,
          updated_at: new Date().toISOString()
        })
        .eq('id', post.id);
    }

    console.log(`✅ Cleaned up data for account ${accountId}`);
  } catch (error) {
    console.error('Error cleaning up account data:', error);
  }
}
