import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';
import { createErrorResponse } from '@/lib/auth/api-auth';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Comprehensive Instagram Token Debugging Endpoint
export async function POST(request: NextRequest) {
  console.log('🔍 Instagram Token Debug - Started');
  
  try {
    // Use service role client to bypass RLS and access all data
    const supabase = createServiceRoleClient();
    
    const debugResults = {
      timestamp: new Date().toISOString(),
      phase: 'INSTAGRAM_TOKEN_DEBUG',
      tests: []
    };
    
    // Test 1: Query stored Instagram tokens directly from database
    console.log('📋 Test 1: Query Stored Instagram Tokens');
    
    const { data: socialAccounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('platform', 'INSTAGRAM');
    
    if (accountsError) {
      console.error('❌ Error fetching Instagram accounts:', accountsError);
      return createErrorResponse('فشل في جلب حسابات Instagram', 500, accountsError);
    }
    
    const accountsTest = {
      testName: 'Query Stored Instagram Tokens',
      success: true,
      accounts: socialAccounts || [],
      count: socialAccounts?.length || 0
    };
    
    debugResults.tests.push(accountsTest);
    
    console.log(`📊 Found ${accountsTest.count} Instagram accounts in database`);
    
    if (accountsTest.count === 0) {
      return NextResponse.json({
        success: true,
        message: 'لا توجد حسابات Instagram في قاعدة البيانات',
        results: debugResults,
        recommendation: 'يجب ربط حساب Instagram أولاً'
      });
    }
    
    // Test 2: Validate each Instagram token
    console.log('🔑 Test 2: Validate Instagram Tokens');
    
    const tokenValidationResults = [];
    
    for (const account of socialAccounts) {
      console.log(`🔍 Testing account: ${account.account_name} (${account.account_id})`);
      
      const validation = {
        account_id: account.account_id,
        account_name: account.account_name,
        platform: account.platform,
        connection_status: account.connection_status,
        created_at: account.created_at,
        updated_at: account.updated_at,
        token_tests: []
      };
      
      // Test 2a: Basic token validation
      try {
        // Generate appsecret_proof for secure API calls
        const crypto = require('crypto');
        const appSecret = process.env.FACEBOOK_APP_SECRET;
        const appsecret_proof = crypto.createHmac('sha256', appSecret).update(account.access_token).digest('hex');
<<<<<<< HEAD

        const tokenResponse = await fetch(
          `https://graph.facebook.com/v18.0/me?access_token=${account.access_token}&fields=id,name&appsecret_proof=${appsecret_proof}`
        );

=======
        
        const tokenResponse = await fetch(
          `https://graph.facebook.com/v18.0/me?access_token=${account.access_token}&fields=id,name&appsecret_proof=${appsecret_proof}`
        );
        
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
        const tokenData = await tokenResponse.json();
        
        validation.token_tests.push({
          test: 'Basic Token Validation',
          success: tokenResponse.ok,
          status: tokenResponse.status,
          data: tokenData,
          error: tokenData.error?.message || null
        });
        
        console.log(`${tokenResponse.ok ? '✅' : '❌'} Basic token validation: ${tokenResponse.ok ? 'SUCCESS' : tokenData.error?.message}`);
        
      } catch (error) {
        validation.token_tests.push({
          test: 'Basic Token Validation',
          success: false,
          error: error.message
        });
        console.error(`❌ Basic token validation failed:`, error);
      }
      
      // Test 2b: Get token permissions
      try {
        // Generate appsecret_proof for secure API calls
        const crypto = require('crypto');
        const appSecret = process.env.FACEBOOK_APP_SECRET;
        const appsecret_proof = crypto.createHmac('sha256', appSecret).update(account.access_token).digest('hex');
<<<<<<< HEAD

        const permissionsResponse = await fetch(
          `https://graph.facebook.com/v18.0/me/permissions?access_token=${account.access_token}&appsecret_proof=${appsecret_proof}`
        );

=======
        
        const permissionsResponse = await fetch(
          `https://graph.facebook.com/v18.0/me/permissions?access_token=${account.access_token}&appsecret_proof=${appsecret_proof}`
        );
        
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
        const permissionsData = await permissionsResponse.json();
        
        validation.token_tests.push({
          test: 'Token Permissions',
          success: permissionsResponse.ok,
          status: permissionsResponse.status,
          data: permissionsData,
          permissions: permissionsData.data?.filter(p => p.status === 'granted').map(p => p.permission) || [],
          error: permissionsData.error?.message || null
        });
        
        const grantedPermissions = permissionsData.data?.filter(p => p.status === 'granted').map(p => p.permission) || [];
        console.log(`${permissionsResponse.ok ? '✅' : '❌'} Token permissions: ${grantedPermissions.join(', ')}`);
        
      } catch (error) {
        validation.token_tests.push({
          test: 'Token Permissions',
          success: false,
          error: error.message
        });
        console.error(`❌ Token permissions test failed:`, error);
      }
      
      // Test 2c: Get Facebook Pages (for Instagram Business accounts)
      try {
        // Generate appsecret_proof for secure API calls
        const crypto = require('crypto');
        const appSecret = process.env.FACEBOOK_APP_SECRET;
        const appsecret_proof = crypto.createHmac('sha256', appSecret).update(account.access_token).digest('hex');
<<<<<<< HEAD

        const pagesResponse = await fetch(
          `https://graph.facebook.com/v18.0/me/accounts?access_token=${account.access_token}&fields=id,name,access_token,instagram_business_account&appsecret_proof=${appsecret_proof}`
        );

=======
        
        const pagesResponse = await fetch(
          `https://graph.facebook.com/v18.0/me/accounts?access_token=${account.access_token}&fields=id,name,access_token,instagram_business_account&appsecret_proof=${appsecret_proof}`
        );
        
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
        const pagesData = await pagesResponse.json();
        
        validation.token_tests.push({
          test: 'Facebook Pages Access',
          success: pagesResponse.ok,
          status: pagesResponse.status,
          data: pagesData,
          pages_count: pagesData.data?.length || 0,
          instagram_pages: pagesData.data?.filter(p => p.instagram_business_account) || [],
          error: pagesData.error?.message || null
        });
        
        const instagramPages = pagesData.data?.filter(p => p.instagram_business_account) || [];
        console.log(`${pagesResponse.ok ? '✅' : '❌'} Facebook Pages: ${pagesData.data?.length || 0} total, ${instagramPages.length} with Instagram`);
        
      } catch (error) {
        validation.token_tests.push({
          test: 'Facebook Pages Access',
          success: false,
          error: error.message
        });
        console.error(`❌ Facebook Pages test failed:`, error);
      }
      
      // Test 2d: Test Instagram API access
      if (account.metadata?.instagramAccountId || account.page_access_token) {
        try {
          const instagramAccountId = account.metadata?.instagramAccountId || account.account_id;
          const accessToken = account.page_access_token || account.access_token;
<<<<<<< HEAD

=======
          
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
          // Generate appsecret_proof for secure API calls
          const crypto = require('crypto');
          const appSecret = process.env.FACEBOOK_APP_SECRET;
          const appsecret_proof = crypto.createHmac('sha256', appSecret).update(accessToken).digest('hex');
<<<<<<< HEAD

          const instagramResponse = await fetch(
            `https://graph.facebook.com/v18.0/${instagramAccountId}?access_token=${accessToken}&fields=id,username,account_type,media_count&appsecret_proof=${appsecret_proof}`
          );

=======
          
          const instagramResponse = await fetch(
            `https://graph.facebook.com/v18.0/${instagramAccountId}?access_token=${accessToken}&fields=id,username,account_type,media_count&appsecret_proof=${appsecret_proof}`
          );
          
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
          const instagramData = await instagramResponse.json();
          
          validation.token_tests.push({
            test: 'Instagram Account Access',
            success: instagramResponse.ok,
            status: instagramResponse.status,
            data: instagramData,
            instagram_account_id: instagramAccountId,
            error: instagramData.error?.message || null
          });
          
          console.log(`${instagramResponse.ok ? '✅' : '❌'} Instagram Account Access: ${instagramResponse.ok ? instagramData.username : instagramData.error?.message}`);
          
        } catch (error) {
          validation.token_tests.push({
            test: 'Instagram Account Access',
            success: false,
            error: error.message
          });
          console.error(`❌ Instagram Account Access test failed:`, error);
        }
      }
      
      tokenValidationResults.push(validation);
    }
    
    debugResults.tests.push({
      testName: 'Token Validation Results',
      success: true,
      validations: tokenValidationResults
    });
    
    // Test 3: Test Instagram Publishing Capability
    console.log('📸 Test 3: Test Instagram Publishing Capability');
    
    const publishingTests = [];
    
    for (const account of socialAccounts) {
      console.log(`📤 Testing publishing capability for: ${account.account_name}`);
      
      try {
        // Test creating a media container (without actually publishing)
        const instagramAccountId = account.metadata?.instagramAccountId || account.account_id;
        const accessToken = account.page_access_token || account.access_token;
        
        // Test with a simple image URL
        const testImageUrl = 'https://images.unsplash.com/photo-*************-80b023f02d71?w=800&h=800&fit=crop';
        const testCaption = '🧪 Test caption for Instagram API validation';
<<<<<<< HEAD

=======
        
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
        // Generate appsecret_proof for secure API calls
        const crypto = require('crypto');
        const appSecret = process.env.FACEBOOK_APP_SECRET;
        const appsecret_proof = crypto.createHmac('sha256', appSecret).update(accessToken).digest('hex');
<<<<<<< HEAD

=======
        
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
        const containerData = {
          image_url: testImageUrl,
          caption: testCaption,
          access_token: accessToken,
          appsecret_proof: appsecret_proof
        };
        
        // Note: This creates a container but doesn't publish it
        const containerResponse = await fetch(
          `https://graph.facebook.com/v18.0/${instagramAccountId}/media`,
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(containerData),
          }
        );
        
        const containerResult = await containerResponse.json();
        
        publishingTests.push({
          account_id: account.account_id,
          account_name: account.account_name,
          test: 'Create Media Container',
          success: containerResponse.ok,
          status: containerResponse.status,
          data: containerResult,
          container_id: containerResult.id || null,
          error: containerResult.error?.message || null
        });
        
        console.log(`${containerResponse.ok ? '✅' : '❌'} Media Container Creation: ${containerResponse.ok ? 'SUCCESS' : containerResult.error?.message}`);
        
      } catch (error) {
        publishingTests.push({
          account_id: account.account_id,
          account_name: account.account_name,
          test: 'Create Media Container',
          success: false,
          error: error.message
        });
        console.error(`❌ Publishing test failed for ${account.account_name}:`, error);
      }
    }
    
    debugResults.tests.push({
      testName: 'Instagram Publishing Tests',
      success: true,
      publishing_tests: publishingTests
    });
    
    // Analysis and Recommendations
    const analysis = {
      total_accounts: socialAccounts.length,
      valid_tokens: tokenValidationResults.filter(v => v.token_tests.some(t => t.test === 'Basic Token Validation' && t.success)).length,
      accounts_with_permissions: tokenValidationResults.filter(v => v.token_tests.some(t => t.test === 'Token Permissions' && t.success && t.permissions.length > 0)).length,
      accounts_with_pages: tokenValidationResults.filter(v => v.token_tests.some(t => t.test === 'Facebook Pages Access' && t.success && t.pages_count > 0)).length,
      accounts_with_instagram: tokenValidationResults.filter(v => v.token_tests.some(t => t.test === 'Instagram Account Access' && t.success)).length,
      publishing_capable: publishingTests.filter(t => t.success).length
    };
    
    const recommendations = [];
    
    if (analysis.valid_tokens === 0) {
      recommendations.push('جميع الرموز المميزة غير صالحة - يجب إعادة المصادقة');
    }
    
    if (analysis.accounts_with_permissions < analysis.total_accounts) {
      recommendations.push('بعض الحسابات تفتقر إلى الصلاحيات المطلوبة');
    }
    
    if (analysis.accounts_with_instagram === 0) {
      recommendations.push('لا يمكن الوصول إلى حسابات Instagram - تحقق من ربط الصفحات');
    }
    
    if (analysis.publishing_capable === 0) {
      recommendations.push('لا يمكن النشر على Instagram - تحقق من صلاحيات النشر');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('جميع الاختبارات نجحت - النظام جاهز للنشر على Instagram');
    }
    
    console.log('📊 Debug Analysis Complete');
    console.log(`   Total Accounts: ${analysis.total_accounts}`);
    console.log(`   Valid Tokens: ${analysis.valid_tokens}`);
    console.log(`   Publishing Capable: ${analysis.publishing_capable}`);
    
    return NextResponse.json({
      success: true,
      message: `تم فحص ${analysis.total_accounts} حساب Instagram`,
      results: debugResults,
      analysis,
      recommendations,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Instagram token debug error:', error);
    return createErrorResponse('خطأ في فحص رموز Instagram', 500, error);
  }
<<<<<<< HEAD
}
=======
}
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
