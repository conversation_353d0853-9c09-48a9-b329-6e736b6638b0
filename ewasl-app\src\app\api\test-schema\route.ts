import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createClient as createServiceClient } from '@supabase/supabase-js'

// Service role client for admin operations
const supabaseService = createServiceClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // Test 1: Check if required tables exist
    console.log('📋 Checking required tables...')
    const requiredTables = [
      'posts',
      'post_social_accounts',
      'scheduled_posts_queue',
      'media_files',
      'social_accounts',
      'oauth_states'
    ]
    
    const tableResults = {}
    
    for (const table of requiredTables) {
      try {
        // Check if table exists by querying a single row
        const { count, error } = await supabaseService
          .from(table)
          .select('*', { count: 'exact', head: true })
        
        tableResults[table] = {
          exists: !error,
          count: count || 0,
          error: error?.message
        }
        
        console.log(`${error ? '❌' : '✅'} Table ${table}: ${error ? 'Error' : 'OK'} (${count || 0} rows)`)
      } catch (error) {
        console.error(`❌ Error checking table ${table}:`, error)
        tableResults[table] = {
          exists: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }

    // Test 2: Check table columns using information_schema
    console.log('🔍 Checking table columns...')
    const columnResults = {}

    for (const table of requiredTables) {
      try {
        const { data, error } = await supabaseService
          .from('information_schema.columns')
          .select('column_name, data_type, is_nullable, column_default')
          .eq('table_name', table)
          .eq('table_schema', 'public')

        if (error) {
          console.error(`❌ Failed to get ${table} columns:`, error)
          columnResults[table] = { error: error.message }
        } else {
          console.log(`✅ ${table} columns retrieved: ${data?.length || 0} columns`)
          columnResults[table] = {
            columns: data?.map(col => ({
              name: col.column_name,
              type: col.data_type,
              nullable: col.is_nullable === 'YES',
              default: col.column_default
            })) || []
          }
        }
      } catch (error) {
        console.error(`❌ Error checking ${table} columns:`, error)
        columnResults[table] = {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }

    // Test 3: Check foreign key relationships
    console.log('🔗 Checking foreign key relationships...')
    let foreignKeys = {}

    try {
      const { data, error } = await supabaseService
        .from('information_schema.table_constraints')
        .select('table_name, constraint_name, constraint_type')
        .eq('table_schema', 'public')
        .eq('constraint_type', 'FOREIGN KEY')

      if (error) {
        console.error('❌ Failed to get foreign keys:', error)
      } else {
        console.log(`✅ Found ${data?.length || 0} foreign key constraints`)
        foreignKeys = data?.reduce((acc, fk) => {
          if (!acc[fk.table_name]) acc[fk.table_name] = []
          acc[fk.table_name].push(fk.constraint_name)
          return acc
        }, {}) || {}
      }
    } catch (error) {
      console.error('❌ Error checking foreign keys:', error)
    }

    // Test 4: Check for missing required columns
    console.log('🔍 Checking for missing required columns...')
    const requiredColumns = {
      'posts': ['id', 'user_id', 'content', 'status', 'created_at'],
      'post_social_accounts': ['id', 'post_id', 'social_account_id', 'platform_post_id', 'status'],
      'scheduled_posts_queue': ['id', 'post_id', 'scheduled_at', 'status'],
      'media_files': ['id', 'user_id', 'file_name', 'public_url'],
      'social_accounts': ['id', 'user_id', 'platform', 'account_id', 'access_token']
    }

    const missingColumns = {}

    for (const table in requiredColumns) {
      if (columnResults[table]?.columns) {
        const tableColumns = columnResults[table].columns.map(c => c.name)
        const missing = requiredColumns[table].filter(col => !tableColumns.includes(col))

        if (missing.length > 0) {
          console.log(`❌ Table ${table} is missing columns: ${missing.join(', ')}`)
          missingColumns[table] = missing
        } else {
          console.log(`✅ Table ${table} has all required columns`)
        }
      }
    }

    return NextResponse.json({
      success: true,
      userId: user.id,
      tests: {
        tables: tableResults,
        columns: columnResults,
        foreignKeys,
        missingColumns,
        summary: {
          tablesExist: Object.values(tableResults).every(t => t.exists),
          hasRequiredColumns: Object.keys(missingColumns).length === 0,
          hasForeignKeys: Object.keys(foreignKeys).length > 0
        }
      }
    })

  } catch (error) {
    console.error('Schema test error:', error)
    return NextResponse.json({
      error: 'Schema test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}
