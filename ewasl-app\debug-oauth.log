OAuth Debug Log - Facebook Integration
=====================================
This file will capture detailed debugging information for the Facebook OAuth callback process.
[2025-07-10T19:10:48.512Z] 🔍 DEBUGGING: Exchanging authorization code for access token...

[2025-07-10T19:10:49.126Z] 🔍 DEBUGGING: Token exchange response:
{
  "hasAccessToken": true,
  "tokenType": "bearer"
}

[2025-07-10T19:10:49.128Z] 🔍 DEBUGGING: Getting long-lived access token...

[2025-07-10T19:10:49.317Z] 🔍 DEBUGGING: Long-lived token response:
{
  "hasAccessToken": true,
  "tokenType": "bearer"
}

[2025-07-10T19:10:49.319Z] 🔍 DEBUGGING: Validating token and fetching user info...

[2025-07-10T19:10:49.767Z] 🔍 DEBUGGING: User info retrieved:
{
  "id": "****************",
  "name": "<PERSON>",
  "hasEmail": false
}

[2025-07-10T19:10:49.768Z] 🔍 DEBUGGING: Fetching user accounts...

[2025-07-10T19:10:49.769Z] 🔍 DEBUGGING: Using access token:
"EAATaqoJvqD8BPA1xm0G..."

[2025-07-10T19:10:50.248Z] 🔍 DEBUGGING: Found 2 accounts:
[
  {
    "id": "fb_***************",
    "platform": "FACEBOOK",
    "accountName": "EWasl.com",
    "accountHandle": "@ewasl.com",
    "connectionStatus": "connected",
    "accessToken": "EAATaqoJvqD8BPP7MeYplHYb9SZCrVE1RWA6jaslOyKnwr4C8xdJ83usUXDpuUP55mnZC7KCzSUpZAHdfkekccVJd7wE2kToSxtDfxrKQrPwueXepoTktnJFjADKg8IZCSLbDMxIIZBiZBSHnWS228vShtaJj7CAiHjnJsx3SF5sUSXsO6T2HjE2uy15RDYqwCfCZB72TkeavrLDtAgdrxIw",
    "refreshToken": null,
    "expiresAt": null,
    "lastValidatedAt": "2025-07-10T19:10:50.246Z",
    "metadata": {
      "pageId": "***************",
      "category": "Software",
      "tasks": [
        "ADVERTISE",
        "ANALYZE",
        "CREATE_CONTENT",
        "MESSAGING",
        "MODERATE",
        "MANAGE"
      ]
    },
    "permissions": [
      "read",
      "write"
    ],
    "profileImageUrl": "https://graph.facebook.com/***************/picture?type=large",
    "createdAt": "2025-07-10T19:10:50.246Z",
    "updatedAt": "2025-07-10T19:10:50.246Z"
  },
  {
    "id": "ig_*****************",
    "platform": "INSTAGRAM",
    "accountName": "EWasl.com",
    "accountHandle": "@ewasl.com",
    "connectionStatus": "connected",
    "accessToken": "EAATaqoJvqD8BPP7MeYplHYb9SZCrVE1RWA6jaslOyKnwr4C8xdJ83usUXDpuUP55mnZC7KCzSUpZAHdfkekccVJd7wE2kToSxtDfxrKQrPwueXepoTktnJFjADKg8IZCSLbDMxIIZBiZBSHnWS228vShtaJj7CAiHjnJsx3SF5sUSXsO6T2HjE2uy15RDYqwCfCZB72TkeavrLDtAgdrxIw",
    "refreshToken": null,
    "expiresAt": null,
    "lastValidatedAt": "2025-07-10T19:10:50.246Z",
    "metadata": {
      "instagramAccountId": "*****************",
      "connectedFacebookPageId": "***************"
    },
    "permissions": [
      "read",
      "write"
    ],
    "profileImageUrl": "https://graph.facebook.com/*****************/picture?type=large",
    "createdAt": "2025-07-10T19:10:50.246Z",
    "updatedAt": "2025-07-10T19:10:50.246Z"
  }
]

[2025-07-10T19:10:50.394Z] 🔍 DEBUGGING: Saving 2 accounts to database...

[2025-07-10T19:10:50.395Z] 🔍 DEBUGGING: ========== PROCESSING ACCOUNT 1/2 ==========

[2025-07-10T19:10:50.396Z] 🔍 DEBUGGING: Account details:
{
  "id": "fb_***************",
  "name": "EWasl.com",
  "platform": "FACEBOOK",
  "hasAccessToken": true,
  "accessTokenPreview": "EAATaqoJvqD8BPP7MeYp...",
  "metadata": {
    "pageId": "***************",
    "category": "Software",
    "tasks": [
      "ADVERTISE",
      "ANALYZE",
      "CREATE_CONTENT",
      "MESSAGING",
      "MODERATE",
      "MANAGE"
    ]
  },
  "connectionStatus": "connected",
  "accountHandle": "@ewasl.com"
}

[2025-07-10T19:10:50.400Z] 🔍 DEBUGGING: Step 1 - Starting account processing for EWasl.com...

[2025-07-10T19:10:50.401Z] 🔍 DEBUGGING: Step 2 - Fetching metrics for account fb_***************...

[2025-07-10T19:10:50.401Z] 🔍 DEBUGGING: Using metrics ID: ***************

[2025-07-10T19:10:50.402Z] 🔍 DEBUGGING: Using token: EAATaqoJvqD8BPP7MeYp...

[2025-07-10T19:10:50.403Z] 🔍 DEBUGGING: Calling getAccountMetrics...

[2025-07-10T19:10:51.003Z] 🔍 DEBUGGING: ✅ Metrics fetched successfully:
{
  "followerCount": 0,
  "engagementRate": 0,
  "postCount": 0
}

[2025-07-10T19:10:51.005Z] 🔍 DEBUGGING: Step 2 completed - Metrics ready

[2025-07-10T19:10:51.006Z] 🔍 DEBUGGING: Step 3 - Preparing account data for database...

[2025-07-10T19:10:51.007Z] 🔍 DEBUGGING: ✅ Account data prepared successfully

[2025-07-10T19:10:51.008Z] 🔍 DEBUGGING: Account data summary:
{
  "account_id": "fb_***************",
  "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
  "platform": "FACEBOOK",
  "account_name": "EWasl.com",
  "has_access_token": true,
  "connection_status": "connected"
}

[2025-07-10T19:10:51.010Z] 🔍 DEBUGGING: Full account data for database:
{
  "account_id": "fb_***************",
  "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
  "platform": "FACEBOOK",
  "account_name": "EWasl.com",
  "access_token": "EAATaqoJvqD8BPP7MeYplHYb9SZCrVE1RWA6jaslOyKnwr4C8xdJ83usUXDpuUP55mnZC7KCzSUpZAHdfkekccVJd7wE2kToSxtDfxrKQrPwueXepoTktnJFjADKg8IZCSLbDMxIIZBiZBSHnWS228vShtaJj7CAiHjnJsx3SF5sUSXsO6T2HjE2uy15RDYqwCfCZB72TkeavrLDtAgdrxIw",
  "account_handle": "@ewasl.com",
  "connection_status": "connected",
  "refresh_token": null,
  "expires_at": null,
  "last_validated_at": "2025-07-10T19:10:50.246Z",
  "metadata": {
    "pageId": "***************",
    "category": "Software",
    "tasks": [
      "ADVERTISE",
      "ANALYZE",
      "CREATE_CONTENT",
      "MESSAGING",
      "MODERATE",
      "MANAGE"
    ],
    "metrics": {
      "followerCount": 0,
      "engagementRate": 0,
      "postCount": 0
    }
  },
  "profile_image_url": "https://graph.facebook.com/***************/picture?type=large",
  "permissions": [
    "read",
    "write"
  ]
}

[2025-07-10T19:10:51.013Z] 🔍 DEBUGGING: Step 4 - Validating required fields...

[2025-07-10T19:10:51.016Z] 🔍 DEBUGGING: ✅ All required fields validated successfully

[2025-07-10T19:10:51.018Z] 🔍 DEBUGGING: Step 5 - Attempting to upsert account to database...

[2025-07-10T19:10:51.019Z] 🔍 DEBUGGING: Using service role client for database operation

[2025-07-10T19:10:51.106Z] 🔍 DEBUGGING: Database operation completed, checking results...

[2025-07-10T19:10:51.107Z] 🚨 DATABASE ERROR saving account fb_***************:
{
  "code": "42P10",
  "details": null,
  "hint": null,
  "message": "there is no unique or exclusion constraint matching the ON CONFLICT specification"
}

[2025-07-10T19:10:51.108Z] 🚨 Account data that failed:
{
  "account_id": "fb_***************",
  "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
  "platform": "FACEBOOK",
  "account_name": "EWasl.com",
  "access_token": "EAATaqoJvqD8BPP7MeYplHYb9SZCrVE1RWA6jaslOyKnwr4C8xdJ83usUXDpuUP55mnZC7KCzSUpZAHdfkekccVJd7wE2kToSxtDfxrKQrPwueXepoTktnJFjADKg8IZCSLbDMxIIZBiZBSHnWS228vShtaJj7CAiHjnJsx3SF5sUSXsO6T2HjE2uy15RDYqwCfCZB72TkeavrLDtAgdrxIw",
  "account_handle": "@ewasl.com",
  "connection_status": "connected",
  "refresh_token": null,
  "expires_at": null,
  "last_validated_at": "2025-07-10T19:10:50.246Z",
  "metadata": {
    "pageId": "***************",
    "category": "Software",
    "tasks": [
      "ADVERTISE",
      "ANALYZE",
      "CREATE_CONTENT",
      "MESSAGING",
      "MODERATE",
      "MANAGE"
    ],
    "metrics": {
      "followerCount": 0,
      "engagementRate": 0,
      "postCount": 0
    }
  },
  "profile_image_url": "https://graph.facebook.com/***************/picture?type=large",
  "permissions": [
    "read",
    "write"
  ]
}

[2025-07-10T19:10:51.117Z] 🚨 Error details:
{
  "code": "42P10",
  "message": "there is no unique or exclusion constraint matching the ON CONFLICT specification",
  "details": null,
  "hint": null
}

[2025-07-10T19:10:51.119Z] 🔍 DEBUGGING: ❌ Account fb_*************** failed to save

[2025-07-10T19:10:51.120Z] 🔍 DEBUGGING: ========== PROCESSING ACCOUNT 2/2 ==========

[2025-07-10T19:10:51.121Z] 🔍 DEBUGGING: Account details:
{
  "id": "ig_*****************",
  "name": "EWasl.com",
  "platform": "INSTAGRAM",
  "hasAccessToken": true,
  "accessTokenPreview": "EAATaqoJvqD8BPP7MeYp...",
  "metadata": {
    "instagramAccountId": "*****************",
    "connectedFacebookPageId": "***************"
  },
  "connectionStatus": "connected",
  "accountHandle": "@ewasl.com"
}

[2025-07-10T19:10:51.123Z] 🔍 DEBUGGING: Step 1 - Starting account processing for EWasl.com...

[2025-07-10T19:10:51.124Z] 🔍 DEBUGGING: Step 2 - Fetching metrics for account ig_*****************...

[2025-07-10T19:10:51.124Z] 🔍 DEBUGGING: Using metrics ID: *****************

[2025-07-10T19:10:51.125Z] 🔍 DEBUGGING: Using token: EAATaqoJvqD8BPP7MeYp...

[2025-07-10T19:10:51.125Z] 🔍 DEBUGGING: Calling getAccountMetrics...

[2025-07-10T19:10:51.526Z] 🔍 DEBUGGING: ✅ Metrics fetched successfully:
{
  "followerCount": 1,
  "engagementRate": 0,
  "postCount": 0
}

[2025-07-10T19:10:51.528Z] 🔍 DEBUGGING: Step 2 completed - Metrics ready

[2025-07-10T19:10:51.528Z] 🔍 DEBUGGING: Step 3 - Preparing account data for database...

[2025-07-10T19:10:51.529Z] 🔍 DEBUGGING: ✅ Account data prepared successfully

[2025-07-10T19:10:51.530Z] 🔍 DEBUGGING: Account data summary:
{
  "account_id": "ig_*****************",
  "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
  "platform": "INSTAGRAM",
  "account_name": "EWasl.com",
  "has_access_token": true,
  "connection_status": "connected"
}

[2025-07-10T19:10:51.533Z] 🔍 DEBUGGING: Full account data for database:
{
  "account_id": "ig_*****************",
  "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
  "platform": "INSTAGRAM",
  "account_name": "EWasl.com",
  "access_token": "EAATaqoJvqD8BPP7MeYplHYb9SZCrVE1RWA6jaslOyKnwr4C8xdJ83usUXDpuUP55mnZC7KCzSUpZAHdfkekccVJd7wE2kToSxtDfxrKQrPwueXepoTktnJFjADKg8IZCSLbDMxIIZBiZBSHnWS228vShtaJj7CAiHjnJsx3SF5sUSXsO6T2HjE2uy15RDYqwCfCZB72TkeavrLDtAgdrxIw",
  "account_handle": "@ewasl.com",
  "connection_status": "connected",
  "refresh_token": null,
  "expires_at": null,
  "last_validated_at": "2025-07-10T19:10:50.246Z",
  "metadata": {
    "instagramAccountId": "*****************",
    "connectedFacebookPageId": "***************",
    "metrics": {
      "followerCount": 1,
      "engagementRate": 0,
      "postCount": 0
    }
  },
  "profile_image_url": "https://graph.facebook.com/*****************/picture?type=large",
  "permissions": [
    "read",
    "write"
  ]
}

[2025-07-10T19:10:51.537Z] 🔍 DEBUGGING: Step 4 - Validating required fields...

[2025-07-10T19:10:51.538Z] 🔍 DEBUGGING: ✅ All required fields validated successfully

[2025-07-10T19:10:51.539Z] 🔍 DEBUGGING: Step 5 - Attempting to upsert account to database...

[2025-07-10T19:10:51.539Z] 🔍 DEBUGGING: Using service role client for database operation

[2025-07-10T19:10:51.686Z] 🔍 DEBUGGING: Database operation completed, checking results...

[2025-07-10T19:10:51.687Z] 🚨 DATABASE ERROR saving account ig_*****************:
{
  "code": "42P10",
  "details": null,
  "hint": null,
  "message": "there is no unique or exclusion constraint matching the ON CONFLICT specification"
}

[2025-07-10T19:10:51.690Z] 🚨 Account data that failed:
{
  "account_id": "ig_*****************",
  "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
  "platform": "INSTAGRAM",
  "account_name": "EWasl.com",
  "access_token": "EAATaqoJvqD8BPP7MeYplHYb9SZCrVE1RWA6jaslOyKnwr4C8xdJ83usUXDpuUP55mnZC7KCzSUpZAHdfkekccVJd7wE2kToSxtDfxrKQrPwueXepoTktnJFjADKg8IZCSLbDMxIIZBiZBSHnWS228vShtaJj7CAiHjnJsx3SF5sUSXsO6T2HjE2uy15RDYqwCfCZB72TkeavrLDtAgdrxIw",
  "account_handle": "@ewasl.com",
  "connection_status": "connected",
  "refresh_token": null,
  "expires_at": null,
  "last_validated_at": "2025-07-10T19:10:50.246Z",
  "metadata": {
    "instagramAccountId": "*****************",
    "connectedFacebookPageId": "***************",
    "metrics": {
      "followerCount": 1,
      "engagementRate": 0,
      "postCount": 0
    }
  },
  "profile_image_url": "https://graph.facebook.com/*****************/picture?type=large",
  "permissions": [
    "read",
    "write"
  ]
}

[2025-07-10T19:10:51.692Z] 🚨 Error details:
{
  "code": "42P10",
  "message": "there is no unique or exclusion constraint matching the ON CONFLICT specification",
  "details": null,
  "hint": null
}

[2025-07-10T19:10:51.694Z] 🔍 DEBUGGING: ❌ Account ig_***************** failed to save

[2025-07-10T19:10:51.694Z] 🔍 DEBUGGING: Final results - Found: 2, Saved: 0

[2025-07-10T19:10:51.695Z] 🚨 WARNING: No real Facebook accounts were saved despite being found

[2025-07-10T19:10:51.696Z] 🔍 This suggests an issue in the account processing loop

[2025-07-10T19:10:51.696Z] 🔍 DEBUGGING: Redirecting with message: OAuth completed but no accounts were saved (2 found)

[2025-07-10T19:13:42.055Z] 🔍 DEBUGGING: Exchanging authorization code for access token...

[2025-07-10T19:13:42.613Z] 🔍 DEBUGGING: Token exchange response:
{
  "hasAccessToken": true,
  "tokenType": "bearer"
}

[2025-07-10T19:13:42.615Z] 🔍 DEBUGGING: Getting long-lived access token...

[2025-07-10T19:13:42.811Z] 🔍 DEBUGGING: Long-lived token response:
{
  "hasAccessToken": true,
  "tokenType": "bearer"
}

[2025-07-10T19:13:42.814Z] 🔍 DEBUGGING: Validating token and fetching user info...

[2025-07-10T19:13:43.222Z] 🔍 DEBUGGING: User info retrieved:
{
  "id": "****************",
  "name": "Ahmed Taha",
  "hasEmail": false
}

[2025-07-10T19:13:43.224Z] 🔍 DEBUGGING: Fetching user accounts...

[2025-07-10T19:13:43.225Z] 🔍 DEBUGGING: Using access token:
"EAATaqoJvqD8BPITE0r2..."

[2025-07-10T19:13:43.675Z] 🔍 DEBUGGING: Found 2 accounts:
[
  {
    "id": "fb_***************",
    "platform": "FACEBOOK",
    "accountName": "EWasl.com",
    "accountHandle": "@ewasl.com",
    "connectionStatus": "connected",
    "accessToken": "EAATaqoJvqD8BPEwkcgBWtGNgQrZAlOwi88muuWyXxfToLSsue4X3FzD1cktgnYfOv65VpFoThXQN3Wa8h4Xpy8bQiFNyXi6qmxvQfWx1DVHvwkbXlX8TLGXSyqZCgxC1w9XLI0UMs1d1XzlONkGCQXv7EUXlt2nbB14zF3hP9bpZBW3xcBRAluyhImOY3FAF2O02tXy9ofPrfffTKz0fHMW",
    "refreshToken": null,
    "expiresAt": null,
    "lastValidatedAt": "2025-07-10T19:13:43.674Z",
    "metadata": {
      "pageId": "***************",
      "category": "Software",
      "tasks": [
        "ADVERTISE",
        "ANALYZE",
        "CREATE_CONTENT",
        "MESSAGING",
        "MODERATE",
        "MANAGE"
      ]
    },
    "permissions": [
      "read",
      "write"
    ],
    "profileImageUrl": "https://graph.facebook.com/***************/picture?type=large",
    "createdAt": "2025-07-10T19:13:43.674Z",
    "updatedAt": "2025-07-10T19:13:43.674Z"
  },
  {
    "id": "ig_*****************",
    "platform": "INSTAGRAM",
    "accountName": "EWasl.com",
    "accountHandle": "@ewasl.com",
    "connectionStatus": "connected",
    "accessToken": "EAATaqoJvqD8BPEwkcgBWtGNgQrZAlOwi88muuWyXxfToLSsue4X3FzD1cktgnYfOv65VpFoThXQN3Wa8h4Xpy8bQiFNyXi6qmxvQfWx1DVHvwkbXlX8TLGXSyqZCgxC1w9XLI0UMs1d1XzlONkGCQXv7EUXlt2nbB14zF3hP9bpZBW3xcBRAluyhImOY3FAF2O02tXy9ofPrfffTKz0fHMW",
    "refreshToken": null,
    "expiresAt": null,
    "lastValidatedAt": "2025-07-10T19:13:43.674Z",
    "metadata": {
      "instagramAccountId": "*****************",
      "connectedFacebookPageId": "***************"
    },
    "permissions": [
      "read",
      "write"
    ],
    "profileImageUrl": "https://graph.facebook.com/*****************/picture?type=large",
    "createdAt": "2025-07-10T19:13:43.674Z",
    "updatedAt": "2025-07-10T19:13:43.674Z"
  }
]

[2025-07-10T19:13:43.786Z] 🔍 DEBUGGING: Saving 2 accounts to database...

[2025-07-10T19:13:43.788Z] 🔍 DEBUGGING: ========== PROCESSING ACCOUNT 1/2 ==========

[2025-07-10T19:13:43.789Z] 🔍 DEBUGGING: Account details:
{
  "id": "fb_***************",
  "name": "EWasl.com",
  "platform": "FACEBOOK",
  "hasAccessToken": true,
  "accessTokenPreview": "EAATaqoJvqD8BPEwkcgB...",
  "metadata": {
    "pageId": "***************",
    "category": "Software",
    "tasks": [
      "ADVERTISE",
      "ANALYZE",
      "CREATE_CONTENT",
      "MESSAGING",
      "MODERATE",
      "MANAGE"
    ]
  },
  "connectionStatus": "connected",
  "accountHandle": "@ewasl.com"
}

[2025-07-10T19:13:43.793Z] 🔍 DEBUGGING: Step 1 - Starting account processing for EWasl.com...

[2025-07-10T19:13:43.794Z] 🔍 DEBUGGING: Step 2 - Fetching metrics for account fb_***************...

[2025-07-10T19:13:43.795Z] 🔍 DEBUGGING: Using metrics ID: ***************

[2025-07-10T19:13:43.795Z] 🔍 DEBUGGING: Using token: EAATaqoJvqD8BPEwkcgB...

[2025-07-10T19:13:43.796Z] 🔍 DEBUGGING: Calling getAccountMetrics...

[2025-07-10T19:13:44.323Z] 🔍 DEBUGGING: ✅ Metrics fetched successfully:
{
  "followerCount": 0,
  "engagementRate": 0,
  "postCount": 0
}

[2025-07-10T19:13:44.325Z] 🔍 DEBUGGING: Step 2 completed - Metrics ready

[2025-07-10T19:13:44.327Z] 🔍 DEBUGGING: Step 3 - Preparing account data for database...

[2025-07-10T19:13:44.328Z] 🔍 DEBUGGING: ✅ Account data prepared successfully

[2025-07-10T19:13:44.330Z] 🔍 DEBUGGING: Account data summary:
{
  "account_id": "fb_***************",
  "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
  "platform": "FACEBOOK",
  "account_name": "EWasl.com",
  "has_access_token": true,
  "connection_status": "connected"
}

[2025-07-10T19:13:44.332Z] 🔍 DEBUGGING: Full account data for database:
{
  "account_id": "fb_***************",
  "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
  "platform": "FACEBOOK",
  "account_name": "EWasl.com",
  "access_token": "EAATaqoJvqD8BPEwkcgBWtGNgQrZAlOwi88muuWyXxfToLSsue4X3FzD1cktgnYfOv65VpFoThXQN3Wa8h4Xpy8bQiFNyXi6qmxvQfWx1DVHvwkbXlX8TLGXSyqZCgxC1w9XLI0UMs1d1XzlONkGCQXv7EUXlt2nbB14zF3hP9bpZBW3xcBRAluyhImOY3FAF2O02tXy9ofPrfffTKz0fHMW",
  "account_handle": "@ewasl.com",
  "connection_status": "connected",
  "refresh_token": null,
  "expires_at": null,
  "last_validated_at": "2025-07-10T19:13:43.674Z",
  "metadata": {
    "pageId": "***************",
    "category": "Software",
    "tasks": [
      "ADVERTISE",
      "ANALYZE",
      "CREATE_CONTENT",
      "MESSAGING",
      "MODERATE",
      "MANAGE"
    ],
    "metrics": {
      "followerCount": 0,
      "engagementRate": 0,
      "postCount": 0
    }
  },
  "profile_image_url": "https://graph.facebook.com/***************/picture?type=large",
  "permissions": [
    "read",
    "write"
  ]
}

[2025-07-10T19:13:44.338Z] 🔍 DEBUGGING: Step 4 - Validating required fields...

[2025-07-10T19:13:44.339Z] 🔍 DEBUGGING: ✅ All required fields validated successfully

[2025-07-10T19:13:44.340Z] 🔍 DEBUGGING: Step 5 - Attempting to upsert account to database...

[2025-07-10T19:13:44.342Z] 🔍 DEBUGGING: Using service role client for database operation

[2025-07-10T19:13:44.428Z] 🔍 DEBUGGING: Database operation completed, checking results...

[2025-07-10T19:13:44.430Z] 🔍 DEBUGGING: ✅ Database insertion successful!

[2025-07-10T19:13:44.431Z] ✅ Successfully saved account: EWasl.com (FACEBOOK)

[2025-07-10T19:13:44.432Z] ✅ Upsert result:
[
  {
    "id": "e8128f67-8c60-49ce-9ec6-56a579854912",
    "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
    "platform": "FACEBOOK",
    "account_id": "fb_***************",
    "account_name": "EWasl.com",
    "access_token": "EAATaqoJvqD8BPEwkcgBWtGNgQrZAlOwi88muuWyXxfToLSsue4X3FzD1cktgnYfOv65VpFoThXQN3Wa8h4Xpy8bQiFNyXi6qmxvQfWx1DVHvwkbXlX8TLGXSyqZCgxC1w9XLI0UMs1d1XzlONkGCQXv7EUXlt2nbB14zF3hP9bpZBW3xcBRAluyhImOY3FAF2O02tXy9ofPrfffTKz0fHMW",
    "refresh_token": null,
    "expires_at": null,
    "created_at": "2025-07-10T19:13:46.603155+00:00",
    "updated_at": "2025-07-10T19:13:46.603155+00:00",
    "connection_status": "connected",
    "last_validated_at": "2025-07-10T19:13:43.674+00:00",
    "metadata": {
      "tasks": [
        "ADVERTISE",
        "ANALYZE",
        "CREATE_CONTENT",
        "MESSAGING",
        "MODERATE",
        "MANAGE"
      ],
      "pageId": "***************",
      "metrics": {
        "postCount": 0,
        "followerCount": 0,
        "engagementRate": 0
      },
      "category": "Software"
    },
    "profile_image_url": "https://graph.facebook.com/***************/picture?type=large",
    "account_handle": "@ewasl.com",
    "permissions": [
      "read",
      "write"
    ]
  }
]

[2025-07-10T19:13:44.439Z] 🔍 DEBUGGING: ✅ Saved count incremented to: 1

[2025-07-10T19:13:44.441Z] 🔍 DEBUGGING: Logging audit entry for account fb_***************...

[2025-07-10T19:13:44.528Z] 🚨 AUDIT LOG ERROR (non-critical):
{
  "code": "22P02",
  "details": null,
  "hint": null,
  "message": "invalid input syntax for type uuid: \"fb_***************\""
}

[2025-07-10T19:13:44.532Z] 🔍 DEBUGGING: ========== ACCOUNT 1/2 PROCESSING COMPLETE ==========

[2025-07-10T19:13:44.533Z] 🔍 DEBUGGING: Account fb_*************** result: SUCCESS

[2025-07-10T19:13:44.535Z] 🔍 DEBUGGING: ========== PROCESSING ACCOUNT 2/2 ==========

[2025-07-10T19:13:44.536Z] 🔍 DEBUGGING: Account details:
{
  "id": "ig_*****************",
  "name": "EWasl.com",
  "platform": "INSTAGRAM",
  "hasAccessToken": true,
  "accessTokenPreview": "EAATaqoJvqD8BPEwkcgB...",
  "metadata": {
    "instagramAccountId": "*****************",
    "connectedFacebookPageId": "***************"
  },
  "connectionStatus": "connected",
  "accountHandle": "@ewasl.com"
}

[2025-07-10T19:13:44.540Z] 🔍 DEBUGGING: Step 1 - Starting account processing for EWasl.com...

[2025-07-10T19:13:44.541Z] 🔍 DEBUGGING: Step 2 - Fetching metrics for account ig_*****************...

[2025-07-10T19:13:44.542Z] 🔍 DEBUGGING: Using metrics ID: *****************

[2025-07-10T19:13:44.544Z] 🔍 DEBUGGING: Using token: EAATaqoJvqD8BPEwkcgB...

[2025-07-10T19:13:44.544Z] 🔍 DEBUGGING: Calling getAccountMetrics...

[2025-07-10T19:13:44.974Z] 🔍 DEBUGGING: ✅ Metrics fetched successfully:
{
  "followerCount": 1,
  "engagementRate": 0,
  "postCount": 0
}

[2025-07-10T19:13:44.976Z] 🔍 DEBUGGING: Step 2 completed - Metrics ready

[2025-07-10T19:13:44.977Z] 🔍 DEBUGGING: Step 3 - Preparing account data for database...

[2025-07-10T19:13:44.979Z] 🔍 DEBUGGING: ✅ Account data prepared successfully

[2025-07-10T19:13:44.980Z] 🔍 DEBUGGING: Account data summary:
{
  "account_id": "ig_*****************",
  "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
  "platform": "INSTAGRAM",
  "account_name": "EWasl.com",
  "has_access_token": true,
  "connection_status": "connected"
}

[2025-07-10T19:13:44.982Z] 🔍 DEBUGGING: Full account data for database:
{
  "account_id": "ig_*****************",
  "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
  "platform": "INSTAGRAM",
  "account_name": "EWasl.com",
  "access_token": "EAATaqoJvqD8BPEwkcgBWtGNgQrZAlOwi88muuWyXxfToLSsue4X3FzD1cktgnYfOv65VpFoThXQN3Wa8h4Xpy8bQiFNyXi6qmxvQfWx1DVHvwkbXlX8TLGXSyqZCgxC1w9XLI0UMs1d1XzlONkGCQXv7EUXlt2nbB14zF3hP9bpZBW3xcBRAluyhImOY3FAF2O02tXy9ofPrfffTKz0fHMW",
  "account_handle": "@ewasl.com",
  "connection_status": "connected",
  "refresh_token": null,
  "expires_at": null,
  "last_validated_at": "2025-07-10T19:13:43.674Z",
  "metadata": {
    "instagramAccountId": "*****************",
    "connectedFacebookPageId": "***************",
    "metrics": {
      "followerCount": 1,
      "engagementRate": 0,
      "postCount": 0
    }
  },
  "profile_image_url": "https://graph.facebook.com/*****************/picture?type=large",
  "permissions": [
    "read",
    "write"
  ]
}

[2025-07-10T19:13:44.989Z] 🔍 DEBUGGING: Step 4 - Validating required fields...

[2025-07-10T19:13:44.991Z] 🔍 DEBUGGING: ✅ All required fields validated successfully

[2025-07-10T19:13:44.992Z] 🔍 DEBUGGING: Step 5 - Attempting to upsert account to database...

[2025-07-10T19:13:44.993Z] 🔍 DEBUGGING: Using service role client for database operation

[2025-07-10T19:13:45.071Z] 🔍 DEBUGGING: Database operation completed, checking results...

[2025-07-10T19:13:45.072Z] 🔍 DEBUGGING: ✅ Database insertion successful!

[2025-07-10T19:13:45.073Z] ✅ Successfully saved account: EWasl.com (INSTAGRAM)

[2025-07-10T19:13:45.074Z] ✅ Upsert result:
[
  {
    "id": "0fe3d878-212e-4b68-9a0a-ab6413db89df",
    "user_id": "796e42db-6ea7-4e91-b726-a779552819ba",
    "platform": "INSTAGRAM",
    "account_id": "ig_*****************",
    "account_name": "EWasl.com",
    "access_token": "EAATaqoJvqD8BPEwkcgBWtGNgQrZAlOwi88muuWyXxfToLSsue4X3FzD1cktgnYfOv65VpFoThXQN3Wa8h4Xpy8bQiFNyXi6qmxvQfWx1DVHvwkbXlX8TLGXSyqZCgxC1w9XLI0UMs1d1XzlONkGCQXv7EUXlt2nbB14zF3hP9bpZBW3xcBRAluyhImOY3FAF2O02tXy9ofPrfffTKz0fHMW",
    "refresh_token": null,
    "expires_at": null,
    "created_at": "2025-07-10T19:13:47.250014+00:00",
    "updated_at": "2025-07-10T19:13:47.250014+00:00",
    "connection_status": "connected",
    "last_validated_at": "2025-07-10T19:13:43.674+00:00",
    "metadata": {
      "metrics": {
        "postCount": 0,
        "followerCount": 1,
        "engagementRate": 0
      },
      "instagramAccountId": "*****************",
      "connectedFacebookPageId": "***************"
    },
    "profile_image_url": "https://graph.facebook.com/*****************/picture?type=large",
    "account_handle": "@ewasl.com",
    "permissions": [
      "read",
      "write"
    ]
  }
]

[2025-07-10T19:13:45.081Z] 🔍 DEBUGGING: ✅ Saved count incremented to: 2

[2025-07-10T19:13:45.082Z] 🔍 DEBUGGING: Logging audit entry for account ig_*****************...

[2025-07-10T19:13:45.151Z] 🚨 AUDIT LOG ERROR (non-critical):
{
  "code": "22P02",
  "details": null,
  "hint": null,
  "message": "invalid input syntax for type uuid: \"ig_*****************\""
}

[2025-07-10T19:13:45.153Z] 🔍 DEBUGGING: ========== ACCOUNT 2/2 PROCESSING COMPLETE ==========

[2025-07-10T19:13:45.155Z] 🔍 DEBUGGING: Account ig_***************** result: SUCCESS

[2025-07-10T19:13:45.156Z] 🔍 DEBUGGING: Final results - Found: 2, Saved: 2

[2025-07-10T19:13:45.157Z] 🔍 DEBUGGING: Redirecting with message: Successfully connected 2 account(s)

