#!/usr/bin/env node

/**
 * Enhanced Dashboard Test Suite
 * Tests the Phase 1 implementation of the enhanced dashboard
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 ENHANCED DASHBOARD TEST SUITE');
console.log('=================================');

let passed = 0;
let total = 0;

function test(name, condition) {
  total++;
  if (condition) {
    console.log(`✅ ${name}`);
    passed++;
  } else {
    console.log(`❌ ${name}`);
  }
}

// Test 1: Enhanced Dashboard Page Structure
console.log('\n📄 DASHBOARD PAGE TESTS');
console.log('------------------------');

const dashboardPath = path.join(__dirname, 'src/app/dashboard/page.tsx');
const dashboardContent = fs.readFileSync(dashboardPath, 'utf8');

test('Dashboard imports WelcomeSection', dashboardContent.includes('WelcomeSection'));
test('Dashboard imports StatsCard', dashboardContent.includes('StatsCard'));
test('Dashboard imports QuickActions', dashboardContent.includes('QuickActions'));
test('Dashboard imports RealtimeActivityFeed', dashboardContent.includes('RealtimeActivityFeed'));
test('Dashboard imports SocialConnectionsWidget', dashboardContent.includes('SocialConnectionsWidget'));
test('Dashboard imports QueueStatusWidget', dashboardContent.includes('QueueStatusWidget'));

// Test 2: API Integration
console.log('\n🔌 API INTEGRATION TESTS');
console.log('-------------------------');

test('Dashboard fetches from /api/analytics/dashboard', dashboardContent.includes('/api/analytics/dashboard'));
test('Dashboard has error handling', dashboardContent.includes('catch') && dashboardContent.includes('error'));
test('Dashboard has loading states', dashboardContent.includes('isLoading') && dashboardContent.includes('setIsLoading'));
test('Dashboard has retry mechanism', dashboardContent.includes('handleRetry') && dashboardContent.includes('retryCount'));

// Test 3: Arabic RTL Support
console.log('\n🌐 ARABIC RTL TESTS');
console.log('-------------------');

test('Dashboard has language state', dashboardContent.includes("useState<'ar' | 'en'>('ar')"));
test('Dashboard uses cn utility for RTL', dashboardContent.includes('cn(') && dashboardContent.includes('rtl'));
test('Dashboard passes language to components', dashboardContent.includes('language={language}'));

// Test 4: Component Usage
console.log('\n🧩 COMPONENT USAGE TESTS');
console.log('-------------------------');

test('Dashboard uses WelcomeSection component', dashboardContent.includes('<WelcomeSection'));
test('Dashboard uses StatsCard components', dashboardContent.includes('<StatsCard'));
test('Dashboard uses QuickActions component', dashboardContent.includes('<QuickActions'));
test('Dashboard uses RealtimeActivityFeed component', dashboardContent.includes('<RealtimeActivityFeed'));
test('Dashboard uses SocialConnectionsWidget component', dashboardContent.includes('<SocialConnectionsWidget'));
test('Dashboard uses QueueStatusWidget component', dashboardContent.includes('<QueueStatusWidget'));

// Test 5: Data Structure
console.log('\n📊 DATA STRUCTURE TESTS');
console.log('------------------------');

test('Dashboard has DashboardData interface', dashboardContent.includes('interface DashboardData'));
test('Dashboard has overview data structure', dashboardContent.includes('overview:'));
test('Dashboard has fallback data', dashboardContent.includes('total_posts: 156'));
test('Dashboard handles user name extraction', dashboardContent.includes('getUserName'));

// Test 6: Required API Endpoint
console.log('\n🛠️ API ENDPOINT TESTS');
console.log('----------------------');

const activityApiPath = path.join(__dirname, 'src/app/api/analytics/activity/route.ts');
test('Activity API endpoint exists', fs.existsSync(activityApiPath));

if (fs.existsSync(activityApiPath)) {
  const activityContent = fs.readFileSync(activityApiPath, 'utf8');
  test('Activity API returns activities array', activityContent.includes('activities:'));
  test('Activity API has Arabic content', activityContent.includes('تم نشر المنشور بنجاح'));
}

// Test 7: Layout Integration
console.log('\n🏗️ LAYOUT INTEGRATION TESTS');
console.log('-----------------------------');

const layoutPath = path.join(__dirname, 'src/app/dashboard/layout.tsx');
const layoutContent = fs.readFileSync(layoutPath, 'utf8');

test('Dashboard layout uses PersistentDashboardLayout', layoutContent.includes('PersistentDashboardLayout'));
test('Dashboard layout has Arabic title', layoutContent.includes('لوحة التحكم'));
test('Dashboard layout has authentication', layoutContent.includes('useRequireAuth'));

// Test 8: Enhanced Components Exist
console.log('\n🎨 ENHANCED COMPONENTS TESTS');
console.log('-----------------------------');

const componentPaths = [
  'src/components/dashboard/welcome-section.tsx',
  'src/components/dashboard/stats-card.tsx',
  'src/components/dashboard/quick-actions.tsx',
  'src/components/dashboard/realtime-activity-feed.tsx',
  'src/components/dashboard/social-connections-widget.tsx',
  'src/components/dashboard/queue-status-widget.tsx'
].map(p => path.join(__dirname, p));

componentPaths.forEach(componentPath => {
  const componentName = path.basename(componentPath, '.tsx');
  test(`${componentName} component exists`, fs.existsSync(componentPath));
});

// Results
console.log('\n📊 TEST RESULTS');
console.log('================');
console.log(`✅ Passed: ${passed}`);
console.log(`❌ Failed: ${total - passed}`);
console.log(`📊 Total: ${total}`);
console.log(`🎯 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

if (passed === total) {
  console.log('\n🎉 ALL TESTS PASSED! Enhanced Dashboard is ready!');
  process.exit(0);
} else {
  console.log('\n⚠️ Some tests failed. Please review the implementation.');
  process.exit(1);
}
