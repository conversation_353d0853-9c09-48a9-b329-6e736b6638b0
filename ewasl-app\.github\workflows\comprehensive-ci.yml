name: Comprehensive CI/CD Pipeline

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  schedule:
    - cron: '0 6 * * 1' # Weekly security scan

env:
  NODE_VERSION: '18'
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1

jobs:
  # Security and dependency scanning
  security-scan:
    name: Security & Dependency Scan
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
        
      - name: Run npm audit
        run: npm audit --audit-level=moderate
        continue-on-error: true
        
      - name: Security scan with Snyk
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high
        continue-on-error: true
        
      - name: Scan for secrets
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD

  # Code quality and linting
  code-quality:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
        
      - name: Run ESLint
        run: npm run lint
        
      - name: Run TypeScript check
        run: npm run type-check
        
      - name: Check code formatting
        run: npm run format:check
        
      - name: Run bundle analyzer
        run: npm run analyze

  # Unit and integration tests
  test-unit:
    name: Unit & Integration Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20]
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
        
      - name: Run unit tests
        run: npm run test:unit -- --coverage
        
      - name: Run integration tests
        run: npm run test:integration
        
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          file: ./coverage/lcov.info

  # E2E tests
  test-e2e:
    name: E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
        
      - name: Build application
        run: npm run build
        
      - name: Start application
        run: npm start &
        
      - name: Wait for app to be ready
        run: npx wait-on http://localhost:3000
        
      - name: Run E2E tests
        run: npm run test:e2e
        
      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/

  # Build and performance testing
  build-and-performance:
    name: Build & Performance Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
        
      - name: Build application
        run: npm run build
        
      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v9
        with:
          configPath: './lighthouserc.js'
          uploadArtifacts: true
          temporaryPublicStorage: true
        
      - name: Bundle size check
        run: npm run bundle:check
        
      - name: Performance regression test
        run: npm run test:performance

  # Database migration testing
  database-migrations:
    name: Database Migration Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
        
      - name: Test database migrations
        run: |
          npm run db:migrate:test
          npm run db:seed:test
          npm run test:database
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

  # Deployment staging
  deploy-staging:
    name: Deploy to Staging
    needs: [security-scan, code-quality, test-unit, test-e2e, build-and-performance]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
        
      - name: Build application
        run: npm run build
        env:
          NEXT_PUBLIC_ENV: staging
        
      - name: Deploy to DigitalOcean App Platform
        uses: digitalocean/app_action@v1.1.5
        with:
          app_name: ewasl-staging
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          images: |
            app=registry.digitalocean.com/ewasl/app:${{ github.sha }}
        
      - name: Run post-deployment tests
        run: npm run test:smoke
        env:
          TEST_URL: https://ewasl-staging.ondigitalocean.app

  # Production deployment
  deploy-production:
    name: Deploy to Production
    needs: [security-scan, code-quality, test-unit, test-e2e, build-and-performance, database-migrations]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    environment: production
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
        
      - name: Build application
        run: npm run build
        env:
          NEXT_PUBLIC_ENV: production
        
      - name: Deploy to DigitalOcean App Platform
        uses: digitalocean/app_action@v1.1.5
        with:
          app_name: ewasl-production
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          images: |
            app=registry.digitalocean.com/ewasl/app:${{ github.sha }}
        
      - name: Run post-deployment tests
        run: npm run test:smoke
        env:
          TEST_URL: https://app.ewasl.com
        
      - name: Notify deployment success
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#deployments'
          text: 'Production deployment successful! 🚀'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Rollback capability
  rollback-production:
    name: Emergency Rollback
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' && github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Rollback deployment
        uses: digitalocean/app_action@v1.1.5
        with:
          app_name: ewasl-production
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          rollback: true
        
      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: warning
          channel: '#deployments'
          text: 'Emergency rollback executed! ⚠️'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }} 