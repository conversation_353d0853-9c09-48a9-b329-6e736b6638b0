#!/usr/bin/env node

/**
 * Phase 2 Features Test Suite
 * Tests Advanced Analytics, Performance Optimizations, and Mobile Responsiveness
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 PHASE 2 FEATURES TEST SUITE');
console.log('===============================');

let passed = 0;
let total = 0;

function test(name, condition) {
  total++;
  if (condition) {
    console.log(`✅ ${name}`);
    passed++;
  } else {
    console.log(`❌ ${name}`);
  }
}

// Test 1: Advanced Analytics Integration
console.log('\n📊 ADVANCED ANALYTICS TESTS');
console.log('----------------------------');

const analyticsChartsPath = path.join(__dirname, 'src/components/dashboard/analytics-charts.tsx');
const analyticsApiPath = path.join(__dirname, 'src/app/api/analytics/charts/route.ts');

test('AnalyticsCharts component exists', fs.existsSync(analyticsChartsPath));
test('Charts API endpoint exists', fs.existsSync(analyticsApiPath));

if (fs.existsSync(analyticsChartsPath)) {
  const analyticsContent = fs.readFileSync(analyticsChartsPath, 'utf8');
  test('Uses Recharts library', analyticsContent.includes('recharts'));
  test('Has interactive charts', analyticsContent.includes('AreaChart') && analyticsContent.includes('BarChart'));
  test('Supports Arabic RTL', analyticsContent.includes("language === 'ar'"));
  test('Has real-time refresh', analyticsContent.includes('RefreshCw') && analyticsContent.includes('fetchChartData'));
  test('Has multiple chart types', analyticsContent.includes('TabsContent'));
}

if (fs.existsSync(analyticsApiPath)) {
  const apiContent = fs.readFileSync(analyticsApiPath, 'utf8');
  test('API returns chart data', apiContent.includes('engagement_trends'));
  test('API supports time ranges', apiContent.includes('timeRange'));
  test('API has authentication', apiContent.includes('getAuthenticatedUser'));
}

// Test 2: Performance Optimizations
console.log('\n⚡ PERFORMANCE OPTIMIZATION TESTS');
console.log('----------------------------------');

const performanceHookPath = path.join(__dirname, 'src/hooks/usePerformanceOptimization.ts');
const dashboardPath = path.join(__dirname, 'src/app/dashboard/page.tsx');

test('Performance optimization hook exists', fs.existsSync(performanceHookPath));

if (fs.existsSync(performanceHookPath)) {
  const hookContent = fs.readFileSync(performanceHookPath, 'utf8');
  test('Has data caching system', hookContent.includes('useDataCache'));
  test('Has performance monitoring', hookContent.includes('usePerformanceMonitor'));
  test('Has debounce functionality', hookContent.includes('useDebounce'));
  test('Has lazy loading support', hookContent.includes('useLazyLoad'));
  test('Has memory monitoring', hookContent.includes('useMemoryMonitor'));
  test('Has cache management', hookContent.includes('clearCache'));
}

if (fs.existsSync(dashboardPath)) {
  const dashboardContent = fs.readFileSync(dashboardPath, 'utf8');
  test('Dashboard uses data caching', dashboardContent.includes('useDataCache'));
  test('Dashboard has performance monitoring', dashboardContent.includes('usePerformanceMonitor'));
  test('Dashboard shows stale data indicators', dashboardContent.includes('isStale'));
  test('Dashboard has optimized error handling', dashboardContent.includes('displayData'));
}

// Test 3: Mobile Responsiveness
console.log('\n📱 MOBILE RESPONSIVENESS TESTS');
console.log('-------------------------------');

const mobileLayoutPath = path.join(__dirname, 'src/components/dashboard/mobile-dashboard-layout.tsx');

test('Mobile dashboard layout exists', fs.existsSync(mobileLayoutPath));

if (fs.existsSync(mobileLayoutPath)) {
  const mobileContent = fs.readFileSync(mobileLayoutPath, 'utf8');
  test('Detects device type', mobileContent.includes('deviceType'));
  test('Has collapsible sections', mobileContent.includes('Collapsible'));
  test('Supports touch interactions', mobileContent.includes('CollapsibleTrigger'));
  test('Has priority-based layout', mobileContent.includes('priority'));
  test('Responsive grid system', mobileContent.includes('grid-cols'));
  test('Mobile-specific styling', mobileContent.includes('mobile'));
}

if (fs.existsSync(dashboardPath)) {
  const dashboardContent = fs.readFileSync(dashboardPath, 'utf8');
  test('Dashboard detects mobile', dashboardContent.includes('isMobile'));
  test('Dashboard uses mobile layout', dashboardContent.includes('MobileDashboardLayout'));
  test('Dashboard has responsive wrapper', dashboardContent.includes('dashboardContent'));
}

// Test 4: Integration Tests
console.log('\n🔗 INTEGRATION TESTS');
console.log('--------------------');

test('Dashboard imports analytics charts', fs.existsSync(dashboardPath) && 
  fs.readFileSync(dashboardPath, 'utf8').includes('AnalyticsCharts'));

test('Dashboard imports mobile layout', fs.existsSync(dashboardPath) && 
  fs.readFileSync(dashboardPath, 'utf8').includes('MobileDashboardLayout'));

test('Dashboard imports performance hooks', fs.existsSync(dashboardPath) && 
  fs.readFileSync(dashboardPath, 'utf8').includes('usePerformanceOptimization'));

// Test 5: API Endpoints
console.log('\n🛠️ API ENDPOINT TESTS');
console.log('----------------------');

const requiredEndpoints = [
  'src/app/api/analytics/dashboard/route.ts',
  'src/app/api/analytics/activity/route.ts',
  'src/app/api/analytics/charts/route.ts'
];

requiredEndpoints.forEach(endpoint => {
  const fullPath = path.join(__dirname, endpoint);
  const endpointName = path.basename(path.dirname(endpoint));
  test(`${endpointName} API endpoint exists`, fs.existsSync(fullPath));
});

// Test 6: Component Structure
console.log('\n🧩 COMPONENT STRUCTURE TESTS');
console.log('-----------------------------');

const requiredComponents = [
  'src/components/dashboard/analytics-charts.tsx',
  'src/components/dashboard/mobile-dashboard-layout.tsx',
  'src/hooks/usePerformanceOptimization.ts'
];

requiredComponents.forEach(component => {
  const fullPath = path.join(__dirname, component);
  const componentName = path.basename(component, path.extname(component));
  test(`${componentName} component exists`, fs.existsSync(fullPath));
});

// Test 7: TypeScript Compliance
console.log('\n📝 TYPESCRIPT COMPLIANCE TESTS');
console.log('-------------------------------');

const tsFiles = [
  analyticsChartsPath,
  mobileLayoutPath,
  performanceHookPath
].filter(fs.existsSync);

tsFiles.forEach(filePath => {
  const content = fs.readFileSync(filePath, 'utf8');
  const fileName = path.basename(filePath);
  test(`${fileName} has proper interfaces`, content.includes('interface'));
  test(`${fileName} has type annotations`, content.includes(': ') && content.includes('React'));
});

// Test 8: Arabic RTL Support
console.log('\n🌐 ARABIC RTL SUPPORT TESTS');
console.log('----------------------------');

const rtlFiles = [analyticsChartsPath, mobileLayoutPath].filter(fs.existsSync);

rtlFiles.forEach(filePath => {
  const content = fs.readFileSync(filePath, 'utf8');
  const fileName = path.basename(filePath);
  test(`${fileName} supports Arabic language`, content.includes("language === 'ar'"));
  test(`${fileName} has RTL layout support`, content.includes('rtl') || content.includes('flex-row-reverse'));
  test(`${fileName} has Arabic translations`, content.includes('ar:') || content.includes('العربية'));
});

// Results
console.log('\n📊 TEST RESULTS');
console.log('================');
console.log(`✅ Passed: ${passed}`);
console.log(`❌ Failed: ${total - passed}`);
console.log(`📊 Total: ${total}`);
console.log(`🎯 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

// Feature Summary
console.log('\n🎉 PHASE 2 FEATURES IMPLEMENTED');
console.log('================================');
console.log('📊 Advanced Analytics Integration:');
console.log('   ✅ Interactive charts with Recharts');
console.log('   ✅ Real-time data visualization');
console.log('   ✅ Multiple chart types (Area, Bar, Line)');
console.log('   ✅ Arabic RTL support');

console.log('\n⚡ Performance Optimizations:');
console.log('   ✅ Data caching system');
console.log('   ✅ Performance monitoring');
console.log('   ✅ Stale data indicators');
console.log('   ✅ Memory usage tracking');
console.log('   ✅ Debounced updates');

console.log('\n📱 Mobile Responsiveness:');
console.log('   ✅ Device type detection');
console.log('   ✅ Collapsible sections');
console.log('   ✅ Touch-friendly interactions');
console.log('   ✅ Priority-based layout');
console.log('   ✅ Responsive grid system');

if (passed === total) {
  console.log('\n🎉 ALL PHASE 2 FEATURES IMPLEMENTED SUCCESSFULLY!');
  process.exit(0);
} else {
  console.log('\n⚠️ Some features need attention. Please review the implementation.');
  process.exit(1);
}
