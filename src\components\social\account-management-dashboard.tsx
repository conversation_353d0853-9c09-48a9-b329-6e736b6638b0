'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  MoreVertical, 
  RefreshCw, 
  Trash2, 
  Settings, 
  Switch,
  CheckCircle,
  AlertTriangle,
  Clock,
  Star,
  Users,
  Calendar
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from 'sonner';

interface SocialAccount {
  id: string;
  platform: string;
  accountName: string;
  accountHandle?: string;
  connectionStatus: string;
  profileImageUrl?: string;
  isDefault?: boolean;
  lastValidatedAt?: string;
  expiresAt?: string;
  metadata?: any;
}

interface AccountManagementDashboardProps {
  userId: string;
  className?: string;
}

export function AccountManagementDashboard({ userId, className = '' }: AccountManagementDashboardProps) {
  const [accounts, setAccounts] = useState<SocialAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedAccount, setSelectedAccount] = useState<SocialAccount | null>(null);
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);
  const [showSwitchDialog, setShowSwitchDialog] = useState(false);
  const [switchOptions, setSwitchOptions] = useState<SocialAccount[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    loadAccounts();
  }, [userId]);

  const loadAccounts = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/social/accounts?userId=${userId}`);
      const data = await response.json();

      if (response.ok) {
        setAccounts(data.accounts || []);
      } else {
        toast.error('فشل في تحميل الحسابات');
      }
    } catch (error) {
      console.error('Error loading accounts:', error);
      toast.error('خطأ في تحميل الحسابات');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = async () => {
    if (!selectedAccount) return;

    try {
      setIsProcessing(true);
      toast.loading('جاري قطع الاتصال...', { id: 'disconnect' });

      const response = await fetch('/api/oauth/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: selectedAccount.platform,
          accountId: selectedAccount.id,
          userId,
          revokeToken: true
        }),
      });

      const data = await response.json();
      toast.dismiss('disconnect');

      if (data.success) {
        toast.success('تم قطع اتصال الحساب بنجاح');
        setShowDisconnectDialog(false);
        setSelectedAccount(null);
        await loadAccounts();
      } else {
        toast.error(data.error || 'فشل في قطع الاتصال');
      }
    } catch (error) {
      toast.dismiss('disconnect');
      console.error('Error disconnecting account:', error);
      toast.error('خطأ في قطع الاتصال');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSwitchAccount = async (toAccountId: string) => {
    if (!selectedAccount) return;

    try {
      setIsProcessing(true);
      toast.loading('جاري تبديل الحساب...', { id: 'switch' });

      const response = await fetch('/api/oauth/switch-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: selectedAccount.platform,
          fromAccountId: selectedAccount.id,
          toAccountId,
          userId,
          transferScheduled: true
        }),
      });

      const data = await response.json();
      toast.dismiss('switch');

      if (data.success) {
        toast.success('تم تبديل الحساب بنجاح');
        setShowSwitchDialog(false);
        setSelectedAccount(null);
        await loadAccounts();
      } else {
        toast.error(data.error || 'فشل في تبديل الحساب');
      }
    } catch (error) {
      toast.dismiss('switch');
      console.error('Error switching account:', error);
      toast.error('خطأ في تبديل الحساب');
    } finally {
      setIsProcessing(false);
    }
  };

  const loadSwitchOptions = async (account: SocialAccount) => {
    try {
      const response = await fetch(`/api/oauth/switch-account?platform=${account.platform}&currentAccountId=${account.id}`);
      const data = await response.json();

      if (data.success) {
        setSwitchOptions(data.accounts.filter((acc: SocialAccount) => acc.id !== account.id));
        setSelectedAccount(account);
        setShowSwitchDialog(true);
      } else {
        toast.error('فشل في تحميل خيارات التبديل');
      }
    } catch (error) {
      console.error('Error loading switch options:', error);
      toast.error('خطأ في تحميل خيارات التبديل');
    }
  };

  const getStatusBadge = (account: SocialAccount) => {
    switch (account.connectionStatus) {
      case 'connected':
        return <Badge variant="default" className="bg-green-500">متصل</Badge>;
      case 'expired':
        return <Badge variant="destructive">منتهي الصلاحية</Badge>;
      case 'error':
        return <Badge variant="destructive">خطأ</Badge>;
      case 'reconnecting':
        return <Badge variant="secondary">إعادة اتصال</Badge>;
      default:
        return <Badge variant="outline">{account.connectionStatus}</Badge>;
    }
  };

  const getStatusIcon = (account: SocialAccount) => {
    switch (account.connectionStatus) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'expired':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'reconnecting':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getPlatformName = (platform: string) => {
    const names: Record<string, string> = {
      'FACEBOOK': 'فيسبوك',
      'INSTAGRAM': 'إنستغرام',
      'TWITTER': 'تويتر',
      'X': 'إكس',
      'LINKEDIN': 'لينكد إن',
      'TIKTOK': 'تيك توك'
    };
    return names[platform] || platform;
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>إدارة الحسابات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin" />
            <span className="mr-2">جاري التحميل...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>إدارة الحسابات</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={loadAccounts}
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              تحديث
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {accounts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              لا توجد حسابات متصلة
            </div>
          ) : (
            <div className="space-y-3">
              {accounts.map((account) => (
                <div
                  key={account.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
                >
                  <div className="flex items-center gap-3">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={account.profileImageUrl} alt={account.accountName} />
                      <AvatarFallback>
                        {account.accountName.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{account.accountName}</h3>
                        {account.isDefault && (
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        )}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{getPlatformName(account.platform)}</span>
                        {account.accountHandle && (
                          <span>• {account.accountHandle}</span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    {getStatusIcon(account)}
                    {getStatusBadge(account)}
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => loadSwitchOptions(account)}>
                          <Switch className="w-4 h-4 mr-2" />
                          تبديل الحساب
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Settings className="w-4 h-4 mr-2" />
                          إعدادات الحساب
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => {
                            setSelectedAccount(account);
                            setShowDisconnectDialog(true);
                          }}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          قطع الاتصال
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Disconnect Dialog */}
      <Dialog open={showDisconnectDialog} onOpenChange={setShowDisconnectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>قطع اتصال الحساب</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من قطع اتصال حساب {selectedAccount?.accountName} على {getPlatformName(selectedAccount?.platform || '')}؟
            </DialogDescription>
          </DialogHeader>
          
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              سيتم إلغاء جميع المنشورات المجدولة وحذف البيانات المرتبطة بهذا الحساب.
            </AlertDescription>
          </Alert>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDisconnectDialog(false)}
              disabled={isProcessing}
            >
              إلغاء
            </Button>
            <Button
              variant="destructive"
              onClick={handleDisconnect}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <RefreshCw className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <Trash2 className="w-4 h-4 mr-2" />
              )}
              قطع الاتصال
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Switch Account Dialog */}
      <Dialog open={showSwitchDialog} onOpenChange={setShowSwitchDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تبديل الحساب</DialogTitle>
            <DialogDescription>
              اختر الحساب الذي تريد التبديل إليه على {getPlatformName(selectedAccount?.platform || '')}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-3 max-h-60 overflow-y-auto">
            {switchOptions.map((option) => (
              <div
                key={option.id}
                className="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-muted/50"
                onClick={() => handleSwitchAccount(option.id)}
              >
                <div className="flex items-center gap-3">
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={option.profileImageUrl} alt={option.accountName} />
                    <AvatarFallback>
                      {option.accountName.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{option.accountName}</div>
                    {option.accountHandle && (
                      <div className="text-sm text-muted-foreground">{option.accountHandle}</div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {option.isDefault && (
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                  )}
                  {getStatusBadge(option)}
                </div>
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowSwitchDialog(false)}
              disabled={isProcessing}
            >
              إلغاء
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
