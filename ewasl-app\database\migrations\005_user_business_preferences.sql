-- Migration: 005_user_business_preferences.sql
-- Create user business account preferences table
-- Date: 2025-01-18
-- Purpose: Store user preferences for which business accounts to use for posting

-- Create user business account preferences table
CREATE TABLE IF NOT EXISTS user_business_account_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Selected business accounts for each platform
    selected_facebook_pages JSONB DEFAULT '[]'::jsonb,
    selected_linkedin_orgs JSONB DEFAULT '[]'::jsonb,
    selected_instagram_accounts JSONB DEFAULT '[]'::jsonb,
    
    -- Default posting preferences
    default_publish_immediately BOOLEAN DEFAULT false,
    default_cross_post BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_user_preferences UNIQUE (user_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_business_preferences_user_id 
ON user_business_account_preferences(user_id);

CREATE INDEX IF NOT EXISTS idx_user_business_preferences_updated_at 
ON user_business_account_preferences(updated_at);

-- Create trigger to update the updated_at column
CREATE OR REPLACE FUNCTION update_user_business_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_user_business_preferences_updated_at
    BEFORE UPDATE ON user_business_account_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_user_business_preferences_updated_at();

-- Enable Row Level Security
ALTER TABLE user_business_account_preferences ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own business account preferences"
    ON user_business_account_preferences FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own business account preferences"
    ON user_business_account_preferences FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own business account preferences"
    ON user_business_account_preferences FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own business account preferences"
    ON user_business_account_preferences FOR DELETE
    USING (auth.uid() = user_id);

-- Add helpful comments
COMMENT ON TABLE user_business_account_preferences 
IS 'User preferences for business account selection and posting defaults';

COMMENT ON COLUMN user_business_account_preferences.selected_facebook_pages 
IS 'Array of selected Facebook page IDs for posting';

COMMENT ON COLUMN user_business_account_preferences.selected_linkedin_orgs 
IS 'Array of selected LinkedIn organization IDs for posting';

COMMENT ON COLUMN user_business_account_preferences.selected_instagram_accounts 
IS 'Array of selected Instagram business account IDs for posting';

COMMENT ON COLUMN user_business_account_preferences.default_publish_immediately 
IS 'Whether to publish posts immediately by default';

COMMENT ON COLUMN user_business_account_preferences.default_cross_post 
IS 'Whether to cross-post to all selected accounts by default';

-- Insert example data (for demo user)
INSERT INTO user_business_account_preferences (
    user_id,
    selected_facebook_pages,
    selected_linkedin_orgs,
    selected_instagram_accounts,
    default_publish_immediately,
    default_cross_post
) VALUES (
    '3ddaeb03-2d95-4fff-abad-2a2c7dd25037', -- demo user
    '[]'::jsonb,
    '[]'::jsonb,
    '[]'::jsonb,
    false,
    true
) ON CONFLICT (user_id) DO NOTHING; 