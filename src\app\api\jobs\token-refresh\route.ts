import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { tokenRefreshJob, getTokenRefreshJobStatus } from '@/lib/jobs/token-refresh-job';

/**
 * Token Refresh Job Management API
 * Handles manual triggering, monitoring, and configuration of automatic token refresh
 */

export async function GET(request: NextRequest) {
  try {
    const { user } = await getAuthenticatedUser(request);
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'status';

    console.log(`🔍 Token refresh job ${action} requested by user:`, user.id);

    switch (action) {
      case 'status':
        return await handleJobStatus();
      
      case 'trigger':
        return await handleJobTrigger();
      
      case 'logs':
        return await handleJobLogs();
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: status, trigger, or logs' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Error in token refresh job API:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في إدارة مهمة تحديث الرموز',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Manual job trigger
 */
export async function POST(request: NextRequest) {
  try {
    const { user } = await getAuthenticatedUser(request);
    const body = await request.json();
    const { action = 'trigger', force = false } = body;

    console.log(`🔧 Manual token refresh job ${action} by user:`, user.id);

    if (action === 'trigger') {
      // Check if job should run (unless forced)
      if (!force && !tokenRefreshJob.shouldRun()) {
        const status = await getTokenRefreshJobStatus();
        return NextResponse.json({
          success: false,
          message: 'المهمة تم تشغيلها مؤخراً',
          lastRun: status.lastRun,
          nextRun: status.nextRun,
          suggestion: 'استخدم force: true لتشغيل المهمة بالقوة'
        });
      }

      // Run the job
      const result = await tokenRefreshJob.trigger();
      
      return NextResponse.json({
        success: result.success,
        message: result.success ? 
          'تم تشغيل مهمة تحديث الرموز بنجاح' : 
          'فشل في تشغيل مهمة تحديث الرموز',
        result,
        triggeredBy: user.id,
        triggeredAt: new Date().toISOString()
      });
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('❌ Error triggering token refresh job:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في تشغيل مهمة تحديث الرموز',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Handle job status request
 */
async function handleJobStatus() {
  try {
    const status = await getTokenRefreshJobStatus();
    
    return NextResponse.json({
      success: true,
      status: {
        ...status,
        shouldRun: tokenRefreshJob.shouldRun(),
        intervalHours: parseInt(process.env.TOKEN_REFRESH_INTERVAL_HOURS || '6'),
        environment: {
          enabled: process.env.TOKEN_REFRESH_ENABLED !== 'false',
          intervalHours: process.env.TOKEN_REFRESH_INTERVAL_HOURS || '6'
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    throw new Error(`Failed to get job status: ${error}`);
  }
}

/**
 * Handle manual job trigger
 */
async function handleJobTrigger() {
  try {
    // Check if job should run
    if (!tokenRefreshJob.shouldRun()) {
      const status = await getTokenRefreshJobStatus();
      return NextResponse.json({
        success: false,
        message: 'المهمة تم تشغيلها مؤخراً',
        lastRun: status.lastRun,
        nextRun: status.nextRun,
        suggestion: 'استخدم POST مع force: true لتشغيل المهمة بالقوة'
      });
    }

    // Run the job
    const result = await tokenRefreshJob.trigger();
    
    return NextResponse.json({
      success: result.success,
      message: result.success ? 
        'تم تشغيل مهمة تحديث الرموز بنجاح' : 
        'فشل في تشغيل مهمة تحديث الرموز',
      result
    });

  } catch (error) {
    throw new Error(`Failed to trigger job: ${error}`);
  }
}

/**
 * Handle job logs request
 */
async function handleJobLogs() {
  try {
    const status = await getTokenRefreshJobStatus();
    
    // Process logs for better display
    const processedLogs = status.recentRuns.map((log: any) => ({
      id: log.id,
      status: log.status,
      createdAt: log.created_at,
      details: log.details,
      duration: log.details?.duration,
      processed: log.details?.processed,
      successful: log.details?.successful,
      failed: log.details?.failed,
      error: log.details?.error
    }));

    // Calculate statistics
    const completedRuns = processedLogs.filter(log => log.status === 'completed');
    const failedRuns = processedLogs.filter(log => log.status === 'failed');
    
    const stats = {
      totalRuns: processedLogs.length,
      completedRuns: completedRuns.length,
      failedRuns: failedRuns.length,
      successRate: processedLogs.length > 0 ? 
        (completedRuns.length / processedLogs.length * 100).toFixed(1) : '0',
      averageDuration: completedRuns.length > 0 ?
        Math.round(completedRuns.reduce((sum, log) => sum + (log.duration || 0), 0) / completedRuns.length) : 0,
      totalTokensProcessed: completedRuns.reduce((sum, log) => sum + (log.processed || 0), 0),
      totalTokensRefreshed: completedRuns.reduce((sum, log) => sum + (log.successful || 0), 0)
    };

    return NextResponse.json({
      success: true,
      logs: processedLogs,
      statistics: stats,
      lastUpdated: new Date().toISOString()
    });

  } catch (error) {
    throw new Error(`Failed to get job logs: ${error}`);
  }
}

/**
 * PUT - Update job configuration
 */
export async function PUT(request: NextRequest) {
  try {
    const { user } = await getAuthenticatedUser(request);
    const body = await request.json();
    const { enabled, intervalHours } = body;

    console.log(`⚙️ Updating token refresh job config by user:`, user.id);

    // Note: In a production environment, you'd want to store this in a database
    // For now, we'll just return the current configuration
    
    const currentConfig = {
      enabled: process.env.TOKEN_REFRESH_ENABLED !== 'false',
      intervalHours: parseInt(process.env.TOKEN_REFRESH_INTERVAL_HOURS || '6')
    };

    return NextResponse.json({
      success: true,
      message: 'إعدادات المهمة محفوظة (ملاحظة: يتطلب إعادة تشغيل الخادم)',
      currentConfig,
      requestedConfig: { enabled, intervalHours },
      note: 'في البيئة الإنتاجية، يجب حفظ هذه الإعدادات في قاعدة البيانات',
      updatedBy: user.id,
      updatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error updating job configuration:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في تحديث إعدادات المهمة',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
