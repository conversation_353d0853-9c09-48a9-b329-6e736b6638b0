'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, Clock, RefreshCw, Trash2, Edit, Play, Pause } from 'lucide-react';
import { toast } from 'sonner';

// Enhanced Scheduled Posts Management Interface
interface ScheduledPost {
  id: string;
  post_id: string;
  scheduled_for: string;
  timezone: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  retry_count: number;
  priority: 'LOW' | 'NORMAL' | 'HIGH';
  error_message?: string;
  created_at: string;
  updated_at: string;
  posts: {
    id: string;
    content: string;
    media_urls: string[];
    user_id: string;
  };
}

export default function ScheduledPostsPage() {
  const [scheduledPosts, setScheduledPosts] = useState<ScheduledPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('PENDING');
  const [processingJobs, setProcessingJobs] = useState(false);

  // Fetch scheduled posts
  const fetchScheduledPosts = async (status: string = 'PENDING') => {
    try {
      setLoading(true);
      const response = await fetch(`/api/scheduled-posts?status=${status}&limit=50`);
      const data = await response.json();

      if (response.ok) {
        setScheduledPosts(data.data?.scheduled_posts || []);
      } else {
        toast.error(data.error || 'فشل في جلب المنشورات المجدولة');
      }
    } catch (error) {
      console.error('Error fetching scheduled posts:', error);
      toast.error('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  // Process scheduled posts manually
  const processScheduledPosts = async () => {
    try {
      setProcessingJobs(true);
      toast.info('جاري معالجة المنشورات المجدولة...');

      const response = await fetch('/api/process-scheduled-posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`تم معالجة ${data.processed} منشور مجدول`);
        
        // Show detailed results
        if (data.results) {
          const { successful, partial, failed } = data.results;
          if (successful > 0) toast.success(`نجح نشر ${successful} منشور`);
          if (partial > 0) toast.warning(`نشر جزئي لـ ${partial} منشور`);
          if (failed > 0) toast.error(`فشل نشر ${failed} منشور`);
        }

        // Refresh the list
        fetchScheduledPosts(activeTab);
      } else {
        toast.error(data.error || 'فشل في معالجة المنشورات المجدولة');
      }
    } catch (error) {
      console.error('Error processing scheduled posts:', error);
      toast.error('خطأ في معالجة المنشورات المجدولة');
    } finally {
      setProcessingJobs(false);
    }
  };

  // Cancel scheduled post
  const cancelScheduledPost = async (queueId: string) => {
    try {
      const response = await fetch(`/api/scheduled-posts?queue_id=${queueId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('تم إلغاء المنشور المجدول بنجاح');
        fetchScheduledPosts(activeTab);
      } else {
        toast.error(data.error || 'فشل في إلغاء المنشور المجدول');
      }
    } catch (error) {
      console.error('Error canceling scheduled post:', error);
      toast.error('خطأ في إلغاء المنشور المجدول');
    }
  };

  // Format date for display
  const formatDate = (dateString: string, timezone: string = 'UTC') => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: timezone,
      timeZoneName: 'short'
    }).format(date);
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'PENDING': return 'secondary';
      case 'PROCESSING': return 'default';
      case 'COMPLETED': return 'default';
      case 'FAILED': return 'destructive';
      default: return 'secondary';
    }
  };

  // Get status text in Arabic
  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING': return 'في الانتظار';
      case 'PROCESSING': return 'قيد المعالجة';
      case 'COMPLETED': return 'مكتمل';
      case 'FAILED': return 'فشل';
      default: return status;
    }
  };

  // Get priority text in Arabic
  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'عالية';
      case 'NORMAL': return 'عادية';
      case 'LOW': return 'منخفضة';
      default: return priority;
    }
  };

  useEffect(() => {
    fetchScheduledPosts(activeTab);
  }, [activeTab]);

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">المنشورات المجدولة</h1>
          <p className="text-muted-foreground">إدارة ومراقبة المنشورات المجدولة للنشر التلقائي</p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={processScheduledPosts}
            disabled={processingJobs}
            className="flex items-center gap-2"
          >
            {processingJobs ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Play className="h-4 w-4" />
            )}
            معالجة المنشورات المستحقة
          </Button>
          <Button
            variant="outline"
            onClick={() => fetchScheduledPosts(activeTab)}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">في الانتظار</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {scheduledPosts.filter(p => p.status === 'PENDING').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">قيد المعالجة</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {scheduledPosts.filter(p => p.status === 'PROCESSING').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">مكتمل</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {scheduledPosts.filter(p => p.status === 'COMPLETED').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">فشل</CardTitle>
            <Trash2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {scheduledPosts.filter(p => p.status === 'FAILED').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Scheduled Posts List */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة المنشورات المجدولة</CardTitle>
          <CardDescription>
            عرض وإدارة جميع المنشورات المجدولة حسب الحالة
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="PENDING">في الانتظار</TabsTrigger>
              <TabsTrigger value="PROCESSING">قيد المعالجة</TabsTrigger>
              <TabsTrigger value="COMPLETED">مكتمل</TabsTrigger>
              <TabsTrigger value="FAILED">فشل</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-6">
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                  <span className="mr-2">جاري التحميل...</span>
                </div>
              ) : scheduledPosts.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  لا توجد منشورات مجدولة في هذه الحالة
                </div>
              ) : (
                <div className="space-y-4">
                  {scheduledPosts.map((post) => (
                    <Card key={post.id} className="border-r-4 border-r-blue-500">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant={getStatusBadgeVariant(post.status)}>
                                {getStatusText(post.status)}
                              </Badge>
                              <Badge variant="outline">
                                أولوية {getPriorityText(post.priority)}
                              </Badge>
                              {post.retry_count > 0 && (
                                <Badge variant="secondary">
                                  محاولة {post.retry_count}
                                </Badge>
                              )}
                            </div>
                            
                            <h3 className="font-semibold mb-2">
                              {post.posts.content.substring(0, 100)}
                              {post.posts.content.length > 100 && '...'}
                            </h3>
                            
                            <div className="text-sm text-muted-foreground space-y-1">
                              <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                موعد النشر: {formatDate(post.scheduled_for, post.timezone)}
                              </div>
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4" />
                                تم الإنشاء: {formatDate(post.created_at)}
                              </div>
                              {post.posts.media_urls.length > 0 && (
                                <div>
                                  الوسائط: {post.posts.media_urls.length} ملف
                                </div>
                              )}
                            </div>

                            {post.error_message && (
                              <div className="mt-2 p-2 bg-destructive/10 border border-destructive/20 rounded text-sm text-destructive">
                                {post.error_message}
                              </div>
                            )}
                          </div>

                          <div className="flex gap-2">
                            {post.status === 'PENDING' && (
                              <>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    // TODO: Implement edit functionality
                                    toast.info('ميزة التعديل قيد التطوير');
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => cancelScheduledPost(post.id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
<<<<<<< HEAD
}
=======
}
>>>>>>> a523c3c4ceb92ea617667694557fa276071ef0da
