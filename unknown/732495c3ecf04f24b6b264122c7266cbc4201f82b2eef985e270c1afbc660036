#!/bin/bash

# Generate SSL certificates for localhost development
echo "🔐 Generating SSL certificates for localhost..."

# Create certificates directory
mkdir -p certs

# Generate private key
openssl genrsa -out certs/localhost.key 2048

# Generate certificate signing request
openssl req -new -key certs/localhost.key -out certs/localhost.csr -subj "/C=US/ST=CA/L=San Francisco/O=eWasl/OU=Development/CN=localhost"

# Generate self-signed certificate
openssl x509 -req -in certs/localhost.csr -signkey certs/localhost.key -out certs/localhost.crt -days 365 -extensions v3_req -extfile <(
cat <<EOF
[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF
)

# Set proper permissions
chmod 600 certs/localhost.key
chmod 644 certs/localhost.crt

echo "✅ SSL certificates generated successfully!"
echo "📁 Certificates location: ./certs/"
echo "🔑 Private key: ./certs/localhost.key"
echo "📜 Certificate: ./certs/localhost.crt"

# Copy certificates to Next.js expected locations
cp certs/localhost.key cert.key
cp certs/localhost.crt cert.crt

echo "✅ Certificates copied for Next.js HTTPS support"
