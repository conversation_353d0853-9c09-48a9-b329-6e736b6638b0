#!/usr/bin/env node

/**
 * Comprehensive Social Page Debugging Script
 * Identifies and reports any issues with the social page implementation
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 SOCIAL PAGE DEBUGGING SCRIPT');
console.log('=' * 50);

// Configuration
const BASE_PATH = process.cwd();
const SOCIAL_PAGE_PATH = 'src/app/social';
const COMPONENTS_PATH = 'src/components';

let totalIssues = 0;
let totalChecks = 0;

function logIssue(category, description, severity = 'warning') {
  totalIssues++;
  const icon = severity === 'error' ? '❌' : severity === 'warning' ? '⚠️' : 'ℹ️';
  console.log(`${icon} [${category.toUpperCase()}] ${description}`);
}

function logSuccess(category, description) {
  const icon = '✅';
  console.log(`${icon} [${category.toUpperCase()}] ${description}`);
}

function checkFileExists(filePath, description) {
  totalChecks++;
  const fullPath = path.join(BASE_PATH, filePath);
  
  if (fs.existsSync(fullPath)) {
    logSuccess('FILE', `${description}: ${filePath}`);
    return true;
  } else {
    logIssue('FILE', `Missing ${description}: ${filePath}`, 'error');
    return false;
  }
}

function checkFileContains(filePath, searchText, description) {
  totalChecks++;
  const fullPath = path.join(BASE_PATH, filePath);
  
  if (!fs.existsSync(fullPath)) {
    logIssue('CONTENT', `Cannot check ${description} - file missing: ${filePath}`, 'error');
    return false;
  }
  
  try {
    const content = fs.readFileSync(fullPath, 'utf8');
    if (content.includes(searchText)) {
      logSuccess('CONTENT', `${description} found in ${filePath}`);
      return true;
    } else {
      logIssue('CONTENT', `${description} missing in ${filePath}`, 'warning');
      return false;
    }
  } catch (error) {
    logIssue('CONTENT', `Error reading ${filePath}: ${error.message}`, 'error');
    return false;
  }
}

function analyzeImports(filePath, description) {
  totalChecks++;
  const fullPath = path.join(BASE_PATH, filePath);
  
  if (!fs.existsSync(fullPath)) {
    return;
  }
  
  try {
    const content = fs.readFileSync(fullPath, 'utf8');
    const lines = content.split('\n');
    
    let hasImportIssues = false;
    let importLines = [];
    
    lines.forEach((line, index) => {
      if (line.trim().startsWith('import ') && line.includes('/')) {
        importLines.push({ line: line.trim(), number: index + 1 });
        
        // Check for problematic imports
        if (line.includes('@heroicons/react/24/outline')) {
          logIssue('IMPORT', `Heroicons v2 import in ${filePath}:${index + 1} - should use lucide-react`, 'warning');
          hasImportIssues = true;
        }
        
        if (line.includes('fluent-ffmpeg') && !line.includes('//')) {
          logIssue('IMPORT', `FFmpeg import in ${filePath}:${index + 1} - should be commented out for production`, 'error');
          hasImportIssues = true;
        }
      }
    });
    
    if (!hasImportIssues) {
      logSuccess('IMPORT', `No import issues in ${description}`);
    }
    
    return importLines;
  } catch (error) {
    logIssue('IMPORT', `Error analyzing imports in ${filePath}: ${error.message}`, 'error');
    return [];
  }
}

function checkComponentDependencies() {
  console.log('\n📦 CHECKING COMPONENT DEPENDENCIES...\n');
  
  // Check main social page files
  const socialPageFiles = [
    'src/app/social/page.tsx',
    'src/app/social/enhanced-page.tsx',
    'src/components/social/enhanced-social-accounts.tsx',
    'src/components/analytics/enhanced-analytics-dashboard.tsx',
    'src/components/testing/enhanced-api-testing.tsx',
    'src/components/social/business-account-selector.tsx',
    'src/components/auth/supabase-provider.tsx'
  ];
  
  socialPageFiles.forEach(file => {
    checkFileExists(file, path.basename(file, '.tsx'));
    analyzeImports(file, path.basename(file, '.tsx'));
  });
}

function checkAPIEndpoints() {
  console.log('\n🔌 CHECKING API ENDPOINTS...\n');
  
  const apiEndpoints = [
    'src/app/api/social/connect/route.ts',
    'src/app/api/social/accounts/route.ts',
    'src/app/api/social/disconnect/route.ts',
    'src/app/api/linkedin/callback/route.ts',
    'src/app/api/facebook/callback/route.ts',
    'src/app/api/instagram/callback/route.ts',
    'src/app/api/x/callback/route.ts',
    'src/app/api/analytics/advanced/route.ts'
  ];
  
  apiEndpoints.forEach(endpoint => {
    checkFileExists(endpoint, `API endpoint: ${path.basename(endpoint, '.ts')}`);
  });
}

function checkSocialPageStructure() {
  console.log('\n🏗️ CHECKING SOCIAL PAGE STRUCTURE...\n');
  
  // Check if main page imports enhanced page
  checkFileContains(
    'src/app/social/page.tsx',
    'EnhancedSocialPage',
    'Enhanced social page import'
  );
  
  // Check if enhanced page has proper tabs
  checkFileContains(
    'src/app/social/enhanced-page.tsx',
    'TabsContent',
    'Tabs implementation'
  );
  
  // Check for proper auth provider usage
  checkFileContains(
    'src/app/social/enhanced-page.tsx',
    'useSupabase',
    'Supabase auth integration'
  );
  
  // Check for social platform configurations
  checkFileContains(
    'src/components/social/enhanced-social-accounts.tsx',
    'platformConfig',
    'Platform configuration object'
  );
}

function checkPackageJson() {
  console.log('\n📋 CHECKING PACKAGE.JSON...\n');
  
  try {
    const packagePath = path.join(BASE_PATH, 'package.json');
    const packageContent = fs.readFileSync(packagePath, 'utf8');
    const packageJson = JSON.parse(packageContent);
    
    // Check essential dependencies
    const requiredDeps = [
      '@heroicons/react',
      '@supabase/supabase-js',
      'next',
      'react',
      'lucide-react',
      'sonner'
    ];
    
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
        logSuccess('DEPENDENCY', `${dep} is installed`);
      } else {
        logIssue('DEPENDENCY', `Missing dependency: ${dep}`, 'error');
      }
    });
    
    // Check for dev script
    if (packageJson.scripts && packageJson.scripts.dev) {
      logSuccess('SCRIPT', 'Development script is available');
    } else {
      logIssue('SCRIPT', 'Missing dev script in package.json', 'error');
    }
    
  } catch (error) {
    logIssue('PACKAGE', `Error reading package.json: ${error.message}`, 'error');
  }
}

function checkEnvironmentVariables() {
  console.log('\n🔧 CHECKING ENVIRONMENT VARIABLES...\n');
  
  const envFiles = ['.env.local', '.env.production', '.env'];
  let envFound = false;
  
  envFiles.forEach(envFile => {
    if (fs.existsSync(path.join(BASE_PATH, envFile))) {
      logSuccess('ENV', `Environment file found: ${envFile}`);
      envFound = true;
      
      try {
        const envContent = fs.readFileSync(path.join(BASE_PATH, envFile), 'utf8');
        
        // Check for required Supabase variables
        const requiredVars = [
          'NEXT_PUBLIC_SUPABASE_URL',
          'NEXT_PUBLIC_SUPABASE_ANON_KEY'
        ];
        
        requiredVars.forEach(varName => {
          if (envContent.includes(varName)) {
            logSuccess('ENV', `${varName} is configured`);
          } else {
            logIssue('ENV', `Missing environment variable: ${varName}`, 'warning');
          }
        });
        
      } catch (error) {
        logIssue('ENV', `Error reading ${envFile}: ${error.message}`, 'error');
      }
    }
  });
  
  if (!envFound) {
    logIssue('ENV', 'No environment files found', 'error');
  }
}

function checkTypeScriptConfiguration() {
  console.log('\n📝 CHECKING TYPESCRIPT CONFIGURATION...\n');
  
  checkFileExists('tsconfig.json', 'TypeScript configuration');
  checkFileExists('next.config.js', 'Next.js configuration');
  
  // Check if there are any TypeScript errors in key files
  const tsFiles = [
    'src/app/social/enhanced-page.tsx',
    'src/components/social/enhanced-social-accounts.tsx'
  ];
  
  tsFiles.forEach(file => {
    if (fs.existsSync(path.join(BASE_PATH, file))) {
      analyzeImports(file, path.basename(file, '.tsx'));
    }
  });
}

function generateRecommendations() {
  console.log('\n💡 RECOMMENDATIONS...\n');
  
  if (totalIssues === 0) {
    console.log('🎉 No issues found! The social page appears to be properly configured.');
    return;
  }
  
  console.log('Based on the analysis, here are the recommended actions:');
  console.log('');
  
  if (totalIssues > 0) {
    console.log('1. 🔧 Fix any import issues by using lucide-react instead of heroicons');
    console.log('2. 🔗 Ensure all API endpoints are properly implemented');
    console.log('3. 🎨 Verify component dependencies are correctly installed');
    console.log('4. 🔑 Check environment variables are properly configured');
    console.log('5. 🏗️ Test the social page functionality manually');
  }
  
  console.log('\n📞 DEBUGGING COMMANDS:');
  console.log('');
  console.log('• Test development server: npm run dev');
  console.log('• Check build: npm run build');
  console.log('• Test social connections: node scripts/test-all-platforms.js');
  console.log('• Check auth system: node scripts/test-user-auth.js');
}

// Main execution
function runDiagnostics() {
  console.log(`Starting diagnostics in: ${BASE_PATH}\n`);
  
  checkPackageJson();
  checkEnvironmentVariables();
  checkComponentDependencies();
  checkAPIEndpoints();
  checkSocialPageStructure();
  checkTypeScriptConfiguration();
  
  console.log('\n📊 SUMMARY');
  console.log('=' * 30);
  console.log(`Total checks performed: ${totalChecks}`);
  console.log(`Issues found: ${totalIssues}`);
  console.log(`Success rate: ${Math.round(((totalChecks - totalIssues) / totalChecks) * 100)}%`);
  
  generateRecommendations();
}

// Run the diagnostics
runDiagnostics(); 