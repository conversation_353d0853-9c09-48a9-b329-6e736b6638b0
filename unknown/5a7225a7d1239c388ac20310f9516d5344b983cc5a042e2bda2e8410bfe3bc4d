# Simple SSL certificate generation for localhost
Write-Host "Generating SSL certificates for localhost..." -ForegroundColor Green

# Create certificates directory
New-Item -ItemType Directory -Force -Path "certs" | Out-Null

# Check if OpenSSL is available
try {
    $null = Get-Command openssl -ErrorAction Stop
    Write-Host "OpenSSL found, generating certificates..." -ForegroundColor Green
    
    # Generate private key
    & openssl genrsa -out certs/localhost.key 2048
    
    # Generate certificate signing request
    & openssl req -new -key certs/localhost.key -out certs/localhost.csr -subj "/C=US/ST=CA/L=San Francisco/O=eWasl/OU=Development/CN=localhost"
    
    # Generate self-signed certificate
    & openssl x509 -req -in certs/localhost.csr -signkey certs/localhost.key -out certs/localhost.crt -days 365
    
    # Copy certificates to Next.js expected locations
    Copy-Item "certs/localhost.key" "cert.key"
    Copy-Item "certs/localhost.crt" "cert.crt"
    
    Write-Host "SSL certificates generated successfully!" -ForegroundColor Green
    Write-Host "Certificates location: ./certs/" -ForegroundColor Yellow
    Write-Host "Private key: ./certs/localhost.key" -ForegroundColor Yellow
    Write-Host "Certificate: ./certs/localhost.crt" -ForegroundColor Yellow
    Write-Host "Certificates copied for Next.js HTTPS support" -ForegroundColor Green
    
} catch {
    Write-Host "OpenSSL not found. Please install OpenSSL or use mkcert instead." -ForegroundColor Red
    Write-Host "Alternative: Install mkcert from https://github.com/FiloSottile/mkcert" -ForegroundColor Yellow
    Write-Host "Then run: mkcert localhost 127.0.0.1 ::1" -ForegroundColor Yellow
}
