#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  console.log('Set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugLinkedInConnection() {
  console.log('🔍 LinkedIn Connection Diagnostic Tool\n');

  try {
    // 1. Check all LinkedIn accounts in database
    console.log('1. Checking all LinkedIn accounts in database...');
    const { data: allLinkedInAccounts, error: linkedInError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('platform', 'LINKEDIN')
      .order('created_at', { ascending: false });

    if (linkedInError) {
      console.error('❌ Error fetching LinkedIn accounts:', linkedInError);
      return;
    }

    console.log(`📊 Found ${allLinkedInAccounts?.length || 0} LinkedIn accounts total`);
    
    if (allLinkedInAccounts && allLinkedInAccounts.length > 0) {
      console.log('\n📋 LinkedIn Accounts Details:');
      allLinkedInAccounts.forEach((account, index) => {
        console.log(`\n${index + 1}. Account: ${account.account_name}`);
        console.log(`   User ID: ${account.user_id}`);
        console.log(`   Account ID: ${account.account_id}`);
        console.log(`   Created: ${account.created_at}`);
        console.log(`   Active: ${account.is_active}`);
        console.log(`   Has Token: ${account.access_token ? 'Yes' : 'No'}`);
      });
    }

    // 2. Check for demo user accounts
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
    console.log(`\n2. Checking demo user (${demoUserId}) accounts...`);
    
    const { data: demoAccounts, error: demoError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', demoUserId);

    if (demoError) {
      console.error('❌ Error fetching demo accounts:', demoError);
    } else {
      console.log(`📊 Demo user has ${demoAccounts?.length || 0} accounts`);
      demoAccounts?.forEach(account => {
        console.log(`   - ${account.platform}: ${account.account_name}`);
      });
    }

    // 3. Check all users who have social accounts
    console.log('\n3. Checking all users with social accounts...');
    const { data: userAccounts, error: userError } = await supabase
      .from('social_accounts')
      .select('user_id, platform, account_name')
      .order('user_id');

    if (userError) {
      console.error('❌ Error fetching user accounts:', userError);
    } else {
      const userGroups = {};
      userAccounts?.forEach(account => {
        if (!userGroups[account.user_id]) {
          userGroups[account.user_id] = [];
        }
        userGroups[account.user_id].push(`${account.platform}: ${account.account_name}`);
      });

      console.log('👥 Users with social accounts:');
      Object.entries(userGroups).forEach(([userId, accounts]) => {
        console.log(`\n   User: ${userId}`);
        accounts.forEach(account => console.log(`     - ${account}`));
      });
    }

    // 4. Check recent activity logs
    console.log('\n4. Checking recent social activity...');
    const { data: activities, error: activityError } = await supabase
      .from('activities')
      .select('*')
      .ilike('action', '%SOCIAL%')
      .order('created_at', { ascending: false })
      .limit(10);

    if (activityError) {
      console.error('❌ Error fetching activities:', activityError);
    } else {
      console.log(`📊 Found ${activities?.length || 0} recent social activities`);
      activities?.forEach(activity => {
        console.log(`   ${activity.created_at}: ${activity.action} - ${activity.details}`);
      });
    }

    // 5. Provide recommendations
    console.log('\n🔧 DIAGNOSTIC RESULTS & RECOMMENDATIONS:\n');
    
    if (allLinkedInAccounts && allLinkedInAccounts.length > 0) {
      const multipleUsers = new Set(allLinkedInAccounts.map(acc => acc.user_id)).size > 1;
      if (multipleUsers) {
        console.log('⚠️  ISSUE FOUND: LinkedIn accounts are saved under different user IDs');
        console.log('   This is why LinkedIn connections don\'t show up for some users');
        console.log('   Need to fix user ID consistency in OAuth callback');
      } else {
        console.log('✅ All LinkedIn accounts use the same user ID');
      }
    } else {
      console.log('ℹ️  No LinkedIn accounts found in database');
      console.log('   Issue might be in OAuth callback not saving accounts properly');
    }

  } catch (error) {
    console.error('❌ Diagnostic error:', error);
  }
}

// Run diagnostic
debugLinkedInConnection().then(() => {
  console.log('\n🏁 Diagnostic complete!');
  process.exit(0);
}).catch(error => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
}); 