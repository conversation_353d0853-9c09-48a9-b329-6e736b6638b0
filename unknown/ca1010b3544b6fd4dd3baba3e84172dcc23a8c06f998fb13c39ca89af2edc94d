import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { publishToFacebookReal } from '@/lib/social/real-api-implementations'

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول', details: authError?.message },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { content = 'Test post from eWasl platform - تجربة نشر من منصة إي وصل' } = body

    // Get user's social accounts
    const { data: socialAccounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'FACEBOOK')
      .eq('is_active', true)

    if (accountsError) {
      return NextResponse.json({
        error: 'Failed to fetch social accounts',
        details: accountsError.message
      }, { status: 500 })
    }

    if (!socialAccounts || socialAccounts.length === 0) {
      return NextResponse.json({
        error: 'No Facebook accounts found',
        message: 'Please connect a Facebook account first'
      }, { status: 404 })
    }

    // Create a test post in the database
    const { data: post, error: postError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content: content,
        status: 'DRAFT',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (postError) {
      return NextResponse.json({
        error: 'Failed to create test post',
        details: postError.message
      }, { status: 500 })
    }

    // Test publishing to each Facebook account
    const results = []
    for (const account of socialAccounts) {
      console.log(`Debug publishing to Facebook account: ${account.account_name}`)
      
      try {
        // Use the real publishing implementation
        const publishResult = await publishToFacebookReal(
          content,
          [], // No media
          account,
          {
            timezone: 'Asia/Riyadh',
            postId: post.id
          }
        )

        // Create post_social_account record
        if (publishResult.success) {
          const { data: psa, error: psaError } = await supabase
            .from('post_social_accounts')
            .insert({
              post_id: post.id,
              social_account_id: account.id,
              platform_post_id: publishResult.postId || null,
              platform_url: publishResult.postUrl || null,
              status: 'PUBLISHED',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single()

          if (psaError) {
            console.error('Failed to create post_social_account record:', psaError)
          }
        }

        results.push({
          accountId: account.account_id,
          accountName: account.account_name,
          publishResult
        })
      } catch (error) {
        console.error(`Error publishing to ${account.account_name}:`, error)
        results.push({
          accountId: account.account_id,
          accountName: account.account_name,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Update post status based on results
    const allSuccessful = results.every(r => r.publishResult?.success)
    await supabase
      .from('posts')
      .update({
        status: allSuccessful ? 'PUBLISHED' : 'FAILED',
        updated_at: new Date().toISOString()
      })
      .eq('id', post.id)

    return NextResponse.json({
      success: true,
      postId: post.id,
      accountsCount: socialAccounts.length,
      results
    })

  } catch (error) {
    console.error('Debug post error:', error)
    return NextResponse.json({
      error: 'Debug failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}
