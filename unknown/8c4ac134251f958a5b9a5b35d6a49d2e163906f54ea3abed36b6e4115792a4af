#!/usr/bin/env node

/**
 * 🔍 SOCIAL MEDIA CREDENTIALS CHECKER
 * 
 * This script checks which social media platforms are properly configured
 * and provides setup instructions for missing credentials.
 */

console.log('🔍 Social Media Credentials Check\n');
console.log('==================================\n');

// Required environment variables for each platform
const platformCredentials = {
  'LinkedIn': {
    variables: ['LINKEDIN_CLIENT_ID', 'LINKEDIN_CLIENT_SECRET'],
    setupUrl: 'https://developer.linkedin.com/docs/oauth',
    status: 'ready'
  },
  'Twitter/X': {
    variables: ['X_CLIENT_ID', 'X_CLIENT_SECRET'],
    setupUrl: 'https://developer.twitter.com/en/portal/dashboard',
    status: 'needs_setup'
  },
  'Facebook': {
    variables: ['FACEBOOK_APP_ID', 'FACEBOOK_APP_SECRET'],
    setupUrl: 'https://developers.facebook.com/apps/',
    status: 'needs_setup'
  },
  'Instagram': {
    variables: ['FACEBOOK_APP_ID', 'FACEBOOK_APP_SECRET'], // Uses Facebook
    setupUrl: 'https://developers.facebook.com/docs/instagram-api/',
    status: 'needs_setup'
  }
};

console.log('📊 Platform Status Summary:\n');

Object.entries(platformCredentials).forEach(([platform, config]) => {
  const hasCredentials = config.variables.every(varName => 
    process.env[varName] && process.env[varName] !== ''
  );
  
  const statusIcon = hasCredentials ? '✅' : '❌';
  const statusText = hasCredentials ? 'CONFIGURED' : 'MISSING CREDENTIALS';
  
  console.log(`${statusIcon} ${platform}: ${statusText}`);
  
  if (!hasCredentials) {
    console.log(`   Missing: ${config.variables.join(', ')}`);
    console.log(`   Setup: ${config.setupUrl}`);
  }
  
  console.log('');
});

console.log('\n🔧 Next Steps:\n');

console.log('1. **LinkedIn is working!** ✅');
console.log('   - Connected accounts should now appear correctly');
console.log('   - Users can connect LinkedIn accounts\n');

console.log('2. **To enable Twitter/X:**');
console.log('   - Go to: https://developer.twitter.com/en/portal/dashboard');
console.log('   - Create a new app or use existing');
console.log('   - Add redirect URL: https://app.ewasl.com/api/x/callback');
console.log('   - Set environment variables: X_CLIENT_ID, X_CLIENT_SECRET\n');

console.log('3. **To enable Facebook/Instagram:**');
console.log('   - Go to: https://developers.facebook.com/apps/');
console.log('   - Create a new app or use existing');
console.log('   - Add redirect URL: https://app.ewasl.com/api/facebook/callback');
console.log('   - Set environment variables: FACEBOOK_APP_ID, FACEBOOK_APP_SECRET\n');

console.log('4. **Update DigitalOcean Environment:**');
console.log('   doctl apps update 92d1f7b6-85f2-47e2-8a69-d823b1586159 --spec app-spec.yaml\n');

console.log('🎯 **Priority Order:**');
console.log('   1. Fix LinkedIn display issue (DONE ✅)');  
console.log('   2. Set up Twitter/X credentials');
console.log('   3. Set up Facebook/Instagram credentials');
console.log('   4. Test all connections end-to-end\n');

console.log('📞 **Test the fix:**');
console.log('   - Visit: https://app.ewasl.com/social');
console.log('   - LinkedIn accounts should now appear');
console.log('   - Other platforms will show credential setup errors');