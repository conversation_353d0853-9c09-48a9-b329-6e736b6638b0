import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

/**
 * DIRECT Supabase API Schema Fix Endpoint
 * Uses provided token to execute schema migration directly
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🚨 [DIRECT SCHEMA FIX] Starting direct Supabase API schema fix...');

    // Use the provided Supabase token for direct API access
    const supabaseUrl = 'https://nnxfzhxqzmriggulsudr.supabase.co';
    const supabaseToken = '********************************************';

    const supabase = createClient(supabaseUrl, supabaseToken, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    const results = {
      timestamp: new Date().toISOString(),
      operations: [] as any[],
      success: true,
      errors: [] as string[]
    };

    // Step 1: Check current table structure
    try {
      console.log('🔍 Checking current social_accounts table structure...');
      const { data: currentColumns, error: columnsError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable, column_default')
        .eq('table_name', 'social_accounts')
        .eq('table_schema', 'public');

      if (columnsError) {
        results.errors.push(`Current schema check: ${columnsError.message}`);
      } else {
        results.operations.push({
          operation: 'current_schema_check',
          status: 'success',
          message: 'Current schema retrieved successfully',
          current_columns: currentColumns?.map(col => col.column_name) || []
        });
      }
    } catch (error) {
      results.errors.push(`Current schema check: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Step 2: Add connection_status column
    try {
      console.log('🔧 Adding connection_status column...');
      const { error: connectionStatusError } = await supabase.rpc('exec_sql', {
        sql: `
          DO $$ 
          BEGIN 
            IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns 
              WHERE table_name = 'social_accounts' 
              AND column_name = 'connection_status'
              AND table_schema = 'public'
            ) THEN
              ALTER TABLE public.social_accounts 
              ADD COLUMN connection_status TEXT DEFAULT 'connected';
              
              ALTER TABLE public.social_accounts 
              ADD CONSTRAINT check_connection_status 
              CHECK (connection_status IN ('connected', 'expired', 'error', 'reconnecting', 'disconnected'));
            END IF;
          END $$;
        `
      });

      if (connectionStatusError) {
        results.errors.push(`Connection status column: ${connectionStatusError.message}`);
      } else {
        results.operations.push({
          operation: 'add_connection_status_column',
          status: 'success',
          message: 'Connection status column added successfully'
        });
      }
    } catch (error) {
      results.errors.push(`Connection status column: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Step 3: Add status column
    try {
      console.log('🔧 Adding status column...');
      const { error: statusError } = await supabase.rpc('exec_sql', {
        sql: `
          DO $$ 
          BEGIN 
            IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns 
              WHERE table_name = 'social_accounts' 
              AND column_name = 'status'
              AND table_schema = 'public'
            ) THEN
              ALTER TABLE public.social_accounts 
              ADD COLUMN status TEXT DEFAULT 'ACTIVE';
              
              ALTER TABLE public.social_accounts 
              ADD CONSTRAINT check_status 
              CHECK (status IN ('ACTIVE', 'INACTIVE', 'EXPIRED', 'ERROR'));
            END IF;
          END $$;
        `
      });

      if (statusError) {
        results.errors.push(`Status column: ${statusError.message}`);
      } else {
        results.operations.push({
          operation: 'add_status_column',
          status: 'success',
          message: 'Status column added successfully'
        });
      }
    } catch (error) {
      results.errors.push(`Status column: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Step 4: Add Facebook page-specific columns
    const pageColumns = [
      { name: 'page_id', type: 'TEXT' },
      { name: 'page_access_token', type: 'TEXT' },
      { name: 'page_name', type: 'TEXT' },
      { name: 'instagram_business_account_id', type: 'TEXT' }
    ];

    for (const column of pageColumns) {
      try {
        console.log(`🔧 Adding ${column.name} column...`);
        const { error } = await supabase.rpc('exec_sql', {
          sql: `
            DO $$ 
            BEGIN 
              IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'social_accounts' 
                AND column_name = '${column.name}'
                AND table_schema = 'public'
              ) THEN
                ALTER TABLE public.social_accounts ADD COLUMN ${column.name} ${column.type};
              END IF;
            END $$;
          `
        });

        if (error) {
          results.errors.push(`${column.name} column: ${error.message}`);
        } else {
          results.operations.push({
            operation: `add_${column.name}_column`,
            status: 'success',
            message: `${column.name} column added successfully`
          });
        }
      } catch (error) {
        results.errors.push(`${column.name} column: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Step 5: Update existing records with default values
    try {
      console.log('🔧 Updating existing records with default values...');
      const { error: updateError } = await supabase
        .from('social_accounts')
        .update({
          connection_status: 'connected',
          status: 'ACTIVE'
        })
        .or('connection_status.is.null,status.is.null');

      if (updateError) {
        results.errors.push(`Update records: ${updateError.message}`);
      } else {
        results.operations.push({
          operation: 'update_existing_records',
          status: 'success',
          message: 'Updated existing records with default values'
        });
      }
    } catch (error) {
      results.errors.push(`Update records: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Step 6: Verify final schema
    try {
      console.log('🔍 Verifying final schema...');
      const { data: finalSchema, error: schemaError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable, column_default')
        .eq('table_name', 'social_accounts')
        .eq('table_schema', 'public');

      if (schemaError) {
        results.errors.push(`Final schema verification: ${schemaError.message}`);
      } else {
        results.operations.push({
          operation: 'final_schema_verification',
          status: 'success',
          message: 'Final schema verified successfully',
          final_columns: finalSchema?.map(col => ({
            name: col.column_name,
            type: col.data_type,
            nullable: col.is_nullable,
            default: col.column_default
          })) || []
        });
      }
    } catch (error) {
      results.errors.push(`Final schema verification: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Step 7: Test OAuth tokens endpoint
    try {
      console.log('🧪 Testing OAuth tokens endpoint...');
      const testResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com'}/api/test-oauth-tokens`);
      const testData = await testResponse.json();
      
      results.operations.push({
        operation: 'oauth_tokens_test',
        status: testData.success ? 'success' : 'failed',
        message: testData.success ? 'OAuth tokens test passed' : 'OAuth tokens test failed',
        test_result: testData
      });
    } catch (error) {
      results.errors.push(`OAuth tokens test: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    if (results.errors.length > 0) {
      results.success = false;
    }

    console.log('✅ [DIRECT SCHEMA FIX] Direct Supabase API schema fix completed');
    
    return NextResponse.json({
      success: results.success,
      message: results.success ? 'تم إصلاح مخطط قاعدة البيانات بنجاح باستخدام Supabase API' : 'فشل في إصلاح مخطط قاعدة البيانات',
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [DIRECT SCHEMA FIX] Critical failure:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'فشل حرج في إصلاح مخطط قاعدة البيانات باستخدام Supabase API',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'استخدم POST لتنفيذ إصلاح مخطط قاعدة البيانات المباشر',
    usage: 'POST /api/direct-schema-fix',
    token: 'Using provided Supabase token: ********************************************',
    timestamp: new Date().toISOString()
  });
}
