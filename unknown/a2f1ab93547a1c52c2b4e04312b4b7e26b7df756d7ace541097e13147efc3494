#!/bin/bash

# eWasl Production Integration Testing Script
# Tests all implemented features with production configuration

set -e

# Configuration
DOMAIN="app.ewasl.com"
BASE_URL="https://$DOMAIN"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

test_endpoint() {
    local endpoint=$1
    local description=$2
    local expected_status=${3:-200}
    
    log_info "Testing: $description"
    
    response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$BASE_URL$endpoint" || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        log_success "$description - OK"
        return 0
    else
        log_error "$description - Failed (HTTP $response)"
        if [ -f /tmp/response.json ]; then
            echo "Response: $(cat /tmp/response.json)"
        fi
        return 1
    fi
}

# Test 1: System Health
test_system_health() {
    echo
    log_info "=== TESTING SYSTEM HEALTH ==="
    
    test_endpoint "/api/system/health" "System Health Check"
    test_endpoint "/api/system/status" "System Status"
    test_endpoint "/api/system/version" "System Version"
}

# Test 2: Authentication System
test_authentication() {
    echo
    log_info "=== TESTING AUTHENTICATION SYSTEM ==="
    
    test_endpoint "/auth/signin" "Sign In Page" 200
    test_endpoint "/api/auth/providers" "Auth Providers"
    test_endpoint "/api/auth/session" "Auth Session" 401
}

# Test 3: Payment System (Stripe)
test_payment_system() {
    echo
    log_info "=== TESTING PAYMENT SYSTEM ==="
    
    test_endpoint "/api/billing/health" "Billing Health Check"
    test_endpoint "/api/billing/plans" "Subscription Plans"
    test_endpoint "/api/stripe/webhook" "Stripe Webhook Endpoint" 405
}

# Test 4: Social Media OAuth
test_social_oauth() {
    echo
    log_info "=== TESTING SOCIAL MEDIA OAUTH ==="
    
    test_endpoint "/api/linkedin/auth" "LinkedIn OAuth Redirect" 302
    test_endpoint "/api/facebook/auth" "Facebook OAuth Redirect" 302
    test_endpoint "/api/x/auth" "Twitter/X OAuth Redirect" 302
}

# Test 5: Background Job System
test_background_jobs() {
    echo
    log_info "=== TESTING BACKGROUND JOB SYSTEM ==="
    
    test_endpoint "/api/scheduler/status" "Scheduler Status"
    test_endpoint "/api/scheduler/jobs" "Job Queue Status"
    test_endpoint "/api/cron/scheduler" "Cron Scheduler Health"
}

# Test 6: Media Processing
test_media_processing() {
    echo
    log_info "=== TESTING MEDIA PROCESSING ==="
    
    test_endpoint "/api/media/health" "Media Processing Health"
    test_endpoint "/api/test/enhanced-media-pipeline-final" "Enhanced Media Pipeline"
}

# Test 7: API Endpoints
test_api_endpoints() {
    echo
    log_info "=== TESTING API ENDPOINTS ==="
    
    test_endpoint "/api/posts" "Posts API" 401
    test_endpoint "/api/accounts" "Accounts API" 401
    test_endpoint "/api/analytics" "Analytics API" 401
    test_endpoint "/api/content" "Content API" 401
}

# Test 8: Database Connectivity
test_database() {
    echo
    log_info "=== TESTING DATABASE CONNECTIVITY ==="
    
    test_endpoint "/api/test/database" "Database Connection Test"
    test_endpoint "/api/test/supabase" "Supabase Connection Test"
}

# Test 9: Performance Tests
test_performance() {
    echo
    log_info "=== TESTING PERFORMANCE ==="
    
    log_info "Testing page load times..."
    
    # Test main pages
    local start_time=$(date +%s%N)
    curl -s "$BASE_URL/" > /dev/null
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 ))
    
    if [ $duration -lt 2000 ]; then
        log_success "Homepage load time: ${duration}ms (< 2s)"
    else
        log_warning "Homepage load time: ${duration}ms (> 2s)"
    fi
    
    # Test dashboard
    start_time=$(date +%s%N)
    curl -s "$BASE_URL/dashboard" > /dev/null
    end_time=$(date +%s%N)
    duration=$(( (end_time - start_time) / 1000000 ))
    
    if [ $duration -lt 3000 ]; then
        log_success "Dashboard load time: ${duration}ms (< 3s)"
    else
        log_warning "Dashboard load time: ${duration}ms (> 3s)"
    fi
}

# Test 10: Security Headers
test_security() {
    echo
    log_info "=== TESTING SECURITY CONFIGURATION ==="
    
    log_info "Checking security headers..."
    
    headers=$(curl -s -I "$BASE_URL/")
    
    if echo "$headers" | grep -q "X-Frame-Options"; then
        log_success "X-Frame-Options header present"
    else
        log_warning "X-Frame-Options header missing"
    fi
    
    if echo "$headers" | grep -q "X-Content-Type-Options"; then
        log_success "X-Content-Type-Options header present"
    else
        log_warning "X-Content-Type-Options header missing"
    fi
    
    if echo "$headers" | grep -q "Strict-Transport-Security"; then
        log_success "HSTS header present"
    else
        log_warning "HSTS header missing"
    fi
}

# Generate Test Report
generate_report() {
    echo
    log_info "=== GENERATING TEST REPORT ==="
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="test-report-$(date '+%Y%m%d-%H%M%S').txt"
    
    {
        echo "eWasl Production Integration Test Report"
        echo "========================================"
        echo "Timestamp: $timestamp"
        echo "Domain: $DOMAIN"
        echo "Base URL: $BASE_URL"
        echo
        echo "Test Results:"
        echo "- System Health: $(test_system_health > /dev/null 2>&1 && echo "PASS" || echo "FAIL")"
        echo "- Authentication: $(test_authentication > /dev/null 2>&1 && echo "PASS" || echo "FAIL")"
        echo "- Payment System: $(test_payment_system > /dev/null 2>&1 && echo "PASS" || echo "FAIL")"
        echo "- Social OAuth: $(test_social_oauth > /dev/null 2>&1 && echo "PASS" || echo "FAIL")"
        echo "- Background Jobs: $(test_background_jobs > /dev/null 2>&1 && echo "PASS" || echo "FAIL")"
        echo "- Media Processing: $(test_media_processing > /dev/null 2>&1 && echo "PASS" || echo "FAIL")"
        echo "- API Endpoints: $(test_api_endpoints > /dev/null 2>&1 && echo "PASS" || echo "FAIL")"
        echo "- Database: $(test_database > /dev/null 2>&1 && echo "PASS" || echo "FAIL")"
        echo
        echo "Performance and Security tests completed."
        echo
        echo "Next Steps:"
        echo "1. Review any failed tests"
        echo "2. Configure missing API credentials"
        echo "3. Complete OAuth application setup"
        echo "4. Test end-to-end user workflows"
    } > "$report_file"
    
    log_success "Test report generated: $report_file"
}

# Main execution
main() {
    echo
    log_info "🧪 Starting eWasl Production Integration Tests"
    log_info "Testing domain: $DOMAIN"
    echo
    
    # Run all tests
    test_system_health
    test_authentication
    test_payment_system
    test_social_oauth
    test_background_jobs
    test_media_processing
    test_api_endpoints
    test_database
    test_performance
    test_security
    
    # Generate report
    generate_report
    
    echo
    log_success "🎉 Production integration testing completed!"
    echo
    echo "📋 Summary:"
    echo "- All core systems tested"
    echo "- Performance benchmarks recorded"
    echo "- Security configuration verified"
    echo "- Test report generated"
    echo
    echo "🔗 Application URL: $BASE_URL"
    echo "📊 Health Check: $BASE_URL/api/system/health"
    echo
}

# Run main function
main "$@"
