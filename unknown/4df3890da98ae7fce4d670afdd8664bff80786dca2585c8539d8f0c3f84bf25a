#!/usr/bin/env node

/**
 * DigitalOcean API Connection Test
 * Tests the provided DigitalOcean API token for connectivity and permissions
 */

const https = require('https');

// DigitalOcean API Configuration
const DO_API_TOKEN = '***********************************************************************';
const DO_API_BASE = 'https://api.digitalocean.com/v2';

/**
 * Make authenticated request to DigitalOcean API
 */
function makeDigitalOceanRequest(endpoint, method = 'GET') {
  return new Promise((resolve, reject) => {
    const url = new URL(endpoint, DO_API_BASE);
    
    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Authorization': `Bearer ${DO_API_TOKEN}`,
        'Content-Type': 'application/json',
        'User-Agent': 'eWasl-API-Test/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

/**
 * Test DigitalOcean API connectivity
 */
async function testDigitalOceanAPI() {
  console.log('🌊 Testing DigitalOcean API Connection');
  console.log('=' * 50);
  
  const tests = [
    {
      name: 'Account Information',
      endpoint: '/account',
      description: 'Get account details and verify token'
    },
    {
      name: 'Apps List',
      endpoint: '/apps',
      description: 'List all apps in the account'
    },
    {
      name: 'Droplets List',
      endpoint: '/droplets',
      description: 'List all droplets'
    },
    {
      name: 'Domains List',
      endpoint: '/domains',
      description: 'List all domains'
    },
    {
      name: 'Projects List',
      endpoint: '/projects',
      description: 'List all projects'
    }
  ];

  const results = [];

  for (const test of tests) {
    console.log(`\n🔍 Testing: ${test.name}`);
    console.log(`📍 Endpoint: ${test.endpoint}`);
    console.log(`📝 Description: ${test.description}`);
    
    try {
      const response = await makeDigitalOceanRequest(test.endpoint);
      
      if (response.status === 200) {
        console.log(`✅ SUCCESS: ${test.name}`);
        console.log(`📊 Status: ${response.status}`);
        
        if (test.endpoint === '/account') {
          console.log(`👤 Account Email: ${response.data.account?.email || 'N/A'}`);
          console.log(`💰 Account Status: ${response.data.account?.status || 'N/A'}`);
          console.log(`🏢 Team: ${response.data.account?.team?.name || 'Personal'}`);
        } else if (test.endpoint === '/apps') {
          const apps = response.data.apps || [];
          console.log(`📱 Total Apps: ${apps.length}`);
          if (apps.length > 0) {
            apps.forEach((app, index) => {
              console.log(`   ${index + 1}. ${app.spec?.name || 'Unnamed'} (${app.live_url || 'No URL'})`);
            });
          }
        } else if (test.endpoint === '/droplets') {
          const droplets = response.data.droplets || [];
          console.log(`💧 Total Droplets: ${droplets.length}`);
        } else if (test.endpoint === '/domains') {
          const domains = response.data.domains || [];
          console.log(`🌐 Total Domains: ${domains.length}`);
          if (domains.length > 0) {
            domains.forEach((domain, index) => {
              console.log(`   ${index + 1}. ${domain.name}`);
            });
          }
        } else if (test.endpoint === '/projects') {
          const projects = response.data.projects || [];
          console.log(`📁 Total Projects: ${projects.length}`);
        }
        
        results.push({ test: test.name, status: 'PASSED', response });
      } else {
        console.log(`❌ FAILED: ${test.name}`);
        console.log(`📊 Status: ${response.status}`);
        console.log(`❗ Error: ${response.data.message || 'Unknown error'}`);
        results.push({ test: test.name, status: 'FAILED', error: response.data });
      }
    } catch (error) {
      console.log(`💥 ERROR: ${test.name}`);
      console.log(`❗ Error: ${error.message}`);
      results.push({ test: test.name, status: 'ERROR', error: error.message });
    }
  }

  // Summary
  console.log('\n' + '=' * 50);
  console.log('📋 DIGITALOCEAN API TEST SUMMARY');
  console.log('=' * 50);
  
  const passed = results.filter(r => r.status === 'PASSED').length;
  const failed = results.filter(r => r.status === 'FAILED').length;
  const errors = results.filter(r => r.status === 'ERROR').length;
  
  console.log(`✅ Passed: ${passed}/${tests.length}`);
  console.log(`❌ Failed: ${failed}/${tests.length}`);
  console.log(`💥 Errors: ${errors}/${tests.length}`);
  
  if (passed === tests.length) {
    console.log('\n🎉 ALL TESTS PASSED! DigitalOcean API is working perfectly.');
  } else if (passed > 0) {
    console.log('\n⚠️  PARTIAL SUCCESS: Some tests passed, check failed tests above.');
  } else {
    console.log('\n🚨 ALL TESTS FAILED: Check your API token and permissions.');
  }
  
  return results;
}

// Run the tests
if (require.main === module) {
  testDigitalOceanAPI()
    .then((results) => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Fatal error:', error);
      process.exit(1);
    });
}

module.exports = { testDigitalOceanAPI, makeDigitalOceanRequest };
