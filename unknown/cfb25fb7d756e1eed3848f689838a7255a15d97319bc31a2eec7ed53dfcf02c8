#!/usr/bin/env node

const http = require('http');

console.log('🧪 Testing Middleware Locally');
console.log('=============================');

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', reject);
    req.setTimeout(5000);
  });
}

async function testLocalMiddleware() {
  const LOCAL_URL = 'http://localhost:3000';
  
  console.log(`\n🌐 Testing: ${LOCAL_URL}`);
  console.log('Make sure to run "npm run dev" in another terminal first!\n');
  
  try {
    // Test health endpoint with headers
    console.log('📡 Checking Local API Health & Headers...');
    const healthResponse = await makeRequest(`${LOCAL_URL}/api/health`);
    
    console.log(`Status: ${healthResponse.statusCode}`);
    console.log('\n🔒 Security Headers:');
    
    const securityHeaders = [
      'x-frame-options',
      'x-content-type-options',
      'referrer-policy',
      'x-xss-protection',
      'permissions-policy'
    ];
    
    let headersFound = 0;
    securityHeaders.forEach(header => {
      const value = healthResponse.headers[header];
      if (value) {
        console.log(`✅ ${header}: ${value}`);
        headersFound++;
      } else {
        console.log(`❌ ${header}: Missing`);
      }
    });
    
    console.log(`\n📊 Security Headers: ${headersFound}/${securityHeaders.length} found`);
    
    if (headersFound >= 4) {
      console.log('🟢 MIDDLEWARE WORKING - Headers are being applied locally');
      console.log('   Issue is likely with deployment, not code');
    } else {
      console.log('🔴 MIDDLEWARE ISSUE - Headers not being applied locally');
      console.log('   Check middleware.ts configuration');
    }
    
  } catch (error) {
    console.error('❌ Error testing local middleware:', error.message);
    console.log('\n💡 Make sure to:');
    console.log('   1. Run "npm run dev" in another terminal');
    console.log('   2. Wait for the dev server to start');
    console.log('   3. Then run this test again');
  }
}

testLocalMiddleware(); 