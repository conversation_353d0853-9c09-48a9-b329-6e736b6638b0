#!/usr/bin/env node

/**
 * Social Connection Verification Script
 * 
 * This script verifies all social media connections in the database
 * and reports any issues. It checks:
 * 
 * 1. Token validity
 * 2. Token expiration
 * 3. API access
 * 4. Permission scopes
 * 
 * Usage: node verify-social-connections.js [--fix] [--verbose]
 * 
 * Options:
 *   --fix       Attempt to refresh tokens that are expired or invalid
 *   --verbose   Show detailed information for each connection
 */

const { PrismaClient } = require('@prisma/client');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const writeFile = promisify(fs.writeFile);

// Load environment variables
require('dotenv').config();

// CLI arguments
const args = process.argv.slice(2);
const shouldFix = args.includes('--fix');
const verbose = args.includes('--verbose');

// Initialize Prisma client
const prisma = new PrismaClient();

// Social platform configurations
const platforms = {
  twitter: {
    name: 'Twitter/X',
    apiEndpoint: 'https://api.twitter.com/2/users/me',
    tokenRefreshEndpoint: 'https://api.twitter.com/2/oauth2/token',
    checkFunction: checkTwitterConnection,
    refreshFunction: refreshTwitterToken,
  },
  facebook: {
    name: 'Facebook',
    apiEndpoint: 'https://graph.facebook.com/v17.0/me',
    tokenRefreshEndpoint: 'https://graph.facebook.com/v17.0/oauth/access_token',
    checkFunction: checkFacebookConnection,
    refreshFunction: refreshFacebookToken,
  },
  instagram: {
    name: 'Instagram',
    apiEndpoint: 'https://graph.instagram.com/v17.0/me',
    tokenRefreshEndpoint: 'https://graph.instagram.com/refresh_access_token',
    checkFunction: checkInstagramConnection,
    refreshFunction: refreshInstagramToken,
  },
  linkedin: {
    name: 'LinkedIn',
    apiEndpoint: 'https://api.linkedin.com/v2/me',
    tokenRefreshEndpoint: 'https://www.linkedin.com/oauth/v2/accessToken',
    checkFunction: checkLinkedInConnection,
    refreshFunction: refreshLinkedInToken,
  }
};

// Results tracking
const results = {
  total: 0,
  valid: 0,
  expired: 0,
  invalid: 0,
  refreshed: 0,
  failed: 0,
  byPlatform: {},
  details: []
};

// Main function
async function main() {
  console.log('🔍 Starting social connection verification...');
  
  try {
    // Get all social connections from database
    const connections = await prisma.socialConnection.findMany({
      include: {
        user: {
          select: {
            id: true,
            email: true
          }
        },
        business: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });
    
    results.total = connections.length;
    console.log(`Found ${connections.length} social connections to verify`);
    
    // Initialize platform stats
    for (const platform of Object.keys(platforms)) {
      results.byPlatform[platform] = {
        total: 0,
        valid: 0,
        expired: 0,
        invalid: 0,
        refreshed: 0,
        failed: 0
      };
    }
    
    // Check each connection
    for (const connection of connections) {
      const platform = connection.platform.toLowerCase();
      
      if (!platforms[platform]) {
        console.log(`⚠️ Unknown platform: ${platform}`);
        continue;
      }
      
      results.byPlatform[platform].total++;
      
      const platformConfig = platforms[platform];
      console.log(`\nChecking ${platformConfig.name} connection for ${connection.user?.email || 'unknown user'}`);
      
      try {
        // Check connection status
        const status = await platformConfig.checkFunction(connection);
        
        if (status.valid) {
          results.valid++;
          results.byPlatform[platform].valid++;
          console.log(`✅ Valid ${platformConfig.name} connection`);
          
          if (verbose) {
            console.log(`   - Expires: ${status.expiresAt ? new Date(status.expiresAt).toLocaleString() : 'N/A'}`);
            console.log(`   - User ID: ${connection.providerUserId}`);
            console.log(`   - Scopes: ${connection.scope || 'N/A'}`);
          }
        } else if (status.expired) {
          results.expired++;
          results.byPlatform[platform].expired++;
          console.log(`⏳ Expired ${platformConfig.name} connection`);
          
          if (shouldFix) {
            try {
              const refreshed = await platformConfig.refreshFunction(connection);
              
              if (refreshed) {
                results.refreshed++;
                results.byPlatform[platform].refreshed++;
                console.log(`🔄 Successfully refreshed token`);
              } else {
                results.failed++;
                results.byPlatform[platform].failed++;
                console.log(`❌ Failed to refresh token`);
              }
            } catch (error) {
              results.failed++;
              results.byPlatform[platform].failed++;
              console.error(`❌ Error refreshing token: ${error.message}`);
            }
          }
        } else {
          results.invalid++;
          results.byPlatform[platform].invalid++;
          console.log(`❌ Invalid ${platformConfig.name} connection: ${status.error}`);
        }
        
        // Store details for report
        results.details.push({
          platform: platformConfig.name,
          userId: connection.user?.id,
          userEmail: connection.user?.email,
          businessId: connection.business?.id,
          businessName: connection.business?.name,
          providerUserId: connection.providerUserId,
          status: status.valid ? 'valid' : status.expired ? 'expired' : 'invalid',
          error: status.error,
          expiresAt: status.expiresAt,
          lastChecked: new Date().toISOString()
        });
        
      } catch (error) {
        results.invalid++;
        results.byPlatform[platform].invalid++;
        console.error(`❌ Error checking connection: ${error.message}`);
        
        results.details.push({
          platform: platformConfig.name,
          userId: connection.user?.id,
          userEmail: connection.user?.email,
          businessId: connection.business?.id,
          businessName: connection.business?.name,
          providerUserId: connection.providerUserId,
          status: 'error',
          error: error.message,
          lastChecked: new Date().toISOString()
        });
      }
    }
    
    // Generate report
    await generateReport();
    
    console.log('\n📊 Summary:');
    console.log(`Total connections: ${results.total}`);
    console.log(`Valid: ${results.valid}`);
    console.log(`Expired: ${results.expired}`);
    console.log(`Invalid: ${results.invalid}`);
    
    if (shouldFix) {
      console.log(`Refreshed: ${results.refreshed}`);
      console.log(`Failed to refresh: ${results.failed}`);
    }
    
    console.log('\nDetailed report saved to social-connections-report.json');
    
  } catch (error) {
    console.error('❌ Error during verification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Platform-specific check functions
async function checkTwitterConnection(connection) {
  try {
    const response = await fetch(platforms.twitter.apiEndpoint, {
      headers: {
        'Authorization': `Bearer ${connection.accessToken}`
      }
    });
    
    const data = await response.json();
    
    if (response.ok && data.data && data.data.id) {
      // Check if token is about to expire (if we have expiry info)
      if (connection.expiresAt) {
        const now = new Date();
        const expiryDate = new Date(connection.expiresAt);
        
        if (expiryDate < now) {
          return { valid: false, expired: true, expiresAt: connection.expiresAt };
        }
      }
      
      return { valid: true, expiresAt: connection.expiresAt };
    } else {
      return { 
        valid: false, 
        expired: false, 
        error: data.errors ? data.errors[0]?.message : 'Unknown error' 
      };
    }
  } catch (error) {
    return { valid: false, expired: false, error: error.message };
  }
}

async function checkFacebookConnection(connection) {
  try {
    const response = await fetch(`${platforms.facebook.apiEndpoint}?fields=id,name&access_token=${connection.accessToken}`);
    const data = await response.json();
    
    if (response.ok && data.id) {
      // Check expiry if available
      if (connection.expiresAt) {
        const now = new Date();
        const expiryDate = new Date(connection.expiresAt);
        
        if (expiryDate < now) {
          return { valid: false, expired: true, expiresAt: connection.expiresAt };
        }
      }
      
      return { valid: true, expiresAt: connection.expiresAt };
    } else {
      return { 
        valid: false, 
        expired: false, 
        error: data.error ? data.error.message : 'Unknown error' 
      };
    }
  } catch (error) {
    return { valid: false, expired: false, error: error.message };
  }
}

async function checkInstagramConnection(connection) {
  try {
    const response = await fetch(`${platforms.instagram.apiEndpoint}?fields=id,username&access_token=${connection.accessToken}`);
    const data = await response.json();
    
    if (response.ok && data.id) {
      // Check expiry if available
      if (connection.expiresAt) {
        const now = new Date();
        const expiryDate = new Date(connection.expiresAt);
        
        if (expiryDate < now) {
          return { valid: false, expired: true, expiresAt: connection.expiresAt };
        }
      }
      
      return { valid: true, expiresAt: connection.expiresAt };
    } else {
      return { 
        valid: false, 
        expired: false, 
        error: data.error ? data.error.message : 'Unknown error' 
      };
    }
  } catch (error) {
    return { valid: false, expired: false, error: error.message };
  }
}

async function checkLinkedInConnection(connection) {
  try {
    const response = await fetch(platforms.linkedin.apiEndpoint, {
      headers: {
        'Authorization': `Bearer ${connection.accessToken}`,
        'X-Restli-Protocol-Version': '2.0.0'
      }
    });
    
    const data = await response.json();
    
    if (response.ok && data.id) {
      // Check expiry if available
      if (connection.expiresAt) {
        const now = new Date();
        const expiryDate = new Date(connection.expiresAt);
        
        if (expiryDate < now) {
          return { valid: false, expired: true, expiresAt: connection.expiresAt };
        }
      }
      
      return { valid: true, expiresAt: connection.expiresAt };
    } else {
      return { 
        valid: false, 
        expired: false, 
        error: data.error ? data.error.message : 'Unknown error' 
      };
    }
  } catch (error) {
    return { valid: false, expired: false, error: error.message };
  }
}

// Token refresh functions
async function refreshTwitterToken(connection) {
  try {
    // Twitter requires client credentials for refresh
    const clientId = process.env.TWITTER_CLIENT_ID;
    const clientSecret = process.env.TWITTER_CLIENT_SECRET;
    
    if (!clientId || !clientSecret) {
      throw new Error('Twitter client credentials not configured');
    }
    
    if (!connection.refreshToken) {
      throw new Error('No refresh token available');
    }
    
    const params = new URLSearchParams();
    params.append('grant_type', 'refresh_token');
    params.append('refresh_token', connection.refreshToken);
    params.append('client_id', clientId);
    
    const response = await fetch(platforms.twitter.tokenRefreshEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params.toString()
    });
    
    const data = await response.json();
    
    if (response.ok && data.access_token) {
      // Calculate new expiry date
      const expiresAt = new Date();
      expiresAt.setSeconds(expiresAt.getSeconds() + data.expires_in);
      
      // Update token in database
      await prisma.socialConnection.update({
        where: { id: connection.id },
        data: {
          accessToken: data.access_token,
          refreshToken: data.refresh_token || connection.refreshToken,
          expiresAt: expiresAt,
          lastUpdated: new Date()
        }
      });
      
      return true;
    } else {
      console.error('Failed to refresh Twitter token:', data);
      return false;
    }
  } catch (error) {
    console.error('Error refreshing Twitter token:', error);
    return false;
  }
}

async function refreshFacebookToken(connection) {
  try {
    const clientId = process.env.FACEBOOK_CLIENT_ID;
    const clientSecret = process.env.FACEBOOK_CLIENT_SECRET;
    
    if (!clientId || !clientSecret) {
      throw new Error('Facebook client credentials not configured');
    }
    
    const params = new URLSearchParams();
    params.append('grant_type', 'fb_exchange_token');
    params.append('client_id', clientId);
    params.append('client_secret', clientSecret);
    params.append('fb_exchange_token', connection.accessToken);
    
    const response = await fetch(`${platforms.facebook.tokenRefreshEndpoint}?${params.toString()}`);
    const data = await response.json();
    
    if (response.ok && data.access_token) {
      // Calculate new expiry date (typically 60 days for long-lived tokens)
      const expiresAt = new Date();
      expiresAt.setSeconds(expiresAt.getSeconds() + (data.expires_in || 5184000)); // Default to 60 days
      
      // Update token in database
      await prisma.socialConnection.update({
        where: { id: connection.id },
        data: {
          accessToken: data.access_token,
          expiresAt: expiresAt,
          lastUpdated: new Date()
        }
      });
      
      return true;
    } else {
      console.error('Failed to refresh Facebook token:', data);
      return false;
    }
  } catch (error) {
    console.error('Error refreshing Facebook token:', error);
    return false;
  }
}

async function refreshInstagramToken(connection) {
  try {
    const params = new URLSearchParams();
    params.append('grant_type', 'ig_refresh_token');
    params.append('access_token', connection.accessToken);
    
    const response = await fetch(`${platforms.instagram.tokenRefreshEndpoint}?${params.toString()}`);
    const data = await response.json();
    
    if (response.ok && data.access_token) {
      // Calculate new expiry date (typically 60 days)
      const expiresAt = new Date();
      expiresAt.setSeconds(expiresAt.getSeconds() + (data.expires_in || 5184000)); // Default to 60 days
      
      // Update token in database
      await prisma.socialConnection.update({
        where: { id: connection.id },
        data: {
          accessToken: data.access_token,
          expiresAt: expiresAt,
          lastUpdated: new Date()
        }
      });
      
      return true;
    } else {
      console.error('Failed to refresh Instagram token:', data);
      return false;
    }
  } catch (error) {
    console.error('Error refreshing Instagram token:', error);
    return false;
  }
}

async function refreshLinkedInToken(connection) {
  try {
    const clientId = process.env.LINKEDIN_CLIENT_ID;
    const clientSecret = process.env.LINKEDIN_CLIENT_SECRET;
    
    if (!clientId || !clientSecret || !connection.refreshToken) {
      throw new Error('LinkedIn client credentials or refresh token not available');
    }
    
    const params = new URLSearchParams();
    params.append('grant_type', 'refresh_token');
    params.append('refresh_token', connection.refreshToken);
    params.append('client_id', clientId);
    params.append('client_secret', clientSecret);
    
    const response = await fetch(platforms.linkedin.tokenRefreshEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params.toString()
    });
    
    const data = await response.json();
    
    if (response.ok && data.access_token) {
      // Calculate new expiry date
      const expiresAt = new Date();
      expiresAt.setSeconds(expiresAt.getSeconds() + data.expires_in);
      
      // Update token in database
      await prisma.socialConnection.update({
        where: { id: connection.id },
        data: {
          accessToken: data.access_token,
          refreshToken: data.refresh_token || connection.refreshToken,
          expiresAt: expiresAt,
          lastUpdated: new Date()
        }
      });
      
      return true;
    } else {
      console.error('Failed to refresh LinkedIn token:', data);
      return false;
    }
  } catch (error) {
    console.error('Error refreshing LinkedIn token:', error);
    return false;
  }
}

// Generate detailed report
async function generateReport() {
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total: results.total,
      valid: results.valid,
      expired: results.expired,
      invalid: results.invalid,
      refreshed: results.refreshed,
      failed: results.failed
    },
    byPlatform: results.byPlatform,
    connections: results.details
  };
  
  // Save report to file
  await writeFile(
    path.join(process.cwd(), 'social-connections-report.json'),
    JSON.stringify(reportData, null, 2)
  );
}

// Run the script
main().catch(console.error); 