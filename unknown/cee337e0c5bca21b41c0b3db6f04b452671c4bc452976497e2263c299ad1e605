import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Step-by-step debugging endpoint to isolate the exact issue
export async function POST(request: NextRequest) {
  console.log('🔍 DEBUG: Starting step-by-step post creation debugging...');
  
  const debugResults: any = {
    steps: [],
    success: false,
    error: null,
    timestamp: new Date().toISOString()
  };

  try {
    // Step 1: Parse request body
    debugResults.steps.push({ step: 1, name: 'Parse request body', status: 'starting' });
    const body = await request.json();
    debugResults.steps[0].status = 'success';
    debugResults.steps[0].data = { bodyKeys: Object.keys(body) };
    console.log('✅ Step 1: Request body parsed successfully');

    // Step 2: Authentication
    debugResults.steps.push({ step: 2, name: 'Authentication', status: 'starting' });
    const { user, supabase } = await getAuthenticatedUser(request);
    debugResults.steps[1].status = 'success';
    debugResults.steps[1].data = { userId: user.id };
    console.log('✅ Step 2: User authenticated successfully:', user.id);

    // Step 3: Check table existence
    debugResults.steps.push({ step: 3, name: 'Check table existence', status: 'starting' });
    
    // Check posts table
    const { data: postsCheck, error: postsError } = await supabase
      .from('posts')
      .select('id')
      .limit(1);
    
    // Check publishing_results table
    const { data: publishingCheck, error: publishingError } = await supabase
      .from('publishing_results')
      .select('id')
      .limit(1);
    
    // Check scheduled_posts_queue table
    const { data: queueCheck, error: queueError } = await supabase
      .from('scheduled_posts_queue')
      .select('id')
      .limit(1);
    
    // Check social_accounts table
    const { data: socialCheck, error: socialError } = await supabase
      .from('social_accounts')
      .select('id')
      .limit(1);

    debugResults.steps[2].status = 'success';
    debugResults.steps[2].data = {
      postsTable: !postsError,
      publishingResultsTable: !publishingError,
      queueTable: !queueError,
      socialAccountsTable: !socialError,
      errors: {
        posts: postsError?.message,
        publishing: publishingError?.message,
        queue: queueError?.message,
        social: socialError?.message
      }
    };
    console.log('✅ Step 3: Table existence checked');

    // Step 4: Try to create a simple post
    debugResults.steps.push({ step: 4, name: 'Create simple post', status: 'starting' });
    
    const { data: post, error: createError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content: body.content || 'Debug test post',
        status: 'DRAFT',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (createError) {
      debugResults.steps[3].status = 'failed';
      debugResults.steps[3].error = createError.message;
      debugResults.error = `Step 4 failed: ${createError.message}`;
      console.error('❌ Step 4: Post creation failed:', createError);
    } else {
      debugResults.steps[3].status = 'success';
      debugResults.steps[3].data = { postId: post.id };
      console.log('✅ Step 4: Post created successfully:', post.id);
    }

    // Step 5: Try to get social accounts
    debugResults.steps.push({ step: 5, name: 'Get social accounts', status: 'starting' });
    
    const { data: socialAccounts, error: socialAccountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id);

    if (socialAccountsError) {
      debugResults.steps[4].status = 'failed';
      debugResults.steps[4].error = socialAccountsError.message;
      console.error('❌ Step 5: Social accounts fetch failed:', socialAccountsError);
    } else {
      debugResults.steps[4].status = 'success';
      debugResults.steps[4].data = { 
        accountCount: socialAccounts?.length || 0,
        accounts: socialAccounts?.map(acc => ({ id: acc.id, platform: acc.platform })) || []
      };
      console.log('✅ Step 5: Social accounts fetched:', socialAccounts?.length || 0);
    }

    debugResults.success = true;
    console.log('🎉 DEBUG: All steps completed successfully');

    return NextResponse.json(debugResults);

  } catch (error: any) {
    console.error('💥 DEBUG: Critical error:', error);
    
    debugResults.success = false;
    debugResults.error = error.message;
    debugResults.stack = error.stack;

    return NextResponse.json(debugResults, { status: 500 });
  }
}
