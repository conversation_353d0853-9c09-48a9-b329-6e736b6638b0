import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface FacebookTokenDebugResponse {
  data: {
    app_id: string;
    type: string;
    application: string;
    data_access_expires_at: number;
    expires_at: number;
    is_valid: boolean;
    scopes: string[];
    user_id: string;
    error?: {
      message: string;
      type: string;
      code: number;
    };
  };
}

interface FacebookPermissionsResponse {
  data: Array<{
    permission: string;
    status: string;
  }>;
}

interface FacebookPageTokenResponse {
  data: Array<{
    access_token: string;
    category: string;
    category_list: Array<{
      id: string;
      name: string;
    }>;
    name: string;
    id: string;
    tasks: string[];
  }>;
}

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const { user } = await getAuthenticatedUser(request);
    
    // Get all social accounts for this user
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true);

    if (accountsError) {
      return NextResponse.json(
        { error: 'Failed to fetch accounts', details: accountsError.message },
        { status: 500 }
      );
    }

    if (!accounts || accounts.length === 0) {
      return NextResponse.json(
        { message: 'No social accounts found for this user' },
        { status: 404 }
      );
    }

    const debugResults = [];

    for (const account of accounts) {
      const result: any = {
        account_id: account.account_id,
        platform: account.platform,
        account_name: account.account_name,
        created_at: account.created_at,
        last_sync_at: account.last_sync_at,
        token_expires_at: account.token_expires_at,
        page_id: account.page_id,
        page_name: account.page_name,
        has_access_token: !!account.access_token,
        has_page_token: !!account.page_access_token,
        access_token_preview: account.access_token ? account.access_token.substring(0, 20) + '...' : null,
        page_token_preview: account.page_access_token ? account.page_access_token.substring(0, 20) + '...' : null
      };

      // Debug Facebook tokens
      if (account.platform === 'facebook' && account.access_token) {
        try {
          // Debug user access token
          const userTokenDebugUrl = `https://graph.facebook.com/debug_token?input_token=${account.access_token}&access_token=${process.env.FACEBOOK_APP_ID}|${process.env.FACEBOOK_APP_SECRET}`;
          const userTokenResponse = await fetch(userTokenDebugUrl);
          const userTokenData: FacebookTokenDebugResponse = await userTokenResponse.json();
          
          result.user_token_debug = {
            is_valid: userTokenData.data?.is_valid,
            expires_at: userTokenData.data?.expires_at,
            scopes: userTokenData.data?.scopes,
            error: userTokenData.data?.error
          };

          // Get user permissions
          const permissionsUrl = `https://graph.facebook.com/me/permissions?access_token=${account.access_token}`;
          const permissionsResponse = await fetch(permissionsUrl);
          const permissionsData: FacebookPermissionsResponse = await permissionsResponse.json();
          
          result.user_permissions = permissionsData.data?.map(p => ({
            permission: p.permission,
            status: p.status
          }));

          // Debug page access token if available
          if (account.page_access_token) {
            const pageTokenDebugUrl = `https://graph.facebook.com/debug_token?input_token=${account.page_access_token}&access_token=${process.env.FACEBOOK_APP_ID}|${process.env.FACEBOOK_APP_SECRET}`;
            const pageTokenResponse = await fetch(pageTokenDebugUrl);
            const pageTokenData: FacebookTokenDebugResponse = await pageTokenResponse.json();
            
            result.page_token_debug = {
              is_valid: pageTokenData.data?.is_valid,
              expires_at: pageTokenData.data?.expires_at,
              scopes: pageTokenData.data?.scopes,
              error: pageTokenData.data?.error
            };

            // Test page posting capability
            const testPostUrl = `https://graph.facebook.com/${account.page_id}/feed`;
            const testPostResponse = await fetch(testPostUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                message: 'Test post from eWasl - this is a test and will be deleted immediately',
                access_token: account.page_access_token,
                published: false // Create as unpublished draft
              })
            });
            
            if (testPostResponse.ok) {
              const testPostData = await testPostResponse.json();
              result.page_posting_test = {
                success: true,
                post_id: testPostData.id
              };
              
              // Delete the test post immediately
              if (testPostData.id) {
                await fetch(`https://graph.facebook.com/${testPostData.id}?access_token=${account.page_access_token}`, {
                  method: 'DELETE'
                });
              }
            } else {
              const errorData = await testPostResponse.json();
              result.page_posting_test = {
                success: false,
                error: errorData
              };
            }
          }

        } catch (error) {
          result.debug_error = error instanceof Error ? error.message : 'Unknown error';
        }
      }

      debugResults.push(result);
    }

    return NextResponse.json({
      user_id: user.id,
      total_accounts: accounts.length,
      accounts: debugResults,
      debug_timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Token debug error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to debug tokens',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
