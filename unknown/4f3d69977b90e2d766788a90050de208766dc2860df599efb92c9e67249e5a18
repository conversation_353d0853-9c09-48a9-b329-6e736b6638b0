#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  console.log('Set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixLinkedInUserIds() {
  console.log('🔧 LinkedIn User ID Fix Tool\n');

  try {
    const demoUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
    
    // 1. Find all LinkedIn accounts under demo user
    console.log('1. Finding LinkedIn accounts under demo user...');
    const { data: demoLinkedInAccounts, error: demoError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', demoUserId)
      .eq('platform', 'LINKEDIN');

    if (demoError) {
      console.error('❌ Error fetching demo LinkedIn accounts:', demoError);
      return;
    }

    console.log(`📊 Found ${demoLinkedInAccounts?.length || 0} LinkedIn accounts under demo user`);

    if (!demoLinkedInAccounts || demoLinkedInAccounts.length === 0) {
      console.log('✅ No LinkedIn accounts found under demo user - nothing to fix!');
      return;
    }

    // 2. Find real users (excluding demo user)
    console.log('\n2. Finding real users...');
    const { data: allUsers, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error('❌ Error fetching users:', usersError);
      return;
    }

    const realUsers = allUsers.users.filter(u => u.id !== demoUserId);
    console.log(`📊 Found ${realUsers.length} real users`);

    if (realUsers.length === 0) {
      console.log('❌ No real users found - cannot reassign LinkedIn accounts');
      return;
    }

    // 3. Get the most recent real user (most likely to be the current user)
    const latestUser = realUsers.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )[0];

    console.log(`📋 Target user: ${latestUser.id} (${latestUser.email || 'no email'})`);

    // 4. Check if target user already has LinkedIn accounts
    const { data: existingAccounts, error: existingError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', latestUser.id)
      .eq('platform', 'LINKEDIN');

    if (existingError) {
      console.error('❌ Error checking existing accounts:', existingError);
      return;
    }

    if (existingAccounts && existingAccounts.length > 0) {
      console.log('⚠️ Target user already has LinkedIn accounts:');
      existingAccounts.forEach(acc => {
        console.log(`   - ${acc.account_name} (${acc.account_id})`);
      });
      console.log('\nSkipping transfer to avoid duplicates.');
      return;
    }

    // 5. Transfer LinkedIn accounts to the target user
    console.log('\n3. Transferring LinkedIn accounts...');
    
    for (const account of demoLinkedInAccounts) {
      console.log(`🔄 Transferring: ${account.account_name} (${account.account_id})`);
      
      const { error: updateError } = await supabase
        .from('social_accounts')
        .update({
          user_id: latestUser.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', account.id);

      if (updateError) {
        console.error(`❌ Failed to transfer account ${account.account_name}:`, updateError);
      } else {
        console.log(`✅ Successfully transferred: ${account.account_name}`);
      }
    }

    // 6. Verify the transfer
    console.log('\n4. Verifying transfer...');
    const { data: transferredAccounts, error: verifyError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', latestUser.id)
      .eq('platform', 'LINKEDIN');

    if (verifyError) {
      console.error('❌ Error verifying transfer:', verifyError);
    } else {
      console.log(`✅ Verification complete: ${transferredAccounts?.length || 0} LinkedIn accounts now under user ${latestUser.id}`);
      
      if (transferredAccounts && transferredAccounts.length > 0) {
        console.log('\n📋 Transferred accounts:');
        transferredAccounts.forEach(acc => {
          console.log(`   - ${acc.account_name} (${acc.account_id})`);
          console.log(`     Created: ${acc.created_at}`);
          console.log(`     Updated: ${acc.updated_at}`);
        });
      }
    }

    // 7. Create activity log
    await supabase
      .from('activities')
      .insert({
        user_id: latestUser.id,
        action: 'SOCIAL_ACCOUNT_TRANSFER',
        metadata: {
          platform: 'LINKEDIN',
          from_user: demoUserId,
          to_user: latestUser.id,
          account_count: demoLinkedInAccounts.length,
          timestamp: new Date().toISOString(),
        },
      });

    console.log('\n🎉 LinkedIn account transfer completed successfully!');
    console.log(`📧 User ${latestUser.email || latestUser.id} should now see their LinkedIn connections.`);

  } catch (error) {
    console.error('💥 Transfer error:', error);
  }
}

// Run the fix
fixLinkedInUserIds().then(() => {
  console.log('\n🏁 LinkedIn User ID fix complete!');
  process.exit(0);
}).catch(error => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
}); 