<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eWasl - OAuth Redirect Flow Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            color: #333;
        }
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
        }
        .platform-card {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
            background: white;
        }
        .platform-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
        }
        .platform-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .platform-name {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
        }
        .connect-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        .connect-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .connect-btn:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .status {
            margin-top: 10px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
        }
        .status.connecting {
            background: #fef5e7;
            color: #d69e2e;
            border: 1px solid #f6e05e;
        }
        .status.success {
            background: #f0fff4;
            color: #38a169;
            border: 1px solid #9ae6b4;
        }
        .status.error {
            background: #fed7d7;
            color: #e53e3e;
            border: 1px solid #feb2b2;
        }
        .log {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .feature {
            background: #f7fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .feature h4 {
            margin: 0 0 8px 0;
            color: #2d3748;
        }
        .feature p {
            margin: 0;
            color: #4a5568;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 eWasl OAuth Redirect Flow Test</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            Testing industry-standard redirect-based OAuth flow with proper PKCE implementation
        </p>

        <div class="features">
            <div class="feature">
                <h4>✅ Redirect-Based Flow</h4>
                <p>Industry standard used by Buffer, Hootsuite, Sprout Social</p>
            </div>
            <div class="feature">
                <h4>🔒 PKCE Security</h4>
                <p>RFC 7636 compliant for Twitter/X OAuth 2.0</p>
            </div>
            <div class="feature">
                <h4>🛡️ CSRF Protection</h4>
                <p>Secure state parameter validation</p>
            </div>
            <div class="feature">
                <h4>📱 Mobile Ready</h4>
                <p>Works perfectly on mobile browsers</p>
            </div>
        </div>

        <div id="platforms">
            <div class="platform-card">
                <h3>🐦 Twitter/X OAuth 2.0 with PKCE</h3>
                <button class="connect-btn" onclick="connectPlatform('TWITTER')">Connect Twitter</button>
            </div>
            <div class="platform-card">
                <h3>📘 Facebook Business Accounts</h3>
                <button class="connect-btn" onclick="connectPlatform('FACEBOOK')">Connect Facebook</button>
            </div>
        </div>

        <div class="log" id="log">
            <div>🔄 Initializing OAuth test environment...</div>
        </div>
    </div>

    <script>
        const platforms = {
            TWITTER: {
                name: 'Twitter/X',
                icon: '🐦',
                description: 'OAuth 2.0 with PKCE',
                color: '#1da1f2'
            },
            FACEBOOK: {
                name: 'Facebook',
                icon: '📘',
                description: 'Business account support',
                color: '#1877f2'
            },
            INSTAGRAM: {
                name: 'Instagram',
                icon: '📷',
                description: 'Via Facebook OAuth',
                color: '#e4405f'
            },
            LINKEDIN: {
                name: 'LinkedIn',
                icon: '💼',
                description: 'Professional networking',
                color: '#0077b5'
            }
        };

        const testUserId = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037'; // Demo user

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : '🔄';
            logDiv.innerHTML += `<div>[${timestamp}] ${icon} ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(platform, status, message) {
            const statusDiv = document.getElementById(`status-${platform}`);
            statusDiv.className = `status ${status}`;
            statusDiv.innerHTML = message;
        }

        async function connectPlatform(platform) {
            const btn = document.getElementById(`btn-${platform}`);
            btn.disabled = true;
            btn.innerHTML = 'Connecting...';
            
            updateStatus(platform, 'connecting', '🔄 Initiating OAuth flow...');
            log(`Starting OAuth connection for ${platforms[platform].name}`);

            try {
                const response = await fetch('/api/social/connect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        platform: platform,
                        userId: testUserId
                    }),
                });

                const data = await response.json();

                if (data.success && data.authUrl) {
                    log(`✅ OAuth URL generated for ${platforms[platform].name}`);
                    log(`🚀 Redirecting to: ${data.authUrl.substring(0, 100)}...`);
                    updateStatus(platform, 'success', '✅ Redirecting to OAuth provider...');
                    
                    // Redirect to OAuth provider (industry standard)
                    setTimeout(() => {
                        window.location.href = data.authUrl;
                    }, 1000);
                } else {
                    throw new Error(data.error || 'Failed to generate OAuth URL');
                }
            } catch (error) {
                log(`❌ Error connecting ${platforms[platform].name}: ${error.message}`, 'error');
                updateStatus(platform, 'error', `❌ ${error.message}`);
                btn.disabled = false;
                btn.innerHTML = 'Connect';
            }
        }

        function renderPlatforms() {
            const container = document.getElementById('platforms');
            container.innerHTML = '';

            Object.entries(platforms).forEach(([key, platform]) => {
                const card = document.createElement('div');
                card.className = 'platform-card';
                card.innerHTML = `
                    <div class="platform-header">
                        <div>
                            <span style="font-size: 24px; margin-right: 10px;">${platform.icon}</span>
                            <span class="platform-name">${platform.name}</span>
                        </div>
                        <button id="btn-${key}" class="connect-btn" onclick="connectPlatform('${key}')">
                            Connect
                        </button>
                    </div>
                    <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
                        ${platform.description}
                    </div>
                    <div id="status-${key}" class="status" style="display: none;"></div>
                `;
                container.appendChild(card);
            });
        }

        // Handle OAuth callback results
        function handleOAuthCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const success = urlParams.get('success');
            const error = urlParams.get('error');
            const platform = urlParams.get('platform');
            const account = urlParams.get('account');

            if (success && platform) {
                log(`🎉 OAuth connection successful for ${platforms[platform]?.name || platform}!`, 'success');
                if (account) {
                    log(`📋 Connected account: ${decodeURIComponent(account)}`, 'success');
                }
                updateStatus(platform, 'success', `✅ Connected successfully!`);
            } else if (error && platform) {
                log(`❌ OAuth connection failed for ${platforms[platform]?.name || platform}: ${decodeURIComponent(error)}`, 'error');
                updateStatus(platform, 'error', `❌ Connection failed`);
            }

            // Clean up URL parameters
            if (success || error) {
                window.history.replaceState({}, '', window.location.pathname);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            renderPlatforms();
            handleOAuthCallback();
            log('✅ OAuth redirect test environment ready');
            log('🎯 Using test user ID: ' + testUserId);
        });
    </script>
</body>
</html> 