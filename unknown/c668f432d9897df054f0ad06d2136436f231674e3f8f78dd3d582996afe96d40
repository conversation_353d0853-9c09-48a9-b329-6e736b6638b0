#!/usr/bin/env node

/**
 * Comprehensive OAuth Flow Testing
 * Tests Facebook/Instagram OAuth integration end-to-end
 */

require('dotenv').config({ path: '.env.local' });

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://localhost:3003';

console.log('🔐 Testing OAuth Flow for Facebook/Instagram...\n');

// Test 1: OAuth Initiation
console.log('📋 Step 1: OAuth Initiation Test...');
const testOAuthInitiation = async () => {
  try {
    const response = await fetch(`${APP_URL}/api/social/facebook/auth`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ OAuth initiation endpoint accessible');
      console.log(`   Status: ${data.status || 'unknown'}`);
      return true;
    } else {
      console.log(`❌ OAuth initiation failed: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Error testing OAuth initiation:', error.message);
    return false;
  }
};

// Test 2: OAuth Callback Handler
console.log('\n📋 Step 2: OAuth Callback Handler Test...');
const testOAuthCallback = async () => {
  try {
    // Test with dummy parameters to check handler existence
    const response = await fetch(`${APP_URL}/api/facebook/callback?code=test&state=test`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      }
    });
    
    // We expect this to fail with auth error, but handler should exist
    console.log(`✅ OAuth callback handler exists (status: ${response.status})`);
    return true;
  } catch (error) {
    console.log('❌ Error testing OAuth callback:', error.message);
    return false;
  }
};

// Test 3: Database Connection Test
console.log('\n📋 Step 3: Database Connection Test...');
const testDatabaseConnection = async () => {
  try {
    const response = await fetch(`${APP_URL}/api/social-accounts/test-connection`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        platform: 'facebook',
        test: true
      })
    });
    
    if (response.ok) {
      console.log('✅ Database connection test endpoint accessible');
      return true;
    } else {
      console.log(`❌ Database connection test failed: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Error testing database connection:', error.message);
    return false;
  }
};

// Test 4: Generate Real OAuth URLs
console.log('\n📋 Step 4: Generating Real OAuth URLs...');
const generateOAuthURLs = () => {
  const facebookScopes = [
    'pages_show_list',
    'pages_manage_posts', 
    'pages_read_engagement',
    'business_management',
    'email',
    'public_profile'
  ];
  
  const instagramScopes = [
    'instagram_basic',
    'instagram_content_publish',
    'pages_show_list',
    'business_management'
  ];
  
  const state = `oauth_test_${Date.now()}`;
  const redirectUri = `${APP_URL}/api/facebook/callback`;
  
  const facebookURL = `https://www.facebook.com/v20.0/dialog/oauth?` +
    `client_id=${process.env.FACEBOOK_APP_ID}&` +
    `redirect_uri=${encodeURIComponent(redirectUri)}&` +
    `scope=${facebookScopes.join(',')}&` +
    `response_type=code&` +
    `state=${state}`;
    
  const instagramURL = `https://www.facebook.com/v20.0/dialog/oauth?` +
    `client_id=${process.env.FACEBOOK_APP_ID}&` +
    `redirect_uri=${encodeURIComponent(redirectUri)}&` +
    `scope=${instagramScopes.join(',')}&` +
    `response_type=code&` +
    `state=${state}`;
  
  console.log('✅ OAuth URLs generated:');
  console.log('\n🔗 Facebook OAuth URL:');
  console.log(facebookURL);
  console.log('\n🔗 Instagram OAuth URL:');
  console.log(instagramURL);
  
  return { facebookURL, instagramURL, state };
};

// Test 5: Manual Testing Instructions
const generateTestInstructions = (urls) => {
  console.log('\n📋 Step 5: Manual Testing Instructions...');
  console.log('🎯 MANUAL OAUTH TESTING STEPS:');
  console.log('');
  console.log('1. 🚀 Start HTTPS development server:');
  console.log('   npm run dev');
  console.log('');
  console.log('2. 🌐 Open browser and visit:');
  console.log(`   ${APP_URL}`);
  console.log('');
  console.log('3. 🔐 Accept SSL certificate warning');
  console.log('');
  console.log('4. 📘 Test Facebook OAuth:');
  console.log(`   ${urls.facebookURL}`);
  console.log('');
  console.log('5. 📷 Test Instagram OAuth:');
  console.log(`   ${urls.instagramURL}`);
  console.log('');
  console.log('6. ✅ Expected Flow:');
  console.log('   - Redirect to Facebook login');
  console.log('   - Grant permissions');
  console.log('   - Redirect back to app');
  console.log('   - Account connected successfully');
  console.log('');
  console.log('7. 🔍 Check Database:');
  console.log('   - social_accounts table should have new entries');
  console.log('   - oauth_logs table should show successful auth');
};

// Run all tests
(async () => {
  console.log('🧪 Running OAuth Flow Tests...\n');
  
  const results = {
    initiation: await testOAuthInitiation(),
    callback: await testOAuthCallback(),
    database: await testDatabaseConnection()
  };
  
  const urls = generateOAuthURLs();
  generateTestInstructions(urls);
  
  console.log('\n📊 Test Results Summary:');
  console.log(`   OAuth Initiation: ${results.initiation ? '✅' : '❌'}`);
  console.log(`   OAuth Callback: ${results.callback ? '✅' : '❌'}`);
  console.log(`   Database Connection: ${results.database ? '✅' : '❌'}`);
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 All OAuth tests passed! Ready for manual testing.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the issues above before manual testing.');
  }
})();
