#!/usr/bin/env ts-node

/**
 * Check Monitoring Status Script
 * 
 * This script checks if the monitoring system is running by calling the social integration status API.
 */

import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
const API_KEY = process.env.API_KEY || process.env.STARTUP_SECRET_KEY;

if (!API_KEY) {
  console.error('❌ API_KEY or STARTUP_SECRET_KEY environment variable is not set');
  process.exit(1);
}

/**
 * Check if the monitoring system is running
 */
async function checkMonitoringStatus() {
  try {
    console.log('🔍 Checking monitoring system status...');
    
    const response = await fetch(`${APP_URL}/api/social-integration-status`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`API returned status code ${response.status}`);
    }
    
    const data = await response.json();
    
    console.log('✅ Monitoring system status:');
    console.log('---------------------------');
    console.log('Social Integrations:');
    
    // Display status for each platform
    if (data.platforms) {
      Object.entries(data.platforms).forEach(([platform, status]) => {
        const statusSymbol = status.connected ? '✅' : '❌';
        console.log(`${statusSymbol} ${platform}: ${status.connected ? 'Connected' : 'Disconnected'}`);
        
        if (status.tokenExpiry) {
          const expiryDate = new Date(status.tokenExpiry);
          const now = new Date();
          const daysUntilExpiry = Math.floor((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
          
          if (daysUntilExpiry < 0) {
            console.log(`   ⚠️ Token expired ${Math.abs(daysUntilExpiry)} days ago`);
          } else if (daysUntilExpiry < 7) {
            console.log(`   ⚠️ Token expires in ${daysUntilExpiry} days`);
          } else {
            console.log(`   ℹ️ Token expires in ${daysUntilExpiry} days`);
          }
        }
        
        if (status.rateLimit) {
          console.log(`   ℹ️ Rate limit: ${status.rateLimit.remaining}/${status.rateLimit.limit} requests remaining`);
        }
      });
    } else {
      console.log('❓ No platform data available');
    }
    
    console.log('\nLast Check:', data.lastCheck ? new Date(data.lastCheck).toLocaleString() : 'Unknown');
    console.log('Health Status:', data.healthy ? '✅ Healthy' : '❌ Unhealthy');
    
    return data.healthy;
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    return false;
  }
}

// Execute the check
checkMonitoringStatus()
  .then((healthy) => {
    if (!healthy) {
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error(`❌ Unhandled error: ${error.message}`);
    process.exit(1);
  }); 