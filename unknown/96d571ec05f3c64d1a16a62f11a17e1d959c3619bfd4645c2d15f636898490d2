#!/usr/bin/env node

/**
 * Test HTTPS Setup for Facebook/Instagram OAuth
 * Verifies SSL certificates and HTTPS server functionality
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

console.log('🔐 Testing HTTPS Setup for Facebook/Instagram OAuth...\n');

// Test 1: Check if SSL certificates exist
console.log('📋 Step 1: Checking SSL Certificates...');
const certPath = path.join(process.cwd(), 'cert.crt');
const keyPath = path.join(process.cwd(), 'cert.key');

if (fs.existsSync(certPath) && fs.existsSync(keyPath)) {
  console.log('✅ SSL certificates found');
  console.log(`   📜 Certificate: ${certPath}`);
  console.log(`   🔑 Private key: ${keyPath}`);
} else {
  console.log('❌ SSL certificates not found');
  console.log('💡 Run: npm run generate-certs or ./scripts/generate-ssl-certs.sh');
  process.exit(1);
}

// Test 2: Verify certificate validity
console.log('\n📋 Step 2: Verifying Certificate Validity...');
try {
  const cert = fs.readFileSync(certPath, 'utf8');
  const key = fs.readFileSync(keyPath, 'utf8');
  
  // Basic validation
  if (cert.includes('BEGIN CERTIFICATE') && key.includes('BEGIN PRIVATE KEY')) {
    console.log('✅ Certificate format is valid');
  } else {
    console.log('❌ Certificate format is invalid');
    process.exit(1);
  }
} catch (error) {
  console.log('❌ Error reading certificates:', error.message);
  process.exit(1);
}

// Test 3: Test HTTPS server startup
console.log('\n📋 Step 3: Testing HTTPS Server...');
const testServer = () => {
  return new Promise((resolve, reject) => {
    try {
      const cert = fs.readFileSync(certPath);
      const key = fs.readFileSync(keyPath);
      
      const server = https.createServer({ key, cert }, (req, res) => {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
          status: 'success', 
          message: 'HTTPS server is working',
          timestamp: new Date().toISOString()
        }));
      });
      
      server.listen(3443, 'localhost', () => {
        console.log('✅ HTTPS server started successfully on https://localhost:3443');
        server.close(() => {
          resolve(true);
        });
      });
      
      server.on('error', (error) => {
        reject(error);
      });
      
    } catch (error) {
      reject(error);
    }
  });
};

testServer()
  .then(() => {
    console.log('✅ HTTPS server test passed');
    
    // Test 4: Environment variables check
    console.log('\n📋 Step 4: Checking Environment Variables...');
    const requiredVars = [
      'NEXT_PUBLIC_APP_URL',
      'FACEBOOK_APP_ID',
      'FACEBOOK_APP_SECRET',
      'FACEBOOK_REDIRECT_URI'
    ];
    
    let allVarsPresent = true;
    requiredVars.forEach(varName => {
      if (process.env[varName]) {
        console.log(`✅ ${varName}: ${varName.includes('SECRET') ? '***' : process.env[varName]}`);
      } else {
        console.log(`❌ ${varName}: Missing`);
        allVarsPresent = false;
      }
    });
    
    if (allVarsPresent) {
      console.log('\n🎉 HTTPS Setup Complete!');
      console.log('📋 Next Steps:');
      console.log('   1. Run: npm run dev (HTTPS mode)');
      console.log('   2. Visit: https://localhost:3003');
      console.log('   3. Accept the self-signed certificate warning');
      console.log('   4. Test Facebook OAuth flow');
    } else {
      console.log('\n❌ Environment variables missing. Please check .env.local');
    }
  })
  .catch((error) => {
    console.log('❌ HTTPS server test failed:', error.message);
    console.log('💡 Make sure certificates are valid and port 3443 is available');
  });
