/**
 * Facebook OAuth Connection Initiation
 * Starts the OAuth flow by redirecting to Facebook's authorization endpoint
 */

import { NextRequest, NextResponse } from 'next/server'
import { createFacebookOAuthService } from '@/lib/oauth/facebook'
import { createClient } from '@/lib/supabase/server'
import { getAuthenticatedUser } from '@/lib/auth/api-auth'
import crypto from 'crypto'

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user from session
    const { user, supabase } = await getAuthenticatedUser(request);

    if (!user?.id) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      )
    }

    const userId = user.id;

    // Define redirect URI
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';
    const redirectUri = `${baseUrl}/api/auth/callback/facebook`;

    // Initialize Facebook OAuth service
    const facebookService = createFacebookOAuthService()

    // Generate OAuth state using direct database operations
    let state: string;
    try {
      // Generate secure random state
      state = crypto.randomBytes(32).toString('hex');

      // Store OAuth state directly in database
      const { error: stateError } = await supabase
        .from('oauth_states')
        .insert({
          user_id: userId,
          platform: 'FACEBOOK',
          state_token: state,
          redirect_uri: redirectUri,
          expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes
        })

      if (stateError) {
        console.error('Failed to create OAuth state:', stateError)
        return NextResponse.json(
          { error: 'Failed to create OAuth state' },
          { status: 500 }
        )
      }

      console.log('OAuth state created successfully:', state)

      // Log the connection attempt to oauth_logs table (optional)
      try {
        await supabase
          .from('oauth_logs')
          .insert({
            user_id: userId,
            platform: 'FACEBOOK',
            action: 'initiate_oauth',
            status: 'started',
            details: {
              state,
              redirect_uri: redirectUri
            }
          })
      } catch (logError) {
        console.warn('Failed to log OAuth initiation (non-critical):', logError)
      }
    } catch (dbError) {
      console.error('Database operation failed:', dbError)
      return NextResponse.json(
        { error: 'Database operation failed' },
        { status: 500 }
      )
    }

    // Generate authorization URL with the database-generated state
    const authUrl = facebookService.getAuthorizationUrl(state)

    // Redirect to Facebook OAuth
    return NextResponse.redirect(authUrl)

  } catch (error) {
    console.error('Facebook OAuth initiation error:', error)

    return NextResponse.json(
      {
        error: 'OAuth initiation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
