<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Media Connection Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .platform {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .platform-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .platform-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: black;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .accounts-list {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Social Media Connection Test</h1>
        <p>Direct testing of OAuth flows for eWasl platform</p>

        <div id="status"></div>
        
        <div id="accounts-section">
            <h3>Connected Accounts</h3>
            <div id="accounts-list" class="accounts-list">
                Loading...
            </div>
        </div>

        <h3>Test Connections</h3>
        <div id="platforms">
            <div class="platform">
                <div class="platform-info">
                    <div class="platform-icon" style="background: #1da1f2;">T</div>
                    <div>
                        <strong>Twitter/X</strong>
                        <div><small>Connect your Twitter account</small></div>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="testConnection('twitter')" id="twitter-btn">
                    Connect Twitter
                </button>
            </div>

            <div class="platform">
                <div class="platform-info">
                    <div class="platform-icon" style="background: #1877f2;">F</div>
                    <div>
                        <strong>Facebook</strong>
                        <div><small>Connect your Facebook account</small></div>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="testConnection('facebook')" id="facebook-btn">
                    Connect Facebook
                </button>
            </div>

            <div class="platform">
                <div class="platform-info">
                    <div class="platform-icon" style="background: #e4405f;">I</div>
                    <div>
                        <strong>Instagram</strong>
                        <div><small>Connect your Instagram account</small></div>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="testConnection('instagram')" id="instagram-btn">
                    Connect Instagram
                </button>
            </div>

            <div class="platform">
                <div class="platform-info">
                    <div class="platform-icon" style="background: #0077b5;">L</div>
                    <div>
                        <strong>LinkedIn</strong>
                        <div><small>Connect your LinkedIn account</small></div>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="testConnection('linkedin')" id="linkedin-btn">
                    Connect LinkedIn
                </button>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 15px; background: #e3f2fd; border-radius: 4px;">
            <h4>Instructions:</h4>
            <ol>
                <li>Click "Connect" button for any platform</li>
                <li>A popup window will open for OAuth authentication</li>
                <li>Complete the login process in the popup</li>
                <li>Close the popup when done</li>
                <li>The page will refresh to show connected accounts</li>
            </ol>
        </div>
    </div>

    <script>
        const TEST_USER_ID = '3ddaeb03-2d95-4fff-abad-2a2c7dd25037';
        let connectedAccounts = [];

        // Load accounts on page load
        window.addEventListener('load', loadAccounts);

        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status status-${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            // Hide after 5 seconds
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }

        async function loadAccounts() {
            try {
                const response = await fetch(`/api/social/accounts?userId=${TEST_USER_ID}`);
                const data = await response.json();
                
                connectedAccounts = data.accounts || [];
                displayAccounts();
                updateButtonStates();
                
            } catch (error) {
                console.error('Error loading accounts:', error);
                document.getElementById('accounts-list').textContent = 'Error loading accounts';
            }
        }

        function displayAccounts() {
            const accountsList = document.getElementById('accounts-list');
            
            if (connectedAccounts.length === 0) {
                accountsList.textContent = 'No accounts connected yet';
                return;
            }

            accountsList.innerHTML = connectedAccounts.map(account => `
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #dee2e6;">
                    <div>
                        <strong>${account.platform}</strong> - @${account.account_name}
                        <small style="color: #6c757d; display: block;">Connected: ${new Date(account.created_at).toLocaleDateString()}</small>
                    </div>
                    <button class="btn btn-warning" onclick="disconnectAccount('${account.id}', '${account.platform}')" style="font-size: 12px;">
                        Disconnect
                    </button>
                </div>
            `).join('');
        }

        function updateButtonStates() {
            const platforms = ['twitter', 'facebook', 'instagram', 'linkedin'];
            
            platforms.forEach(platform => {
                const btn = document.getElementById(`${platform}-btn`);
                const isConnected = connectedAccounts.some(acc => 
                    acc.platform.toLowerCase() === platform || 
                    (platform === 'twitter' && acc.platform === 'TWITTER')
                );
                
                if (isConnected) {
                    btn.textContent = 'Reconnect';
                    btn.className = 'btn btn-warning';
                } else {
                    btn.textContent = `Connect ${platform.charAt(0).toUpperCase() + platform.slice(1)}`;
                    btn.className = 'btn btn-primary';
                }
            });
        }

        async function testConnection(platform) {
            const btn = document.getElementById(`${platform}-btn`);
            const originalText = btn.textContent;
            
            btn.disabled = true;
            btn.textContent = 'Connecting...';
            
            try {
                console.log(`Testing ${platform} connection...`);
                
                const response = await fetch('/api/social/connect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        platform: platform.toUpperCase(),
                        userId: TEST_USER_ID
                    }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log(`${platform} OAuth response:`, data);

                if (data.success && data.authUrl) {
                    showStatus(`OAuth URL generated for ${platform}`, 'success');
                    
                    // Open popup for OAuth
                    const popup = window.open(
                        data.authUrl,
                        'oauth-popup',
                        'width=600,height=600,scrollbars=yes,resizable=yes'
                    );

                    // Monitor popup
                    const checkClosed = setInterval(() => {
                        if (popup.closed) {
                            clearInterval(checkClosed);
                            btn.disabled = false;
                            btn.textContent = originalText;
                            
                            // Reload accounts after popup closes
                            setTimeout(() => {
                                loadAccounts();
                            }, 1000);
                        }
                    }, 1000);

                    // Stop checking after 2 minutes
                    setTimeout(() => {
                        clearInterval(checkClosed);
                        btn.disabled = false;
                        btn.textContent = originalText;
                    }, 120000);

                } else {
                    throw new Error(data.error || 'Failed to generate OAuth URL');
                }

            } catch (error) {
                console.error(`Error connecting ${platform}:`, error);
                showStatus(`Error connecting ${platform}: ${error.message}`, 'error');
                btn.disabled = false;
                btn.textContent = originalText;
            }
        }

        async function disconnectAccount(accountId, platform) {
            try {
                const response = await fetch('/api/social/disconnect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        accountId: accountId,
                        userId: TEST_USER_ID,
                    }),
                });

                const data = await response.json();
                if (data.success) {
                    showStatus(`Disconnected ${platform}`, 'success');
                    loadAccounts();
                } else {
                    throw new Error(data.error || 'Failed to disconnect');
                }
            } catch (error) {
                console.error('Disconnect error:', error);
                showStatus(`Error disconnecting ${platform}: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html> 