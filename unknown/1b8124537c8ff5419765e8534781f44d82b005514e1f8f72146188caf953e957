#!/usr/bin/env node

/**
 * Production Environment Validation Script
 * Enhanced Media Processing Pipeline - DigitalOcean Deployment
 * 
 * This script validates all required environment variables and configurations
 * before production deployment to prevent deployment failures.
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Logging functions
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  header: (msg) => console.log(`${colors.cyan}${msg}${colors.reset}`)
};

// Required environment variables for production
const requiredEnvVars = {
  // Application
  'NODE_ENV': { required: true, expectedValue: 'production' },
  'NEXT_PUBLIC_APP_URL': { required: true, pattern: /^https:\/\// },
  'NEXT_PUBLIC_API_URL': { required: true, pattern: /^https:\/\// },
  
  // Supabase
  'NEXT_PUBLIC_SUPABASE_URL': { required: true, pattern: /^https:\/\// },
  'NEXT_PUBLIC_SUPABASE_ANON_KEY': { required: true, minLength: 100 },
  'SUPABASE_SERVICE_ROLE_KEY': { required: true, minLength: 100 },
  
  // DigitalOcean
  'DIGITALOCEAN_API_TOKEN': { required: true, pattern: /^dop_v1_/ },
  'DIGITALOCEAN_SPACES_ENDPOINT': { required: true, pattern: /^https:\/\// },
  'DIGITALOCEAN_SPACES_BUCKET': { required: true, minLength: 3 },
  'DIGITALOCEAN_SPACES_REGION': { required: true, minLength: 2 },
  
  // CDN
  'CDN_PROVIDER': { required: true, expectedValue: 'digitalocean' },
  'CDN_DOMAIN': { required: true, pattern: /\.digitaloceanspaces\.com$/ },
  
  // Stripe
  'STRIPE_PUBLISHABLE_KEY': { required: true, pattern: /^pk_live_/ },
  'STRIPE_SECRET_KEY': { required: true, pattern: /^sk_live_/ },
  
  // Social Media APIs
  'LINKEDIN_CLIENT_ID': { required: true, minLength: 10 },
  'LINKEDIN_CLIENT_SECRET': { required: true, minLength: 20 },
  'FACEBOOK_APP_ID': { required: true, pattern: /^\d+$/ },
  'FACEBOOK_APP_SECRET': { required: false, minLength: 20 },
  
  // Media Processing
  'MEDIA_MAX_FILE_SIZE': { required: true, pattern: /^\d+$/ },
  'MEDIA_MAX_CONCURRENT_UPLOADS': { required: true, pattern: /^\d+$/ },
  'MEDIA_ENABLE_AI_OPTIMIZATION': { required: true, expectedValue: 'true' },
  
  // Monitoring
  'MONITORING_ENABLE_METRICS': { required: true, expectedValue: 'true' },
  'MONITORING_ENABLE_ALERTS': { required: true, expectedValue: 'true' },
  'MONITORING_LOG_LEVEL': { required: true, allowedValues: ['error', 'warn', 'info', 'debug'] },
  
  // Security
  'SECURITY_ENABLE_RATE_LIMIT': { required: true, expectedValue: 'true' },
  'SECURITY_RATE_LIMIT_MAX': { required: true, pattern: /^\d+$/ },
  'SECURITY_ALLOWED_ORIGINS': { required: true, pattern: /^https:\/\// },
  
  // Next.js
  'NEXTAUTH_URL': { required: true, pattern: /^https:\/\// },
  'NEXTAUTH_SECRET': { required: false, minLength: 32 }
};

// Optional but recommended environment variables
const recommendedEnvVars = [
  'TWITTER_CLIENT_ID',
  'TWITTER_CLIENT_SECRET',
  'FACEBOOK_APP_SECRET',
  'NEXTAUTH_SECRET',
  'SENTRY_DSN',
  'GOOGLE_ANALYTICS_ID'
];

// Required files for deployment
const requiredFiles = [
  '.do/app.yaml',
  'package.json',
  'next.config.js',
  'src/app/layout.tsx',
  'src/app/page.tsx',
  'src/app/api/system/health/route.ts'
];

// Validation functions
function validateEnvironmentVariables() {
  log.header('\n🔍 Validating Environment Variables...');
  
  let errors = 0;
  let warnings = 0;
  
  // Load environment variables from .env.production if it exists
  const envFile = path.join(process.cwd(), '.env.production');
  if (fs.existsSync(envFile)) {
    const envContent = fs.readFileSync(envFile, 'utf8');
    const envLines = envContent.split('\n');
    
    envLines.forEach(line => {
      const [key, value] = line.split('=');
      if (key && value && !process.env[key]) {
        process.env[key] = value;
      }
    });
    
    log.info('Loaded environment variables from .env.production');
  }
  
  // Validate required environment variables
  Object.entries(requiredEnvVars).forEach(([key, config]) => {
    const value = process.env[key];
    
    if (!value) {
      if (config.required) {
        log.error(`Missing required environment variable: ${key}`);
        errors++;
      } else {
        log.warning(`Missing optional environment variable: ${key}`);
        warnings++;
      }
      return;
    }
    
    // Validate expected value
    if (config.expectedValue && value !== config.expectedValue) {
      log.error(`Invalid value for ${key}. Expected: ${config.expectedValue}, Got: ${value}`);
      errors++;
      return;
    }
    
    // Validate allowed values
    if (config.allowedValues && !config.allowedValues.includes(value)) {
      log.error(`Invalid value for ${key}. Allowed: ${config.allowedValues.join(', ')}, Got: ${value}`);
      errors++;
      return;
    }
    
    // Validate pattern
    if (config.pattern && !config.pattern.test(value)) {
      log.error(`Invalid format for ${key}. Pattern: ${config.pattern}, Got: ${value}`);
      errors++;
      return;
    }
    
    // Validate minimum length
    if (config.minLength && value.length < config.minLength) {
      log.error(`Invalid length for ${key}. Minimum: ${config.minLength}, Got: ${value.length}`);
      errors++;
      return;
    }
    
    log.success(`✓ ${key}`);
  });
  
  // Check recommended variables
  recommendedEnvVars.forEach(key => {
    if (!process.env[key]) {
      log.warning(`Recommended environment variable missing: ${key}`);
      warnings++;
    } else {
      log.success(`✓ ${key} (recommended)`);
    }
  });
  
  return { errors, warnings };
}

function validateRequiredFiles() {
  log.header('\n📁 Validating Required Files...');
  
  let errors = 0;
  
  requiredFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      log.success(`✓ ${file}`);
    } else {
      log.error(`Missing required file: ${file}`);
      errors++;
    }
  });
  
  return { errors };
}

function validatePackageJson() {
  log.header('\n📦 Validating package.json...');
  
  let errors = 0;
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // Check required scripts
    const requiredScripts = ['build', 'start'];
    requiredScripts.forEach(script => {
      if (packageJson.scripts && packageJson.scripts[script]) {
        log.success(`✓ Script: ${script}`);
      } else {
        log.error(`Missing required script: ${script}`);
        errors++;
      }
    });
    
    // Check required dependencies
    const requiredDeps = ['next', 'react', '@supabase/supabase-js'];
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        log.success(`✓ Dependency: ${dep}`);
      } else {
        log.error(`Missing required dependency: ${dep}`);
        errors++;
      }
    });
    
    // Check Node.js version
    if (packageJson.engines && packageJson.engines.node) {
      log.success(`✓ Node.js version specified: ${packageJson.engines.node}`);
    } else {
      log.warning('Node.js version not specified in engines field');
    }
    
  } catch (error) {
    log.error(`Failed to parse package.json: ${error.message}`);
    errors++;
  }
  
  return { errors };
}

function validateAppSpec() {
  log.header('\n⚙️ Validating DigitalOcean App Spec...');
  
  let errors = 0;
  
  try {
    const appSpecPath = '.do/app.yaml';
    if (!fs.existsSync(appSpecPath)) {
      log.error('DigitalOcean app specification not found at .do/app.yaml');
      return { errors: 1 };
    }
    
    const appSpecContent = fs.readFileSync(appSpecPath, 'utf8');
    
    // Basic validation
    if (appSpecContent.includes('name:')) {
      log.success('✓ App name specified');
    } else {
      log.error('App name not specified in app.yaml');
      errors++;
    }
    
    if (appSpecContent.includes('services:')) {
      log.success('✓ Services configuration found');
    } else {
      log.error('Services configuration not found in app.yaml');
      errors++;
    }
    
    if (appSpecContent.includes('health_check:')) {
      log.success('✓ Health check configuration found');
    } else {
      log.warning('Health check configuration not found (recommended)');
    }
    
    if (appSpecContent.includes('envs:')) {
      log.success('✓ Environment variables configuration found');
    } else {
      log.warning('Environment variables configuration not found');
    }
    
  } catch (error) {
    log.error(`Failed to validate app spec: ${error.message}`);
    errors++;
  }
  
  return { errors };
}

function validateBuildConfiguration() {
  log.header('\n🔨 Validating Build Configuration...');
  
  let errors = 0;
  
  // Check Next.js config
  if (fs.existsSync('next.config.js')) {
    log.success('✓ Next.js configuration found');
  } else {
    log.warning('Next.js configuration not found (using defaults)');
  }
  
  // Check TypeScript config
  if (fs.existsSync('tsconfig.json')) {
    log.success('✓ TypeScript configuration found');
  } else {
    log.warning('TypeScript configuration not found');
  }
  
  // Check Tailwind config
  if (fs.existsSync('tailwind.config.js')) {
    log.success('✓ Tailwind CSS configuration found');
  } else {
    log.warning('Tailwind CSS configuration not found');
  }
  
  return { errors };
}

function generateValidationReport(results) {
  log.header('\n📊 Validation Report');
  console.log('='.repeat(50));
  
  const totalErrors = Object.values(results).reduce((sum, result) => sum + result.errors, 0);
  const totalWarnings = Object.values(results).reduce((sum, result) => sum + (result.warnings || 0), 0);
  
  console.log(`Total Errors: ${totalErrors}`);
  console.log(`Total Warnings: ${totalWarnings}`);
  
  if (totalErrors === 0) {
    log.success('\n🎉 Production environment validation passed!');
    log.info('Your application is ready for deployment to DigitalOcean.');
    return true;
  } else {
    log.error(`\n❌ Production environment validation failed with ${totalErrors} errors.`);
    log.info('Please fix the errors above before deploying to production.');
    return false;
  }
}

// Main validation function
function main() {
  console.log(`${colors.cyan}
╔══════════════════════════════════════════════════════════════╗
║                    eWasl.com Production                      ║
║              Environment Validation Script                   ║
║         Enhanced Media Processing Pipeline                   ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`);
  
  const results = {
    environment: validateEnvironmentVariables(),
    files: validateRequiredFiles(),
    package: validatePackageJson(),
    appSpec: validateAppSpec(),
    build: validateBuildConfiguration()
  };
  
  const isValid = generateValidationReport(results);
  
  if (isValid) {
    console.log(`\n${colors.green}✅ Ready for production deployment!${colors.reset}`);
    process.exit(0);
  } else {
    console.log(`\n${colors.red}❌ Fix errors before deployment!${colors.reset}`);
    process.exit(1);
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  validateEnvironmentVariables,
  validateRequiredFiles,
  validatePackageJson,
  validateAppSpec,
  validateBuildConfiguration,
  generateValidationReport
};
