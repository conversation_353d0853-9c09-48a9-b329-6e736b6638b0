const https = require('https');

console.log('🚀 Testing NextAuth.js Implementation...\n');

const BASE_URL = 'https://app.ewasl.com';

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      method: 'GET',
      timeout: 15000,
      headers: {
        'User-Agent': 'eWasl-NextAuth-Test/1.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        ...options.headers
      },
      ...options
    };

    const req = https.request(url, requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    req.setTimeout(15000);
    req.end();
  });
}

async function testNextAuthImplementation() {
  console.log('🔍 NEXTAUTH.JS IMPLEMENTATION TEST');
  console.log('==================================\n');

  const tests = [
    {
      name: 'NextAuth.js API Route',
      url: `${BASE_URL}/api/auth/providers`,
      expected: [200, 404], // 404 is OK if not fully configured yet
      description: 'Testing NextAuth.js providers endpoint'
    },
    {
      name: 'Facebook OAuth Provider',
      url: `${BASE_URL}/api/auth/signin/facebook`,
      expected: [200, 302, 307],
      description: 'Testing Facebook OAuth provider'
    },
    {
      name: 'Twitter OAuth Provider',
      url: `${BASE_URL}/api/auth/signin/twitter`,
      expected: [200, 302, 307],
      description: 'Testing Twitter OAuth provider'
    },
    {
      name: 'LinkedIn OAuth Provider',
      url: `${BASE_URL}/api/auth/signin/linkedin`,
      expected: [200, 302, 307],
      description: 'Testing LinkedIn OAuth provider'
    },
    {
      name: 'Social Page Access',
      url: `${BASE_URL}/social`,
      expected: [200, 307],
      description: 'Testing social page accessibility'
    }
  ];

  for (const test of tests) {
    try {
      console.log(`🧪 Testing: ${test.name}`);
      console.log(`   URL: ${test.url}`);
      
      const response = await makeRequest(test.url);
      
      console.log(`   Status: ${response.statusCode}`);
      
      if (test.expected.includes(response.statusCode)) {
        console.log(`   ✅ SUCCESS: Expected ${test.expected.join(' or ')}, got ${response.statusCode}`);
      } else {
        console.log(`   ❌ UNEXPECTED: Expected ${test.expected.join(' or ')}, got ${response.statusCode}`);
      }
      
      if (response.headers.location) {
        console.log(`   🔄 Redirect to: ${response.headers.location}`);
      }
      
      console.log('');
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
      console.log('');
    }
  }

  console.log('\n📋 TEST SUMMARY');
  console.log('===============');
  console.log('✅ If NextAuth.js routes return 200/302/307, implementation is working');
  console.log('✅ If routes return 404, NextAuth.js needs to be properly configured');
  console.log('🔧 Next step: Deploy and test OAuth connections');
  console.log('\n🚀 Test completed!');
}

testNextAuthImplementation().catch(console.error); 