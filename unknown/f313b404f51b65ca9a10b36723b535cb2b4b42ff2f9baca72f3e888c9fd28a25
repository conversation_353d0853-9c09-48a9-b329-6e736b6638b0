import { NextRequest, NextResponse } from 'next/server';
import { createFacebookOAuthService } from '@/lib/oauth/facebook';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 OAUTH FLOW DEEP INVESTIGATION STARTING...');
    
    // Get URL parameters for OAuth code if provided
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    
    // Environment variables check
    const envCheck = {
      FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID ? 'SET' : 'MISSING',
      FACEBOOK_APP_SECRET: process.env.FACEBOOK_APP_SECRET ? 'SET' : 'MISSING',
      FACEBOOK_BUSINESS_ID: process.env.FACEBOOK_BUSINESS_ID ? 'SET' : 'MISSING',
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'NOT_SET'
    };

    // Create Facebook OAuth service
    const facebookService = createFacebookOAuthService();
    
    // Get authenticated user
    const { createClient } = await import('@/lib/supabase/server');
    const cookieStore = cookies();
    const supabase = createClient(cookieStore);
    
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({
        error: 'User not authenticated',
        details: userError?.message
      }, { status: 401 });
    }

    // INVESTIGATION 1: OAuth URL Analysis
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com';
    const redirectUri = `${baseUrl}/api/oauth/facebook/callback`;
    const scopes = [
      'pages_show_list',
      'pages_read_engagement', 
      'pages_manage_posts',
      'pages_read_user_content',
      'instagram_basic',
      'instagram_content_publish',
      'business_management'
    ].join(',');
    
    const oauthUrl = `https://www.facebook.com/v18.0/dialog/oauth?client_id=${process.env.FACEBOOK_APP_ID}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scopes)}&response_type=code&state=test_state`;

    // INVESTIGATION 2: If OAuth code provided, test token exchange
    let tokenExchangeTest = null;
    if (code) {
      try {
        console.log('🔍 Testing token exchange with provided code...');
        const tokenData = await facebookService.exchangeCodeForToken(code);
        
        // Test the received token
        const appSecretProof = require('crypto')
          .createHmac('sha256', process.env.FACEBOOK_APP_SECRET!)
          .update(tokenData.access_token)
          .digest('hex');

        // Test /me endpoint to determine token type
        const meResponse = await fetch(`https://graph.facebook.com/v18.0/me?fields=id,name,email,category,about&access_token=${tokenData.access_token}&appsecret_proof=${appSecretProof}`);
        const meData = await meResponse.json();

        // Test debug_token endpoint
        const appAccessToken = `${process.env.FACEBOOK_APP_ID}|${process.env.FACEBOOK_APP_SECRET}`;
        const debugResponse = await fetch(`https://graph.facebook.com/v18.0/debug_token?input_token=${tokenData.access_token}&access_token=${appAccessToken}`);
        const debugData = await debugResponse.json();

        tokenExchangeTest = {
          tokenReceived: !!tokenData.access_token,
          tokenType: tokenData.token_type,
          expiresIn: tokenData.expires_in,
          meEndpointTest: {
            success: meResponse.ok,
            data: meData
          },
          debugTokenTest: {
            success: debugResponse.ok,
            data: debugData
          }
        };
      } catch (error) {
        tokenExchangeTest = {
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }

    // INVESTIGATION 3: Facebook App Configuration Analysis
    const appConfigAnalysis = {
      appId: process.env.FACEBOOK_APP_ID,
      redirectUri: redirectUri,
      scopes: scopes.split(','),
      oauthUrl: oauthUrl,
      expectedTokenType: 'User Access Token',
      scopesAnalysis: {
        userLevel: ['pages_show_list', 'pages_read_engagement', 'pages_manage_posts', 'pages_read_user_content', 'business_management'],
        pageLevel: [],
        instagramLevel: ['instagram_basic', 'instagram_content_publish']
      }
    };

    return NextResponse.json({
      message: 'OAuth Flow Deep Investigation',
      userId: user.id,
      environmentVariables: envCheck,
      oauthParameters: {
        code: code ? 'PROVIDED' : 'NOT_PROVIDED',
        state: state ? 'PROVIDED' : 'NOT_PROVIDED'
      },
      appConfigAnalysis,
      tokenExchangeTest,
      instructions: {
        step1: 'Visit the OAuth URL to initiate Facebook login',
        step2: 'After Facebook redirects back, the callback will contain code parameter',
        step3: 'Call this endpoint again with the code parameter to test token exchange',
        oauthUrl: oauthUrl
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('🔍 OAuth flow investigation error:', error);
    return NextResponse.json({
      error: 'Investigation failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
