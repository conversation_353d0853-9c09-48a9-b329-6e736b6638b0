#!/usr/bin/env node

/**
 * eWasl Platform - End-to-End Testing Suite
 * 
 * This script tests all critical user journeys and API endpoints
 * to ensure the platform is production-ready.
 */

const https = require('https');
const http = require('http');

// Configuration
const config = {
  baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com',
  timeout: 10000,
  retries: 3
};

// Color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Test results tracker
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

/**
 * Make HTTP request with timeout
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https:');
    const requestModule = isHttps ? https : http;
    
    const requestOptions = {
      timeout: config.timeout,
      ...options
    };

    const req = requestModule.get(url, requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data,
          url: url
        });
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`Request timeout after ${config.timeout}ms`));
    });

    req.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Log test result
 */
function logTest(name, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`${colors.green}✅ PASS${colors.reset} ${name}`);
  } else {
    testResults.failed++;
    console.log(`${colors.red}❌ FAIL${colors.reset} ${name}`);
    if (details) {
      console.log(`   ${colors.yellow}Details: ${details}${colors.reset}`);
    }
  }
  
  testResults.details.push({
    name,
    passed,
    details
  });
}

/**
 * Test 1: Basic Application Health
 */
async function testApplicationHealth() {
  console.log(`\n${colors.bold}🏥 Testing Application Health${colors.reset}`);
  
  try {
    // Test main application
    const response = await makeRequest(config.baseUrl);
    logTest('Main application loads', response.statusCode === 200, `Status: ${response.statusCode}`);
    
    // Test health endpoint
    try {
      const healthResponse = await makeRequest(`${config.baseUrl}/api/health`);
      logTest('Health endpoint responds', healthResponse.statusCode === 200, `Status: ${healthResponse.statusCode}`);
    } catch (error) {
      logTest('Health endpoint responds', false, error.message);
    }
    
    // Test system health
    try {
      const systemHealthResponse = await makeRequest(`${config.baseUrl}/api/system/health`);
      logTest('System health endpoint responds', systemHealthResponse.statusCode === 200, `Status: ${systemHealthResponse.statusCode}`);
    } catch (error) {
      logTest('System health endpoint responds', false, error.message);
    }
    
  } catch (error) {
    logTest('Application health check', false, error.message);
  }
}

/**
 * Test 2: Security Headers
 */
async function testSecurityHeaders() {
  console.log(`\n${colors.bold}🔒 Testing Security Headers${colors.reset}`);
  
  try {
    const response = await makeRequest(config.baseUrl);
    const headers = response.headers;
    
    // Required security headers
    const requiredHeaders = [
      'x-frame-options',
      'x-content-type-options', 
      'referrer-policy'
    ];
    
    requiredHeaders.forEach(header => {
      const exists = headers[header] !== undefined;
      logTest(`Security header: ${header}`, exists, exists ? headers[header] : 'Missing');
    });
    
    // Check HTTPS redirect
    if (config.baseUrl.startsWith('https:')) {
      logTest('HTTPS enforced', true, 'Using HTTPS URL');
    } else {
      logTest('HTTPS enforced', false, 'Using HTTP URL - should redirect to HTTPS');
    }
    
  } catch (error) {
    logTest('Security headers check', false, error.message);
  }
}

/**
 * Test 3: API Endpoints
 */
async function testAPIEndpoints() {
  console.log(`\n${colors.bold}🔌 Testing Critical API Endpoints${colors.reset}`);
  
  const endpoints = [
    { path: '/api/health', expectedStatus: 200 },
    { path: '/api/social/accounts', expectedStatus: [200, 401] }, // May require auth
    { path: '/api/posts', expectedStatus: [200, 401] }, // May require auth
    { path: '/api/analytics/overview', expectedStatus: [200, 401] }, // May require auth
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await makeRequest(`${config.baseUrl}${endpoint.path}`);
      const isExpected = Array.isArray(endpoint.expectedStatus) 
        ? endpoint.expectedStatus.includes(response.statusCode)
        : response.statusCode === endpoint.expectedStatus;
      
      logTest(`API ${endpoint.path}`, isExpected, `Status: ${response.statusCode}`);
    } catch (error) {
      logTest(`API ${endpoint.path}`, false, error.message);
    }
  }
}

/**
 * Test 4: Authentication Pages
 */
async function testAuthenticationPages() {
  console.log(`\n${colors.bold}🔐 Testing Authentication Pages${colors.reset}`);
  
  const authPages = [
    '/auth/signin',
    '/auth/signup', 
    '/auth/forgot-password'
  ];
  
  for (const page of authPages) {
    try {
      const response = await makeRequest(`${config.baseUrl}${page}`);
      logTest(`Auth page ${page}`, response.statusCode === 200, `Status: ${response.statusCode}`);
    } catch (error) {
      logTest(`Auth page ${page}`, false, error.message);
    }
  }
}

/**
 * Test 5: Dashboard and Protected Routes
 */
async function testProtectedRoutes() {
  console.log(`\n${colors.bold}🛡️ Testing Protected Routes${colors.reset}`);
  
  const protectedRoutes = [
    '/dashboard',
    '/social',
    '/posts',
    '/analytics',
    '/settings'
  ];
  
  for (const route of protectedRoutes) {
    try {
      const response = await makeRequest(`${config.baseUrl}${route}`);
      // Protected routes should redirect to auth (302/307) or return 401/403
      const isProtected = [302, 307, 401, 403, 200].includes(response.statusCode);
      logTest(`Protected route ${route}`, isProtected, `Status: ${response.statusCode}`);
    } catch (error) {
      logTest(`Protected route ${route}`, false, error.message);
    }
  }
}

/**
 * Test 6: Error Handling
 */
async function testErrorHandling() {
  console.log(`\n${colors.bold}🚫 Testing Error Handling${colors.reset}`);
  
  try {
    // Test 404 handling
    const response404 = await makeRequest(`${config.baseUrl}/non-existent-page-12345`);
    logTest('404 error handling', response404.statusCode === 404, `Status: ${response404.statusCode}`);
    
    // Test API error handling
    const apiError = await makeRequest(`${config.baseUrl}/api/non-existent-endpoint`);
    logTest('API error handling', [404, 405].includes(apiError.statusCode), `Status: ${apiError.statusCode}`);
    
  } catch (error) {
    logTest('Error handling test', false, error.message);
  }
}

/**
 * Generate Test Report
 */
function generateReport() {
  console.log(`\n${colors.bold}📊 TEST REPORT${colors.reset}`);
  console.log(`${colors.bold}${'='.repeat(50)}${colors.reset}`);
  
  const successRate = Math.round((testResults.passed / testResults.total) * 100);
  const statusColor = successRate >= 90 ? colors.green : successRate >= 70 ? colors.yellow : colors.red;
  
  console.log(`${colors.bold}Total Tests:${colors.reset} ${testResults.total}`);
  console.log(`${colors.green}Passed:${colors.reset} ${testResults.passed}`);
  console.log(`${colors.red}Failed:${colors.reset} ${testResults.failed}`);
  console.log(`${statusColor}Success Rate: ${successRate}%${colors.reset}`);
  
  if (testResults.failed > 0) {
    console.log(`\n${colors.bold}${colors.red}FAILED TESTS:${colors.reset}`);
    testResults.details.filter(test => !test.passed).forEach(test => {
      console.log(`${colors.red}❌ ${test.name}${colors.reset}: ${test.details}`);
    });
  }
  
  console.log(`\n${colors.bold}PRODUCTION READINESS:${colors.reset}`);
  if (successRate >= 95) {
    console.log(`${colors.green}🚀 EXCELLENT - Ready for production launch!${colors.reset}`);
  } else if (successRate >= 85) {
    console.log(`${colors.yellow}⚠️  GOOD - Minor issues to address before launch${colors.reset}`);
  } else if (successRate >= 70) {
    console.log(`${colors.yellow}⚠️  FAIR - Several issues need attention${colors.reset}`);
  } else {
    console.log(`${colors.red}🚫 POOR - Critical issues must be fixed before launch${colors.reset}`);
  }
  
  console.log(`${colors.bold}${'='.repeat(50)}${colors.reset}\n`);
}

/**
 * Main test runner
 */
async function runTests() {
  console.log(`${colors.bold}${colors.blue}🧪 eWasl Platform - End-to-End Testing Suite${colors.reset}`);
  console.log(`${colors.bold}Testing URL: ${config.baseUrl}${colors.reset}`);
  console.log(`${colors.bold}Timeout: ${config.timeout}ms${colors.reset}\n`);
  
  const startTime = Date.now();
  
  // Run all test suites
  await testApplicationHealth();
  await testSecurityHeaders();
  await testAPIEndpoints();
  await testAuthenticationPages();
  await testProtectedRoutes();
  await testErrorHandling();
  
  const endTime = Date.now();
  const duration = Math.round((endTime - startTime) / 1000);
  
  console.log(`\n${colors.bold}Testing completed in ${duration} seconds${colors.reset}`);
  generateReport();
  
  // Exit with appropriate code
  process.exit(testResults.failed === 0 ? 0 : 1);
}

// Handle command line arguments
if (process.argv.length > 2) {
  const customUrl = process.argv[2];
  if (customUrl.startsWith('http')) {
    config.baseUrl = customUrl;
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error(`${colors.red}Test runner error: ${error.message}${colors.reset}`);
    process.exit(1);
  });
}

module.exports = { runTests }; 