const fs = require('fs');
const path = require('path');

const TEST_ROUTES_TO_REMOVE = [
  'src/app/test-account-selection',
  'src/app/test-analytics',
  'src/app/test-api',
  'src/app/test-auth-system',
  'src/app/test-email-system',
  'src/app/test-facebook',
  'src/app/test-instagram',
  'src/app/test-linkedin',
  'src/app/test-linkedin-integration',
  'src/app/test-media',
  'src/app/test-posts',
  'src/app/test-scheduler',
  'src/app/test-social',
  'src/app/test-social-media-integration',
  'src/app/test-stripe',
  'src/app/test-team',
  'src/app/test-twitter-oauth',
  'src/app/test-x',
  'src/app/api/test/ai-media-optimization',
  'src/app/api/test/analytics-v2',
  'src/app/api/test/cdn-integration',
  'src/app/api/test/enhanced-media-pipeline-final',
  'src/app/api/test/facebook-cleanup',
  'src/app/api/test/facebook-connectivity',
  'src/app/api/test/facebook-disconnect',
  'src/app/api/test/facebook-oauth',
  'src/app/api/test/facebook-publisher-v2',
  'src/app/api/test/facebook-token-analysis',
  'src/app/api/test/linkedin-live-post',
  'src/app/api/test/linkedin-post-demo',
  'src/app/api/test/linkedin-posting',
  'src/app/api/test/media-management-ui',
  'src/app/api/test/media-processing',
  'src/app/api/test/social-integration',
  'src/app/api/test/social-media-integrations',
  'src/app/api/test/twitter-oauth-url'
];

const ROUTES_TO_KEEP = [
  'src/app/api/test/email-system', // Keep for email testing
  'src/app/test-auth', // Keep for auth testing
  'src/app/test-publishing-system' // Keep for publishing tests
];

const DEMO_ROUTES_TO_KEEP = [
  'src/app/demo-login',
  'src/app/bypass',
  'src/app/no-auth'
];

function removeDirectory(dirPath) {
  const fullPath = path.join(__dirname, '..', dirPath);
  
  if (fs.existsSync(fullPath)) {
    console.log(`Removing: ${dirPath}`);
    fs.rmSync(fullPath, { recursive: true, force: true });
    return true;
  } else {
    console.log(`Not found: ${dirPath}`);
    return false;
  }
}

function cleanupTestRoutes() {
  console.log('🧹 Starting cleanup of test routes...\n');
  
  let removedCount = 0;
  let notFoundCount = 0;
  
  TEST_ROUTES_TO_REMOVE.forEach(route => {
    if (!ROUTES_TO_KEEP.includes(route) && !DEMO_ROUTES_TO_KEEP.includes(route)) {
      if (removeDirectory(route)) {
        removedCount++;
      } else {
        notFoundCount++;
      }
    } else {
      console.log(`Keeping: ${route} (marked to keep)`);
    }
  });
  
  console.log('\n📊 Cleanup Summary:');
  console.log(`✅ Removed: ${removedCount} test routes`);
  console.log(`ℹ️  Not found: ${notFoundCount} routes`);
  console.log(`🔒 Kept: ${ROUTES_TO_KEEP.length + DEMO_ROUTES_TO_KEEP.length} essential routes`);
  
  console.log('\n🎯 Essential routes kept for functionality:');
  [...ROUTES_TO_KEEP, ...DEMO_ROUTES_TO_KEEP].forEach(route => {
    console.log(`  - ${route}`);
  });
  
  console.log('\n✨ Cleanup completed successfully!');
}

function validateEnvironmentVariables() {
  console.log('\n🔍 Checking environment variables...');
  
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXT_PUBLIC_APP_URL'
  ];
  
  const optionalVars = [
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'FACEBOOK_APP_ID',
    'FACEBOOK_APP_SECRET',
    'LINKEDIN_CLIENT_ID',
    'LINKEDIN_CLIENT_SECRET',
    'TWITTER_CLIENT_ID',
    'TWITTER_CLIENT_SECRET'
  ];
  
  console.log('\nRequired variables:');
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    console.log(`  ${varName}: ${value ? '✅ Set' : '❌ Missing'}`);
  });
  
  console.log('\nOptional variables (for social media):');
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    console.log(`  ${varName}: ${value ? '✅ Set' : '⚠️  Not set'}`);
  });
}

function createProductionChecklist() {
  const checklist = `# 🚀 eWasl Production Deployment Checklist

## ✅ Environment Configuration
- [ ] NEXT_PUBLIC_SUPABASE_URL configured
- [ ] NEXT_PUBLIC_SUPABASE_ANON_KEY configured  
- [ ] SUPABASE_SERVICE_ROLE_KEY configured (secure)
- [ ] NEXT_PUBLIC_APP_URL set to production domain
- [ ] Social media OAuth credentials configured
- [ ] Email service configured (SMTP or service)

## ✅ Security
- [ ] Rate limiting enabled on all API routes
- [ ] CSRF protection active
- [ ] Security headers configured
- [ ] Input validation in place
- [ ] SQL injection protection verified

## ✅ Database
- [ ] All required tables created
- [ ] Row Level Security (RLS) policies active
- [ ] Database backups configured
- [ ] Migration scripts tested

## ✅ Email System
- [ ] Email service working
- [ ] Welcome emails sending
- [ ] Password reset emails functional
- [ ] Email templates tested in Arabic

## ✅ Social Media Integrations
- [ ] Facebook OAuth working
- [ ] LinkedIn OAuth working
- [ ] Twitter/X OAuth working
- [ ] Instagram Basic Display working
- [ ] Publishing tests passed

## ✅ Performance
- [ ] Images optimized
- [ ] Database queries optimized
- [ ] CDN configured
- [ ] Caching implemented
- [ ] Bundle size optimized

## ✅ Monitoring
- [ ] Error tracking configured
- [ ] Performance monitoring active
- [ ] Uptime monitoring setup
- [ ] Log aggregation working

## 🧪 Testing Checklist
- [ ] Authentication flow tested
- [ ] Email system tested
- [ ] Social media connections tested
- [ ] Publishing system tested
- [ ] Payment system tested (if applicable)
- [ ] Mobile responsiveness verified
- [ ] Cross-browser compatibility checked

## 🔄 Post-Deployment
- [ ] DNS configured correctly
- [ ] SSL certificate active
- [ ] Redirects working
- [ ] Sitemap submitted
- [ ] Analytics tracking active

Generated: ${new Date().toISOString()}
`;

  fs.writeFileSync(path.join(__dirname, '..', 'PRODUCTION_CHECKLIST.md'), checklist);
  console.log('\n📋 Created PRODUCTION_CHECKLIST.md');
}

// Run cleanup if this script is called directly
if (require.main === module) {
  cleanupTestRoutes();
  validateEnvironmentVariables();
  createProductionChecklist();
}

module.exports = {
  cleanupTestRoutes,
  validateEnvironmentVariables,
  createProductionChecklist
};