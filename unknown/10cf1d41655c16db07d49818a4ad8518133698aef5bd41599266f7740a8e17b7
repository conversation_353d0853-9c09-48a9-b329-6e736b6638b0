#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

console.log('🔍 Testing User Authentication & User ID Detection\n');

// Test what user ID is being passed
async function testUserID() {
  console.log('1. Testing social connect endpoint with demo user...');
  
  try {
    const response = await fetch('https://app.ewasl.com/api/social/connect', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        platform: 'LINKEDIN',
        userId: '3ddaeb03-2d95-4fff-abad-2a2c7dd25037' // Demo user
      }),
    });

    const data = await response.json();
    console.log('📊 Response status:', response.status);
    console.log('📊 Response data:', data);
    
    if (response.ok) {
      console.log('✅ OAuth initiation successful with demo user');
    } else {
      console.log('❌ OAuth initiation failed:', data.error);
    }
    
  } catch (error) {
    console.error('💥 Request error:', error.message);
  }
}

// Test accounts endpoint
async function testAccountsEndpoint() {
  console.log('\n2. Testing accounts endpoint...');
  
  try {
    const response = await fetch('https://app.ewasl.com/api/social/accounts?userId=3ddaeb03-2d95-4fff-abad-2a2c7dd25037');
    const data = await response.json();
    
    console.log('📊 Accounts response status:', response.status);
    console.log('📊 Accounts data:', data);
    
    if (data.success) {
      console.log(`✅ Found ${data.accounts?.length || 0} accounts`);
      data.accounts?.forEach(acc => {
        console.log(`   - ${acc.platform}: ${acc.account_name}`);
      });
    } else {
      console.log('❌ Failed to fetch accounts:', data.error);
    }
    
  } catch (error) {
    console.error('💥 Request error:', error.message);
  }
}

// Run tests
async function runTests() {
  await testUserID();
  await testAccountsEndpoint();
  console.log('\n🏁 Tests complete!');
}

runTests(); 