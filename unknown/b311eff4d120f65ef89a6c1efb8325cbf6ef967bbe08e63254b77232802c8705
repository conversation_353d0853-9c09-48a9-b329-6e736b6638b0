import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    console.log('🗑️ CLEARING EXISTING FACEBOOK TOKENS FROM DATABASE...');

    // Get user ID from request body or use default test user
    const body = await request.json().catch(() => ({}));
    const userId = body.user_id || '20f4600f-196c-4c53-9a25-efa3740f3705';

    console.log(`🔍 Clearing Facebook tokens for user: ${userId}`);

    // Clear existing Facebook tokens from social_accounts table
    const { data: deletedAccounts, error: deleteError } = await supabase
      .from('social_accounts')
      .delete()
      .eq('user_id', userId)
      .eq('platform', 'FACEBOOK')
      .select();

    if (deleteError) {
      console.error('❌ Failed to clear Facebook tokens:', deleteError);
      return NextResponse.json({
        success: false,
        message: 'Failed to clear existing Facebook tokens',
        error: deleteError.message,
        userId: userId
      }, { status: 500 });
    }

    console.log('✅ Successfully cleared existing Facebook tokens');
    console.log(`📊 Deleted ${deletedAccounts?.length || 0} Facebook accounts`);

    // Also clear any OAuth states for Facebook
    const { error: stateDeleteError } = await supabase
      .from('oauth_states')
      .delete()
      .eq('user_id', userId)
      .eq('platform', 'facebook');

    if (stateDeleteError) {
      console.warn('⚠️ Failed to clear OAuth states:', stateDeleteError);
    }

    return NextResponse.json({
      success: true,
      message: 'Facebook tokens cleared successfully',
      action: 'tokens_cleared',
      deletedAccounts: deletedAccounts?.length || 0,
      userId: userId,
      nextSteps: [
        '1. Navigate to /social page',
        '2. Click "ربط حساب جديد" (Connect New Account)',
        '3. Select Facebook',
        '4. Complete OAuth flow with corrected scopes',
        '5. Verify User token generation instead of Page tokens'
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error clearing Facebook tokens:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error while clearing tokens',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 ENHANCED FACEBOOK OAUTH DIAGNOSTIC STARTING...');

    // Get user ID from query params or use default test user
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id') || '20f4600f-196c-4c53-9a25-efa3740f3705';
    const runInfraTest = searchParams.get('infra_test') === 'true';

    // If infrastructure test is requested, run comprehensive diagnostic
    if (runInfraTest) {
      console.log('🧪 RUNNING OAUTH CALLBACK INFRASTRUCTURE TEST...');

      const testResults = {
        timestamp: new Date().toISOString(),
        steps: [] as any[],
        success: false,
        error: null as any,
        summary: ''
      };

      // STEP 1: Test Database Connection
      testResults.steps.push({
        step: 1,
        name: 'Database Connection Test',
        status: 'testing',
        details: 'Testing Supabase service client connection'
      });

      try {
        const { data: testData, error: dbError } = await supabase
          .from('social_accounts')
          .select('count')
          .limit(1);

        if (dbError) {
          testResults.steps[0].status = 'failed';
          testResults.steps[0].error = dbError.message;
          testResults.steps[0].details = `Database connection failed: ${dbError.message}`;
        } else {
          testResults.steps[0].status = 'passed';
          testResults.steps[0].details = 'Database connection successful';
        }
      } catch (dbTestError) {
        testResults.steps[0].status = 'failed';
        testResults.steps[0].error = dbTestError.message;
        testResults.steps[0].details = `Database connection exception: ${dbTestError.message}`;
      }

      // STEP 2: Test Facebook API Access
      testResults.steps.push({
        step: 2,
        name: 'Facebook API Access Test',
        status: 'testing',
        details: 'Testing Facebook debug_token API access'
      });

      try {
        const testToken = 'test_token_123';
        const debugResponse = await fetch(
          `https://graph.facebook.com/debug_token?input_token=${testToken}&access_token=${process.env.FACEBOOK_APP_ID}|${process.env.FACEBOOK_APP_SECRET}`
        );

        const debugData = await debugResponse.json();

        if (debugResponse.ok || debugData.error?.code === 190) {
          testResults.steps[1].status = 'passed';
          testResults.steps[1].details = 'Facebook API accessible (expected invalid token error)';
          testResults.steps[1].response = debugData;
        } else {
          testResults.steps[1].status = 'failed';
          testResults.steps[1].error = debugData.error?.message || 'Unknown API error';
          testResults.steps[1].details = `Facebook API error: ${debugData.error?.message}`;
        }
      } catch (apiTestError) {
        testResults.steps[1].status = 'failed';
        testResults.steps[1].error = apiTestError.message;
        testResults.steps[1].details = `Facebook API exception: ${apiTestError.message}`;
      }

      // Calculate overall success
      const passedSteps = testResults.steps.filter(step => step.status === 'passed').length;
      const totalSteps = testResults.steps.length;

      testResults.success = passedSteps === totalSteps;
      testResults.summary = `${passedSteps}/${totalSteps} infrastructure tests passed`;

      return NextResponse.json({
        success: true,
        message: 'OAuth callback infrastructure test completed',
        infrastructureTest: testResults,
        timestamp: new Date().toISOString()
      });
    }

    console.log(`🔍 Analyzing Facebook OAuth for user: ${userId}`);

    // Check for existing Facebook accounts
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', userId)
      .eq('platform', 'FACEBOOK');

    if (accountsError) {
      console.error('❌ Error fetching accounts:', accountsError);
    }

    // Check for recent OAuth logs
    const { data: oauthLogs, error: logsError } = await supabase
      .from('oauth_logs')
      .select('*')
      .eq('user_id', userId)
      .eq('platform', 'facebook')
      .order('created_at', { ascending: false })
      .limit(5);

    if (logsError) {
      console.error('❌ Error fetching OAuth logs:', logsError);
    }

    // If we have accounts, test the token
    let tokenAnalysis = null;
    if (accounts && accounts.length > 0) {
      const account = accounts[0];
      const accessToken = account.access_token;

      if (accessToken) {
        console.log('🔍 Testing stored Facebook token...');

        // Test Facebook debug_token API
        try {
          const debugResponse = await fetch(
            `https://graph.facebook.com/debug_token?input_token=${accessToken}&access_token=${process.env.FACEBOOK_APP_ID}|${process.env.FACEBOOK_APP_SECRET}`
          );

          if (debugResponse.ok) {
            const debugData = await debugResponse.json();
            tokenAnalysis = {
              success: true,
              tokenType: debugData.data?.type,
              scopes: debugData.data?.scopes,
              userId: debugData.data?.user_id,
              profileId: debugData.data?.profile_id,
              isValid: debugData.data?.is_valid,
              expiresAt: debugData.data?.expires_at,
              appId: debugData.data?.app_id
            };
          }
        } catch (tokenError) {
          console.error('❌ Token analysis error:', tokenError);
          tokenAnalysis = { success: false, error: tokenError.message };
        }

        // Test /me/accounts endpoint
        try {
          const accountsResponse = await fetch(
            `https://graph.facebook.com/v18.0/me/accounts?access_token=${accessToken}`
          );

          const accountsData = await accountsResponse.json();
          tokenAnalysis.accountsTest = {
            success: accountsResponse.ok,
            data: accountsData,
            canAccessAccounts: accountsResponse.ok && accountsData.data
          };
        } catch (accountsError) {
          tokenAnalysis.accountsTest = { success: false, error: accountsError.message };
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Enhanced Facebook OAuth diagnostic completed',
      userId: userId,
      analysis: {
        accountsInDatabase: accounts?.length || 0,
        accounts: accounts || [],
        recentOAuthLogs: oauthLogs || [],
        tokenAnalysis: tokenAnalysis,
        diagnosis: {
          hasAccounts: (accounts?.length || 0) > 0,
          hasRecentLogs: (oauthLogs?.length || 0) > 0,
          tokenType: tokenAnalysis?.tokenType || 'unknown',
          canAccessAccounts: tokenAnalysis?.accountsTest?.canAccessAccounts || false,
          rootCause: tokenAnalysis?.tokenType === 'PAGE'
            ? 'Facebook returning Page tokens instead of User tokens'
            : tokenAnalysis?.tokenType === 'USER'
            ? 'User token received but /me/accounts failing'
            : 'No token available for analysis'
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Enhanced diagnostic error:', error);
    return NextResponse.json({
      success: false,
      message: 'Enhanced diagnostic failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
