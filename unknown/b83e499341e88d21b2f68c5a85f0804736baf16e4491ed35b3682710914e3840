#!/usr/bin/env node

/**
 * eWasl Platform - Complete Testing Suite Runner
 * 
 * Runs end-to-end, performance, and generates UAT checklist
 * Provides comprehensive testing coverage for production readiness.
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com',
  outputDir: path.join(__dirname, '../test-results'),
  timestamp: new Date().toISOString().replace(/[:.]/g, '-')
};

// Color codes
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Test results
const testResults = {
  endToEnd: null,
  performance: null,
  startTime: Date.now(),
  endTime: null
};

/**
 * Create output directory
 */
function createOutputDirectory() {
  if (!fs.existsSync(config.outputDir)) {
    fs.mkdirSync(config.outputDir, { recursive: true });
  }
}

/**
 * Run a test script and capture output
 */
function runTestScript(scriptPath, scriptName) {
  return new Promise((resolve, reject) => {
    console.log(`\n${colors.bold}${colors.blue}🚀 Starting ${scriptName}...${colors.reset}`);
    
    const startTime = Date.now();
    const child = spawn('node', [scriptPath, config.baseUrl], {
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: true
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      process.stdout.write(output); // Real-time output
    });

    child.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      process.stderr.write(output); // Real-time output
    });

    child.on('close', (code) => {
      const endTime = Date.now();
      const duration = Math.round((endTime - startTime) / 1000);
      
      console.log(`\n${colors.bold}${scriptName} completed in ${duration} seconds${colors.reset}`);
      
      // Save output to file
      const outputFile = path.join(config.outputDir, `${scriptName.toLowerCase().replace(' ', '-')}-${config.timestamp}.log`);
      fs.writeFileSync(outputFile, `${scriptName} Test Results\n${'='.repeat(50)}\n\nSTDOUT:\n${stdout}\n\nSTDERR:\n${stderr}\n`);
      
      resolve({
        success: code === 0,
        code,
        duration,
        stdout,
        stderr,
        outputFile
      });
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

/**
 * Generate comprehensive test report
 */
function generateTestReport() {
  console.log(`\n${colors.bold}${colors.cyan}📊 COMPREHENSIVE TEST REPORT${colors.reset}`);
  console.log(`${colors.bold}${'='.repeat(70)}${colors.reset}`);
  
  const totalDuration = Math.round((testResults.endTime - testResults.startTime) / 1000);
  console.log(`${colors.bold}Testing completed in ${totalDuration} seconds${colors.reset}`);
  console.log(`${colors.bold}Test URL: ${config.baseUrl}${colors.reset}`);
  console.log(`${colors.bold}Results saved to: ${config.outputDir}${colors.reset}\n`);

  // Test Summary
  console.log(`${colors.bold}TEST SUMMARY:${colors.reset}`);
  
  if (testResults.endToEnd) {
    const status = testResults.endToEnd.success ? 
      `${colors.green}✅ PASSED${colors.reset}` : 
      `${colors.red}❌ FAILED${colors.reset}`;
    console.log(`  End-to-End Tests: ${status} (${testResults.endToEnd.duration}s)`);
  }
  
  if (testResults.performance) {
    const status = testResults.performance.success ? 
      `${colors.green}✅ PASSED${colors.reset}` : 
      `${colors.red}❌ FAILED${colors.reset}`;
    console.log(`  Performance Tests: ${status} (${testResults.performance.duration}s)`);
  }

  // Overall Assessment
  const allTestsPassed = testResults.endToEnd?.success && testResults.performance?.success;
  const someTestsPassed = testResults.endToEnd?.success || testResults.performance?.success;
  
  console.log(`\n${colors.bold}OVERALL ASSESSMENT:${colors.reset}`);
  
  if (allTestsPassed) {
    console.log(`${colors.green}🚀 EXCELLENT - Platform ready for production launch!${colors.reset}`);
    console.log(`${colors.green}   All automated tests passed successfully.${colors.reset}`);
  } else if (someTestsPassed) {
    console.log(`${colors.yellow}⚠️  PARTIAL SUCCESS - Some issues need attention${colors.reset}`);
    console.log(`${colors.yellow}   Review failed tests and address issues before launch.${colors.reset}`);
  } else {
    console.log(`${colors.red}🚫 CRITICAL ISSUES - Platform not ready for production${colors.reset}`);
    console.log(`${colors.red}   Multiple test failures require immediate attention.${colors.reset}`);
  }

  // Next Steps
  console.log(`\n${colors.bold}NEXT STEPS:${colors.reset}`);
  
  if (allTestsPassed) {
    console.log(`${colors.green}1. ✅ Proceed with User Acceptance Testing${colors.reset}`);
    console.log(`${colors.green}2. ✅ Schedule production deployment${colors.reset}`);
    console.log(`${colors.green}3. ✅ Prepare launch communications${colors.reset}`);
  } else {
    console.log(`${colors.yellow}1. 🔧 Review test failure logs in ${config.outputDir}${colors.reset}`);
    console.log(`${colors.yellow}2. 🐛 Fix identified issues${colors.reset}`);
    console.log(`${colors.yellow}3. 🔄 Re-run failed tests${colors.reset}`);
    console.log(`${colors.yellow}4. 📋 Complete manual UAT checklist${colors.reset}`);
  }

  // Files Generated
  console.log(`\n${colors.bold}FILES GENERATED:${colors.reset}`);
  console.log(`📁 Test Results Directory: ${config.outputDir}`);
  
  if (testResults.endToEnd?.outputFile) {
    console.log(`📄 End-to-End Results: ${path.basename(testResults.endToEnd.outputFile)}`);
  }
  
  if (testResults.performance?.outputFile) {
    console.log(`📄 Performance Results: ${path.basename(testResults.performance.outputFile)}`);
  }
  
  console.log(`📋 UAT Checklist: USER_ACCEPTANCE_TESTING_CHECKLIST.md`);

  // User Acceptance Testing Instructions
  console.log(`\n${colors.bold}${colors.cyan}👥 USER ACCEPTANCE TESTING:${colors.reset}`);
  console.log(`${colors.cyan}Complete the manual UAT checklist to validate user experience:${colors.reset}`);
  console.log(`${colors.cyan}1. Open: USER_ACCEPTANCE_TESTING_CHECKLIST.md${colors.reset}`);
  console.log(`${colors.cyan}2. Follow the 15-minute testing procedure${colors.reset}`);
  console.log(`${colors.cyan}3. Document any issues found${colors.reset}`);
  console.log(`${colors.cyan}4. Make final go/no-go decision${colors.reset}`);

  console.log(`\n${colors.bold}${'='.repeat(70)}${colors.reset}\n`);
}

/**
 * Create test summary file
 */
function createTestSummaryFile() {
  const summaryContent = `# eWasl Platform - Test Results Summary

**Test Date:** ${new Date().toLocaleString()}  
**Test URL:** ${config.baseUrl}  
**Total Duration:** ${Math.round((testResults.endTime - testResults.startTime) / 1000)} seconds  

## Test Results

### End-to-End Tests
- **Status:** ${testResults.endToEnd?.success ? '✅ PASSED' : '❌ FAILED'}
- **Duration:** ${testResults.endToEnd?.duration || 'N/A'} seconds
- **Exit Code:** ${testResults.endToEnd?.code || 'N/A'}

### Performance Tests  
- **Status:** ${testResults.performance?.success ? '✅ PASSED' : '❌ FAILED'}
- **Duration:** ${testResults.performance?.duration || 'N/A'} seconds
- **Exit Code:** ${testResults.performance?.code || 'N/A'}

## Overall Assessment

${testResults.endToEnd?.success && testResults.performance?.success ? 
  '🚀 **PRODUCTION READY** - All automated tests passed successfully!' :
  testResults.endToEnd?.success || testResults.performance?.success ?
  '⚠️ **PARTIAL SUCCESS** - Some issues need attention before launch.' :
  '🚫 **NOT READY** - Critical issues must be resolved before production.'}

## Next Steps

${testResults.endToEnd?.success && testResults.performance?.success ? 
  '1. Complete User Acceptance Testing\n2. Schedule production deployment\n3. Prepare launch communications' :
  '1. Review test failure logs\n2. Fix identified issues\n3. Re-run failed tests\n4. Complete manual UAT checklist'}

## Files Generated

- End-to-End Results: ${testResults.endToEnd?.outputFile ? path.basename(testResults.endToEnd.outputFile) : 'Not generated'}
- Performance Results: ${testResults.performance?.outputFile ? path.basename(testResults.performance.outputFile) : 'Not generated'}  
- UAT Checklist: USER_ACCEPTANCE_TESTING_CHECKLIST.md

---

**Generated by eWasl Testing Suite**
`;

  const summaryFile = path.join(config.outputDir, `test-summary-${config.timestamp}.md`);
  fs.writeFileSync(summaryFile, summaryContent);
  console.log(`📄 Test summary saved: ${summaryFile}`);
}

/**
 * Main test runner
 */
async function runAllTests() {
  console.log(`${colors.bold}${colors.cyan}🧪 eWasl Platform - Complete Testing Suite${colors.reset}`);
  console.log(`${colors.bold}Testing URL: ${config.baseUrl}${colors.reset}`);
  console.log(`${colors.bold}Output Directory: ${config.outputDir}${colors.reset}\n`);
  
  createOutputDirectory();
  
  try {
    // Phase 1: End-to-End Testing (30 minutes)
    console.log(`${colors.bold}${colors.yellow}🎯 Phase 1: End-to-End Testing (Target: 30 minutes)${colors.reset}`);
    testResults.endToEnd = await runTestScript(
      path.join(__dirname, 'end-to-end-testing.js'),
      'End-to-End Testing'
    );
    
    // Phase 2: Performance Validation (15 minutes)
    console.log(`\n${colors.bold}${colors.yellow}⚡ Phase 2: Performance Validation (Target: 15 minutes)${colors.reset}`);
    testResults.performance = await runTestScript(
      path.join(__dirname, 'performance-validation.js'),
      'Performance Testing'
    );
    
    testResults.endTime = Date.now();
    
    // Generate comprehensive report
    generateTestReport();
    createTestSummaryFile();
    
    // Exit with appropriate code
    const overallSuccess = testResults.endToEnd?.success && testResults.performance?.success;
    process.exit(overallSuccess ? 0 : 1);
    
  } catch (error) {
    console.error(`\n${colors.red}❌ Test runner error: ${error.message}${colors.reset}`);
    testResults.endTime = Date.now();
    generateTestReport();
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.length > 2) {
  const customUrl = process.argv[2];
  if (customUrl.startsWith('http')) {
    config.baseUrl = customUrl;
  }
}

// Run all tests if this script is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = { runAllTests }; 