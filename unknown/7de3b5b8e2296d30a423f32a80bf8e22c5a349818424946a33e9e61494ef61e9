#!/usr/bin/env node

/**
 * OAuth Connections Test Script
 * Tests all social media OAuth configurations and flows
 */

require('dotenv').config({ path: '../.env.local' });

const BASE_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

async function testPlatformConfiguration(platform) {
  console.log(`\n🔍 Testing ${platform} Configuration...`);
  
  try {
    // Test configuration endpoint
    const configResponse = await fetch(`${BASE_URL}/api/social/connect?platform=${platform}`);
    const configData = await configResponse.json();
    
    console.log(`   📊 Configuration Status:`);
    console.log(`      ✅ Platform: ${configData.platform}`);
    console.log(`      ${configData.configured ? '✅' : '❌'} Configured: ${configData.configured}`);
    
    if (configData.config) {
      console.log(`      ${configData.config.clientId !== 'missing' ? '✅' : '❌'} Client ID: ${configData.config.clientId}`);
      console.log(`      ${configData.config.clientSecret !== 'missing' ? '✅' : '❌'} Client Secret: ${configData.config.clientSecret}`);
      console.log(`      🔗 Auth URL: ${configData.config.authUrl}`);
      console.log(`      📝 Scopes: ${configData.config.scopes.join(', ')}`);
      console.log(`      🔐 Uses PKCE: ${configData.config.usePKCE}`);
      console.log(`      📍 Redirect URI: ${configData.config.redirectUri}`);
    }
    
    return {
      platform,
      configured: configData.configured,
      hasClientId: configData.config?.clientId !== 'missing',
      hasClientSecret: configData.config?.clientSecret !== 'missing',
      authUrl: configData.config?.authUrl,
      redirectUri: configData.config?.redirectUri
    };
    
  } catch (error) {
    console.log(`   ❌ Configuration test failed:`, error.message);
    return {
      platform,
      configured: false,
      error: error.message
    };
  }
}

async function testOAuthInitiation(platform) {
  console.log(`\n🚀 Testing ${platform} OAuth Initiation...`);
  
  try {
    const testUserId = 'test-user-123';
    const response = await fetch(`${BASE_URL}/api/social/connect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        platform,
        userId: testUserId,
        redirectUri: `${BASE_URL}/social`
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log(`   ✅ OAuth initiation successful`);
      console.log(`   🔗 Auth URL generated: ${data.authUrl.substring(0, 100)}...`);
      
      // Validate auth URL structure
      const authUrl = new URL(data.authUrl);
      const params = authUrl.searchParams;
      
      console.log(`   📊 OAuth Parameters:`);
      console.log(`      ✅ client_id: ${params.get('client_id') ? 'Present' : 'Missing'}`);
      console.log(`      ✅ redirect_uri: ${params.get('redirect_uri') ? 'Present' : 'Missing'}`);
      console.log(`      ✅ scope: ${params.get('scope') ? 'Present' : 'Missing'}`);
      console.log(`      ✅ response_type: ${params.get('response_type')}`);
      console.log(`      ✅ state: ${params.get('state') ? 'Present' : 'Missing'}`);
      
      if (params.get('code_challenge')) {
        console.log(`      🔐 PKCE code_challenge: Present`);
        console.log(`      🔐 PKCE method: ${params.get('code_challenge_method')}`);
      }
      
      return {
        platform,
        success: true,
        authUrl: data.authUrl,
        hasRequiredParams: !!(params.get('client_id') && params.get('redirect_uri') && params.get('scope'))
      };
    } else {
      console.log(`   ❌ OAuth initiation failed:`, data.error);
      console.log(`   💡 Details:`, data.details);
      return {
        platform,
        success: false,
        error: data.error,
        details: data.details
      };
    }
    
  } catch (error) {
    console.log(`   ❌ OAuth initiation error:`, error.message);
    return {
      platform,
      success: false,
      error: error.message
    };
  }
}

async function testEnvironmentVariables() {
  console.log(`\n🔧 Testing Environment Variables...`);
  
  const requiredVars = {
    'X_CLIENT_ID': process.env.X_CLIENT_ID,
    'X_CLIENT_SECRET': process.env.X_CLIENT_SECRET,
    'FACEBOOK_CLIENT_ID': process.env.FACEBOOK_CLIENT_ID,
    'FACEBOOK_CLIENT_SECRET': process.env.FACEBOOK_CLIENT_SECRET,
    'LINKEDIN_CLIENT_ID': process.env.LINKEDIN_CLIENT_ID,
    'LINKEDIN_CLIENT_SECRET': process.env.LINKEDIN_CLIENT_SECRET,
  };
  
  const results = {};
  
  for (const [varName, value] of Object.entries(requiredVars)) {
    const isSet = !!value;
    const status = isSet ? '✅' : '❌';
    const display = isSet ? `${value.substring(0, 10)}...` : 'Not set';
    
    console.log(`   ${status} ${varName}: ${display}`);
    results[varName] = isSet;
  }
  
  return results;
}

async function generateTestReport(results) {
  console.log(`\n📋 OAUTH TEST REPORT`);
  console.log(`==========================================`);
  
  const platforms = ['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN'];
  let totalPlatforms = platforms.length;
  let configuredPlatforms = 0;
  let workingOAuth = 0;
  
  for (const platform of platforms) {
    const config = results.configs[platform];
    const oauth = results.oauth[platform];
    
    console.log(`\n${platform}:`);
    
    if (config && config.configured) {
      console.log(`   ✅ Configuration: Ready`);
      configuredPlatforms++;
    } else {
      console.log(`   ❌ Configuration: ${config?.error || 'Missing credentials'}`);
    }
    
    if (oauth && oauth.success) {
      console.log(`   ✅ OAuth Flow: Working`);
      workingOAuth++;
    } else {
      console.log(`   ❌ OAuth Flow: ${oauth?.error || 'Failed'}`);
    }
  }
  
  console.log(`\n📊 SUMMARY:`);
  console.log(`   🔧 Configured Platforms: ${configuredPlatforms}/${totalPlatforms}`);
  console.log(`   🚀 Working OAuth Flows: ${workingOAuth}/${totalPlatforms}`);
  console.log(`   📈 Success Rate: ${Math.round((workingOAuth / totalPlatforms) * 100)}%`);
  
  if (workingOAuth === totalPlatforms) {
    console.log(`\n🎉 ALL SOCIAL MEDIA INTEGRATIONS ARE READY!`);
    console.log(`✨ You can now test connections on ${BASE_URL}/social`);
  } else if (workingOAuth > 0) {
    console.log(`\n⚠️ Partial configuration detected.`);
    console.log(`💡 Check missing credentials and retry configuration.`);
  } else {
    console.log(`\n❌ No platforms are properly configured.`);
    console.log(`🔧 Please run the credential configuration script first.`);
  }
  
  return {
    totalPlatforms,
    configuredPlatforms,
    workingOAuth,
    successRate: Math.round((workingOAuth / totalPlatforms) * 100)
  };
}

async function main() {
  console.log(`🧪 OAuth Connections Test Suite`);
  console.log(`Base URL: ${BASE_URL}\n`);
  
  try {
    // Test environment variables
    const envResults = await testEnvironmentVariables();
    
    // Test platform configurations
    const platforms = ['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN'];
    const configResults = {};
    const oauthResults = {};
    
    for (const platform of platforms) {
      configResults[platform] = await testPlatformConfiguration(platform);
      
      // Only test OAuth if configuration is ready
      if (configResults[platform].configured) {
        oauthResults[platform] = await testOAuthInitiation(platform);
      } else {
        console.log(`\n⏭️ Skipping OAuth test for ${platform} (not configured)`);
        oauthResults[platform] = { platform, success: false, error: 'Not configured' };
      }
    }
    
    // Generate comprehensive report
    const report = await generateTestReport({
      env: envResults,
      configs: configResults,
      oauth: oauthResults
    });
    
    // Exit with appropriate code
    process.exit(report.successRate === 100 ? 0 : 1);
    
  } catch (error) {
    console.error(`\n💥 Test suite error:`, error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { 
  testPlatformConfiguration, 
  testOAuthInitiation, 
  testEnvironmentVariables, 
  generateTestReport 
}; 