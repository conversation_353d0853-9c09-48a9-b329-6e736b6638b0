import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { createClient } from '@supabase/supabase-js';
import { createFacebookOAuthService } from '@/lib/oauth/facebook';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const { user } = await getAuthenticatedUser(request);
    
    // Get all Facebook social accounts for this user
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'FACEBOOK');

    if (accountsError) {
      return NextResponse.json(
        { error: 'Failed to fetch Facebook accounts', details: accountsError.message },
        { status: 500 }
      );
    }

    if (!accounts || accounts.length === 0) {
      return NextResponse.json(
        { message: 'No Facebook accounts found for this user' },
        { status: 404 }
      );
    }

    const debugResults = [];

    for (const account of accounts) {
      const result: any = {
        account_id: account.account_id,
        account_name: account.account_name,
        stored_page_id: account.page_id,
        stored_page_access_token: account.page_access_token ? account.page_access_token.substring(0, 20) + '...' : null,
        stored_page_name: account.page_name,
        has_user_access_token: !!account.access_token,
        user_access_token_preview: account.access_token ? account.access_token.substring(0, 20) + '...' : null
      };

      // Test Facebook Graph API call using the OAuth service
      if (account.access_token) {
        try {
          console.log(`🔍 Testing Facebook Graph API for account ${account.account_id}`);

          // Use the Facebook OAuth service to get pages (includes proper appsecret_proof)
          const facebookService = createFacebookOAuthService();
          const accounts = await facebookService.getUserAccounts(account.access_token);
          result.facebook_api_test = {
            success: true,
            pages_count: accounts.length,
            pages_data: accounts.map((acc: any) => ({
              id: acc.id,
              name: acc.accountName,
              has_access_token: !!acc.accessToken,
              access_token_preview: acc.accessToken ? acc.accessToken.substring(0, 20) + '...' : null,
              platform: acc.platform,
              has_page_connection: !!acc.pageId,
              page_id: acc.pageId,
              page_access_token_preview: acc.pageAccessToken ? acc.pageAccessToken.substring(0, 20) + '...' : null,
              page_name: acc.pageName,
              account_type: acc.accountType,
              connection_status: acc.connectionStatus
            }))
          };

          // Check if any page matches the stored account
          const matchingPage = accounts.find((acc: any) => acc.pageId === account.page_id);
          if (matchingPage) {
            result.page_match_found = {
              stored_page_id_matches: true,
              current_page_token: matchingPage.pageAccessToken ? matchingPage.pageAccessToken.substring(0, 20) + '...' : null,
              tokens_match: account.page_access_token === matchingPage.pageAccessToken,
              live_page_data: {
                id: matchingPage.pageId,
                name: matchingPage.pageName,
                account_type: matchingPage.accountType
              }
            };
          } else {
            result.page_match_found = {
              stored_page_id_matches: false,
              available_page_ids: accounts.map((acc: any) => acc.pageId).filter(Boolean)
            };
          }

        } catch (apiError) {
          result.facebook_api_test = {
            success: false,
            error: apiError instanceof Error ? apiError.message : 'Unknown error',
            exception: true
          };
        }
      } else {
        result.facebook_api_test = {
          success: false,
          error: 'No user access token available'
        };
      }

      debugResults.push(result);
    }

    return NextResponse.json({
      user_id: user.id,
      total_facebook_accounts: accounts.length,
      debug_timestamp: new Date().toISOString(),
      accounts: debugResults,
      summary: {
        accounts_with_page_tokens: debugResults.filter(a => a.stored_page_access_token).length,
        accounts_with_user_tokens: debugResults.filter(a => a.has_user_access_token).length,
        successful_api_calls: debugResults.filter(a => a.facebook_api_test?.success).length
      }
    });

  } catch (error) {
    console.error('Facebook pages debug error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to debug Facebook pages',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
