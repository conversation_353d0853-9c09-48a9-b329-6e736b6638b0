#!/usr/bin/env node

/**
 * Comprehensive System Monitoring and Testing Script
 * 
 * This script runs a complete suite of tests and monitoring checks
 * to verify that all systems are working correctly.
 * 
 * Usage: node run-monitoring-tests.js [--fix] [--verbose]
 * 
 * Options:
 *   --fix       Attempt to fix issues automatically
 *   --verbose   Show detailed information for each test
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);

// CLI arguments
const args = process.argv.slice(2);
const shouldFix = args.includes('--fix');
const verbose = args.includes('--verbose');

// Test results
const results = {
  timestamp: new Date().toISOString(),
  summary: {
    passed: 0,
    failed: 0,
    skipped: 0,
    fixed: 0
  },
  tests: [],
  environment: {},
  socialConnections: {},
  apiHealth: {},
  performance: {},
  errors: []
};

// Main function
async function main() {
  console.log('🚀 Starting comprehensive system monitoring and tests');
  console.log('=================================================');
  
  try {
    // Check environment variables
    await runTest('Environment Variables Check', checkEnvironmentVariables);
    
    // Check database connection
    await runTest('Database Connection Test', checkDatabaseConnection);
    
    // Check Supabase auth
    await runTest('Supabase Auth Test', checkSupabaseAuth);
    
    // Check social media connections
    await runTest('Social Media Connections Test', checkSocialConnections);
    
    // Check API health
    await runTest('API Health Check', checkApiHealth);
    
    // Run unit tests
    await runTest('Unit Tests', runUnitTests);
    
    // Run integration tests
    await runTest('Integration Tests', runIntegrationTests);
    
    // Run performance tests
    await runTest('Performance Tests', runPerformanceTests);
    
    // Generate report
    await generateReport();
    
    // Print summary
    console.log('\n📊 Test Summary:');
    console.log(`Total Tests: ${results.summary.passed + results.summary.failed + results.summary.skipped}`);
    console.log(`✅ Passed: ${results.summary.passed}`);
    console.log(`❌ Failed: ${results.summary.failed}`);
    console.log(`⏭️ Skipped: ${results.summary.skipped}`);
    
    if (shouldFix) {
      console.log(`🔧 Fixed: ${results.summary.fixed}`);
    }
    
    console.log('\nDetailed report saved to system-monitoring-report.json');
    
    // Exit with appropriate code
    process.exit(results.summary.failed > 0 ? 1 : 0);
    
  } catch (error) {
    console.error('❌ Error during testing:', error);
    results.errors.push({
      phase: 'main',
      message: error.message,
      stack: error.stack
    });
    
    await generateReport();
    process.exit(1);
  }
}

// Helper function to run a test and handle errors
async function runTest(name, testFn) {
  console.log(`\n🧪 Running test: ${name}`);
  
  const testResult = {
    name,
    status: 'pending',
    startTime: Date.now(),
    duration: 0,
    details: {},
    errors: []
  };
  
  try {
    const details = await testFn();
    testResult.status = 'passed';
    testResult.details = details || {};
    results.summary.passed++;
    console.log(`✅ ${name} passed`);
  } catch (error) {
    testResult.status = 'failed';
    testResult.errors.push({
      message: error.message,
      stack: error.stack
    });
    results.summary.failed++;
    console.error(`❌ ${name} failed: ${error.message}`);
    
    // Try to fix if requested
    if (shouldFix) {
      try {
        const fixFnName = `fix${testFn.name.substring(5)}`;
        if (typeof global[fixFnName] === 'function') {
          console.log(`🔧 Attempting to fix: ${name}`);
          await global[fixFnName]();
          results.summary.fixed++;
          console.log(`🔧 Fix applied for: ${name}`);
        }
      } catch (fixError) {
        console.error(`❌ Fix failed: ${fixError.message}`);
      }
    }
  }
  
  testResult.duration = Date.now() - testResult.startTime;
  results.tests.push(testResult);
}

// Environment Variables Check
async function checkEnvironmentVariables() {
  console.log('Checking environment variables...');
  
  // Required environment variables by category
  const requiredVars = {
    core: [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY',
      'DATABASE_URL'
    ],
    oauth: [
      'GOOGLE_CLIENT_ID',
      'GOOGLE_CLIENT_SECRET',
      'TWITTER_CLIENT_ID',
      'TWITTER_CLIENT_SECRET',
      'FACEBOOK_CLIENT_ID',
      'FACEBOOK_CLIENT_SECRET',
      'INSTAGRAM_CLIENT_ID',
      'INSTAGRAM_CLIENT_SECRET'
    ]
  };
  
  const missingVars = {
    critical: [],
    noncritical: []
  };
  
  // Check core variables (critical)
  for (const envVar of requiredVars.core) {
    if (!process.env[envVar]) {
      missingVars.critical.push(envVar);
    }
  }
  
  // Check other variables (non-critical)
  const nonCriticalCategories = ['oauth'];
  for (const category of nonCriticalCategories) {
    for (const envVar of requiredVars[category]) {
      if (!process.env[envVar]) {
        missingVars.noncritical.push(envVar);
      }
    }
  }
  
  // Store results
  results.environment = {
    missingCriticalVars: missingVars.critical,
    missingNonCriticalVars: missingVars.noncritical,
    totalChecked: Object.values(requiredVars).flat().length,
    present: Object.values(requiredVars).flat().length - 
             missingVars.critical.length - 
             missingVars.noncritical.length
  };
  
  // Fail if critical variables are missing
  if (missingVars.critical.length > 0) {
    throw new Error(`Missing critical environment variables: ${missingVars.critical.join(', ')}`);
  }
  
  // Warn about non-critical variables
  if (missingVars.noncritical.length > 0) {
    console.warn(`⚠️ Missing non-critical environment variables: ${missingVars.noncritical.join(', ')}`);
  }
  
  return {
    missingCriticalVars: missingVars.critical,
    missingNonCriticalVars: missingVars.noncritical
  };
}

// Database Connection Test
async function checkDatabaseConnection() {
  console.log('Checking database connection...');
  
  try {
    // Try a simple database query
    const output = execSync('npx prisma db execute --stdin', {
      input: 'SELECT 1 as test',
      encoding: 'utf-8'
    });
    
    if (verbose) {
      console.log('Database query result:', output);
    }
    
    return { connected: true };
  } catch (error) {
    throw new Error(`Database connection failed: ${error.message}`);
  }
}

// Supabase Auth Test
async function checkSupabaseAuth() {
  console.log('Checking Supabase auth...');
  
  try {
    // Create a test file to check auth
    const testFile = path.join(__dirname, 'temp-auth-test.js');
    
    await writeFile(testFile, `
      const { createClient } = require('@supabase/supabase-js');
      
      async function checkAuth() {
        const supabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        );
        
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          throw error;
        }
        
        console.log(JSON.stringify({ success: true }));
      }
      
      checkAuth().catch(error => {
        console.error(JSON.stringify({ 
          success: false, 
          error: error.message 
        }));
        process.exit(1);
      });
    `);
    
    const output = execSync(`node ${testFile}`, { encoding: 'utf-8' });
    fs.unlinkSync(testFile);
    
    const result = JSON.parse(output);
    
    if (!result.success) {
      throw new Error(result.error || 'Unknown auth error');
    }
    
    return { authenticated: true };
  } catch (error) {
    throw new Error(`Supabase auth failed: ${error.message}`);
  }
}

// Social Media Connections Test
async function checkSocialConnections() {
  console.log('Checking social media connections...');
  
  try {
    // Run the social connections verification script
    const output = execSync('node scripts/verify-social-connections.js --verbose', { 
      encoding: 'utf-8' 
    });
    
    if (verbose) {
      console.log(output);
    }
    
    // Parse the report file
    const reportPath = path.join(process.cwd(), 'social-connections-report.json');
    const reportData = JSON.parse(await readFile(reportPath, 'utf-8'));
    
    results.socialConnections = reportData.summary;
    
    // Check if there are critical issues
    if (reportData.summary.invalid > reportData.summary.total * 0.2) {
      throw new Error(`Too many invalid social connections: ${reportData.summary.invalid} out of ${reportData.summary.total}`);
    }
    
    return reportData.summary;
  } catch (error) {
    throw new Error(`Social connections check failed: ${error.message}`);
  }
}

// API Health Check
async function checkApiHealth() {
  console.log('Checking API health...');
  
  try {
    // Get the base URL from environment or use default
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    
    // Create a test file to check API health
    const testFile = path.join(__dirname, 'temp-api-health-test.js');
    
    await writeFile(testFile, `
      const fetch = require('node-fetch');
      
      async function checkApiHealth() {
        const endpoints = [
          '/api/health',
          '/api/health/database',
          '/api/health/auth',
          '/api/health/social'
        ];
        
        const results = {};
        
        for (const endpoint of endpoints) {
          try {
            const response = await fetch('${baseUrl}' + endpoint);
            const data = await response.json();
            
            results[endpoint] = {
              status: response.status,
              healthy: data.status === 'healthy',
              data
            };
          } catch (error) {
            results[endpoint] = {
              status: 'error',
              healthy: false,
              error: error.message
            };
          }
        }
        
        console.log(JSON.stringify(results));
      }
      
      checkApiHealth().catch(error => {
        console.error(JSON.stringify({ error: error.message }));
        process.exit(1);
      });
    `);
    
    const output = execSync(`node ${testFile}`, { encoding: 'utf-8' });
    fs.unlinkSync(testFile);
    
    const apiResults = JSON.parse(output);
    results.apiHealth = apiResults;
    
    // Check if any endpoint is unhealthy
    const unhealthyEndpoints = Object.entries(apiResults)
      .filter(([_, data]) => !data.healthy)
      .map(([endpoint]) => endpoint);
    
    if (unhealthyEndpoints.length > 0) {
      throw new Error(`Unhealthy API endpoints: ${unhealthyEndpoints.join(', ')}`);
    }
    
    return apiResults;
  } catch (error) {
    throw new Error(`API health check failed: ${error.message}`);
  }
}

// Unit Tests
async function runUnitTests() {
  console.log('Running unit tests...');
  
  try {
    const output = execSync('npm run test:unit', { encoding: 'utf-8' });
    
    if (verbose) {
      console.log(output);
    }
    
    return { success: true };
  } catch (error) {
    throw new Error(`Unit tests failed: ${error.message}`);
  }
}

// Integration Tests
async function runIntegrationTests() {
  console.log('Running integration tests...');
  
  try {
    const output = execSync('npm run test:integration', { encoding: 'utf-8' });
    
    if (verbose) {
      console.log(output);
    }
    
    return { success: true };
  } catch (error) {
    throw new Error(`Integration tests failed: ${error.message}`);
  }
}

// Performance Tests
async function runPerformanceTests() {
  console.log('Running performance tests...');
  
  try {
    // Create a test file for performance testing
    const testFile = path.join(__dirname, 'temp-performance-test.js');
    
    await writeFile(testFile, `
      const fetch = require('node-fetch');
      
      async function runPerformanceTest() {
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
        const endpoints = [
          '/',
          '/api/health',
          '/api/posts',
          '/api/analytics/summary'
        ];
        
        const results = {};
        
        for (const endpoint of endpoints) {
          const url = baseUrl + endpoint;
          const times = [];
          
          // Run 5 requests and measure time
          for (let i = 0; i < 5; i++) {
            const start = Date.now();
            try {
              const response = await fetch(url);
              await response.text();
              const end = Date.now();
              times.push(end - start);
            } catch (error) {
              times.push(null);
            }
          }
          
          // Calculate average (excluding nulls)
          const validTimes = times.filter(t => t !== null);
          const average = validTimes.length > 0 
            ? validTimes.reduce((sum, t) => sum + t, 0) / validTimes.length 
            : null;
          
          results[endpoint] = {
            times,
            average,
            failed: times.filter(t => t === null).length
          };
        }
        
        console.log(JSON.stringify(results));
      }
      
      runPerformanceTest().catch(error => {
        console.error(JSON.stringify({ error: error.message }));
        process.exit(1);
      });
    `);
    
    const output = execSync(`node ${testFile}`, { encoding: 'utf-8' });
    fs.unlinkSync(testFile);
    
    const perfResults = JSON.parse(output);
    results.performance = perfResults;
    
    // Check if any endpoint is too slow (>1000ms average)
    const slowEndpoints = Object.entries(perfResults)
      .filter(([_, data]) => data.average !== null && data.average > 1000)
      .map(([endpoint]) => `${endpoint} (${Math.round(perfResults[endpoint].average)}ms)`);
    
    if (slowEndpoints.length > 0) {
      console.warn(`⚠️ Slow endpoints detected: ${slowEndpoints.join(', ')}`);
    }
    
    return perfResults;
  } catch (error) {
    throw new Error(`Performance tests failed: ${error.message}`);
  }
}

// Generate report
async function generateReport() {
  await writeFile(
    path.join(process.cwd(), 'system-monitoring-report.json'),
    JSON.stringify(results, null, 2)
  );
}

// Run the script
main().catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
}); 