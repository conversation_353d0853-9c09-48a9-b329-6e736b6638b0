{"name": "ewasl-testing-suite", "version": "1.0.0", "description": "Comprehensive testing suite for eWasl platform", "main": "run-all-tests.js", "scripts": {"test": "node run-all-tests.js", "test:e2e": "node end-to-end-testing.js", "test:performance": "node performance-validation.js", "test:local": "node run-all-tests.js http://localhost:3000", "test:staging": "node run-all-tests.js https://staging.ewasl.com", "test:production": "node run-all-tests.js https://app.ewasl.com"}, "keywords": ["testing", "e2e", "performance", "automation", "social-media", "e<PERSON>l"], "author": "eWasl Team", "license": "MIT", "engines": {"node": ">=14.0.0"}}