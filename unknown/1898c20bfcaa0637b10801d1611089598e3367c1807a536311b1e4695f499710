#!/usr/bin/env node

/**
 * End-to-End Post Publishing Test
 * Tests real Facebook/Instagram post publishing functionality
 */

require('dotenv').config({ path: '.env.local' });

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'https://localhost:3003';

console.log('📝 Testing Post Publishing to Facebook/Instagram...\n');

// Test Data
const testPosts = [
  {
    id: 'test_immediate',
    content: '🚀 Testing eWasl social media platform! This is an immediate post test. #eWasl #SocialMedia #Test',
    platforms: ['facebook', 'instagram'],
    scheduledFor: null, // Immediate
    mediaFiles: []
  },
  {
    id: 'test_scheduled',
    content: '⏰ This is a scheduled post test from eWasl platform. Should be published in 5 minutes! #eWasl #Scheduled',
    platforms: ['facebook'],
    scheduledFor: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes from now
    mediaFiles: []
  },
  {
    id: 'test_with_media',
    content: '📸 Testing media upload with eWasl! This post includes an image. #eWasl #Media #Test',
    platforms: ['facebook', 'instagram'],
    scheduledFor: null,
    mediaFiles: [
      {
        type: 'image',
        url: 'https://via.placeholder.com/1080x1080/4F46E5/FFFFFF?text=eWasl+Test+Image',
        alt: 'eWasl test image'
      }
    ]
  }
];

// Test 1: Check Post Creation Endpoint
console.log('📋 Step 1: Testing Post Creation Endpoint...');
const testPostCreation = async (postData) => {
  try {
    const response = await fetch(`${APP_URL}/api/posts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test_token' // Will need real auth in actual test
      },
      body: JSON.stringify(postData)
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Post creation successful for: ${postData.id}`);
      console.log(`   Post ID: ${result.id || 'unknown'}`);
      return result;
    } else {
      const error = await response.text();
      console.log(`❌ Post creation failed for ${postData.id}: ${response.status}`);
      console.log(`   Error: ${error}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ Error creating post ${postData.id}:`, error.message);
    return null;
  }
};

// Test 2: Check Publishing Status
console.log('\n📋 Step 2: Testing Publishing Status Check...');
const checkPublishingStatus = async (postId) => {
  try {
    const response = await fetch(`${APP_URL}/api/posts/${postId}/status`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer test_token'
      }
    });
    
    if (response.ok) {
      const status = await response.json();
      console.log(`✅ Status check successful for post: ${postId}`);
      console.log(`   Status: ${status.status || 'unknown'}`);
      console.log(`   Platforms: ${JSON.stringify(status.platforms || {})}`);
      return status;
    } else {
      console.log(`❌ Status check failed for post: ${postId}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ Error checking status for post ${postId}:`, error.message);
    return null;
  }
};

// Test 3: Test Immediate Publishing
console.log('\n📋 Step 3: Testing Immediate Publishing...');
const testImmediatePublishing = async () => {
  const testPost = testPosts[0]; // Immediate post
  
  console.log(`📝 Creating immediate post: "${testPost.content.substring(0, 50)}..."`);
  const result = await testPostCreation(testPost);
  
  if (result) {
    console.log('⏳ Waiting 10 seconds for publishing...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    const status = await checkPublishingStatus(result.id);
    return { result, status };
  }
  
  return null;
};

// Test 4: Test Scheduled Publishing
console.log('\n📋 Step 4: Testing Scheduled Publishing...');
const testScheduledPublishing = async () => {
  const testPost = testPosts[1]; // Scheduled post
  
  console.log(`📅 Creating scheduled post for: ${testPost.scheduledFor}`);
  const result = await testPostCreation(testPost);
  
  if (result) {
    const status = await checkPublishingStatus(result.id);
    return { result, status };
  }
  
  return null;
};

// Test 5: Test Media Upload
console.log('\n📋 Step 5: Testing Media Upload...');
const testMediaUpload = async () => {
  const testPost = testPosts[2]; // Post with media
  
  console.log(`📸 Creating post with media: ${testPost.mediaFiles.length} files`);
  const result = await testPostCreation(testPost);
  
  if (result) {
    const status = await checkPublishingStatus(result.id);
    return { result, status };
  }
  
  return null;
};

// Test 6: Verify Posts on Social Platforms
console.log('\n📋 Step 6: Social Platform Verification...');
const generateVerificationInstructions = (results) => {
  console.log('🔍 MANUAL VERIFICATION STEPS:');
  console.log('');
  console.log('1. 📘 Check Facebook Page:');
  console.log('   - Go to your connected Facebook Page');
  console.log('   - Look for test posts with #eWasl hashtag');
  console.log('   - Verify post content matches what was sent');
  console.log('');
  console.log('2. 📷 Check Instagram Account:');
  console.log('   - Go to your connected Instagram Business account');
  console.log('   - Look for test posts with #eWasl hashtag');
  console.log('   - Verify images uploaded correctly');
  console.log('');
  console.log('3. ⏰ Check Scheduled Posts:');
  console.log('   - Wait for scheduled time');
  console.log('   - Verify posts appear at correct time');
  console.log('   - Check database for queue processing');
  console.log('');
  console.log('4. 📊 Check Analytics:');
  console.log('   - Visit eWasl dashboard');
  console.log('   - Verify post metrics are tracked');
  console.log('   - Check engagement data collection');
  
  if (results.length > 0) {
    console.log('\n📋 Test Posts Created:');
    results.forEach((result, index) => {
      if (result && result.result) {
        console.log(`   ${index + 1}. Post ID: ${result.result.id}`);
        console.log(`      Content: "${testPosts[index].content.substring(0, 50)}..."`);
        console.log(`      Platforms: ${testPosts[index].platforms.join(', ')}`);
        console.log(`      Status: ${result.status?.status || 'unknown'}`);
      }
    });
  }
};

// Test 7: Database Verification
console.log('\n📋 Step 7: Database Verification...');
const testDatabaseVerification = async () => {
  try {
    const response = await fetch(`${APP_URL}/api/posts/test-results`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer test_token'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Database verification successful');
      console.log(`   Total posts: ${data.totalPosts || 0}`);
      console.log(`   Published posts: ${data.publishedPosts || 0}`);
      console.log(`   Scheduled posts: ${data.scheduledPosts || 0}`);
      console.log(`   Failed posts: ${data.failedPosts || 0}`);
      return data;
    } else {
      console.log('❌ Database verification failed');
      return null;
    }
  } catch (error) {
    console.log('❌ Error verifying database:', error.message);
    return null;
  }
};

// Run all tests
(async () => {
  console.log('🧪 Running Post Publishing Tests...\n');
  
  const results = [];
  
  // Test immediate publishing
  const immediateResult = await testImmediatePublishing();
  results.push(immediateResult);
  
  // Test scheduled publishing
  const scheduledResult = await testScheduledPublishing();
  results.push(scheduledResult);
  
  // Test media upload
  const mediaResult = await testMediaUpload();
  results.push(mediaResult);
  
  // Database verification
  const dbVerification = await testDatabaseVerification();
  
  // Generate verification instructions
  generateVerificationInstructions(results);
  
  console.log('\n📊 Test Results Summary:');
  console.log(`   Immediate Publishing: ${immediateResult ? '✅' : '❌'}`);
  console.log(`   Scheduled Publishing: ${scheduledResult ? '✅' : '❌'}`);
  console.log(`   Media Upload: ${mediaResult ? '✅' : '❌'}`);
  console.log(`   Database Verification: ${dbVerification ? '✅' : '❌'}`);
  
  const successCount = results.filter(r => r !== null).length;
  console.log(`\n🎯 Success Rate: ${successCount}/${results.length} tests passed`);
  
  if (successCount === results.length) {
    console.log('\n🎉 All publishing tests passed! Check social media platforms for verification.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the issues above and verify API configuration.');
  }
})();

// Additional Error Handling Tests
console.log('\n🔧 PHASE 5: Error Handling Tests...');

// Test Rate Limiting
const testRateLimit = async () => {
  console.log('\n📋 Testing Rate Limit Handling...');

  // Simulate multiple rapid requests
  const promises = Array.from({ length: 10 }, (_, i) =>
    fetch(`${APP_URL}/api/posts`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        content: `Rate limit test ${i}`,
        platforms: ['facebook']
      })
    })
  );

  try {
    const responses = await Promise.all(promises);
    const rateLimited = responses.filter(r => r.status === 429);

    console.log(`✅ Rate limiting test: ${rateLimited.length}/10 requests rate limited`);
    return rateLimited.length > 0;
  } catch (error) {
    console.log('❌ Rate limiting test failed:', error.message);
    return false;
  }
};

// Test Token Expiration
const testTokenExpiration = async () => {
  console.log('\n📋 Testing Token Expiration Handling...');

  try {
    const response = await fetch(`${APP_URL}/api/social/facebook/refresh-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer expired_token'
      }
    });

    console.log(`✅ Token refresh endpoint accessible (status: ${response.status})`);
    return true;
  } catch (error) {
    console.log('❌ Token refresh test failed:', error.message);
    return false;
  }
};

// Test Invalid Content
const testInvalidContent = async () => {
  console.log('\n📋 Testing Invalid Content Handling...');

  const invalidPosts = [
    { content: '', platforms: ['facebook'] }, // Empty content
    { content: 'x'.repeat(64000), platforms: ['facebook'] }, // Too long
    { content: 'Test', platforms: [] }, // No platforms
    { content: 'Test', platforms: ['invalid_platform'] } // Invalid platform
  ];

  let errorsCaught = 0;

  for (const post of invalidPosts) {
    try {
      const response = await fetch(`${APP_URL}/api/posts`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(post)
      });

      if (!response.ok) {
        errorsCaught++;
      }
    } catch (error) {
      errorsCaught++;
    }
  }

  console.log(`✅ Invalid content handling: ${errorsCaught}/${invalidPosts.length} errors caught`);
  return errorsCaught === invalidPosts.length;
};
