#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

console.log('🔍 Testing ALL Social Media Platforms\n');

const platforms = ['TWITTER', 'FACEBOOK', 'INSTAGRAM', 'LINKEDIN'];

// Test OAuth initiation for all platforms
async function testAllPlatforms() {
  const results = [];
  
  for (const platform of platforms) {
    console.log(`📱 Testing ${platform}...`);
    
    try {
      const response = await fetch('https://app.ewasl.com/api/social/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          platform: platform,
          userId: '3ddaeb03-2d95-4fff-abad-2a2c7dd25037' // Demo user
        }),
      });

      const data = await response.json();
      
      const result = {
        platform,
        status: response.status,
        success: response.ok,
        hasAuthUrl: !!data.authUrl,
        error: data.error,
        message: data.message
      };
      
      results.push(result);
      
      if (response.ok) {
        console.log(`   ✅ ${platform}: OAuth URL generated successfully`);
        console.log(`      Auth URL: ${data.authUrl?.substring(0, 100)}...`);
      } else {
        console.log(`   ❌ ${platform}: ${data.error || 'Unknown error'}`);
      }
      
    } catch (error) {
      console.log(`   💥 ${platform}: Network error - ${error.message}`);
      results.push({
        platform,
        status: 'ERROR',
        success: false,
        error: error.message
      });
    }
    
    console.log(''); // Empty line for readability
  }
  
  return results;
}

// Test platform credentials
async function testPlatformCredentials() {
  console.log('🔑 Testing Platform Credentials Configuration...\n');
  
  const credentialTests = [
    {
      platform: 'LINKEDIN',
      clientId: process.env.LINKEDIN_CLIENT_ID,
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET
    },
    {
      platform: 'FACEBOOK',
      clientId: process.env.FACEBOOK_APP_ID,
      clientSecret: process.env.FACEBOOK_APP_SECRET
    },
    {
      platform: 'TWITTER/X',
      clientId: process.env.X_CLIENT_ID,
      clientSecret: process.env.X_CLIENT_SECRET
    }
  ];
  
  credentialTests.forEach(test => {
    const hasClientId = !!test.clientId;
    const hasClientSecret = !!test.clientSecret;
    const isConfigured = hasClientId && hasClientSecret;
    
    console.log(`${test.platform}:`);
    console.log(`   Client ID: ${hasClientId ? '✅ SET' : '❌ MISSING'}`);
    console.log(`   Client Secret: ${hasClientSecret ? '✅ SET' : '❌ MISSING'}`);
    console.log(`   Status: ${isConfigured ? '✅ CONFIGURED' : '⚠️ NEEDS SETUP'}`);
    console.log('');
  });
}

// Generate summary report
function generateSummary(results) {
  console.log('📊 PLATFORM TEST SUMMARY\n');
  console.log('═'.repeat(50));
  
  const working = results.filter(r => r.success);
  const needsCredentials = results.filter(r => !r.success && r.error?.includes('not configured'));
  const hasErrors = results.filter(r => !r.success && !r.error?.includes('not configured'));
  
  console.log(`✅ Working Platforms: ${working.length}/${platforms.length}`);
  working.forEach(r => console.log(`   - ${r.platform}`));
  
  console.log(`\n⚠️ Needs API Credentials: ${needsCredentials.length}`);
  needsCredentials.forEach(r => console.log(`   - ${r.platform}: ${r.error}`));
  
  console.log(`\n❌ Has Errors: ${hasErrors.length}`);
  hasErrors.forEach(r => console.log(`   - ${r.platform}: ${r.error}`));
  
  console.log('\n═'.repeat(50));
  
  if (working.length === platforms.length) {
    console.log('🎉 ALL PLATFORMS WORKING! Your social media integration is fully functional!');
  } else if (needsCredentials.length > 0) {
    console.log('🔧 SETUP NEEDED: Some platforms need API credentials configured.');
    console.log('   Once you add the missing credentials, all platforms will work!');
  } else {
    console.log('🚨 ISSUES FOUND: Some platforms have configuration errors that need fixing.');
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting comprehensive social media platform testing...\n');
  
  // Test credentials
  await testPlatformCredentials();
  
  // Test all platforms
  const results = await testAllPlatforms();
  
  // Generate summary
  generateSummary(results);
  
  console.log('\n🏁 All tests complete!');
}

runAllTests(); 