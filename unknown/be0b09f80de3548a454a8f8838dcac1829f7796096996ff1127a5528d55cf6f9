const https = require('https');

console.log('🚀 Testing Social Page Authentication Fix...\n');

const BASE_URL = 'https://app.ewasl.com';

function makeRequest(url, method = 'GET', options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      method,
      timeout: 10000,
      headers: {
        'User-Agent': 'eWasl-Test-Agent/1.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        ...options.headers
      },
      ...options
    };

    const req = https.request(url, requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testEndpoint(url, expectedStatus, description) {
  try {
    console.log(`\n📋 Testing: ${description}`);
    console.log(`🔗 URL: ${url}`);
    
    const response = await makeRequest(url);
    
    console.log(`📊 Status: ${response.statusCode}`);
    
    if (response.statusCode === expectedStatus) {
      console.log(`✅ SUCCESS: Expected ${expectedStatus}, got ${response.statusCode}`);
    } else {
      console.log(`❌ UNEXPECTED: Expected ${expectedStatus}, got ${response.statusCode}`);
    }
    
    // Check for specific content in response
    if (response.body.includes('Authentication Error') && expectedStatus !== 200) {
      console.log('⚠️  CRITICAL: Still seeing Authentication Error in response');
    }
    
    if (response.statusCode === 307 || response.statusCode === 302) {
      const location = response.headers.location;
      console.log(`🔄 Redirect to: ${location}`);
      
      if (location && location.includes('/auth/signin')) {
        console.log('✅ Correct redirect to sign-in page');
      }
    }
    
    return response;
  } catch (error) {
    console.log(`💥 ERROR: ${error.message}`);
    return null;
  }
}

async function testSocialPageFlow() {
  console.log('=' * 80);
  console.log('🔐 COMPREHENSIVE SOCIAL PAGE AUTHENTICATION TEST');
  console.log('=' * 80);
  
  // Test 1: Social page without authentication
  await testEndpoint(
    `${BASE_URL}/social`,
    307,
    'Social page access (should redirect to sign-in)'
  );
  
  // Test 2: Social accounts API without authentication
  await testEndpoint(
    `${BASE_URL}/api/social/accounts`,
    401,
    'Social accounts API (should return 401)'
  );
  
  // Test 3: Sign-in page accessibility
  await testEndpoint(
    `${BASE_URL}/auth/signin`,
    200,
    'Sign-in page accessibility'
  );
  
  // Test 4: Sign-in with redirect parameter
  await testEndpoint(
    `${BASE_URL}/auth/signin?redirectTo=/social`,
    200,
    'Sign-in page with social redirect parameter'
  );
  
  // Test 5: OAuth callback endpoints
  const oauthCallbacks = [
    '/api/auth/callback/facebook',
    '/api/auth/callback/twitter', 
    '/api/social/callback/x',
    '/api/social/callback/linkedin'
  ];
  
  for (const callback of oauthCallbacks) {
    await testEndpoint(
      `${BASE_URL}${callback}`,
      400, // Usually returns 400 without proper OAuth parameters
      `OAuth callback: ${callback}`
    );
  }
  
  console.log('\n' + '=' * 80);
  console.log('📋 TEST SUMMARY');
  console.log('=' * 80);
  console.log('✅ If all tests show expected status codes, the fix is working!');
  console.log('🔄 Social page should redirect to sign-in (307/302)');
  console.log('🔒 API endpoints should return 401 for unauthenticated users');
  console.log('📱 OAuth callbacks should be accessible (even if returning 400)');
  console.log('\n🚀 Test completed!');
}

// Run the test
testSocialPageFlow().catch(console.error); 