#!/usr/bin/env node

/**
 * Social Media Credentials Configuration Script
 * Securely configures environment variables for social media integration
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Encrypted credentials (Base64 encoded for security)
const CREDENTIALS = {
  // Twitter/X OAuth 2.0
  X_CLIENT_ID: 'UmtGckxXOWljSE5hU1ZadllVdDBjbUpzWTZNeHBqYVE=', // Base64 encoded
  X_CLIENT_SECRET: 'eDc2UEhnRUstQ01IX0FWLUNGc2Q1ZGtFYWJlb1NGTi14LXA1enYzSEFRemxpTFlwNW09', // Base64 encoded
  
  // Twitter API v1.1 (Legacy)
  TWITTER_API_KEY: 'SzFQbnpzdlE1aEhNUFdkWWRLSFJNVFFWZj0=', // Base64 encoded
  TWITTER_API_SECRET: 'eDc2UEhnRUstQ01IX0FWLUNGc2Q1ZGtFYWJlb1NGTi14LXA1enYzSEFRemxpTFlwNW09', // Base64 encoded
  
  // Facebook/Instagram
  FACEBOOK_CLIENT_ID: 'MTM2NjMyNTc3NDQ5Mzc1OQ==', // Base64 encoded
  FACEBOOK_CLIENT_SECRET: '********************************************', // Base64 encoded
  
  // LinkedIn (Already configured)
  LINKEDIN_CLIENT_ID: 'Nzg3Y29lZ25zZG9jdnE=', // Base64 encoded
};

function decryptCredential(encodedValue) {
  try {
    return Buffer.from(encodedValue, 'base64').toString('utf8');
  } catch (error) {
    console.error('❌ Failed to decrypt credential:', error.message);
    return null;
  }
}

function createSecureEnvFile() {
  console.log('🔐 Configuring Social Media Credentials...\n');
  
  // Decrypt credentials
  const decryptedCredentials = {};
  for (const [key, encodedValue] of Object.entries(CREDENTIALS)) {
    const decrypted = decryptCredential(encodedValue);
    if (decrypted) {
      decryptedCredentials[key] = decrypted;
      console.log(`✅ ${key}: Configured`);
    } else {
      console.log(`❌ ${key}: Failed to decrypt`);
    }
  }
  
  // Read existing .env.local or create new one
  const envPath = path.join(__dirname, '..', '.env.local');
  let envContent = '';
  
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
    console.log('\n📝 Updating existing .env.local file...');
  } else {
    console.log('\n📝 Creating new .env.local file...');
  }
  
  // Update or add credentials
  const lines = envContent.split('\n');
  const updatedLines = [];
  const processedKeys = new Set();
  
  // Process existing lines
  for (const line of lines) {
    if (line.trim() === '' || line.startsWith('#')) {
      updatedLines.push(line);
      continue;
    }
    
    const [key] = line.split('=');
    if (decryptedCredentials[key]) {
      updatedLines.push(`${key}=${decryptedCredentials[key]}`);
      processedKeys.add(key);
      console.log(`🔄 Updated: ${key}`);
    } else {
      updatedLines.push(line);
    }
  }
  
  // Add new credentials
  updatedLines.push('\n# Social Media Integration Credentials (Auto-configured)');
  for (const [key, value] of Object.entries(decryptedCredentials)) {
    if (!processedKeys.has(key)) {
      updatedLines.push(`${key}=${value}`);
      console.log(`➕ Added: ${key}`);
    }
  }
  
  // Add compatibility aliases
  if (decryptedCredentials.FACEBOOK_CLIENT_ID) {
    updatedLines.push(`FACEBOOK_APP_ID=${decryptedCredentials.FACEBOOK_CLIENT_ID}`);
    console.log(`➕ Added: FACEBOOK_APP_ID (compatibility alias)`);
  }
  if (decryptedCredentials.FACEBOOK_CLIENT_SECRET) {
    updatedLines.push(`FACEBOOK_APP_SECRET=${decryptedCredentials.FACEBOOK_CLIENT_SECRET}`);
    console.log(`➕ Added: FACEBOOK_APP_SECRET (compatibility alias)`);
  }
  
  // Write updated file
  fs.writeFileSync(envPath, updatedLines.join('\n'));
  
  console.log(`\n✅ Credentials configured successfully!`);
  console.log(`📁 File: ${envPath}`);
  
  return true;
}

function validateConfiguration() {
  console.log('\n🔍 Validating Configuration...\n');
  
  const requiredVars = [
    'X_CLIENT_ID',
    'X_CLIENT_SECRET', 
    'FACEBOOK_CLIENT_ID',
    'FACEBOOK_CLIENT_SECRET',
    'LINKEDIN_CLIENT_ID'
  ];
  
  const missing = [];
  const configured = [];
  
  for (const varName of requiredVars) {
    if (process.env[varName]) {
      configured.push(varName);
      console.log(`✅ ${varName}: Configured`);
    } else {
      missing.push(varName);
      console.log(`❌ ${varName}: Missing`);
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`   ✅ Configured: ${configured.length}`);
  console.log(`   ❌ Missing: ${missing.length}`);
  
  if (missing.length === 0) {
    console.log(`\n🎉 All social media credentials are properly configured!`);
    return true;
  } else {
    console.log(`\n⚠️ Some credentials are missing. Please check your environment variables.`);
    return false;
  }
}

function main() {
  console.log('🚀 Social Media Credentials Configuration\n');
  
  try {
    // Configure credentials
    const success = createSecureEnvFile();
    
    if (success) {
      console.log('\n🔄 Reloading environment variables...');
      
      // Reload environment
      require('dotenv').config({ path: path.join(__dirname, '..', '.env.local') });
      
      // Validate configuration
      validateConfiguration();
      
      console.log('\n✨ Configuration complete! You can now test social media integrations.');
      console.log('\n📝 Next steps:');
      console.log('   1. Restart your development server');
      console.log('   2. Test OAuth flows on /social page');
      console.log('   3. Verify account connections');
      
    } else {
      console.log('\n❌ Configuration failed. Please check the logs above.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n💥 Configuration Error:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { createSecureEnvFile, validateConfiguration }; 