<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eWasl Dashboard - Direct Access</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">eWasl</h1>
                    <span class="mr-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">مباشر</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">وصول مباشر بدون تسجيل دخول</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Success Message -->
        <div class="mb-8 bg-green-50 border border-green-200 rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="mr-3">
                    <h3 class="text-sm font-medium text-green-800">تم إزالة تسجيل الدخول بنجاح</h3>
                    <p class="text-sm text-green-700 mt-1">يمكنك الآن الوصول إلى جميع ميزات لوحة التحكم مباشرة</p>
                </div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Posts Card -->
            <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-medium text-gray-900">المنشورات</h3>
                        <p class="text-sm text-gray-600">إدارة المحتوى</p>
                    </div>
                </div>
                <div class="mt-4">
                    <button onclick="window.location.href='/posts'" class="w-full bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        عرض المنشورات
                    </button>
                </div>
            </div>

            <!-- Social Accounts Card -->
            <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-medium text-gray-900">الحسابات الاجتماعية</h3>
                        <p class="text-sm text-gray-600">ربط المنصات</p>
                    </div>
                </div>
                <div class="mt-4">
                    <button onclick="window.location.href='/social'" class="w-full bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                        إدارة الحسابات
                    </button>
                </div>
            </div>

            <!-- Schedule Card -->
            <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zM4 7h12v9H4V7z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-medium text-gray-900">الجدولة</h3>
                        <p class="text-sm text-gray-600">تنظيم المحتوى</p>
                    </div>
                </div>
                <div class="mt-4">
                    <button onclick="window.location.href='/schedule'" class="w-full bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 transition-colors">
                        عرض الجدولة
                    </button>
                </div>
            </div>

            <!-- Settings Card -->
            <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-gray-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-medium text-gray-900">الإعدادات</h3>
                        <p class="text-sm text-gray-600">تخصيص التطبيق</p>
                    </div>
                </div>
                <div class="mt-4">
                    <button onclick="window.location.href='/settings'" class="w-full bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        فتح الإعدادات
                    </button>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-4">إجراءات سريعة</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="window.location.href='/posts/new'" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                    إنشاء منشور جديد
                </button>
                <button onclick="window.location.href='/analytics'" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                    عرض التحليلات
                </button>
                <button onclick="window.location.href='/dashboard'" class="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors">
                    لوحة التحكم الرئيسية
                </button>
            </div>
        </div>

        <!-- Status Info -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-sm font-medium text-blue-800 mb-2">معلومات الحالة</h3>
            <ul class="text-sm text-blue-700 space-y-1">
                <li>✅ تم إزالة تسجيل الدخول بنجاح</li>
                <li>✅ جميع الميزات متاحة للوصول المباشر</li>
                <li>✅ لا حاجة لكلمة مرور أو اسم مستخدم</li>
                <li>✅ يمكن الوصول لجميع صفحات التطبيق</li>
            </ul>
        </div>
    </div>

    <script>
        // Auto-redirect to main dashboard after 3 seconds
        setTimeout(() => {
            if (confirm('هل تريد الانتقال إلى لوحة التحكم الرئيسية؟')) {
                window.location.href = '/dashboard';
            }
        }, 3000);
    </script>
</body>
</html>
