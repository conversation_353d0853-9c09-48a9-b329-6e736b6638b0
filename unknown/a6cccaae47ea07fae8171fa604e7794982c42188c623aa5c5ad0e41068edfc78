const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://ajpcbugydftdyhlbddpl.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqcGNidWd5ZGZ0ZHlobGJkZHBsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODAzMzgwNiwiZXhwIjoyMDYzNjA5ODA2fQ.oEzL8RGuwDyLOupfw2TQLbpViDujaFnTGfDl6sBMFkc';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function removeTestUser() {
  console.log('🗑️  Removing Test User to Prevent Email Bounces');
  console.log('===============================================');
  
  const testUserId = 'f99adad7-dab1-4c67-8170-598bab99c038';
  const testEmail = '<EMAIL>';
  
  try {
    // Remove from users table (this will cascade to other tables due to foreign key constraints)
    const { error: deleteError } = await supabase
      .from('users')
      .delete()
      .eq('id', testUserId);
    
    if (deleteError) {
      console.error(`❌ Failed to delete test user: ${deleteError.message}`);
      return;
    }
    
    console.log(`✅ Successfully removed test user: ${testEmail}`);
    console.log(`✅ User ID: ${testUserId}`);
    
    // Verify removal
    const { data: remainingUsers, error: fetchError } = await supabase
      .from('users')
      .select('id, email')
      .eq('id', testUserId);
    
    if (fetchError) {
      console.error(`❌ Error verifying removal: ${fetchError.message}`);
      return;
    }
    
    if (!remainingUsers || remainingUsers.length === 0) {
      console.log('✅ Verification: Test user successfully removed');
    } else {
      console.log('⚠️  Warning: Test user still exists in database');
    }
    
    // Check total users
    const { data: allUsers, error: countError } = await supabase
      .from('users')
      .select('id, email');
    
    if (!countError) {
      console.log(`📊 Remaining users in database: ${allUsers?.length || 0}`);
      if (allUsers && allUsers.length > 0) {
        console.log('📧 Remaining emails:');
        allUsers.forEach(user => {
          console.log(`   - ${user.email}`);
        });
      }
    }
    
    console.log('\n🎉 EMAIL BOUNCE PREVENTION COMPLETE!');
    console.log('====================================');
    console.log('✅ Test email removed from database');
    console.log('✅ Enhanced email validation implemented');
    console.log('✅ Real-time email suggestions added');
    console.log('✅ Disposable email blocking enabled');
    console.log('✅ Development email patterns blocked');
    
    console.log('\n🔧 NEXT: Configure Custom SMTP Provider');
    console.log('=======================================');
    console.log('1. Open Supabase Auth Settings:');
    console.log('   https://supabase.com/dashboard/project/ajpcbugydftdyhlbddpl/auth/providers');
    console.log('2. Configure SendGrid SMTP settings');
    console.log('3. Test with valid email addresses only');
    
  } catch (error) {
    console.error('❌ Error removing test user:', error.message);
  }
}

// Run the removal
removeTestUser().catch(console.error);
