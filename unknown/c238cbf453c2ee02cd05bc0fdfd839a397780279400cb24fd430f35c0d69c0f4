import { NextRequest, NextResponse } from 'next/server'
import { createFacebookOAuthService } from '@/lib/oauth/facebook'

export async function GET(request: NextRequest) {
  try {
    // Get access token from query params (for testing)
    const { searchParams } = new URL(request.url)
    const accessToken = searchParams.get('token')
    
    if (!accessToken) {
      return NextResponse.json({ error: 'Access token required' }, { status: 400 })
    }

    console.log('🔍 DEBUG: Testing Facebook API with token:', accessToken.substring(0, 20) + '...')

    const facebookService = createFacebookOAuthService()
    
    // Test getUserAccounts function
    console.log('🔍 DEBUG: Calling getUserAccounts...')
    const accounts = await facebookService.getUserAccounts(accessToken)
    
    console.log('🔍 DEBUG: Raw accounts response:', JSON.stringify(accounts, null, 2))

    return NextResponse.json({
      success: true,
      accountsFound: accounts.length,
      accounts: accounts.map(acc => ({
        id: acc.id,
        accountName: acc.accountName,
        accountHandle: acc.accountHandle,
        platform: acc.platform,
        connectionStatus: acc.connectionStatus,
        hasAccessToken: !!acc.accessToken,
        accessTokenPreview: acc.accessToken ? acc.accessToken.substring(0, 20) + '...' : null,
        metadata: acc.metadata,
        permissions: acc.permissions
      }))
    })
  } catch (error) {
    console.error('🚨 DEBUG ERROR:', error)
    return NextResponse.json({
      error: 'Debug test failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
