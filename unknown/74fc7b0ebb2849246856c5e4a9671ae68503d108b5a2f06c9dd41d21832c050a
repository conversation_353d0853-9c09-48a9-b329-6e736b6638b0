import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const { user } = await getAuthenticatedUser(request);

    // Get all social accounts for this user
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id);

    if (accountsError) {
      return NextResponse.json(
        { error: 'Failed to fetch accounts', details: accountsError.message },
        { status: 500 }
      );
    }

    if (!accounts || accounts.length === 0) {
      return NextResponse.json(
        { message: 'No social accounts found for this user' },
        { status: 404 }
      );
    }

    const publishingResults = [];

    for (const account of accounts) {
      const result: any = {
        account_id: account.account_id,
        platform: account.platform,
        account_name: account.account_name,
        page_id: account.page_id,
        page_name: account.page_name,
        has_access_token: !!account.access_token,
        has_page_token: !!account.page_access_token,
        access_token_preview: account.access_token ? account.access_token.substring(0, 20) + '...' : null,
        page_token_preview: account.page_access_token ? account.page_access_token.substring(0, 20) + '...' : null
      };

      // Test Facebook publishing capability
      if (account.platform === 'facebook' && account.page_access_token && account.page_id) {
        try {
          // Test posting to Facebook page
          const testPostUrl = `https://graph.facebook.com/${account.page_id}/feed`;
          const testPostResponse = await fetch(testPostUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              message: '🧪 Test post from eWasl - Publishing system test (will be deleted immediately)',
              access_token: account.page_access_token,
              published: false // Create as unpublished draft
            })
          });

          if (testPostResponse.ok) {
            const testPostData = await testPostResponse.json();
            result.publishing_test = {
              success: true,
              post_id: testPostData.id,
              message: 'Successfully created test post (unpublished)'
            };

            // Delete the test post immediately
            if (testPostData.id) {
              const deleteResponse = await fetch(`https://graph.facebook.com/${testPostData.id}?access_token=${account.page_access_token}`, {
                method: 'DELETE'
              });

              if (deleteResponse.ok) {
                result.publishing_test.cleanup = 'Test post deleted successfully';
              } else {
                result.publishing_test.cleanup = 'Failed to delete test post';
              }
            }
          } else {
            const errorData = await testPostResponse.json();
            result.publishing_test = {
              success: false,
              error: errorData,
              message: 'Failed to create test post'
            };
          }

        } catch (error) {
          result.publishing_test = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            message: 'Exception during publishing test'
          };
        }
      }

      // Test Instagram publishing capability
      if (account.platform === 'instagram' && account.access_token) {
        try {
          // Test Instagram media creation (without publishing)
          const testMediaUrl = `https://graph.facebook.com/${account.account_id}/media`;
          const testMediaResponse = await fetch(testMediaUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              image_url: 'https://picsum.photos/800/600',
              caption: '🧪 Test media from eWasl - Publishing system test (not published)',
              access_token: account.access_token
            })
          });

          if (testMediaResponse.ok) {
            const testMediaData = await testMediaResponse.json();
            result.publishing_test = {
              success: true,
              media_id: testMediaData.id,
              message: 'Successfully created test media (not published)'
            };
          } else {
            const errorData = await testMediaResponse.json();
            result.publishing_test = {
              success: false,
              error: errorData,
              message: 'Failed to create test media'
            };
          }

        } catch (error) {
          result.publishing_test = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            message: 'Exception during Instagram publishing test'
          };
        }
      }

      publishingResults.push(result);
    }

    return NextResponse.json({
      user_id: user.id,
      total_accounts: accounts.length,
      accounts: publishingResults,
      debug_timestamp: new Date().toISOString(),
      summary: {
        facebook_accounts: publishingResults.filter(a => a.platform === 'facebook').length,
        instagram_accounts: publishingResults.filter(a => a.platform === 'instagram').length,
        accounts_with_page_tokens: publishingResults.filter(a => a.has_page_token).length,
        successful_tests: publishingResults.filter(a => a.publishing_test?.success).length
      }
    });

  } catch (error) {
    console.error('Publishing debug error:', error);
    return NextResponse.json(
      {
        error: 'Failed to debug publishing system',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
