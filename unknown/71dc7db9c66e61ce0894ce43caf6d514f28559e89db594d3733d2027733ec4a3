const https = require('https');

console.log('🚀 Verifying Social Page Authentication Fix Deployment...\n');

const BASE_URL = 'https://app.ewasl.com';

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, {
      method: 'GET',
      timeout: 10000,
      headers: {
        'User-Agent': 'eWasl-Verification-Agent/1.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function waitForDeployment() {
  console.log('⏳ Waiting for GitHub deployment to complete...');
  console.log('💡 This usually takes 2-3 minutes after push to GitHub\n');
  
  // Wait 2 minutes for deployment
  await new Promise(resolve => setTimeout(resolve, 120000));
  
  console.log('✅ Deployment wait completed. Starting verification...\n');
}

async function verifyFix() {
  console.log('🔍 VERIFYING SOCIAL PAGE AUTHENTICATION FIX');
  console.log('=' * 60);
  
  try {
    // Test the social page
    console.log('\n📋 Testing Social Page...');
    const socialResponse = await makeRequest(`${BASE_URL}/social`);
    
    console.log(`📊 Status: ${socialResponse.statusCode}`);
    
    if (socialResponse.statusCode === 307 || socialResponse.statusCode === 302) {
      const location = socialResponse.headers.location;
      console.log(`🔄 Redirect: ${location}`);
      
      if (location && location.includes('/auth/signin')) {
        console.log('✅ SUCCESS: Social page correctly redirects to sign-in');
      } else {
        console.log('❌ ISSUE: Unexpected redirect location');
      }
    } else if (socialResponse.statusCode === 200) {
      // Check if this is showing the actual authenticated social page
      if (socialResponse.body.includes('Authentication Error')) {
        console.log('❌ CRITICAL: Still showing Authentication Error!');
        console.log('🔧 The fix may not have deployed yet or there\'s another issue');
      } else if (socialResponse.body.includes('Social Media Accounts')) {
        console.log('✅ SUCCESS: User is authenticated and seeing social page');
      } else {
        console.log('⚠️  UNCLEAR: Getting 200 but content is unclear');
      }
    } else {
      console.log(`❌ UNEXPECTED: Got status ${socialResponse.statusCode}`);
    }
    
    // Test the API endpoint
    console.log('\n📋 Testing Social Accounts API...');
    const apiResponse = await makeRequest(`${BASE_URL}/api/social/accounts`);
    
    console.log(`📊 Status: ${apiResponse.statusCode}`);
    
    if (apiResponse.statusCode === 401) {
      console.log('✅ SUCCESS: API correctly returns 401 for unauthenticated users');
    } else {
      console.log(`❌ UNEXPECTED: API returned ${apiResponse.statusCode}`);
    }
    
    console.log('\n' + '=' * 60);
    console.log('🎯 FINAL VERIFICATION RESULTS');
    console.log('=' * 60);
    
    if ((socialResponse.statusCode === 307 || socialResponse.statusCode === 302) && apiResponse.statusCode === 401) {
      console.log('✅ 🎉 COMPLETE SUCCESS! 🎉');
      console.log('🔧 The authentication fix has been successfully deployed!');
      console.log('📱 Users will now get proper authentication flow instead of errors');
      console.log('🔒 API endpoints are properly secured');
    } else {
      console.log('⚠️  Partial success or still deploying...');
      console.log('💡 If you still see issues, wait a few more minutes for deployment');
    }
    
  } catch (error) {
    console.log(`💥 Verification Error: ${error.message}`);
    console.log('💡 This might mean the deployment is still in progress');
  }
}

async function runVerification() {
  await waitForDeployment();
  await verifyFix();
}

// Run verification
runVerification().catch(console.error); 