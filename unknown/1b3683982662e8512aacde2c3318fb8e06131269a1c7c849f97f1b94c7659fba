import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

/**
 * EMERGENCY Database Schema Fix Endpoint
 * Direct SQL execution to add missing columns to social_accounts table
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🚨 [EMERGENCY SCHEMA FIX] Starting critical database schema fix...');

    // Create Supabase client with service role key for admin access
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !serviceRoleKey) {
      throw new Error('Missing Supabase configuration');
    }

    const supabase = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    const results = {
      timestamp: new Date().toISOString(),
      operations: [] as any[],
      success: true,
      errors: [] as string[]
    };

    // Step 1: Add connection_status column
    try {
      console.log('🔧 Adding connection_status column...');
      const { error: connectionStatusError } = await supabase.rpc('exec_sql', {
        sql: `
          DO $$ 
          BEGIN 
            IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns 
              WHERE table_name = 'social_accounts' 
              AND column_name = 'connection_status'
            ) THEN
              ALTER TABLE social_accounts 
              ADD COLUMN connection_status TEXT DEFAULT 'connected' 
              CHECK (connection_status IN ('connected', 'expired', 'error', 'reconnecting', 'disconnected'));
            END IF;
          END $$;
        `
      });

      if (connectionStatusError) {
        results.errors.push(`Connection status column: ${connectionStatusError.message}`);
      } else {
        results.operations.push({
          operation: 'add_connection_status_column',
          status: 'success',
          message: 'Connection status column added/verified successfully'
        });
      }
    } catch (error) {
      results.errors.push(`Connection status column: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Step 2: Add status column
    try {
      console.log('🔧 Adding status column...');
      const { error: statusError } = await supabase.rpc('exec_sql', {
        sql: `
          DO $$ 
          BEGIN 
            IF NOT EXISTS (
              SELECT 1 FROM information_schema.columns 
              WHERE table_name = 'social_accounts' 
              AND column_name = 'status'
            ) THEN
              ALTER TABLE social_accounts 
              ADD COLUMN status TEXT DEFAULT 'ACTIVE' 
              CHECK (status IN ('ACTIVE', 'INACTIVE', 'EXPIRED', 'ERROR'));
            END IF;
          END $$;
        `
      });

      if (statusError) {
        results.errors.push(`Status column: ${statusError.message}`);
      } else {
        results.operations.push({
          operation: 'add_status_column',
          status: 'success',
          message: 'Status column added/verified successfully'
        });
      }
    } catch (error) {
      results.errors.push(`Status column: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Step 3: Add Facebook page-specific columns
    const pageColumns = [
      { name: 'page_id', type: 'TEXT' },
      { name: 'page_access_token', type: 'TEXT' },
      { name: 'page_name', type: 'TEXT' },
      { name: 'instagram_business_account_id', type: 'TEXT' }
    ];

    for (const column of pageColumns) {
      try {
        console.log(`🔧 Adding ${column.name} column...`);
        const { error } = await supabase.rpc('exec_sql', {
          sql: `
            DO $$ 
            BEGIN 
              IF NOT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'social_accounts' 
                AND column_name = '${column.name}'
              ) THEN
                ALTER TABLE social_accounts ADD COLUMN ${column.name} ${column.type};
              END IF;
            END $$;
          `
        });

        if (error) {
          results.errors.push(`${column.name} column: ${error.message}`);
        } else {
          results.operations.push({
            operation: `add_${column.name}_column`,
            status: 'success',
            message: `${column.name} column added/verified successfully`
          });
        }
      } catch (error) {
        results.errors.push(`${column.name} column: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Step 4: Update existing records with default values
    try {
      console.log('🔧 Updating existing records...');
      const { error: updateError } = await supabase
        .from('social_accounts')
        .update({
          connection_status: 'connected',
          status: 'ACTIVE'
        })
        .is('connection_status', null);

      if (updateError) {
        results.errors.push(`Update records: ${updateError.message}`);
      } else {
        results.operations.push({
          operation: 'update_existing_records',
          status: 'success',
          message: 'Updated existing records with default values'
        });
      }
    } catch (error) {
      results.errors.push(`Update records: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Step 5: Verify schema
    try {
      console.log('🔍 Verifying final schema...');
      const { data: schemaData, error: schemaError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable, column_default')
        .eq('table_name', 'social_accounts')
        .eq('table_schema', 'public');

      if (schemaError) {
        results.errors.push(`Schema verification: ${schemaError.message}`);
      } else {
        results.operations.push({
          operation: 'schema_verification',
          status: 'success',
          message: 'Schema verified successfully',
          columns: schemaData?.map(col => ({
            name: col.column_name,
            type: col.data_type,
            nullable: col.is_nullable,
            default: col.column_default
          })) || []
        });
      }
    } catch (error) {
      results.errors.push(`Schema verification: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    if (results.errors.length > 0) {
      results.success = false;
    }

    console.log('✅ [EMERGENCY SCHEMA FIX] Database schema fix completed');
    
    return NextResponse.json({
      success: results.success,
      message: results.success ? 'تم إصلاح مخطط قاعدة البيانات بنجاح' : 'فشل في إصلاح مخطط قاعدة البيانات',
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [EMERGENCY SCHEMA FIX] Critical failure:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'فشل حرج في إصلاح مخطط قاعدة البيانات',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'استخدم POST لتنفيذ إصلاح مخطط قاعدة البيانات الطارئ',
    usage: 'POST /api/emergency-schema-fix',
    timestamp: new Date().toISOString()
  });
}
