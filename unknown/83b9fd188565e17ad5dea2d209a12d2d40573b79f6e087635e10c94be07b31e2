#!/bin/bash

# Emergency build script for DigitalOcean deployment
echo "🚨 Emergency Build Script for eWasl"
echo "==================================="

# Set error handling
set -e

# Environment setup
export NODE_ENV=production
export NEXT_TELEMETRY_DISABLED=1

echo "📋 Environment Information:"
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"
echo "Working directory: $(pwd)"
echo "Available memory: $(free -h 2>/dev/null || echo 'Memory info not available')"

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf .next
rm -rf node_modules/.cache

# Install dependencies with specific flags
echo "📦 Installing dependencies..."
npm ci --production=false --prefer-offline --no-audit --no-fund

# Verify critical dependencies
echo "🔍 Verifying critical dependencies..."
npm list next react typescript --depth=0

# Build with memory optimization
echo "🏗️  Building application..."
export NODE_OPTIONS="--max-old-space-size=2048"
npm run build

# Verify build output
echo "✅ Build verification..."
if [ -d ".next" ]; then
    echo "✅ .next directory created"
    if [ -f ".next/BUILD_ID" ]; then
        echo "✅ Build ID: $(cat .next/BUILD_ID)"
    fi
    if [ -d ".next/static" ]; then
        echo "✅ Static assets generated"
    fi
    if [ -f ".next/standalone/server.js" ]; then
        echo "✅ Standalone server generated"
    fi
else
    echo "❌ .next directory not found"
    exit 1
fi

echo "🎉 Emergency build completed successfully!"
