# eWasl Scripts

This directory contains utility scripts for the eWasl application.

## Available Scripts

### Monitoring Scripts

- `initialize-monitoring.js`: Initializes the monitoring system
  ```bash
  npm run init:monitoring
  ```

- `check-monitoring-status.ts`: Checks the status of the monitoring system
  ```bash
  npm run check:monitoring
  ```

### Other Utility Scripts

- `test-enhanced-integrations.ts`: Tests enhanced integrations
  ```bash
  npm run test:enhanced-integration
  ```

## Usage

These scripts can be run using npm:

```bash
npm run <script-name>
```

For example:

```bash
npm run init:monitoring
```

## Adding New Scripts

When adding new scripts to this directory:

1. Make the script executable (for Unix-like systems):
   ```bash
   chmod +x your-script-name.js
   ```

2. Add an entry to the `scripts` section in `package.json`:
   ```json
   "scripts": {
     "your-script": "node scripts/your-script-name.js"
   }
   ```

3. Document the script in this README file

## Best Practices

- Include a descriptive comment header in each script
- Add proper error handling
- Use environment variables for configuration
- Add logging for important events and errors 