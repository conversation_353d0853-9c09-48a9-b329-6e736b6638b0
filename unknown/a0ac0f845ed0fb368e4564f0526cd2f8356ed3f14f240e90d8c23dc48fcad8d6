import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debugging database schema and data...');
    
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('✅ User authenticated:', user.id);

    // Check social_accounts table structure
    console.log('🔍 Checking social_accounts table structure...');
    const { data: tableInfo, error: tableError } = await supabase
      .rpc('get_table_columns', { table_name: 'social_accounts' })
      .single();

    if (tableError) {
      console.log('⚠️ Could not get table structure, checking data directly...');
    }

    // Get user's social accounts with all fields
    const { data: accounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id);

    if (accountsError) {
      console.error('❌ Failed to fetch social accounts:', accountsError);
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch social accounts',
        details: accountsError
      }, { status: 500 });
    }

    console.log(`Found ${accounts?.length || 0} social accounts`);

    // Analyze each account
    const accountAnalysis = accounts?.map(account => {
      const fields = Object.keys(account);
      const requiredFields = [
        'id', 'user_id', 'platform', 'account_id', 'account_name', 
        'access_token', 'page_id', 'page_access_token', 'page_name',
        'instagram_business_account_id', 'connection_status'
      ];
      
      const missingFields = requiredFields.filter(field => !(field in account));
      const presentFields = requiredFields.filter(field => field in account);
      
      return {
        id: account.id,
        platform: account.platform,
        account_name: account.account_name,
        fields_present: presentFields,
        fields_missing: missingFields,
        has_access_token: !!account.access_token,
        has_page_id: !!account.page_id,
        has_page_token: !!account.page_access_token,
        connection_status: account.connection_status,
        all_fields: fields
      };
    }) || [];

    // Check posts table
    console.log('🔍 Checking posts table...');
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .select('id, title, content, status, created_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(5);

    // Check post_social_accounts table
    console.log('🔍 Checking post_social_accounts table...');
    const { data: postSocialAccounts, error: psaError } = await supabase
      .from('post_social_accounts')
      .select('*')
      .limit(10);

    // Check scheduled_posts_queue table
    console.log('🔍 Checking scheduled_posts_queue table...');
    const { data: scheduledPosts, error: spqError } = await supabase
      .from('scheduled_posts_queue')
      .select('*')
      .limit(10);

    // Environment variables check
    const envCheck = {
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasSupabaseAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      hasServiceRoleKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV
    };

    return NextResponse.json({
      success: true,
      message: 'Database debugging complete',
      user_id: user.id,
      environment: envCheck,
      social_accounts: {
        count: accounts?.length || 0,
        analysis: accountAnalysis,
        raw_data: accounts
      },
      posts: {
        count: posts?.length || 0,
        recent: posts,
        error: postsError
      },
      post_social_accounts: {
        count: postSocialAccounts?.length || 0,
        sample: postSocialAccounts,
        error: psaError
      },
      scheduled_posts_queue: {
        count: scheduledPosts?.length || 0,
        sample: scheduledPosts,
        error: spqError
      },
      table_structure: tableInfo || 'Could not retrieve'
    });

  } catch (error: any) {
    console.error('❌ Database debugging error:', error);
    return NextResponse.json({
      success: false,
      error: 'Database debugging failed',
      details: {
        message: error.message,
        stack: error.stack
      }
    }, { status: 500 });
  }
}

// POST endpoint to apply database schema fixes
export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Applying database schema fixes...');
    
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Apply the schema fixes from the SQL file
    const schemaFixes = [
      `ALTER TABLE social_accounts ADD COLUMN IF NOT EXISTS page_id TEXT`,
      `ALTER TABLE social_accounts ADD COLUMN IF NOT EXISTS page_access_token TEXT`,
      `ALTER TABLE social_accounts ADD COLUMN IF NOT EXISTS page_name TEXT`,
      `ALTER TABLE social_accounts ADD COLUMN IF NOT EXISTS instagram_business_account_id TEXT`,
      `ALTER TABLE social_accounts ADD COLUMN IF NOT EXISTS token_expires_at TIMESTAMPTZ`,
      `ALTER TABLE social_accounts ADD COLUMN IF NOT EXISTS last_validated_at TIMESTAMPTZ`,
      `ALTER TABLE social_accounts ADD COLUMN IF NOT EXISTS connection_status TEXT DEFAULT 'connected'`
    ];

    const results = [];
    for (const sql of schemaFixes) {
      try {
        console.log('Executing:', sql);
        const { data, error } = await supabase.rpc('execute_sql', { sql_query: sql });
        results.push({
          sql,
          success: !error,
          error: error?.message,
          data
        });
      } catch (sqlError: any) {
        results.push({
          sql,
          success: false,
          error: sqlError.message
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Schema fixes applied',
      results
    });

  } catch (error: any) {
    console.error('❌ Schema fix error:', error);
    return NextResponse.json({
      success: false,
      error: 'Schema fix failed',
      details: error.message
    }, { status: 500 });
  }
}
