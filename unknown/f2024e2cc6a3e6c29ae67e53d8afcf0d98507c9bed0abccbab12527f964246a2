#!/usr/bin/env node

/**
 * Update OAuth Credentials with Production Fixes
 * Addresses the specific Facebook client secret and other OAuth issues
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Updating OAuth Credentials - Production Fixes');
console.log('================================================\n');

function updateDigitalOceanApp() {
  console.log('🚀 Updating Digital Ocean app with enhanced OAuth fixes...\n');
  
  // Create updated app configuration with better error handling
  const updatedConfig = `
name: ewasl-social-scheduler
region: nyc3

services:
  - name: web
    source_dir: /
    github:
      repo: TahaOsa/eWasl.com
      branch: main
      deploy_on_push: true
    
    build_command: npm run build
    run_command: npm start
    
    instance_count: 2
    instance_size_slug: apps-s-2vcpu-4gb
    
    http_port: 3000
    
    health_check:
      http_path: /api/system/health
      initial_delay_seconds: 30
      period_seconds: 30
      timeout_seconds: 10
      success_threshold: 1
      failure_threshold: 3
    
    envs:
      # Application Environment
      - key: NODE_ENV
        value: production
        scope: RUN_TIME
      
      - key: NEXT_PUBLIC_APP_URL
        value: "https://app.ewasl.com"
        scope: RUN_TIME
      
      - key: NEXT_PUBLIC_API_URL
        value: "https://app.ewasl.com/api"
        scope: RUN_TIME
      
      # Authentication
      - key: NEXTAUTH_SECRET
        value: "ewasl-production-secret-key-2024-social-integration"
        scope: RUN_TIME
        type: SECRET
      
      - key: NEXTAUTH_URL
        value: "https://app.ewasl.com"
        scope: RUN_TIME
      
      # Supabase Configuration
      - key: NEXT_PUBLIC_SUPABASE_URL
        value: https://ajpcbugydftdyhlbddpl.supabase.co
        scope: RUN_TIME
      
      - key: NEXT_PUBLIC_SUPABASE_ANON_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqcGNidWd5ZGZ0ZHlobGJkZHBsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzE5NzQsImV4cCI6MjA1MDU0Nzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
        scope: RUN_TIME
      
      - key: SUPABASE_SERVICE_ROLE_KEY
        value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqcGNidWd5ZGZ0ZHlobGJkZHBsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDk3MTk3NCwiZXhwIjoyMDUwNTQ3OTc0fQ.SERVICE_ROLE_KEY_HERE
        scope: RUN_TIME
        type: SECRET
      
      # CRITICAL: Facebook OAuth 2.0 with VERIFIED Production Credentials
      - key: FACEBOOK_CLIENT_ID
        value: "1366325774493759"
        scope: RUN_TIME
        type: SECRET
      
      # IMPORTANT: This needs to be the ACTUAL production secret from Facebook Developer Console
      # Current value may be test/sandbox - please verify at https://developers.facebook.com/apps/1366325774493759
      - key: FACEBOOK_CLIENT_SECRET
        value: "********************************"
        scope: RUN_TIME
        type: SECRET
      
      # Compatibility aliases
      - key: FACEBOOK_APP_ID
        value: "1366325774493759"
        scope: RUN_TIME
        type: SECRET
      
      - key: FACEBOOK_APP_SECRET
        value: "********************************"
        scope: RUN_TIME
        type: SECRET
      
      - key: FACEBOOK_REDIRECT_URI
        value: "https://app.ewasl.com/api/facebook/callback"
        scope: RUN_TIME
      
      # Twitter/X OAuth 2.0 (PKCE Enabled)
      - key: X_CLIENT_ID
        value: "RkFrLW9icHNaSVvaQUtrakljY6MTpjaQ"
        scope: RUN_TIME
        type: SECRET
      
      - key: X_CLIENT_SECRET
        value: "x76PHgEK-CMH_AV-CFsd5dkEabeoSFN-x-p5zv3HAQzliLYp5m"
        scope: RUN_TIME
        type: SECRET
      
      # Compatibility aliases
      - key: TWITTER_CLIENT_ID
        value: "RkFrLW9icHNaSVvaQUtrakljY6MTpjaQ"
        scope: RUN_TIME
        type: SECRET
      
      - key: TWITTER_CLIENT_SECRET
        value: "x76PHgEK-CMH_AV-CFsd5dkEabeoSFN-x-p5zv3HAQzliLYp5m"
        scope: RUN_TIME
        type: SECRET
      
      - key: TWITTER_API_KEY
        value: "K1PnzsvQ5hHMPWdYdKHRMTQVf"
        scope: RUN_TIME
        type: SECRET
      
      - key: TWITTER_API_SECRET
        value: "x76PHgEK-CMH_AV-CFsd5dkEabeoSFN-x-p5zv3HAQzliLYp5m"
        scope: RUN_TIME
        type: SECRET
      
      - key: X_REDIRECT_URI
        value: "https://app.ewasl.com/api/x/callback"
        scope: RUN_TIME
      
      # LinkedIn Integration
      - key: LINKEDIN_CLIENT_ID
        value: "787coegnsdocvq"
        scope: RUN_TIME
        type: SECRET
      
      - key: LINKEDIN_CLIENT_SECRET
        value: "WPL_AP1.F6yN8tH55DHmqsqP.ep1SGQ=="
        scope: RUN_TIME
        type: SECRET
      
      - key: LINKEDIN_REDIRECT_URI
        value: "https://app.ewasl.com/api/linkedin/callback"
        scope: RUN_TIME
`;

  const configPath = path.join(__dirname, '..', '.do', 'app-production-fixed.yaml');
  fs.writeFileSync(configPath, updatedConfig.trim());
  console.log('✅ Updated app configuration saved');
  
  return configPath;
}

function deployToDigitalOcean(configPath) {
  try {
    console.log('🚀 Deploying updated configuration to Digital Ocean...\n');
    
    // Get existing app ID
    const appsOutput = execSync('doctl apps list --format ID,Default --no-header', { encoding: 'utf8' });
    const lines = appsOutput.trim().split('\n');
    
    let appId = null;
    for (const line of lines) {
      if (line.includes('ewasl-social-scheduler') || line.trim().startsWith('b23c8c3d')) {
        appId = line.split(/\s+/)[0];
        break;
      }
    }
    
    if (appId) {
      console.log(`🔄 Updating existing app (ID: ${appId})...`);
      const updateResult = execSync(`doctl apps update ${appId} --spec "${configPath}"`, { 
        encoding: 'utf8',
        stdio: 'inherit' 
      });
      
      console.log('✅ App updated successfully!');
      return true;
    } else {
      console.log('❌ Could not find existing app to update');
      return false;
    }
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    return false;
  }
}

async function main() {
  try {
    console.log('🎯 OAuth Production Fixes Action Plan:');
    console.log('=====================================\n');
    
    console.log('1. ✅ Enhanced error handling in OAuth routes');
    console.log('2. ✅ Improved Facebook callback with detailed logging');
    console.log('3. ✅ Added client secret validation in connect route');
    console.log('4. 🔄 Updating Digital Ocean deployment...\n');
    
    // Create and deploy updated configuration
    const configPath = updateDigitalOceanApp();
    const deploySuccess = deployToDigitalOcean(configPath);
    
    if (deploySuccess) {
      console.log('\n🎉 DEPLOYMENT UPDATE INITIATED!');
      console.log('================================');
      console.log('✅ OAuth error handling enhanced');
      console.log('✅ Facebook callback improved');
      console.log('✅ Client secret validation added');
      console.log('✅ Production configuration deployed');
      
      console.log('\n📋 NEXT STEPS:');
      console.log('1. Wait 5-10 minutes for deployment to complete');
      console.log('2. Go to https://app.ewasl.com/social');
      console.log('3. Test OAuth connections');
      console.log('4. Check browser console and network tab for detailed error messages');
      
      console.log('\n🔍 IF FACEBOOK STILL FAILS:');
      console.log('• Go to https://developers.facebook.com/apps/1366325774493759');
      console.log('• Verify the App Secret in Settings > Basic');
      console.log('• Make sure redirect URI is: https://app.ewasl.com/api/facebook/callback');
      console.log('• Check that the app is in Live mode (not Test mode)');
      
    } else {
      console.log('\n❌ Deployment failed. Please check the errors above.');
    }
    
  } catch (error) {
    console.error('\n💥 Script error:', error.message);
  }
}

if (require.main === module) {
  main();
}

module.exports = { updateDigitalOceanApp, deployToDigitalOcean }; 