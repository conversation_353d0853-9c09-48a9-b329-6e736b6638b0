import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  // Check if admin user already exists
  const adminExists = await prisma.user.findFirst({
    where: {
      email: '<EMAIL>',
    },
  });

  if (!adminExists) {
    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 12);

    await prisma.user.create({
      data: {
        name: 'Admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
      },
    });

    console.log('Admin user created successfully');
  } else {
    console.log('Admin user already exists');
  }

  // Create demo user if it doesn't exist
  const demoExists = await prisma.user.findFirst({
    where: {
      email: '<EMAIL>',
    },
  });

  if (!demoExists) {
    // Create demo user
    const hashedPassword = await bcrypt.hash('demo123', 12);

    await prisma.user.create({
      data: {
        name: 'Demo User',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'USER',
      },
    });

    console.log('Demo user created successfully');
  } else {
    console.log('Demo user already exists');
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
