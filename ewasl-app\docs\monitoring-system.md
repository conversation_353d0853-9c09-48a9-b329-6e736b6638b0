# eWasl Monitoring System Documentation

## Overview

The eWasl monitoring system provides comprehensive monitoring and logging capabilities for the application, focusing on social media integrations, environment variables, system resources, and database connections. This documentation outlines the components, usage, and configuration of the monitoring system.

## Architecture

```mermaid
flowchart TD
    subgraph "Monitoring System"
        A["Initialize Monitoring"] --> B["Social Integration Monitor"]
        A --> C["Environment Monitor"]
        
        B --> D["Twitter/X Monitor"]
        B --> E["Facebook Monitor"]
        B --> F["Instagram Monitor"]
        B --> G["LinkedIn Monitor"]
        
        C --> H["Env Variables Check"]
        C --> I["System Resources Monitor"]
        C --> J["Database Connection Monitor"]
    end
    
    subgraph "API Layer"
        K["API Startup"] --> A
        L["API Social Status"] --> B
        M["API Environment Status"] --> C
    end
    
    subgraph "UI Components"
        N["SocialIntegrationStatus"] --> L
        O["SocialIntegrationsPanel"] --> N
        P["Dashboard Page"] --> O
        P --> Q["Environment Status Panel"]
        Q --> M
    end
    
    subgraph "Initialization"
        R["Application Startup"] --> <PERSON>
        S["Manual Script"] --> <PERSON>
    end
```

## Components

### 1. Social Integration Monitor

Located in `src/lib/monitoring/social-integration-monitor.ts`, this component monitors the status of social media integrations:

- **Tracked Platforms**: Twitter/X, Facebook, Instagram, LinkedIn
- **Monitored Aspects**: Connection status, token expiration, API rate limits
- **Implementation**: Singleton pattern with periodic health checks
- **Error Handling**: Comprehensive error catching and logging

### 2. Environment Monitor

Located in `src/lib/monitoring/environment-monitor.ts`, this component monitors:

- **Environment Variables**: Checks for required environment variables
- **System Resources**: Monitors CPU and memory usage
- **Database Connections**: Tests database and Supabase connections

### 3. Monitoring Initialization System

Located in `src/lib/monitoring/initialize-monitoring.ts`, this component:

- Initializes all monitoring services
- Configures logging levels
- Sets up periodic checks

### 4. UI Components

- **SocialIntegrationStatus**: Displays integration status with visual indicators
- **SocialIntegrationsPanel**: Panel showing all social integrations
- **Dashboard Page**: Monitoring dashboard with tabs for personal and business accounts

### 5. API Endpoints

- **`/api/social-integration-status`**: Fetches integration status
- **`/api/startup`**: Initializes monitoring systems on application startup

## Usage

### Starting the Monitoring System

The monitoring system can be initialized in two ways:

1. **Automatically on Application Startup**:
   The monitoring system is automatically initialized when the application starts.

2. **Manually via Script**:
   ```bash
   npm run init:monitoring
   ```

### Viewing Monitoring Data

1. **Dashboard**:
   Navigate to `/dashboard/monitoring/social` to view the social integration monitoring dashboard.

2. **API**:
   - GET `/api/social-integration-status` - Returns current status of social integrations
   - GET `/api/environment-status` - Returns environment and system status

## Configuration

### Environment Variables

The monitoring system requires the following environment variables:

```
STARTUP_SECRET_KEY=your_secret_key
NEXT_PUBLIC_APP_URL=your_app_url
```

### Customizing Monitoring Intervals

Monitoring intervals can be configured in the respective monitor files:

- Social Integration Monitor: `HEALTH_CHECK_INTERVAL_MS` (default: 300000ms / 5 minutes)
- Environment Monitor: `CHECK_INTERVAL_MS` (default: 600000ms / 10 minutes)

## Alerts and Notifications

The monitoring system can be configured to send alerts through:

1. **UI Notifications**: Real-time notifications in the application UI
2. **Email Alerts**: Configurable email notifications for critical issues
3. **Logging**: All monitoring events are logged for later analysis

## Troubleshooting

### Common Issues

1. **Social Integration Connection Failures**:
   - Check token validity and expiration
   - Verify API rate limits
   - Ensure correct API keys and secrets

2. **Environment Monitoring Errors**:
   - Verify all required environment variables are set
   - Check database connection strings
   - Ensure sufficient system resources

### Logs

Monitoring logs can be found in:

- Development: Console logs
- Production: Application logs and configured log destinations

## Best Practices

1. **Regular Token Rotation**: Rotate social media API tokens regularly
2. **Monitoring Dashboard Review**: Review the monitoring dashboard daily
3. **Alert Configuration**: Configure alerts for critical issues
4. **Log Analysis**: Regularly analyze logs for patterns and issues

## Future Enhancements

1. **Additional Integrations**: Support for more social media platforms
2. **Advanced Analytics**: Enhanced analytics for monitoring data
3. **Predictive Monitoring**: Implement predictive monitoring for potential issues
4. **Custom Alert Channels**: Support for custom alert channels (Slack, SMS, etc.) 