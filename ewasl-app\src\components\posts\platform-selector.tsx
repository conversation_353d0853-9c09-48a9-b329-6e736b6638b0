'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import { 
  Facebook, 
  Instagram, 
  Twitter, 
  Linkedin, 
  Users,
  AlertCircle,
  CheckCircle2,
  Clock,
  RefreshCw
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface SocialAccount {
  id: string
  account_id: string
  platform: 'FACEBOOK' | 'INSTAGRAM' | 'TWITTER' | 'LINKEDIN'
  account_name: string
  account_handle?: string
  connection_status: 'connected' | 'expired' | 'error' | 'reconnecting' | 'disconnected'
  profile_image_url?: string
  metadata?: {
    metrics?: {
      followerCount?: number
      engagementRate?: number
      postCount?: number
    }
    pageId?: string
    instagramAccountId?: string
  }
  last_validated_at?: string
}

interface PlatformSelectorProps {
  selectedAccounts: string[]
  onSelectionChange: (accountIds: string[]) => void
  onCharacterLimitChange?: (limits: Record<string, number>) => void
  className?: string
}

// Platform character limits
const PLATFORM_LIMITS = {
  FACEBOOK: 63206,
  INSTAGRAM: 2200,
  TWITTER: 280,
  LINKEDIN: 3000
}

// Platform icons
const PlatformIcon = ({ platform, className }: { platform: string; className?: string }) => {
  switch (platform) {
    case 'FACEBOOK':
      return <Facebook className={cn('h-4 w-4', className)} />
    case 'INSTAGRAM':
      return <Instagram className={cn('h-4 w-4', className)} />
    case 'TWITTER':
      return <Twitter className={cn('h-4 w-4', className)} />
    case 'LINKEDIN':
      return <Linkedin className={cn('h-4 w-4', className)} />
    default:
      return <Users className={cn('h-4 w-4', className)} />
  }
}

// Status icons
const StatusIcon = ({ status }: { status: string }) => {
  switch (status) {
    case 'connected':
      return <CheckCircle2 className="h-3 w-3 text-green-500" />
    case 'expired':
      return <Clock className="h-3 w-3 text-yellow-500" />
    case 'error':
    case 'disconnected':
      return <AlertCircle className="h-3 w-3 text-red-500" />
    case 'reconnecting':
      return <RefreshCw className="h-3 w-3 text-blue-500 animate-spin" />
    default:
      return <AlertCircle className="h-3 w-3 text-gray-500" />
  }
}

export function PlatformSelector({
  selectedAccounts,
  onSelectionChange,
  onCharacterLimitChange,
  className
}: PlatformSelectorProps) {
  const [accounts, setAccounts] = useState<SocialAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch connected social accounts
  useEffect(() => {
    fetchAccounts()
  }, [])

  // Update character limits when selection changes
  useEffect(() => {
    if (onCharacterLimitChange) {
      const limits: Record<string, number> = {}
      selectedAccounts.forEach(accountId => {
        const account = accounts.find(acc => acc.id === accountId)
        if (account) {
          limits[account.platform] = PLATFORM_LIMITS[account.platform]
        }
      })
      onCharacterLimitChange(limits)
    }
  }, [selectedAccounts, accounts, onCharacterLimitChange])

  const fetchAccounts = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/social/accounts/enhanced')
      if (!response.ok) {
        throw new Error('فشل في جلب الحسابات المتصلة')
      }

      const data = await response.json()
      setAccounts(data.accounts || [])
    } catch (err) {
      console.error('Error fetching accounts:', err)
      setError(err instanceof Error ? err.message : 'حدث خطأ في جلب الحسابات')
      toast.error('فشل في جلب الحسابات المتصلة')
    } finally {
      setLoading(false)
    }
  }

  const handleAccountToggle = (accountId: string, checked: boolean) => {
    const newSelection = checked
      ? [...selectedAccounts, accountId]
      : selectedAccounts.filter(id => id !== accountId)
    
    onSelectionChange(newSelection)
  }

  const handleSelectAll = () => {
    const connectedAccounts = accounts
      .filter(acc => acc.connection_status === 'connected')
      .map(acc => acc.id)
    
    onSelectionChange(connectedAccounts)
  }

  const handleDeselectAll = () => {
    onSelectionChange([])
  }

  const formatFollowerCount = (count?: number): string => {
    if (!count) return '0'
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}م`
    if (count >= 1000) return `${(count / 1000).toFixed(1)}ك`
    return count.toLocaleString('ar-SA')
  }

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'connected': return 'متصل'
      case 'expired': return 'منتهي الصلاحية'
      case 'error': return 'خطأ'
      case 'reconnecting': return 'إعادة الاتصال'
      case 'disconnected': return 'غير متصل'
      default: return 'غير معروف'
    }
  }

  const connectedAccounts = accounts.filter(acc => acc.connection_status === 'connected')
  const selectedConnectedAccounts = selectedAccounts.filter(id => 
    connectedAccounts.some(acc => acc.id === id)
  )

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-right">اختيار المنصات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="mr-2 text-muted-foreground">جاري تحميل الحسابات...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-right">اختيار المنصات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchAccounts} variant="outline">
              <RefreshCw className="h-4 w-4 ml-2" />
              إعادة المحاولة
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (accounts.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-right">اختيار المنصات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">لا توجد حسابات متصلة</p>
            <Button onClick={() => window.location.href = '/social'} variant="outline">
              ربط حساب جديد
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-right">اختيار المنصات</CardTitle>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={handleSelectAll}
              disabled={connectedAccounts.length === 0}
            >
              تحديد الكل
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handleDeselectAll}
              disabled={selectedAccounts.length === 0}
            >
              إلغاء التحديد
            </Button>
          </div>
        </div>
        
        {selectedConnectedAccounts.length > 0 && (
          <div className="text-sm text-muted-foreground text-right">
            تم اختيار {selectedConnectedAccounts.length} من {connectedAccounts.length} حسابات
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {accounts.map((account) => {
          const isSelected = selectedAccounts.includes(account.id)
          const isConnected = account.connection_status === 'connected'
          const followerCount = account.metadata?.metrics?.followerCount

          return (
            <div
              key={account.id}
              className={cn(
                'flex items-center space-x-3 space-x-reverse p-3 rounded-lg border transition-colors',
                isSelected && isConnected && 'bg-primary/5 border-primary',
                !isConnected && 'opacity-60'
              )}
            >
              <Checkbox
                id={account.id}
                checked={isSelected}
                onCheckedChange={(checked) => handleAccountToggle(account.id, !!checked)}
                disabled={!isConnected}
                className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
              />

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <PlatformIcon platform={account.platform} />
                  <span className="font-medium text-sm">{account.account_name}</span>
                  <StatusIcon status={account.connection_status} />
                </div>

                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  {account.account_handle && (
                    <span>@{account.account_handle}</span>
                  )}
                  {followerCount && (
                    <>
                      <Separator orientation="vertical" className="h-3" />
                      <span>{formatFollowerCount(followerCount)} متابع</span>
                    </>
                  )}
                  <Separator orientation="vertical" className="h-3" />
                  <span>{getStatusText(account.connection_status)}</span>
                </div>

                {/* Character limit info */}
                {isSelected && (
                  <div className="mt-2">
                    <Badge variant="secondary" className="text-xs">
                      الحد الأقصى: {PLATFORM_LIMITS[account.platform].toLocaleString('ar-SA')} حرف
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          )
        })}

        {connectedAccounts.length === 0 && accounts.length > 0 && (
          <div className="text-center py-4">
            <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              جميع الحسابات تحتاج إلى إعادة اتصال
            </p>
            <Button 
              size="sm" 
              variant="outline" 
              className="mt-2"
              onClick={() => window.location.href = '/social'}
            >
              إدارة الحسابات
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
