"use client";

import React, { useState, useEffect } from 'react';
import { ImprovedPostForm } from '@/components/posts/improved-post-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  ArrowLeft,
  Sparkles,
  Zap,
  Target,
  Clock,
  Info,
  CheckCircle,
  TrendingUp,
  Users,
  Globe
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';

interface CreateImprovedPostPageProps {
  searchParams?: {
    lang?: string;
  };
}

export default function CreateImprovedPostPage({ searchParams }: CreateImprovedPostPageProps) {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const router = useRouter();
  const supabase = createClient();

  // Translations
  const t = {
    ar: {
      title: 'إنشاء منشور محسن',
      subtitle: 'تجربة محسنة لإنشاء المحتوى مع سير عمل مبسط',
      backToPosts: 'العودة للمنشورات',
      switchLanguage: 'English',
      newFeature: 'جديد',
      improved: 'محسن',
      features: {
        title: 'الميزات الجديدة',
        stepByStep: {
          title: 'سير عمل خطوة بخطوة',
          description: 'عملية إنشاء مبسطة مع إرشادات واضحة'
        },
        autoSave: {
          title: 'حفظ تلقائي',
          description: 'لن تفقد عملك أبداً مع الحفظ التلقائي كل ثانيتين'
        },
        smartPreview: {
          title: 'معاينة ذكية',
          description: 'شاهد كيف سيبدو منشورك على كل منصة'
        },
        optimizedTiming: {
          title: 'توقيت محسن',
          description: 'اقتراحات لأفضل أوقات النشر لزيادة التفاعل'
        }
      },
      tips: {
        title: 'نصائح للنجاح',
        engagement: 'المنشورات مع الصور تحصل على تفاعل أكثر بـ 650%',
        timing: 'أفضل أوقات النشر: 9-11 صباحاً و 7-9 مساءً',
        hashtags: 'استخدم 3-5 هاشتاغات ذات صلة لزيادة الوصول',
        consistency: 'النشر المنتظم يزيد من نمو المتابعين بنسبة 23%'
      },
      stats: {
        title: 'إحصائيات سريعة',
        avgEngagement: 'متوسط التفاعل',
        bestTime: 'أفضل وقت للنشر',
        topPlatform: 'أفضل منصة',
        weeklyPosts: 'منشورات هذا الأسبوع'
      }
    },
    en: {
      title: 'Create Improved Post',
      subtitle: 'Enhanced content creation experience with streamlined workflow',
      backToPosts: 'Back to Posts',
      switchLanguage: 'العربية',
      newFeature: 'New',
      improved: 'Improved',
      features: {
        title: 'New Features',
        stepByStep: {
          title: 'Step-by-Step Workflow',
          description: 'Simplified creation process with clear guidance'
        },
        autoSave: {
          title: 'Auto-Save',
          description: 'Never lose your work with auto-save every 2 seconds'
        },
        smartPreview: {
          title: 'Smart Preview',
          description: 'See how your post will look on each platform'
        },
        optimizedTiming: {
          title: 'Optimized Timing',
          description: 'Suggestions for best posting times to maximize engagement'
        }
      },
      tips: {
        title: 'Success Tips',
        engagement: 'Posts with images get 650% more engagement',
        timing: 'Best posting times: 9-11 AM and 7-9 PM',
        hashtags: 'Use 3-5 relevant hashtags to increase reach',
        consistency: 'Regular posting increases follower growth by 23%'
      },
      stats: {
        title: 'Quick Stats',
        avgEngagement: 'Avg Engagement',
        bestTime: 'Best Time to Post',
        topPlatform: 'Top Platform',
        weeklyPosts: 'Posts This Week'
      }
    }
  };

  const text = t[language];

  useEffect(() => {
    // Set language from search params
    if (searchParams?.lang === 'en') {
      setLanguage('en');
    }

    // Check authentication
    const checkAuth = async () => {
      try {
        const { data: { user }, error } = await supabase.auth.getUser();
        
        if (error || !user) {
          router.push('/auth/signin');
          return;
        }

        setUser(user);
      } catch (error) {
        console.error('Auth check failed:', error);
        toast.error(language === 'ar' ? 'فشل في التحقق من الهوية' : 'Authentication failed');
        router.push('/auth/signin');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [searchParams, language, router, supabase.auth]);

  const toggleLanguage = () => {
    const newLang = language === 'ar' ? 'en' : 'ar';
    setLanguage(newLang);
    
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('lang', newLang);
    window.history.replaceState({}, '', url.toString());
  };

  const handleSave = async (data: any) => {
    try {
      const response = await fetch('/api/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to save post');
      }

      const result = await response.json();
      console.log('Post saved:', result);
    } catch (error) {
      console.error('Save error:', error);
      throw error;
    }
  };

  const handlePublish = async (data: any) => {
    try {
      const response = await fetch('/api/posts/publish', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to publish post');
      }

      const result = await response.json();
      console.log('Post published:', result);
    } catch (error) {
      console.error('Publish error:', error);
      throw error;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="text-gray-600">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "min-h-screen bg-gray-50",
      language === 'ar' ? "rtl" : "ltr"
    )}>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className={cn(
          "flex items-center justify-between",
          language === 'ar' ? "flex-row-reverse" : ""
        )}>
          <div className={language === 'ar' ? "text-right" : ""}>
            <div className={cn(
              "flex items-center gap-3 mb-2",
              language === 'ar' ? "flex-row-reverse" : ""
            )}>
              <h1 className="text-4xl font-bold text-gray-900">{text.title}</h1>
              <Badge variant="secondary" className="text-xs">
                <Sparkles className="w-3 h-3 mr-1" />
                {text.improved}
              </Badge>
            </div>
            <p className="text-xl text-gray-600">{text.subtitle}</p>
          </div>
          
          <div className={cn(
            "flex items-center gap-4",
            language === 'ar' ? "flex-row-reverse" : ""
          )}>
            <Button
              variant="outline"
              onClick={toggleLanguage}
              className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}
            >
              <Globe className="w-4 h-4" />
              {text.switchLanguage}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => router.push('/posts')}
              className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}
            >
              <ArrowLeft className="w-4 h-4" />
              {text.backToPosts}
            </Button>
          </div>
        </div>

        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Target className="w-5 h-5 text-blue-600" />
                </div>
                <CardTitle className="text-lg">{text.features.stepByStep.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.features.stepByStep.description}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
                <CardTitle className="text-lg">{text.features.autoSave.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.features.autoSave.description}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Zap className="w-5 h-5 text-purple-600" />
                </div>
                <CardTitle className="text-lg">{text.features.smartPreview.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.features.smartPreview.description}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className={cn(
                "flex items-center gap-3",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Clock className="w-5 h-5 text-orange-600" />
                </div>
                <CardTitle className="text-lg">{text.features.optimizedTiming.title}</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className={language === 'ar' ? "text-right" : ""}>
                {text.features.optimizedTiming.description}
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* Success Tips */}
        <Alert className="border-blue-200 bg-blue-50">
          <Info className="h-4 w-4 text-blue-600" />
          <AlertTitle className={cn(
            "text-blue-900",
            language === 'ar' ? "text-right" : ""
          )}>
            {text.tips.title}
          </AlertTitle>
          <AlertDescription className={cn(
            "text-blue-800 space-y-2",
            language === 'ar' ? "text-right" : ""
          )}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
              <div className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <TrendingUp className="w-4 h-4 text-blue-600" />
                <span className="text-sm">{text.tips.engagement}</span>
              </div>
              <div className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <Clock className="w-4 h-4 text-blue-600" />
                <span className="text-sm">{text.tips.timing}</span>
              </div>
              <div className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <Target className="w-4 h-4 text-blue-600" />
                <span className="text-sm">{text.tips.hashtags}</span>
              </div>
              <div className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <Users className="w-4 h-4 text-blue-600" />
                <span className="text-sm">{text.tips.consistency}</span>
              </div>
            </div>
          </AlertDescription>
        </Alert>

        {/* Improved Post Form */}
        <ImprovedPostForm
          language={language}
          onSave={handleSave}
          onPublish={handlePublish}
          className="bg-white rounded-lg shadow-sm"
        />
      </div>
    </div>
  );
}
