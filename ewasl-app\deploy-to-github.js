#!/usr/bin/env node

/**
 * eWasl Social Media Platform - GitHub Deployment Script
 * 
 * This script pushes the eWasl application code to the GitHub repository
 * connected to Vercel for automatic deployment.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Repository configuration
const GITHUB_REPO = 'https://github.com/Mr-Taha-1/ewasl-social-platform.git';
const BRANCH = 'main';

console.log('🚀 eWasl Social Media Platform - GitHub Deployment');
console.log('=' .repeat(60));

function runCommand(command, description, options = {}) {
  console.log(`\n📋 ${description}...`);
  try {
    const output = execSync(command, { 
      stdio: options.silent ? 'pipe' : 'inherit', 
      encoding: 'utf8',
      cwd: __dirname,
      ...options
    });
    console.log(`✅ ${description} completed successfully`);
    return output;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    if (error.stdout) console.log('STDOUT:', error.stdout);
    if (error.stderr) console.log('STDERR:', error.stderr);
    throw error;
  }
}

function checkPrerequisites() {
  console.log('\n🔍 Checking prerequisites...');
  
  // Check if we're in the right directory
  if (!fs.existsSync('./package.json')) {
    throw new Error('package.json not found. Make sure you\'re in the ewasl-app directory.');
  }
  
  // Check if git is available
  try {
    execSync('git --version', { stdio: 'pipe' });
  } catch (error) {
    throw new Error('Git is not installed or not available in PATH.');
  }
  
  console.log('✅ Prerequisites check completed');
}

function initializeGitRepository() {
  console.log('\n📦 Setting up Git repository...');
  
  // Initialize git if not already initialized
  if (!fs.existsSync('.git')) {
    runCommand('git init', 'Initialize Git repository');
  }
  
  // Configure git user (if not already configured)
  try {
    execSync('git config user.name', { stdio: 'pipe' });
  } catch (error) {
    runCommand('git config user.name "eWasl Deployment"', 'Configure Git user name');
  }
  
  try {
    execSync('git config user.email', { stdio: 'pipe' });
  } catch (error) {
    runCommand('git config user.email "<EMAIL>"', 'Configure Git user email');
  }
  
  // Add remote origin if not exists
  try {
    const remotes = execSync('git remote', { stdio: 'pipe', encoding: 'utf8' });
    if (!remotes.includes('origin')) {
      runCommand(`git remote add origin ${GITHUB_REPO}`, 'Add remote origin');
    }
  } catch (error) {
    runCommand(`git remote add origin ${GITHUB_REPO}`, 'Add remote origin');
  }
}

function createGitignore() {
  console.log('\n📝 Creating .gitignore...');
  
  const gitignoreContent = `# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage
test-results/
playwright-report/
playwright/.cache/

# Next.js
/.next/
/out/

# Production
/build
/dist

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Local certificates
*.crt
*.key
*.pem

# Database
*.db
*.sqlite

# Supabase
.supabase/

# Test files and reports
test-*.png
test-*.html
test-*.json
test-*.txt
test-*.md
*test-report*
*-test.js
*-test.ts
comprehensive-test*
final-*test*
browser-test*
playwright-test*
performance-*test*

# Documentation and reports
*-REPORT.md
*-SUMMARY.md
*-ANALYSIS.md
*-GUIDE.md
*-STATUS.md
*-CHECKLIST.md
DEPLOYMENT*.md
TESTING*.md
AUDIT*.md
PHASE*.md
PRIORITY*.md
IMPLEMENTATION*.md
COMPREHENSIVE*.md

# Scripts and utilities
fix-*.js
setup-*.js
debug-*.js
test-*.js
verify-*.js
check-*.js
cleanup-*.js
create-*.js
manual-*.js
quick-*.js
smart-*.js
nuclear-*.js

# Backup files
*.backup
*-backup.*
backup-*/

# Screenshots and images
*.png
*.jpg
*.jpeg
*.gif
auth-*.png
dashboard-*.png
homepage-*.png
signin-*.png
social-*.png
settings-*.png
posts-*.png
analytics-*.png

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Deployment
deploy*.sh
deploy*.bat
deploy*.js
app-spec*.yaml
doctl*

# Certificates and keys
ca.*
cert.*

# Environment and config
.env.*
production.env
`;

  fs.writeFileSync('.gitignore', gitignoreContent);
  console.log('✅ .gitignore created successfully');
}

function createReadme() {
  console.log('\n📖 Creating README.md...');
  
  const readmeContent = `# eWasl Social Media Platform

A comprehensive social media management platform built with Next.js, Supabase, and modern web technologies.

## 🚀 Features

- **Multi-Platform Publishing**: Facebook, Instagram, LinkedIn, Twitter/X support
- **Rich Text Editor**: TipTap editor with Arabic RTL support
- **Media Management**: Supabase Storage integration
- **Advanced Scheduling**: Queue system with background job processing
- **Analytics Dashboard**: Comprehensive social media analytics
- **Team Collaboration**: Multi-user workspace management
- **Arabic Localization**: Full RTL support and Arabic interface

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Backend**: Next.js API Routes, Supabase
- **Database**: PostgreSQL (Supabase)
- **Authentication**: NextAuth.js with Supabase
- **Styling**: Tailwind CSS, shadcn/ui
- **Editor**: TipTap
- **Storage**: Supabase Storage
- **Deployment**: Vercel

## 🌐 Live Demo

Visit the live application: [https://ewasl-social-platform.vercel.app](https://ewasl-social-platform.vercel.app)

## 📱 Key Pages

- **Dashboard**: `/dashboard` - Main application dashboard
- **Post Creation**: `/posts/new` - Create and schedule posts
- **Social Accounts**: `/social` - Manage connected social media accounts
- **Analytics**: `/analytics` - View performance metrics
- **Schedule**: `/schedule` - Manage scheduled posts
- **Settings**: `/settings` - Application settings

## 🔧 Development

\`\`\`bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
\`\`\`

## 🚀 Deployment

This application is automatically deployed to Vercel when changes are pushed to the main branch.

## 📄 License

Private - eWasl Social Media Platform

## 🤝 Contributing

This is a private project. For access or contributions, please contact the development team.
`;

  fs.writeFileSync('README.md', readmeContent);
  console.log('✅ README.md created successfully');
}

function stageAndCommitFiles() {
  console.log('\n📦 Staging and committing files...');
  
  // Add all files
  runCommand('git add .', 'Stage all files');
  
  // Check if there are changes to commit
  try {
    const status = execSync('git status --porcelain', { stdio: 'pipe', encoding: 'utf8' });
    if (!status.trim()) {
      console.log('ℹ️  No changes to commit');
      return false;
    }
  } catch (error) {
    // Continue with commit
  }
  
  // Commit changes
  const commitMessage = `Deploy eWasl Social Media Platform

- Complete Next.js application with Arabic RTL support
- Multi-platform social media publishing (Facebook, Instagram, LinkedIn, Twitter)
- TipTap rich text editor with media upload
- Advanced scheduling and queue system
- Comprehensive analytics dashboard
- Team collaboration features
- Supabase integration for database and storage
- Production-ready with environment variables configured

Deployment: ${new Date().toISOString()}`;

  runCommand(`git commit -m "${commitMessage}"`, 'Commit changes');
  return true;
}

function pushToGitHub() {
  console.log('\n🚀 Pushing to GitHub...');
  
  try {
    // Try to push to existing branch
    runCommand(`git push origin ${BRANCH}`, 'Push to GitHub');
  } catch (error) {
    console.log('⚠️  Push failed, trying to set upstream...');
    try {
      runCommand(`git push -u origin ${BRANCH}`, 'Push with upstream');
    } catch (upstreamError) {
      console.log('⚠️  Upstream push failed, trying force push...');
      runCommand(`git push -f origin ${BRANCH}`, 'Force push to GitHub');
    }
  }
}

function displayDeploymentStatus() {
  console.log('\n🎉 Deployment Status:');
  console.log('=' .repeat(60));
  console.log('✅ Code successfully pushed to GitHub');
  console.log('✅ Vercel will automatically deploy the changes');
  console.log('✅ Environment variables are already configured');
  console.log('\n🔗 Links:');
  console.log(`📦 GitHub Repository: ${GITHUB_REPO}`);
  console.log('🌐 Production URL: https://ewasl-social-platform.vercel.app');
  console.log('⚙️  Vercel Dashboard: https://vercel.com/ewasls-projects/ewasl-social-platform');
  
  console.log('\n⏳ Next Steps:');
  console.log('1. Wait for Vercel automatic deployment (2-3 minutes)');
  console.log('2. Update Facebook app settings with production domain');
  console.log('3. Test the application at the production URL');
  console.log('4. Verify Facebook/Instagram publishing works');
  
  console.log('\n🧪 Testing URLs:');
  console.log('- Homepage: https://ewasl-social-platform.vercel.app');
  console.log('- Post Creation: https://ewasl-social-platform.vercel.app/posts/new');
  console.log('- Debug Tools: https://ewasl-social-platform.vercel.app/debug-post');
  console.log('- Social Accounts: https://ewasl-social-platform.vercel.app/social');
}

async function main() {
  try {
    console.log('Starting eWasl deployment to GitHub...\n');
    
    // Step 1: Check prerequisites
    checkPrerequisites();
    
    // Step 2: Initialize Git repository
    initializeGitRepository();
    
    // Step 3: Create necessary files
    createGitignore();
    createReadme();
    
    // Step 4: Stage and commit files
    const hasChanges = stageAndCommitFiles();
    
    if (hasChanges) {
      // Step 5: Push to GitHub
      pushToGitHub();
    }
    
    // Step 6: Display deployment status
    displayDeploymentStatus();
    
    console.log('\n🎉 Deployment completed successfully!');
    console.log('The eWasl Social Media Platform is now deploying to production.');
    
  } catch (error) {
    console.error('\n❌ Deployment failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure Git is installed and configured');
    console.log('2. Check GitHub repository access permissions');
    console.log('3. Verify network connectivity');
    console.log('4. Try manual deployment using Git commands');
    process.exit(1);
  }
}

// Run the deployment
if (require.main === module) {
  main();
}

module.exports = { main };
