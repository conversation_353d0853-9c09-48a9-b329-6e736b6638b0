/**
 * Health Check API
 * Comprehensive system health monitoring endpoint
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createServiceClient } from '@/lib/supabase/service';
import { getOAuthConfigs } from '@/lib/social/oauth-config';
import { logger } from '@/lib/monitoring/enhanced-logger';

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  components: {
    database: ComponentStatus;
    auth: ComponentStatus;
    socialMedia: {
      [platform: string]: ComponentStatus;
    };
    api: ComponentStatus;
    tokens: ComponentStatus;
    queue: ComponentStatus;
  };
  uptime: number;
}

interface ComponentStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  message?: string;
  latency?: number;
  details?: Record<string, any>;
}

// Server start time for uptime calculation
const SERVER_START_TIME = Date.now();

/**
 * Health check handler
 * GET /api/health
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  // Initialize health status
  const healthStatus: HealthStatus = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    components: {
      database: { status: 'healthy' },
      auth: { status: 'healthy' },
      socialMedia: {
        twitter: { status: 'healthy' },
        facebook: { status: 'healthy' },
        instagram: { status: 'healthy' },
        linkedin: { status: 'healthy' }
      },
      api: { status: 'healthy' },
      tokens: { status: 'healthy' },
      queue: { status: 'healthy' }
    },
    uptime: Math.floor((Date.now() - SERVER_START_TIME) / 1000) // in seconds
  };

  try {
    // Check database health using Supabase client
    const dbStartTime = Date.now();
    try {
      // Test database connection with a simple query using Supabase
      const supabase = createClient();
      const { data, error } = await supabase
        .from('users')
        .select('count')
        .limit(1);

      if (error) {
        throw new Error(error.message);
      }

      healthStatus.components.database.latency = Date.now() - dbStartTime;
      healthStatus.components.database.details = {
        connection: 'supabase',
        queryResult: 'success'
      };
    } catch (error) {
      healthStatus.components.database.status = 'unhealthy';
      healthStatus.components.database.message = error instanceof Error ? error.message : 'Database connection failed';
      healthStatus.status = 'degraded';
    }

    // Check auth health
    const authStartTime = Date.now();
    try {
      const supabase = createClient();
      const { error } = await supabase.auth.getSession();
      healthStatus.components.auth.latency = Date.now() - authStartTime;
      
      if (error) {
        healthStatus.components.auth.status = 'degraded';
        healthStatus.components.auth.message = error.message;
        healthStatus.status = 'degraded';
      }
    } catch (error) {
      healthStatus.components.auth.status = 'unhealthy';
      healthStatus.components.auth.message = error instanceof Error ? error.message : 'Auth system check failed';
      healthStatus.status = 'degraded';
    }

    // Check social media configurations
    try {
      const oauthConfigs = getOAuthConfigs();
      
      // Check Twitter/X configuration
      if (!oauthConfigs.twitter?.clientId || !oauthConfigs.twitter?.clientSecret) {
        healthStatus.components.socialMedia.twitter.status = 'degraded';
        healthStatus.components.socialMedia.twitter.message = 'Missing Twitter API credentials';
        healthStatus.status = 'degraded';
      }
      
      // Check Facebook configuration
      if (!oauthConfigs.facebook?.clientId || !oauthConfigs.facebook?.clientSecret) {
        healthStatus.components.socialMedia.facebook.status = 'degraded';
        healthStatus.components.socialMedia.facebook.message = 'Missing Facebook API credentials';
        healthStatus.status = 'degraded';
      }
      
      // Check Instagram configuration
      if (!oauthConfigs.instagram?.clientId || !oauthConfigs.instagram?.clientSecret) {
        healthStatus.components.socialMedia.instagram.status = 'degraded';
        healthStatus.components.socialMedia.instagram.message = 'Missing Instagram API credentials';
        healthStatus.status = 'degraded';
      }
      
      // Check LinkedIn configuration
      if (!oauthConfigs.linkedin?.clientId || !oauthConfigs.linkedin?.clientSecret) {
        healthStatus.components.socialMedia.linkedin.status = 'degraded';
        healthStatus.components.socialMedia.linkedin.message = 'Missing LinkedIn API credentials';
        healthStatus.status = 'degraded';
      }
    } catch (error) {
      Object.keys(healthStatus.components.socialMedia).forEach(platform => {
        healthStatus.components.socialMedia[platform].status = 'unhealthy';
        healthStatus.components.socialMedia[platform].message = 'Failed to check social media configurations';
      });
      healthStatus.status = 'degraded';
    }

    // Check API health
    const apiStartTime = Date.now();
    try {
      // Check if required API endpoints are working
      // This is just a placeholder - in a real implementation, you might check
      // critical API endpoints or external services
      healthStatus.components.api.latency = Date.now() - apiStartTime;
    } catch (error) {
      healthStatus.components.api.status = 'unhealthy';
      healthStatus.components.api.message = error instanceof Error ? error.message : 'API health check failed';
      healthStatus.status = 'degraded';
    }

    // Check token health
    const tokenStartTime = Date.now();
    try {
      const supabaseService = createServiceClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );

      const { data: accounts, error } = await supabaseService
        .from('social_accounts')
        .select('id, platform, expires_at, connection_status')
        .eq('is_active', true);

      if (error) {
        throw new Error(error.message);
      }

      const now = new Date();
      const expiredCount = accounts?.filter(account => {
        if (!account.expires_at) return false;
        return new Date(account.expires_at) < now;
      }).length || 0;

      const expiringCount = accounts?.filter(account => {
        if (!account.expires_at) return false;
        const expiry = new Date(account.expires_at);
        const daysUntilExpiry = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
      }).length || 0;

      healthStatus.components.tokens.latency = Date.now() - tokenStartTime;
      healthStatus.components.tokens.details = {
        total: accounts?.length || 0,
        expired: expiredCount,
        expiring: expiringCount
      };

      if (expiredCount > 0) {
        healthStatus.components.tokens.status = 'degraded';
        healthStatus.components.tokens.message = `${expiredCount} tokens expired, ${expiringCount} expiring soon`;
        healthStatus.status = 'degraded';
      } else if (expiringCount > 0) {
        healthStatus.components.tokens.status = 'degraded';
        healthStatus.components.tokens.message = `${expiringCount} tokens expiring within 7 days`;
        healthStatus.status = 'degraded';
      } else {
        healthStatus.components.tokens.message = 'All tokens are valid';
      }
    } catch (error) {
      healthStatus.components.tokens.status = 'unhealthy';
      healthStatus.components.tokens.message = error instanceof Error ? error.message : 'Token health check failed';
      healthStatus.status = 'degraded';
    }

    // Check queue health
    const queueStartTime = Date.now();
    try {
      const supabaseService = createServiceClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!
      );

      const { data: queueItems, error } = await supabaseService
        .from('scheduled_posts_queue')
        .select('id, status, scheduled_for, retry_count')
        .order('scheduled_for', { ascending: true });

      if (error) {
        throw new Error(error.message);
      }

      const now = new Date();
      const overdue = queueItems?.filter(item =>
        item.status === 'pending' && new Date(item.scheduled_for) < now
      ).length || 0;

      const failed = queueItems?.filter(item =>
        item.status === 'failed'
      ).length || 0;

      healthStatus.components.queue.latency = Date.now() - queueStartTime;
      healthStatus.components.queue.details = {
        total: queueItems?.length || 0,
        pending: queueItems?.filter(item => item.status === 'pending').length || 0,
        processing: queueItems?.filter(item => item.status === 'processing').length || 0,
        completed: queueItems?.filter(item => item.status === 'completed').length || 0,
        failed: failed,
        overdue: overdue
      };

      if (overdue > 0 || failed > 10) {
        healthStatus.components.queue.status = 'degraded';
        healthStatus.components.queue.message = `${overdue} overdue items, ${failed} failed items`;
        healthStatus.status = 'degraded';
      } else {
        healthStatus.components.queue.message = 'Queue processing normally';
      }
    } catch (error) {
      healthStatus.components.queue.status = 'unhealthy';
      healthStatus.components.queue.message = error instanceof Error ? error.message : 'Queue health check failed';
      healthStatus.status = 'degraded';
    }

    // Calculate total response time
    const totalResponseTime = Date.now() - startTime;
    
    // Log health check results
    logger.info('Health Check', {
      status: healthStatus.status,
      responseTime: totalResponseTime
    }, {
      metrics: { responseTime: totalResponseTime }
    });

    // Return health status
    return NextResponse.json(healthStatus, {
      status: healthStatus.status === 'healthy' ? 200 : 
             healthStatus.status === 'degraded' ? 200 : 503
    });
  } catch (error) {
    // Log any unexpected errors
    logger.error('Health Check Failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    // Return error response
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      message: 'Health check failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Detailed Health Check (Admin only)
 * GET /api/health?detailed=true
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;
    
    if (action === 'clear_metrics') {
      performanceMonitor.clearMetrics();
      return NextResponse.json({ 
        success: true, 
        message: 'Performance metrics cleared' 
      });
    }
    
    if (action === 'test_platforms') {
      // Test OAuth configurations for all platforms
      const oauthConfigs = getOAuthConfigs();
      const testResults = [];
      
      for (const [platform, config] of Object.entries(oauthConfigs)) {
        const testResult = {
          platform,
          configured: !!(config.clientId && config.clientSecret),
          enabled: config.enabled,
          hasClientId: !!config.clientId,
          hasClientSecret: !!config.clientSecret,
          redirectUri: config.redirectUri,
          scopes: config.scope
        };
        testResults.push(testResult);
      }
      
      return NextResponse.json({
        success: true,
        platforms: testResults
      });
    }
    
    return NextResponse.json({
      error: 'Unknown action'
    }, { status: 400 });
    
  } catch (error) {
    console.error('Health check action failed:', error);
    return NextResponse.json({
      error: 'Action failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}