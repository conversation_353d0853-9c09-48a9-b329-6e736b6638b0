import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

/**
 * FIXED Posts API Route - Resolves 500 error in publishing workflow
 * 
 * ROOT CAUSE IDENTIFIED:
 * - Posts were created successfully in posts table
 * - Publishing functions were called and returned results
 * - MISSING: No code to create post_social_accounts entries
 * - Result: Publishing results were lost, causing 500 errors
 * 
 * SOLUTION:
 * - Add post_social_accounts creation logic after post creation
 * - Store publishing results in database properly
 * - Maintain transaction integrity
 */

// Validation schema for creating posts
const createPostSchema = z.object({
  content: z.string().min(1, 'المحتوى مطلوب').max(2800, 'المحتوى طويل جداً'),
  media_urls: z.array(z.string().url()).default([]),
  status: z.enum(['DRAFT', 'SCHEDULED', 'PUBLISHED']).default('DRAFT'),
  scheduled_at: z.union([z.string().datetime(), z.undefined(), z.null()]).optional(),
  platforms: z.array(z.string()).optional(),
  social_account_ids: z.array(z.string()).optional(),
  timezone: z.string().optional().default('UTC'),
}).refine(data => {
  return (data.platforms && data.platforms.length > 0) ||
         (data.social_account_ids && data.social_account_ids.length > 0);
}, {
  message: 'يرجى اختيار منصة واحدة على الأقل',
  path: ['platforms']
});

export async function POST(request: NextRequest) {
  console.log('🚀 POST /api/posts-fixed - FIXED Handler started');
  
  try {
    // Get authenticated user
    const { user } = await getAuthenticatedUser(request);
    const supabase = createClient();
    console.log('✅ User authenticated:', user.id);

    // Parse and validate request body
    const body = await request.json();
    console.log('📝 Request body received:', { 
      content: body.content?.substring(0, 50) + '...',
      status: body.status,
      platforms: body.platforms,
      social_account_ids: body.social_account_ids
    });

    const validation = createPostSchema.safeParse(body);
    if (!validation.success) {
      console.error('❌ Validation failed:', validation.error.errors);
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { content, media_urls, status, scheduled_at, timezone } = validation.data;
    const platforms = validation.data.platforms || [];
    const social_account_ids = validation.data.social_account_ids || [];

    console.log('✅ Validation passed:', { content: content.substring(0, 50) + '...', status, platforms, social_account_ids });

    // Get social accounts
    let socialAccountsQuery = supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id);

    if (social_account_ids.length > 0) {
      socialAccountsQuery = socialAccountsQuery.in('id', social_account_ids);
    } else if (platforms.length > 0) {
      socialAccountsQuery = socialAccountsQuery.in('platform', platforms.map(p => p.toUpperCase()));
    }

    const { data: socialAccounts, error: accountsError } = await socialAccountsQuery;

    if (accountsError) {
      console.error('❌ Error fetching social accounts:', accountsError);
      return NextResponse.json(
        { error: 'فشل في جلب الحسابات الاجتماعية', details: accountsError },
        { status: 500 }
      );
    }

    if (!socialAccounts || socialAccounts.length === 0) {
      console.warn('⚠️ No social accounts found');
      return NextResponse.json(
        { error: 'لا توجد حسابات اجتماعية متصلة' },
        { status: 404 }
      );
    }

    console.log(`✅ Found ${socialAccounts.length} social accounts`);

    // Step 1: Create post in posts table
    const { data: post, error: createError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content,
        media_url: media_urls.length > 0 ? media_urls[0] : null,
        status,
        scheduled_at: scheduled_at ? new Date(scheduled_at).toISOString() : null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ Error creating post:', createError);
      return NextResponse.json(
        { error: 'فشل في إنشاء المنشور', details: createError },
        { status: 500 }
      );
    }

    console.log('✅ Post created successfully:', post.id);

    // Step 2: Create post_social_accounts entries (MISSING IN ORIGINAL CODE)
    console.log('🔗 Creating post_social_accounts entries...');
    
    const postSocialAccountsData = socialAccounts.map(account => ({
      post_id: post.id,
      social_account_id: account.id,
      platform: account.platform,
      status: status === 'PUBLISHED' ? 'pending' : 'draft',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    const { data: postSocialAccounts, error: psaError } = await supabase
      .from('post_social_accounts')
      .insert(postSocialAccountsData)
      .select();

    if (psaError) {
      console.error('❌ CRITICAL: Failed to create post_social_accounts entries:', psaError);
      
      // Clean up: Delete the created post since we can't link it to social accounts
      await supabase.from('posts').delete().eq('id', post.id);

      return NextResponse.json(
        { error: 'فشل في ربط المنشور بالحسابات الاجتماعية', details: psaError },
        { status: 500 }
      );
    }

    console.log(`✅ Created ${postSocialAccounts.length} post_social_accounts entries`);

    // Initialize publishResults for response
    let publishResults: any[] = [];

    // Step 3: If status is PUBLISHED, publish immediately
    if (status === 'PUBLISHED') {
      console.log('🚀 Starting immediate publishing...');

      try {
        // Import enhanced publishing functions
        const { publishToFacebookEnhanced, publishToInstagramEnhanced } = await import('@/lib/social/enhanced-publishing');

        // Publish to each account and update post_social_accounts entries
        for (let i = 0; i < socialAccounts.length; i++) {
          const account = socialAccounts[i];
          const psaEntry = postSocialAccounts[i];
          
          console.log(`📤 Publishing to ${account.platform} (${account.account_name})`);

          let result;

          try {
            switch (account.platform.toUpperCase()) {
              case 'FACEBOOK':
                result = await publishToFacebookEnhanced(content, media_urls, account, {
                  timezone,
                  postId: post.id
                });
                break;
              case 'INSTAGRAM':
                result = await publishToInstagramEnhanced(content, media_urls, account, {
                  timezone,
                  postId: post.id
                });
                break;
              default:
                result = {
                  success: false,
                  error: `Platform ${account.platform} not supported`,
                  step: 'platform_validation',
                  platform: account.platform
                };
            }

            console.log(`${result.success ? '✅' : '❌'} ${account.platform} result:`, {
              success: result.success,
              postId: result.postId,
              error: result.error
            });

          } catch (publishError: any) {
            console.error(`❌ Publishing error for ${account.platform}:`, publishError);
            result = {
              success: false,
              error: `Publishing error: ${publishError.message}`,
              step: 'publishing_exception',
              platform: account.platform
            };
          }

          // Step 4: Update post_social_accounts entry with publishing result
          const updateData: any = {
            status: result.success ? 'published' : 'failed',
            updated_at: new Date().toISOString()
          };

          if (result.success) {
            updateData.platform_post_id = result.postId;
            updateData.platform_url = result.url;
            updateData.published_at = new Date().toISOString();
          } else {
            updateData.error_message = result.error;
          }

          // Update the specific post_social_accounts entry
          const { error: updateError } = await supabase
            .from('post_social_accounts')
            .update(updateData)
            .eq('id', psaEntry.id);

          if (updateError) {
            console.error('❌ Failed to update post_social_accounts entry:', updateError);
          } else {
            console.log(`✅ Updated post_social_accounts entry for ${account.platform}`);
          }

          publishResults.push({ ...result, platform: account.platform, accountId: account.id });
        }

        // Step 5: Update post status based on results
        const allSuccessful = publishResults.every(r => r.success);
        const someSuccessful = publishResults.some(r => r.success);

        let finalStatus: 'DRAFT' | 'SCHEDULED' | 'PUBLISHED' = status;
        if (publishResults.every(r => !r.success)) {
          finalStatus = 'DRAFT'; // All failed
        } else if (allSuccessful || someSuccessful) {
          finalStatus = 'PUBLISHED'; // At least one succeeded
        }

        // Update post with final status
        await supabase
          .from('posts')
          .update({
            status: finalStatus,
            published_at: someSuccessful ? new Date().toISOString() : null,
            updated_at: new Date().toISOString()
          })
          .eq('id', post.id);

        post.status = finalStatus;
        post.published_at = someSuccessful ? new Date().toISOString() : null;

        console.log(`📊 Publishing summary: ${publishResults.filter(r => r.success).length}/${publishResults.length} successful`);

      } catch (publishError: any) {
        console.error('💥 Critical publishing error:', publishError);

        // Update post status to DRAFT and all post_social_accounts to failed
        await supabase.from('posts').update({ status: 'DRAFT' }).eq('id', post.id);
        await supabase
          .from('post_social_accounts')
          .update({ 
            status: 'failed', 
            error_message: publishError.message,
            updated_at: new Date().toISOString()
          })
          .eq('post_id', post.id);

        post.status = 'DRAFT';
      }
    }

    // Step 6: If status is SCHEDULED, add to queue
    if (status === 'SCHEDULED' && scheduled_at) {
      console.log('📅 Adding post to scheduled queue...');
      
      try {
        const { error: queueError } = await supabase
          .from('scheduled_posts_queue')
          .insert({
            post_id: post.id,
            scheduled_for: new Date(scheduled_at).toISOString(),
            timezone: timezone || 'UTC',
            status: 'pending',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (queueError) {
          console.error('❌ Error adding to queue:', queueError);
        } else {
          console.log('✅ Post added to scheduled queue');
        }
      } catch (queueError) {
        console.error('💥 Queue error:', queueError);
      }
    }

    return NextResponse.json({
      post,
      message: status === 'PUBLISHED' ? 'تم نشر المنشور بنجاح' :
               status === 'SCHEDULED' ? 'تم جدولة المنشور بنجاح' :
               'تم حفظ المنشور كمسودة',
      post_social_accounts: postSocialAccounts,
      publishResults: status === 'PUBLISHED' ? publishResults : undefined,
    }, { status: 201 });

  } catch (error: any) {
    console.error('❌ CRITICAL ERROR in POST /api/posts-fixed:', error);
    return NextResponse.json(
      { error: 'خطأ في الخادم الداخلي', details: error.message },
      { status: 500 }
    );
  }
}
