/**
 * Facebook/Instagram OAuth Integration Service
 * Handles OAuth 2.0 flow, token management, and API calls for Facebook and Instagram
 * Updated to fix import issues and add appsecret_proof support
 */

import { SocialAccount, SocialPlatform } from '@/types/social-enhanced'
import crypto from 'crypto'

// Define ConnectionStatus locally to avoid import issues
const ConnectionStatus = {
  CONNECTED: 'connected' as const,
  EXPIRED: 'expired' as const,
  ERROR: 'error' as const,
  RECONNECTING: 'reconnecting' as const,
  DISCONNECTED: 'disconnected' as const
} as const;

// Facebook Graph API Configuration
const FACEBOOK_API_VERSION = 'v18.0'
const FACEBOOK_BASE_URL = `https://graph.facebook.com/${FACEBOOK_API_VERSION}`

// OAuth Scopes for Facebook/Instagram (User OAuth - NOT Business OAuth)
const FACEBOOK_SCOPES = [
  'pages_show_list',
  'pages_read_engagement',
  'pages_manage_posts',
  'pages_read_user_content',
  'instagram_basic',
  'instagram_content_publish'
  // REMOVED: 'business_management' - This scope triggers Business OAuth flow
  // which returns Page tokens instead of User tokens, preventing account discovery
].join(',')

export interface FacebookOAuthConfig {
  appId: string
  appSecret: string
  businessId: string
  redirectUri: string
}

export interface FacebookTokenResponse {
  access_token: string
  token_type: string
  expires_in?: number
  scope?: string
}

export interface FacebookUserInfo {
  id: string
  name: string
  email?: string
  picture?: {
    data: {
      url: string
    }
  }
}

export interface FacebookPageInfo {
  id: string
  name: string
  access_token: string
  category: string
  tasks: string[]
  instagram_business_account?: {
    id: string
  }
}

export class FacebookOAuthService {
  private config: FacebookOAuthConfig

  constructor(config: FacebookOAuthConfig) {
    this.config = config
  }

  /**
   * Generate appsecret_proof for secure API calls
   * Required by Facebook for server-side API calls
   */
  private generateAppSecretProof(accessToken: string): string {
    return crypto
      .createHmac('sha256', this.config.appSecret)
      .update(accessToken)
      .digest('hex')
  }

  /**
   * Calculate token expiration date (Facebook long-lived tokens last ~60 days)
   * We set expiration to 55 days to refresh before actual expiration
   */
  private calculateTokenExpiration(): Date {
    const expirationDate = new Date()
    expirationDate.setDate(expirationDate.getDate() + 55) // 55 days from now
    return expirationDate
  }

  /**
   * Generate OAuth authorization URL
   */
  getAuthorizationUrl(state?: string): string {
    const params = new URLSearchParams({
      client_id: this.config.appId,
      redirect_uri: this.config.redirectUri,
      scope: FACEBOOK_SCOPES,
      response_type: 'code',
      state: state || ''
    })

    return `https://www.facebook.com/v18.0/dialog/oauth?${params.toString()}`
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(code: string): Promise<FacebookTokenResponse> {
    const params = new URLSearchParams({
      client_id: this.config.appId,
      client_secret: this.config.appSecret,
      redirect_uri: this.config.redirectUri,
      code
    })

    const response = await fetch(`${FACEBOOK_BASE_URL}/oauth/access_token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: params.toString()
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(`Facebook OAuth error: ${error.error?.message || 'Unknown error'}`)
    }

    return response.json()
  }

  /**
   * Get long-lived access token
   */
  async getLongLivedToken(shortLivedToken: string): Promise<FacebookTokenResponse> {
    const params = new URLSearchParams({
      grant_type: 'fb_exchange_token',
      client_id: this.config.appId,
      client_secret: this.config.appSecret,
      fb_exchange_token: shortLivedToken
    })

    const response = await fetch(`${FACEBOOK_BASE_URL}/oauth/access_token?${params.toString()}`)

    if (!response.ok) {
      const error = await response.json()
      throw new Error(`Facebook token exchange error: ${error.error?.message || 'Unknown error'}`)
    }

    return response.json()
  }

  /**
   * Validate access token and get user info
   */
  async validateToken(accessToken: string): Promise<FacebookUserInfo> {
    const appSecretProof = this.generateAppSecretProof(accessToken)
    const response = await fetch(
      `${FACEBOOK_BASE_URL}/me?fields=id,name,email,picture&access_token=${accessToken}&appsecret_proof=${appSecretProof}`
    )

    if (!response.ok) {
      const error = await response.json()
      throw new Error(`Facebook API error: ${error.error?.message || 'Invalid token'}`)
    }

    return response.json()
  }

  /**
   * Get user's Facebook pages and Instagram accounts with enhanced token type handling
   */
  async getUserAccounts(accessToken: string, tokenType?: string, tokenMetadata?: any): Promise<SocialAccount[]> {
    try {
      console.log('🔍 getUserAccounts called with token type:', tokenType);

      const appSecretProof = this.generateAppSecretProof(accessToken);
      const accounts: SocialAccount[] = [];

      // CRITICAL FIX: Handle different token types
      if (tokenType === 'PAGE') {
        console.log('🔧 HANDLING PAGE TOKEN - Using alternative approach');

        // For Page tokens, we cannot call /me/accounts, so we extract page info from token metadata
        if (tokenMetadata?.profile_id && tokenMetadata?.user_id) {
          console.log('📄 Extracting page info from token metadata');

          try {
            // Get page information directly using the page ID from token metadata
            const pageId = tokenMetadata.profile_id
            const pageResponse = await fetch(
              `${FACEBOOK_BASE_URL}/${pageId}?fields=id,name,category,fan_count,about,website,is_verified,picture.type(large),instagram_business_account&access_token=${accessToken}&appsecret_proof=${appSecretProof}`
            )

            if (pageResponse.ok) {
              const pageData = await pageResponse.json();
              console.log('✅ Page data retrieved:', pageData);

              // Create account object for the page
              const pageAccount: SocialAccount = {
                id: pageData.id,
                accountName: pageData.name,
                platform: 'FACEBOOK' as const,
                accountType: 'page',
                accessToken: accessToken, // This is the page access token
                pageId: pageData.id,
                pageName: pageData.name,
                pageAccessToken: accessToken,
                profileImageUrl: pageData.picture?.data?.url,
                metadata: {
                  category: pageData.category,
                  fan_count: pageData.fan_count,
                  about: pageData.about,
                  website: pageData.website,
                  is_verified: pageData.is_verified,
                  token_type: 'PAGE',
                  facebook_user_id: tokenMetadata.user_id,
                  facebook_profile_id: tokenMetadata.profile_id
                },
                permissions: tokenMetadata.scopes || []
              }

              accounts.push(pageAccount);
              console.log('✅ Added page account from token metadata');

              // Check for Instagram Business Account
              if (pageData.instagram_business_account?.id) {
                console.log('📸 Found Instagram Business Account, fetching details...');
                await this.addInstagramAccount(pageData.instagram_business_account.id, accessToken, accounts, appSecretProof);
              }
            } else {
              console.error('❌ Failed to fetch page data:', await pageResponse.text());
            }
          } catch (pageError) {
            console.error('❌ Error fetching page data:', pageError);
          }
        } else {
          console.error('❌ Page token metadata missing profile_id or user_id');
        }

      } else {
        console.log('🔧 HANDLING USER TOKEN - Using /me/accounts endpoint');

        // For User tokens, use the standard /me/accounts approach
        const pagesResponse = await fetch(
          `${FACEBOOK_BASE_URL}/me/accounts?fields=id,name,access_token,category,tasks,instagram_business_account,fan_count,about,website,is_verified,picture.type(large)&access_token=${accessToken}&appsecret_proof=${appSecretProof}`
        );

        if (!pagesResponse.ok) {
          const errorData = await pagesResponse.json();
          console.error('❌ Facebook Pages API error:', errorData);

          // If this fails and we have a USER token, it might actually be a PAGE token
          if (errorData.error?.code === 100 && errorData.error?.message?.includes('nonexisting field (accounts)')) {
            console.log('🔧 USER token failed /me/accounts - might actually be PAGE token, retrying...');
            return this.getUserAccounts(accessToken, 'PAGE', tokenMetadata);
          }

          throw new Error(`Failed to fetch Facebook pages: ${errorData.error?.message || 'Unknown error'}`);
        }

        const pagesData = await pagesResponse.json();
        console.log('✅ Facebook Pages API response:', JSON.stringify(pagesData, null, 2));

        // Process Facebook pages for User token
        for (const page of pagesData.data || []) {
          console.log(`📄 Processing page: ${page.name} (${page.id})`);

          // Get additional page insights if available
        const pageMetrics = await this.getPageMetrics(page.access_token, page.id);

        const facebookAccount: SocialAccount = {
          id: `fb_${page.id}`,
          platform: 'FACEBOOK',
          accountName: page.name,
          accountHandle: `@${page.name.toLowerCase().replace(/\s+/g, '')}`,
          connectionStatus: ConnectionStatus.CONNECTED,
          accessToken: accessToken, // Store user access token
          refreshToken: null,
          expiresAt: this.calculateTokenExpiration(), // Set proper expiration for long-lived tokens
          lastValidatedAt: new Date(),
          metadata: {
            pageId: page.id,
            pageAccessToken: page.access_token, // Store page token in metadata
            category: page.category,
            tasks: page.tasks || [],
            fanCount: page.fan_count || 0,
            about: page.about,
            website: page.website,
            isVerified: page.is_verified || false,
            profilePictureUrl: page.picture?.data?.url,
            token_type: tokenType || 'USER', // Track token type used
            page_token_available: !!page.access_token,
            oauth_timestamp: new Date().toISOString(),
            ...pageMetrics
          },
          permissions: ['read', 'write'],
          profileImageUrl: page.picture?.data?.url || `https://graph.facebook.com/${page.id}/picture?type=large`,
          createdAt: new Date(),
          updatedAt: new Date(),
          // Add page-specific fields for database storage
          pageId: page.id,
          pageAccessToken: page.access_token,
          pageName: page.name
        }

        accounts.push(facebookAccount);
        console.log(`✅ Added Facebook page: ${page.name}`);

        // Check for connected Instagram Business account
        if (page.instagram_business_account) {
          console.log('📸 Found Instagram Business Account, fetching details...');
          const instagramMetrics = await this.getInstagramMetrics(page.access_token, page.instagram_business_account.id);

          const instagramAccount: SocialAccount = {
            id: `ig_${page.instagram_business_account.id}`,
            platform: 'INSTAGRAM',
            accountName: page.name,
            accountHandle: `@${page.name.toLowerCase().replace(/\s+/g, '')}`,
            connectionStatus: ConnectionStatus.CONNECTED,
            accessToken: page.access_token,
            refreshToken: null,
            expiresAt: this.calculateTokenExpiration(),
            lastValidatedAt: new Date(),
            metadata: {
              instagramAccountId: page.instagram_business_account.id,
              connectedFacebookPageId: page.id,
              ...instagramMetrics
            },
            permissions: ['read', 'write'],
            profileImageUrl: `https://graph.facebook.com/${page.instagram_business_account.id}/picture?type=large`,
            createdAt: new Date(),
            updatedAt: new Date()
          }

          accounts.push(instagramAccount)
        }
      }
      } // Close for loop

      console.log(`Created ${accounts.length} accounts:`, accounts.map(acc => ({
        id: acc.id,
        platform: acc.platform,
        name: acc.accountName
      })));

      console.log(`✅ getUserAccounts completed: Found ${accounts.length} accounts`);
      return accounts;
    } catch (error) {
      console.error('❌ Error fetching user accounts:', error);
      console.error('Error details:', error);
      throw error;
    }
  }

  /**
   * Get Facebook page metrics (followers, engagement rate)
   */
  private async getPageMetrics(pageAccessToken: string, pageId: string) {
    try {
      const appSecretProof = this.generateAppSecretProof(pageAccessToken)

      // Get page insights for follower count and engagement
      const insightsResponse = await fetch(
        `${FACEBOOK_BASE_URL}/${pageId}/insights?metric=page_fans,page_post_engagements,page_impressions&period=day&access_token=${pageAccessToken}&appsecret_proof=${appSecretProof}`
      )

      if (insightsResponse.ok) {
        const insightsData = await insightsResponse.json()
        const metrics = insightsData.data || []

        const followers = metrics.find((m: any) => m.name === 'page_fans')?.values?.[0]?.value || 0
        const engagements = metrics.find((m: any) => m.name === 'page_post_engagements')?.values?.[0]?.value || 0
        const impressions = metrics.find((m: any) => m.name === 'page_impressions')?.values?.[0]?.value || 0

        return {
          followerCount: followers,
          engagementRate: followers > 0 ? (engagements / followers) * 100 : 0,
          impressions,
          lastUpdated: new Date().toISOString()
        }
      }
    } catch (error) {
      console.warn('Failed to fetch Facebook page metrics:', error);
    }

    return {
      followerCount: 0,
      engagementRate: 0,
      impressions: 0,
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Get Instagram Business account metrics
   */
  private async getInstagramMetrics(pageAccessToken: string, instagramAccountId: string) {
    try {
      const appSecretProof = this.generateAppSecretProof(pageAccessToken)

      // Get Instagram account info
      const accountResponse = await fetch(
        `${FACEBOOK_BASE_URL}/${instagramAccountId}?fields=username,name,profile_picture_url,followers_count,media_count,biography&access_token=${pageAccessToken}&appsecret_proof=${appSecretProof}`
      )

      if (accountResponse.ok) {
        const accountData = await accountResponse.json()

        // Get Instagram insights (may fail for accounts without sufficient followers)
        let insights = {}
        try {
          const insightsResponse = await fetch(
            `${FACEBOOK_BASE_URL}/${instagramAccountId}/insights?metric=impressions,reach,profile_views&period=day&access_token=${pageAccessToken}&appsecret_proof=${appSecretProof}`
          )

          if (insightsResponse.ok) {
            const insightsData = await insightsResponse.json()
            const metrics = insightsData.data || []

            insights = {
              impressions: metrics.find((m: any) => m.name === 'impressions')?.values?.[0]?.value || 0,
              reach: metrics.find((m: any) => m.name === 'reach')?.values?.[0]?.value || 0,
              profileViews: metrics.find((m: any) => m.name === 'profile_views')?.values?.[0]?.value || 0
            }
          }
        } catch (insightsError) {
          console.warn('Instagram insights not available (may require business account with sufficient followers):', insightsError);
        }

        return {
          username: accountData.username,
          displayName: accountData.name,
          biography: accountData.biography,
          followerCount: accountData.followers_count || 0,
          mediaCount: accountData.media_count || 0,
          profilePictureUrl: accountData.profile_picture_url,
          ...insights,
          lastUpdated: new Date().toISOString()
        }
      }
    } catch (error) {
      console.warn('Failed to fetch Instagram metrics:', error);
    }

    return {
      followerCount: 0,
      mediaCount: 0,
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Get account metrics (followers, engagement, etc.)
   */
  async getAccountMetrics(accountId: string, accessToken: string, platform: SocialPlatform) {
    try {
      const appSecretProof = this.generateAppSecretProof(accessToken)

      if (platform === 'FACEBOOK') {
        // Get Facebook page insights
        const response = await fetch(
          `${FACEBOOK_BASE_URL}/${accountId}/insights?metric=page_fans,page_post_engagements&access_token=${accessToken}&appsecret_proof=${appSecretProof}`
        )

        if (response.ok) {
          const data = await response.json()
          const metrics = data.data || []

          const followers = metrics.find((m: any) => m.name === 'page_fans')?.values?.[0]?.value || 0
          const engagements = metrics.find((m: any) => m.name === 'page_post_engagements')?.values?.[0]?.value || 0

          return {
            followerCount: followers,
            engagementRate: followers > 0 ? (engagements / followers) * 100 : 0,
            postCount: 0 // Would need additional API call
          }
        }
      } else if (platform === 'INSTAGRAM') {
        // Get Instagram insights
        const response = await fetch(
          `${FACEBOOK_BASE_URL}/${accountId}?fields=followers_count,media_count&access_token=${accessToken}&appsecret_proof=${appSecretProof}`
        )

        if (response.ok) {
          const data = await response.json()
          return {
            followerCount: data.followers_count || 0,
            engagementRate: 0, // Would need additional calculation
            postCount: data.media_count || 0
          }
        }
      }

      // Return default metrics if API calls fail
      return {
        followerCount: 0,
        engagementRate: 0,
        postCount: 0
      }
    } catch (error) {
      console.error('Error fetching account metrics:', error);
      return {
        followerCount: 0,
        engagementRate: 0,
        postCount: 0
      }
    }
  }

  /**
   * Extend Facebook access token (convert short-lived to long-lived)
   */
  async extendAccessToken(shortLivedToken: string): Promise<{ access_token: string; expires_in: number } | null> {
    try {
      const response = await fetch(
        `${FACEBOOK_BASE_URL}/oauth/access_token?grant_type=fb_exchange_token&client_id=${this.config.appId}&client_secret=${this.config.appSecret}&fb_exchange_token=${shortLivedToken}`
      )

      if (response.ok) {
        const data = await response.json()
        return {
          access_token: data.access_token,
          expires_in: data.expires_in || 5183944 // ~60 days default
        }
      }

      console.warn('Failed to extend Facebook access token:', await response.text());
      return null;
    } catch (error) {
      console.error('Error extending Facebook access token:', error);
      return null;
    }
  }

  /**
   * Validate Facebook access token
   */
  async validateToken(accessToken: string): Promise<boolean> {
    try {
      const appSecretProof = this.generateAppSecretProof(accessToken)
      const response = await fetch(
        `${FACEBOOK_BASE_URL}/me?access_token=${accessToken}&appsecret_proof=${appSecretProof}`
      )

      return response.ok
    } catch (error) {
      console.error('Error validating Facebook token:', error);
      return false;
    }
  }

  /**
   * Refresh access token using existing long-lived token
   * Facebook allows refreshing long-lived tokens to extend their lifetime
   */
  async refreshAccessToken(currentToken: string): Promise<FacebookTokenResponse> {
    try {
      console.log('🔄 Refreshing Facebook access token...');

      // Use the same endpoint as getLongLivedToken but with current token
      const params = new URLSearchParams({
        grant_type: 'fb_exchange_token',
        client_id: this.config.appId,
        client_secret: this.config.appSecret,
        fb_exchange_token: currentToken
      })

      const response = await fetch(`${FACEBOOK_BASE_URL}/oauth/access_token?${params.toString()}`)

      if (!response.ok) {
        const error = await response.json()
        console.error('❌ Facebook token refresh failed:', error);
        throw new Error(`Facebook token refresh error: ${error.error?.message || 'Unknown error'}`);
      }

      const tokenData = await response.json();
      console.log('✅ Facebook token refreshed successfully');

      return tokenData;
    } catch (error) {
      console.error('❌ Error refreshing Facebook token:', error);
      throw error;
    }
  }

  /**
   * Revoke access token
   */
  async revokeToken(accessToken: string): Promise<boolean> {
    try {
      const appSecretProof = this.generateAppSecretProof(accessToken)
      const response = await fetch(
        `${FACEBOOK_BASE_URL}/me/permissions?access_token=${accessToken}&appsecret_proof=${appSecretProof}`,
        { method: 'DELETE' }
      )

      return response.ok
    } catch (error) {
      console.error('Error revoking Facebook token:', error);
      return false;
    }
  }
}

// Factory function to create Facebook OAuth service
export function createFacebookOAuthService(): FacebookOAuthService {
  // Use FACEBOOK_REDIRECT_URI if available, otherwise construct from base URL
  // Prioritize production URLs over temporary Vercel URLs
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL ||
                 process.env.NEXTAUTH_URL ||
                 'https://app.ewasl.com';

  const redirectUri = process.env.FACEBOOK_REDIRECT_URI || `${baseUrl}/api/oauth/facebook/callback`;

  // Debug logging for environment variables
  console.log('Facebook OAuth Environment Variables:', {
    appId: process.env.FACEBOOK_APP_ID ? 'SET' : 'MISSING',
    appSecret: process.env.FACEBOOK_APP_SECRET ? 'SET' : 'MISSING',
    businessId: process.env.FACEBOOK_BUSINESS_ID ? 'SET' : 'MISSING',
    redirectUri: redirectUri
  });

  const config: FacebookOAuthConfig = {
    appId: process.env.FACEBOOK_APP_ID!,
    appSecret: process.env.FACEBOOK_APP_SECRET!,
    businessId: process.env.FACEBOOK_BUSINESS_ID!,
    redirectUri: redirectUri
  };

  if (!config.appId || !config.appSecret) {
    console.error('Facebook OAuth configuration missing:', {
      appId: config.appId,
      appSecret: config.appSecret ? 'SET' : 'MISSING',
      businessId: config.businessId
    });
    throw new Error('Facebook OAuth configuration is missing');
  }

  return new FacebookOAuthService(config);
}
