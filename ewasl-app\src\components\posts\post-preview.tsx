'use client'

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Facebook, 
  Instagram, 
  Twitter, 
  Linkedin,
  Heart,
  MessageCircle,
  Share,
  MoreHorizontal,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'
import Image from 'next/image'

interface PostPreviewProps {
  content: string
  mediaUrls?: string[]
  selectedPlatforms: string[]
  accounts: Array<{
    id: string
    platform: 'FACEBOOK' | 'INSTAGRAM' | 'TWITTER' | 'LINKEDIN'
    account_name: string
    profile_image_url?: string
  }>
  className?: string
}

// Platform character limits
const PLATFORM_LIMITS = {
  FACEBOOK: 63206,
  INSTAGRAM: 2200,
  TWITTER: 280,
  LINKEDIN: 3000
}

// Platform colors
const PLATFORM_COLORS = {
  FACEBOOK: 'bg-blue-600',
  INSTAGRAM: 'bg-gradient-to-r from-purple-500 to-pink-500',
  TWITTER: 'bg-sky-500',
  LINKEDIN: 'bg-blue-700'
}

// Extract plain text from HTML
const extractPlainText = (html: string): string => {
  const div = document.createElement('div')
  div.innerHTML = html
  return div.textContent || div.innerText || ''
}

// Truncate text based on platform limits
const truncateForPlatform = (text: string, platform: string): { text: string; truncated: boolean } => {
  const limit = PLATFORM_LIMITS[platform as keyof typeof PLATFORM_LIMITS]
  if (text.length <= limit) {
    return { text, truncated: false }
  }
  return { text: text.substring(0, limit - 3) + '...', truncated: true }
}

// Platform-specific preview components
const FacebookPreview = ({ account, content, mediaUrls }: {
  account: any
  content: string
  mediaUrls?: string[]
}) => {
  const plainText = extractPlainText(content)
  const { text, truncated } = truncateForPlatform(plainText, 'FACEBOOK')

  return (
    <div className="bg-white border rounded-lg p-4 max-w-md">
      {/* Header */}
      <div className="flex items-center gap-3 mb-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={account.profile_image_url} />
          <AvatarFallback className="bg-blue-100 text-blue-600">
            {account.account_name.charAt(0)}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <p className="font-semibold text-sm">{account.account_name}</p>
          <p className="text-xs text-gray-500">الآن · 🌍</p>
        </div>
        <MoreHorizontal className="h-5 w-5 text-gray-400" />
      </div>

      {/* Content */}
      <div className="mb-3">
        <p className="text-sm text-right leading-relaxed whitespace-pre-wrap">
          {text}
        </p>
        {truncated && (
          <Badge variant="destructive" className="mt-2 text-xs">
            تم اقتطاع النص ({plainText.length} حرف)
          </Badge>
        )}
      </div>

      {/* Media */}
      {mediaUrls && mediaUrls.length > 0 && (
        <div className="mb-3 rounded-lg overflow-hidden">
          <div className="relative aspect-video bg-gray-100">
            <Image
              src={mediaUrls[0]}
              alt="Post media"
              fill
              className="object-cover"
            />
            {mediaUrls.length > 1 && (
              <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                +{mediaUrls.length - 1}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between pt-2 border-t">
        <div className="flex items-center gap-4">
          <button className="flex items-center gap-1 text-gray-500 hover:text-blue-600">
            <Heart className="h-4 w-4" />
            <span className="text-xs">إعجاب</span>
          </button>
          <button className="flex items-center gap-1 text-gray-500 hover:text-blue-600">
            <MessageCircle className="h-4 w-4" />
            <span className="text-xs">تعليق</span>
          </button>
          <button className="flex items-center gap-1 text-gray-500 hover:text-blue-600">
            <Share className="h-4 w-4" />
            <span className="text-xs">مشاركة</span>
          </button>
        </div>
      </div>
    </div>
  )
}

const InstagramPreview = ({ account, content, mediaUrls }: {
  account: any
  content: string
  mediaUrls?: string[]
}) => {
  const plainText = extractPlainText(content)
  const { text, truncated } = truncateForPlatform(plainText, 'INSTAGRAM')

  return (
    <div className="bg-white border rounded-lg max-w-sm">
      {/* Header */}
      <div className="flex items-center gap-3 p-3">
        <Avatar className="h-8 w-8">
          <AvatarImage src={account.profile_image_url} />
          <AvatarFallback className="bg-pink-100 text-pink-600">
            {account.account_name.charAt(0)}
          </AvatarFallback>
        </Avatar>
        <div className="flex-1">
          <p className="font-semibold text-sm">{account.account_name}</p>
        </div>
        <MoreHorizontal className="h-5 w-5 text-gray-400" />
      </div>

      {/* Media */}
      {mediaUrls && mediaUrls.length > 0 ? (
        <div className="aspect-square bg-gray-100 relative">
          <Image
            src={mediaUrls[0]}
            alt="Post media"
            fill
            className="object-cover"
          />
          {mediaUrls.length > 1 && (
            <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
              +{mediaUrls.length - 1}
            </div>
          )}
        </div>
      ) : (
        <div className="aspect-square bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
          <Instagram className="h-12 w-12 text-gray-400" />
        </div>
      )}

      {/* Actions */}
      <div className="p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-3">
            <Heart className="h-6 w-6" />
            <MessageCircle className="h-6 w-6" />
            <Share className="h-6 w-6" />
          </div>
        </div>

        {/* Content */}
        <div className="text-sm text-right">
          <span className="font-semibold">{account.account_name}</span>
          <span className="mr-2">{text}</span>
        </div>
        {truncated && (
          <Badge variant="destructive" className="mt-2 text-xs">
            تم اقتطاع النص ({plainText.length} حرف)
          </Badge>
        )}
      </div>
    </div>
  )
}

const TwitterPreview = ({ account, content, mediaUrls }: {
  account: any
  content: string
  mediaUrls?: string[]
}) => {
  const plainText = extractPlainText(content)
  const { text, truncated } = truncateForPlatform(plainText, 'TWITTER')

  return (
    <div className="bg-white border rounded-lg p-4 max-w-md">
      {/* Header */}
      <div className="flex items-start gap-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={account.profile_image_url} />
          <AvatarFallback className="bg-sky-100 text-sky-600">
            {account.account_name.charAt(0)}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1">
          <div className="flex items-center gap-1 mb-1">
            <p className="font-semibold text-sm">{account.account_name}</p>
            <span className="text-gray-500 text-sm">@{account.account_name.toLowerCase()}</span>
            <span className="text-gray-500 text-sm">· الآن</span>
          </div>

          {/* Content */}
          <div className="mb-3">
            <p className="text-sm text-right leading-relaxed whitespace-pre-wrap">
              {text}
            </p>
            {truncated && (
              <Badge variant="destructive" className="mt-2 text-xs">
                تم اقتطاع النص ({plainText.length} حرف)
              </Badge>
            )}
          </div>

          {/* Media */}
          {mediaUrls && mediaUrls.length > 0 && (
            <div className="mb-3 rounded-lg overflow-hidden">
              <div className="relative aspect-video bg-gray-100">
                <Image
                  src={mediaUrls[0]}
                  alt="Post media"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between text-gray-500">
            <button className="flex items-center gap-1 hover:text-sky-600">
              <MessageCircle className="h-4 w-4" />
              <span className="text-xs">رد</span>
            </button>
            <button className="flex items-center gap-1 hover:text-green-600">
              <Share className="h-4 w-4" />
              <span className="text-xs">إعادة تغريد</span>
            </button>
            <button className="flex items-center gap-1 hover:text-red-600">
              <Heart className="h-4 w-4" />
              <span className="text-xs">إعجاب</span>
            </button>
            <button className="flex items-center gap-1 hover:text-sky-600">
              <Share className="h-4 w-4" />
              <span className="text-xs">مشاركة</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export function PostPreview({
  content,
  mediaUrls,
  selectedPlatforms,
  accounts,
  className
}: PostPreviewProps) {
  const selectedAccounts = accounts.filter(acc => selectedPlatforms.includes(acc.id))

  if (selectedAccounts.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-right flex items-center gap-2">
            <Eye className="h-5 w-5" />
            معاينة المنشور
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            اختر منصة واحدة على الأقل لمعاينة المنشور
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-right flex items-center gap-2">
          <Eye className="h-5 w-5" />
          معاينة المنشور
        </CardTitle>
        <p className="text-sm text-muted-foreground text-right">
          كيف سيظهر منشورك على المنصات المختارة
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {selectedAccounts.map((account) => (
            <div key={account.id}>
              {/* Platform Header */}
              <div className="flex items-center gap-2 mb-3">
                <div className={cn(
                  'p-1 rounded text-white',
                  PLATFORM_COLORS[account.platform]
                )}>
                  {account.platform === 'FACEBOOK' && <Facebook className="h-4 w-4" />}
                  {account.platform === 'INSTAGRAM' && <Instagram className="h-4 w-4" />}
                  {account.platform === 'TWITTER' && <Twitter className="h-4 w-4" />}
                  {account.platform === 'LINKEDIN' && <Linkedin className="h-4 w-4" />}
                </div>
                <span className="font-medium text-sm">{account.account_name}</span>
                <Badge variant="outline" className="text-xs">
                  {account.platform}
                </Badge>
              </div>

              {/* Platform Preview */}
              <div className="flex justify-center">
                {account.platform === 'FACEBOOK' && (
                  <FacebookPreview account={account} content={content} mediaUrls={mediaUrls} />
                )}
                {account.platform === 'INSTAGRAM' && (
                  <InstagramPreview account={account} content={content} mediaUrls={mediaUrls} />
                )}
                {account.platform === 'TWITTER' && (
                  <TwitterPreview account={account} content={content} mediaUrls={mediaUrls} />
                )}
              </div>

              {selectedAccounts.length > 1 && account.id !== selectedAccounts[selectedAccounts.length - 1].id && (
                <Separator className="mt-6" />
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
