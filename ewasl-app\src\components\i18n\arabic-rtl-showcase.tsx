"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Calendar,
  Clock,
  Globe,
  Heart,
  MessageCircle,
  Share,
  TrendingUp,
  Users,
  Eye,
  BarChart3,
  Settings,
  Info,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  Star,
  Bookmark,
  Send,
  Image,
  Video,
  Mic,
  Camera
} from 'lucide-react';
import { useRTL } from '@/lib/i18n/arabic-rtl-optimizer';
import { cn } from '@/lib/utils';

interface ArabicRTLShowcaseProps {
  language?: 'ar' | 'en';
  onLanguageChange?: (language: 'ar' | 'en') => void;
  className?: string;
}

export function ArabicRTLShowcase({
  language = 'ar',
  onLanguageChange,
  className
}: ArabicRTLShowcaseProps) {
  const [selectedTab, setSelectedTab] = useState('typography');
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    platform: '',
    date: '',
    time: ''
  });

  // Use RTL optimization hook
  const rtl = useRTL(language);

  // Sample data for demonstrations
  const sampleMetrics = {
    posts: 156,
    engagement: 12847,
    reach: 45623,
    followers: 2341,
    growth: 18.5
  };

  const samplePosts = [
    {
      id: 1,
      content: language === 'ar'
        ? 'هذا مثال على منشور باللغة العربية مع دعم كامل للكتابة من اليمين إلى اليسار. يتضمن المنشور تنسيقاً مثالياً للنصوص العربية.'
        : 'This is an example of an English post with proper left-to-right text formatting and layout optimization.',
      author: language === 'ar' ? 'أحمد محمد' : 'Ahmed Mohammed',
      time: new Date(Date.now() - 2 * 60 * 60 * 1000),
      likes: 234,
      comments: 45,
      shares: 12
    },
    {
      id: 2,
      content: language === 'ar'
        ? 'منشور آخر يوضح كيفية التعامل مع النصوص الطويلة والتنسيق المتقدم في اللغة العربية مع الحفاظ على جودة القراءة.'
        : 'Another post demonstrating how to handle long texts and advanced formatting in English while maintaining readability.',
      author: language === 'ar' ? 'فاطمة أحمد' : 'Fatima Ahmed',
      time: new Date(Date.now() - 5 * 60 * 60 * 1000),
      likes: 189,
      comments: 23,
      shares: 8
    }
  ];

  return (
    <div className={cn(
      "space-y-6",
      language === 'ar' ? "rtl" : "ltr",
      className
    )} dir={rtl.direction}>
      {/* Header */}
      <div className={rtl.cn(
        "flex items-center justify-between",
        rtl.flex()
      )}>
        <div className={rtl.textAlign()}>
          <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
            {rtl.t('settings.title')} - {language === 'ar' ? 'عرض RTL' : 'RTL Showcase'}
          </h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar'
              ? 'عرض شامل لتحسينات اللغة العربية ودعم الكتابة من اليمين إلى اليسار'
              : 'Comprehensive showcase of Arabic language optimizations and RTL support'
            }
          </p>
        </div>

        <div className={rtl.cn(
          "flex items-center gap-4",
          rtl.flex()
        )}>
          <Button
            variant="outline"
            onClick={() => onLanguageChange?.(language === 'ar' ? 'en' : 'ar')}
            className={rtl.cn(
              "flex items-center gap-2",
              rtl.flex()
            )}
          >
            <Globe className="w-4 h-4" />
            {language === 'ar' ? 'English' : 'العربية'}
          </Button>
        </div>
      </div>

      {/* Language Info Alert */}
      <Alert className="border-blue-200 bg-blue-50">
        <Info className="h-4 w-4 text-blue-600" />
        <AlertTitle className={rtl.cn(
          "text-blue-900",
          rtl.textAlign()
        )}>
          {language === 'ar' ? 'معلومات اللغة' : 'Language Information'}
        </AlertTitle>
        <AlertDescription className={rtl.cn(
          "text-blue-800",
          rtl.textAlign()
        )}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
            <div className={rtl.cn(
              "flex items-center gap-2",
              rtl.flex()
            )}>
              <span className="font-medium">
                {language === 'ar' ? 'الاتجاه:' : 'Direction:'}
              </span>
              <Badge variant="secondary">{rtl.direction.toUpperCase()}</Badge>
            </div>
            <div className={rtl.cn(
              "flex items-center gap-2",
              rtl.flex()
            )}>
              <span className="font-medium">
                {language === 'ar' ? 'الخط:' : 'Font:'}
              </span>
              <Badge variant="secondary">
                {language === 'ar' ? 'عربي محسن' : 'Optimized'}
              </Badge>
            </div>
            <div className={rtl.cn(
              "flex items-center gap-2",
              rtl.flex()
            )}>
              <span className="font-medium">
                {language === 'ar' ? 'الأرقام:' : 'Numbers:'}
              </span>
              <Badge variant="secondary">
                {rtl.formatNumber(12345)}
              </Badge>
            </div>
          </div>
        </AlertDescription>
      </Alert>

      {/* RTL Demonstration Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="typography">
            {language === 'ar' ? 'الطباعة' : 'Typography'}
          </TabsTrigger>
          <TabsTrigger value="layout">
            {language === 'ar' ? 'التخطيط' : 'Layout'}
          </TabsTrigger>
          <TabsTrigger value="forms">
            {language === 'ar' ? 'النماذج' : 'Forms'}
          </TabsTrigger>
          <TabsTrigger value="data">
            {language === 'ar' ? 'البيانات' : 'Data'}
          </TabsTrigger>
          <TabsTrigger value="content">
            {language === 'ar' ? 'المحتوى' : 'Content'}
          </TabsTrigger>
        </TabsList>

        {/* Typography Tab */}
        <TabsContent value="typography" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {language === 'ar' ? 'عينات الطباعة' : 'Typography Samples'}
              </CardTitle>
              <CardDescription className={rtl.textAlign()}>
                {language === 'ar'
                  ? 'أمثلة على أحجام وأنواع الخطوط المحسنة للغة العربية'
                  : 'Examples of optimized font sizes and types for Arabic language'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Headings */}
              <div className="space-y-4">
                <h1 className={rtl.cn("text-4xl font-bold", rtl.textAlign())} style={{ fontFamily: rtl.getFontFamily('heading') }}>
                  {language === 'ar' ? 'عنوان رئيسي كبير' : 'Large Main Heading'}
                </h1>
                <h2 className={rtl.cn("text-3xl font-semibold", rtl.textAlign())} style={{ fontFamily: rtl.getFontFamily('heading') }}>
                  {language === 'ar' ? 'عنوان فرعي متوسط' : 'Medium Sub Heading'}
                </h2>
                <h3 className={rtl.cn("text-2xl font-medium", rtl.textAlign())} style={{ fontFamily: rtl.getFontFamily('heading') }}>
                  {language === 'ar' ? 'عنوان صغير' : 'Small Heading'}
                </h3>
              </div>

              {/* Body Text */}
              <div className="space-y-4">
                <p className={rtl.cn("text-lg leading-relaxed", rtl.textAlign())} style={{ fontFamily: rtl.getFontFamily('primary') }}>
                  {language === 'ar'
                    ? 'هذا نص تجريبي باللغة العربية يوضح كيفية عرض النصوص الطويلة مع التنسيق المناسب والمسافات الصحيحة بين الكلمات والأسطر. يتم استخدام خطوط محسنة خصيصاً للغة العربية لضمان أفضل تجربة قراءة.'
                    : 'This is sample English text demonstrating how long texts are displayed with proper formatting and correct spacing between words and lines. Optimized fonts are used specifically for the best reading experience.'
                  }
                </p>
                <p className={rtl.cn("text-base text-gray-600", rtl.textAlign())} style={{ fontFamily: rtl.getFontFamily('primary') }}>
                  {language === 'ar'
                    ? 'نص إضافي بحجم أصغر ولون رمادي لإظهار التدرج في أهمية المحتوى والتسلسل الهرمي للمعلومات.'
                    : 'Additional text in smaller size and gray color to show content importance gradient and information hierarchy.'
                  }
                </p>
              </div>

              {/* Numbers and Dates */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                  <h4 className={rtl.cn("font-semibold mb-2", rtl.textAlign())}>
                    {language === 'ar' ? 'الأرقام والعملات' : 'Numbers and Currency'}
                  </h4>
                  <div className="space-y-2">
                    <div className={rtl.cn("flex justify-between", rtl.flex())}>
                      <span>{language === 'ar' ? 'رقم عادي:' : 'Regular number:'}</span>
                      <span className="font-mono">{rtl.formatNumber(12345)}</span>
                    </div>
                    <div className={rtl.cn("flex justify-between", rtl.flex())}>
                      <span>{language === 'ar' ? 'نسبة مئوية:' : 'Percentage:'}</span>
                      <span className="font-mono">{rtl.formatPercentage(85.7)}</span>
                    </div>
                    <div className={rtl.cn("flex justify-between", rtl.flex())}>
                      <span>{language === 'ar' ? 'عملة:' : 'Currency:'}</span>
                      <span className="font-mono">{rtl.formatCurrency(1250.50)}</span>
                    </div>
                    <div className={rtl.cn("flex justify-between", rtl.flex())}>
                      <span>{language === 'ar' ? 'رقم مختصر:' : 'Compact:'}</span>
                      <span className="font-mono">{rtl.formatCompactNumber(1250000)}</span>
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <h4 className={rtl.cn("font-semibold mb-2", rtl.textAlign())}>
                    {language === 'ar' ? 'التواريخ والأوقات' : 'Dates and Times'}
                  </h4>
                  <div className="space-y-2">
                    <div className={rtl.cn("flex justify-between", rtl.flex())}>
                      <span>{language === 'ar' ? 'تاريخ كامل:' : 'Full date:'}</span>
                      <span>{rtl.formatDate(new Date())}</span>
                    </div>
                    <div className={rtl.cn("flex justify-between", rtl.flex())}>
                      <span>{language === 'ar' ? 'وقت نسبي:' : 'Relative time:'}</span>
                      <span>{rtl.formatRelativeTime(new Date(Date.now() - 2 * 60 * 60 * 1000))}</span>
                    </div>
                    <div className={rtl.cn("flex justify-between", rtl.flex())}>
                      <span>{language === 'ar' ? 'وقت فقط:' : 'Time only:'}</span>
                      <span>{rtl.formatTime(new Date())}</span>
                    </div>
                  </div>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Layout Tab */}
        <TabsContent value="layout" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {language === 'ar' ? 'تخطيط RTL' : 'RTL Layout'}
              </CardTitle>
              <CardDescription className={rtl.textAlign()}>
                {language === 'ar'
                  ? 'أمثلة على التخطيطات المحسنة للكتابة من اليمين إلى اليسار'
                  : 'Examples of layouts optimized for right-to-left writing'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Flex Layout Examples */}
              <div className="space-y-4">
                <h4 className={rtl.cn("font-semibold", rtl.textAlign())}>
                  {language === 'ar' ? 'تخطيطات مرنة' : 'Flex Layouts'}
                </h4>

                {/* Navigation-style layout */}
                <Card className="p-4">
                  <div className={rtl.cn("flex items-center justify-between", rtl.flex())}>
                    <div className={rtl.cn("flex items-center gap-3", rtl.flex())}>
                      <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <BarChart3 className="w-4 h-4 text-white" />
                      </div>
                      <span className="font-medium">
                        {language === 'ar' ? 'عنصر التنقل' : 'Navigation Item'}
                      </span>
                    </div>
                    <Badge variant="secondary">
                      {language === 'ar' ? 'جديد' : 'New'}
                    </Badge>
                  </div>
                </Card>

                {/* Button group */}
                <Card className="p-4">
                  <div className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                    <Button variant="default">
                      {language === 'ar' ? 'الأساسي' : 'Primary'}
                    </Button>
                    <Button variant="outline">
                      {language === 'ar' ? 'ثانوي' : 'Secondary'}
                    </Button>
                    <Button variant="ghost">
                      {language === 'ar' ? 'شفاف' : 'Ghost'}
                    </Button>
                  </div>
                </Card>

                {/* Icon with text */}
                <Card className="p-4">
                  <div className={rtl.cn("flex items-center gap-4", rtl.flex())}>
                    <Heart className="w-5 h-5 text-red-500" />
                    <div className={rtl.textAlign()}>
                      <p className="font-medium">
                        {language === 'ar' ? 'عنوان العنصر' : 'Item Title'}
                      </p>
                      <p className="text-sm text-gray-600">
                        {language === 'ar' ? 'وصف مختصر للعنصر' : 'Brief item description'}
                      </p>
                    </div>
                    <div className={rtl.cn("ml-auto", language === 'ar' ? "mr-auto ml-0" : "")}>
                      <ArrowLeft className={rtl.cn("w-4 h-4 text-gray-400", language === 'ar' ? "rotate-180" : "")} />
                    </div>
                  </div>
                </Card>
              </div>

              {/* Grid Layout Examples */}
              <div className="space-y-4">
                <h4 className={rtl.cn("font-semibold", rtl.textAlign())}>
                  {language === 'ar' ? 'تخطيطات الشبكة' : 'Grid Layouts'}
                </h4>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[1, 2, 3].map((item) => (
                    <Card key={item} className="p-4">
                      <div className={rtl.cn("flex items-center gap-3 mb-3", rtl.flex())}>
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                          <Star className="w-5 h-5 text-white" />
                        </div>
                        <div className={rtl.textAlign()}>
                          <h5 className="font-medium">
                            {language === 'ar' ? `عنصر ${item}` : `Item ${item}`}
                          </h5>
                          <p className="text-xs text-gray-500">
                            {language === 'ar' ? 'وصف قصير' : 'Short description'}
                          </p>
                        </div>
                      </div>
                      <p className={rtl.cn("text-sm text-gray-600", rtl.textAlign())}>
                        {language === 'ar'
                          ? 'محتوى تجريبي لإظهار كيفية ترتيب العناصر في الشبكة.'
                          : 'Sample content to show how elements are arranged in the grid.'
                        }
                      </p>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Forms Tab */}
        <TabsContent value="forms" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {language === 'ar' ? 'النماذج المحسنة' : 'Optimized Forms'}
              </CardTitle>
              <CardDescription className={rtl.textAlign()}>
                {language === 'ar'
                  ? 'نماذج محسنة للغة العربية مع دعم RTL كامل'
                  : 'Forms optimized for Arabic language with full RTL support'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Form Fields */}
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="title" className={rtl.textAlign()}>
                      {language === 'ar' ? 'عنوان المنشور' : 'Post Title'}
                    </Label>
                    <Input
                      id="title"
                      placeholder={language === 'ar' ? 'أدخل عنوان المنشور...' : 'Enter post title...'}
                      value={formData.title}
                      onChange={(e) => setFormData({...formData, title: e.target.value})}
                      className={rtl.textAlign()}
                      dir={rtl.direction}
                    />
                  </div>

                  <div>
                    <Label htmlFor="content" className={rtl.textAlign()}>
                      {language === 'ar' ? 'محتوى المنشور' : 'Post Content'}
                    </Label>
                    <Textarea
                      id="content"
                      placeholder={language === 'ar' ? 'اكتب محتوى المنشور هنا...' : 'Write your post content here...'}
                      value={formData.content}
                      onChange={(e) => setFormData({...formData, content: e.target.value})}
                      className={rtl.cn("min-h-[100px]", rtl.textAlign())}
                      dir={rtl.direction}
                    />
                  </div>

                  <div>
                    <Label htmlFor="platform" className={rtl.textAlign()}>
                      {language === 'ar' ? 'المنصة' : 'Platform'}
                    </Label>
                    <Select value={formData.platform} onValueChange={(value) => setFormData({...formData, platform: value})}>
                      <SelectTrigger className={rtl.textAlign()}>
                        <SelectValue placeholder={language === 'ar' ? 'اختر المنصة' : 'Select platform'} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="instagram">
                          {language === 'ar' ? 'إنستغرام' : 'Instagram'}
                        </SelectItem>
                        <SelectItem value="facebook">
                          {language === 'ar' ? 'فيسبوك' : 'Facebook'}
                        </SelectItem>
                        <SelectItem value="twitter">
                          {language === 'ar' ? 'تويتر' : 'Twitter'}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="date" className={rtl.textAlign()}>
                        {language === 'ar' ? 'التاريخ' : 'Date'}
                      </Label>
                      <Input
                        id="date"
                        type="date"
                        value={formData.date}
                        onChange={(e) => setFormData({...formData, date: e.target.value})}
                        className={rtl.textAlign()}
                      />
                    </div>
                    <div>
                      <Label htmlFor="time" className={rtl.textAlign()}>
                        {language === 'ar' ? 'الوقت' : 'Time'}
                      </Label>
                      <Input
                        id="time"
                        type="time"
                        value={formData.time}
                        onChange={(e) => setFormData({...formData, time: e.target.value})}
                        className={rtl.textAlign()}
                      />
                    </div>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="space-y-4">
                  <h4 className={rtl.cn("font-semibold", rtl.textAlign())}>
                    {language === 'ar' ? 'إجراءات النموذج' : 'Form Actions'}
                  </h4>

                  <div className="space-y-3">
                    <Button className={rtl.cn("w-full flex items-center gap-2", rtl.flex())}>
                      <Send className="w-4 h-4" />
                      {language === 'ar' ? 'نشر الآن' : 'Publish Now'}
                    </Button>

                    <Button variant="outline" className={rtl.cn("w-full flex items-center gap-2", rtl.flex())}>
                      <Clock className="w-4 h-4" />
                      {language === 'ar' ? 'جدولة النشر' : 'Schedule Post'}
                    </Button>

                    <Button variant="ghost" className={rtl.cn("w-full flex items-center gap-2", rtl.flex())}>
                      <Bookmark className="w-4 h-4" />
                      {language === 'ar' ? 'حفظ كمسودة' : 'Save as Draft'}
                    </Button>
                  </div>

                  {/* Media Upload Buttons */}
                  <div className="space-y-2">
                    <h5 className={rtl.cn("text-sm font-medium", rtl.textAlign())}>
                      {language === 'ar' ? 'إضافة وسائط' : 'Add Media'}
                    </h5>
                    <div className="grid grid-cols-3 gap-2">
                      <Button variant="outline" size="sm" className={rtl.cn("flex items-center gap-1", rtl.flex())}>
                        <Image className="w-3 h-3" />
                        {language === 'ar' ? 'صورة' : 'Image'}
                      </Button>
                      <Button variant="outline" size="sm" className={rtl.cn("flex items-center gap-1", rtl.flex())}>
                        <Video className="w-3 h-3" />
                        {language === 'ar' ? 'فيديو' : 'Video'}
                      </Button>
                      <Button variant="outline" size="sm" className={rtl.cn("flex items-center gap-1", rtl.flex())}>
                        <Mic className="w-3 h-3" />
                        {language === 'ar' ? 'صوت' : 'Audio'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Data Tab */}
        <TabsContent value="data" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {language === 'ar' ? 'عرض البيانات' : 'Data Display'}
              </CardTitle>
              <CardDescription className={rtl.textAlign()}>
                {language === 'ar'
                  ? 'أمثلة على عرض البيانات والإحصائيات مع دعم RTL'
                  : 'Examples of data and statistics display with RTL support'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Metrics Cards */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card className="p-4">
                  <div className={rtl.cn("flex items-center justify-between", rtl.flex())}>
                    <div className={rtl.textAlign()}>
                      <p className="text-2xl font-bold">{rtl.formatCompactNumber(sampleMetrics.posts)}</p>
                      <p className="text-sm text-gray-600">{rtl.t('analytics.metrics.totalPosts')}</p>
                    </div>
                    <BarChart3 className="w-8 h-8 text-blue-500" />
                  </div>
                </Card>

                <Card className="p-4">
                  <div className={rtl.cn("flex items-center justify-between", rtl.flex())}>
                    <div className={rtl.textAlign()}>
                      <p className="text-2xl font-bold">{rtl.formatCompactNumber(sampleMetrics.engagement)}</p>
                      <p className="text-sm text-gray-600">{rtl.t('analytics.engagement')}</p>
                    </div>
                    <Heart className="w-8 h-8 text-red-500" />
                  </div>
                </Card>

                <Card className="p-4">
                  <div className={rtl.cn("flex items-center justify-between", rtl.flex())}>
                    <div className={rtl.textAlign()}>
                      <p className="text-2xl font-bold">{rtl.formatCompactNumber(sampleMetrics.reach)}</p>
                      <p className="text-sm text-gray-600">{rtl.t('analytics.reach')}</p>
                    </div>
                    <Users className="w-8 h-8 text-green-500" />
                  </div>
                </Card>

                <Card className="p-4">
                  <div className={rtl.cn("flex items-center justify-between", rtl.flex())}>
                    <div className={rtl.textAlign()}>
                      <p className="text-2xl font-bold">{rtl.formatPercentage(sampleMetrics.growth)}</p>
                      <p className="text-sm text-gray-600">{rtl.t('analytics.growth')}</p>
                    </div>
                    <TrendingUp className="w-8 h-8 text-purple-500" />
                  </div>
                </Card>
              </div>

              {/* Progress Bars */}
              <div className="space-y-4">
                <h4 className={rtl.cn("font-semibold", rtl.textAlign())}>
                  {language === 'ar' ? 'مؤشرات التقدم' : 'Progress Indicators'}
                </h4>

                <div className="space-y-3">
                  <div>
                    <div className={rtl.cn("flex justify-between text-sm mb-1", rtl.flex())}>
                      <span>{rtl.t('analytics.engagement')}</span>
                      <span>{rtl.formatPercentage(85)}</span>
                    </div>
                    <Progress value={85} className="h-2" />
                  </div>

                  <div>
                    <div className={rtl.cn("flex justify-between text-sm mb-1", rtl.flex())}>
                      <span>{rtl.t('analytics.reach')}</span>
                      <span>{rtl.formatPercentage(72)}</span>
                    </div>
                    <Progress value={72} className="h-2" />
                  </div>

                  <div>
                    <div className={rtl.cn("flex justify-between text-sm mb-1", rtl.flex())}>
                      <span>{rtl.t('analytics.growth')}</span>
                      <span>{rtl.formatPercentage(94)}</span>
                    </div>
                    <Progress value={94} className="h-2" />
                  </div>
                </div>
              </div>

              {/* Data Table */}
              <div className="space-y-4">
                <h4 className={rtl.cn("font-semibold", rtl.textAlign())}>
                  {language === 'ar' ? 'جدول البيانات' : 'Data Table'}
                </h4>

                <Card>
                  <CardContent className="p-0">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead className="border-b">
                          <tr>
                            <th className={rtl.cn("p-3 text-sm font-medium", rtl.textAlign())}>
                              {language === 'ar' ? 'المنصة' : 'Platform'}
                            </th>
                            <th className={rtl.cn("p-3 text-sm font-medium", rtl.textAlign())}>
                              {rtl.t('analytics.metrics.totalPosts')}
                            </th>
                            <th className={rtl.cn("p-3 text-sm font-medium", rtl.textAlign())}>
                              {rtl.t('analytics.engagement')}
                            </th>
                            <th className={rtl.cn("p-3 text-sm font-medium", rtl.textAlign())}>
                              {rtl.t('analytics.reach')}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {[
                            { platform: 'Instagram', posts: 45, engagement: 5234, reach: 18567 },
                            { platform: 'Facebook', posts: 38, engagement: 3892, reach: 15234 },
                            { platform: 'Twitter', posts: 42, engagement: 2456, reach: 8934 },
                            { platform: 'LinkedIn', posts: 31, engagement: 1265, reach: 2888 }
                          ].map((row, index) => (
                            <tr key={index} className="border-b last:border-b-0">
                              <td className={rtl.cn("p-3", rtl.textAlign())}>
                                <div className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                                  <span className="text-lg">
                                    {row.platform === 'Instagram' ? '📷' :
                                     row.platform === 'Facebook' ? '👥' :
                                     row.platform === 'Twitter' ? '🐦' : '💼'}
                                  </span>
                                  {row.platform}
                                </div>
                              </td>
                              <td className={rtl.cn("p-3", rtl.textAlign())}>{rtl.formatNumber(row.posts)}</td>
                              <td className={rtl.cn("p-3", rtl.textAlign())}>{rtl.formatCompactNumber(row.engagement)}</td>
                              <td className={rtl.cn("p-3", rtl.textAlign())}>{rtl.formatCompactNumber(row.reach)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Content Tab */}
        <TabsContent value="content" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {language === 'ar' ? 'عرض المحتوى' : 'Content Display'}
              </CardTitle>
              <CardDescription className={rtl.textAlign()}>
                {language === 'ar'
                  ? 'أمثلة على عرض المحتوى والمنشورات مع دعم RTL كامل'
                  : 'Examples of content and post display with full RTL support'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Sample Posts */}
              <div className="space-y-4">
                {samplePosts.map((post) => (
                  <Card key={post.id} className="p-4">
                    <div className="space-y-4">
                      {/* Post Header */}
                      <div className={rtl.cn("flex items-center justify-between", rtl.flex())}>
                        <div className={rtl.cn("flex items-center gap-3", rtl.flex())}>
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                            <span className="text-white font-medium text-sm">
                              {post.author.charAt(0)}
                            </span>
                          </div>
                          <div className={rtl.textAlign()}>
                            <p className="font-medium">{post.author}</p>
                            <p className="text-sm text-gray-500">
                              {rtl.formatRelativeTime(post.time)}
                            </p>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Settings className="w-4 h-4" />
                        </Button>
                      </div>

                      {/* Post Content */}
                      <div className={rtl.textAlign()}>
                        <p className="text-gray-900 leading-relaxed" style={{ fontFamily: rtl.getFontFamily('primary') }}>
                          {post.content}
                        </p>
                      </div>

                      {/* Post Actions */}
                      <div className={rtl.cn("flex items-center justify-between pt-3 border-t", rtl.flex())}>
                        <div className={rtl.cn("flex items-center gap-6", rtl.flex())}>
                          <button className={rtl.cn("flex items-center gap-2 text-gray-600 hover:text-red-500 transition-colors", rtl.flex())}>
                            <Heart className="w-4 h-4" />
                            <span className="text-sm">{rtl.formatCompactNumber(post.likes)}</span>
                          </button>
                          <button className={rtl.cn("flex items-center gap-2 text-gray-600 hover:text-blue-500 transition-colors", rtl.flex())}>
                            <MessageCircle className="w-4 h-4" />
                            <span className="text-sm">{rtl.formatCompactNumber(post.comments)}</span>
                          </button>
                          <button className={rtl.cn("flex items-center gap-2 text-gray-600 hover:text-green-500 transition-colors", rtl.flex())}>
                            <Share className="w-4 h-4" />
                            <span className="text-sm">{rtl.formatCompactNumber(post.shares)}</span>
                          </button>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Bookmark className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              {/* Content Summary */}
              <Card className="p-4 bg-gradient-to-r from-blue-50 to-purple-50">
                <div className={rtl.cn("flex items-center gap-4", rtl.flex())}>
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                  <div className={rtl.textAlign()}>
                    <h4 className="font-semibold text-gray-900">
                      {language === 'ar' ? 'تحسين RTL مكتمل' : 'RTL Optimization Complete'}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {language === 'ar'
                        ? 'جميع العناصر محسنة للكتابة من اليمين إلى اليسار مع دعم كامل للغة العربية'
                        : 'All elements are optimized for right-to-left writing with full Arabic language support'
                      }
                    </p>
                  </div>
                </div>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}