"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import {
  Users,
  UserPlus,
  Settings,
  Shield,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Crown,
  Star,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  MessageSquare,
  Calendar,
  BarChart3,
  Briefcase,
  Mail,
  Phone,
  Globe,
  Filter,
  Search,
  RefreshCw,
  Plus,
  Minus,
  ArrowUpDown,
  FileText,
  Send,
  ThumbsUp,
  ThumbsDown,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRTL } from '@/lib/i18n/arabic-rtl-optimizer';
import { toast } from 'sonner';

interface TeamMember {
  id: string;
  userId: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'editor' | 'reviewer' | 'viewer';
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  permissions: {
    canCreatePosts: boolean;
    canEditPosts: boolean;
    canDeletePosts: boolean;
    canManageTeam: boolean;
    canViewAnalytics: boolean;
    canApproveContent: boolean;
    canManageWorkspaces: boolean;
  };
  joinedAt: Date;
  lastActive?: Date;
  invitedBy?: string;
  workspaces: string[];
}

interface Workspace {
  id: string;
  name: string;
  description?: string;
  color: string;
  memberCount: number;
  postCount: number;
  pendingApprovals: number;
  createdAt: Date;
  isActive: boolean;
}

interface ApprovalWorkflow {
  id: string;
  name: string;
  description?: string;
  steps: {
    id: string;
    name: string;
    approvers: string[];
    requiredApprovals: number;
    autoApprove: boolean;
  }[];
  isActive: boolean;
  workspaceId?: string;
}

interface TeamCollaborationManagerProps {
  language?: 'ar' | 'en';
  className?: string;
  organizationId: string;
  currentUserId: string;
  currentUserRole: string;
  onMemberUpdate?: (member: TeamMember) => void;
  onWorkspaceUpdate?: (workspace: Workspace) => void;
}

export function TeamCollaborationManager({
  language = 'ar',
  className,
  organizationId,
  currentUserId,
  currentUserRole,
  onMemberUpdate,
  onWorkspaceUpdate
}: TeamCollaborationManagerProps) {
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [approvalWorkflows, setApprovalWorkflows] = useState<ApprovalWorkflow[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showInviteDialog, setShowInviteDialog] = useState(false);
  const [showWorkspaceDialog, setShowWorkspaceDialog] = useState(false);
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const rtl = useRTL(language);

  // Translations
  const t = {
    ar: {
      title: 'إدارة التعاون الجماعي',
      subtitle: 'إدارة أعضاء الفريق والأذونات وسير العمل التعاوني',
      tabs: {
        members: 'أعضاء الفريق',
        workspaces: 'مساحات العمل',
        approvals: 'سير الموافقات',
        permissions: 'الأذونات',
        activity: 'النشاط'
      },
      roles: {
        owner: 'المالك',
        admin: 'مدير',
        editor: 'محرر',
        reviewer: 'مراجع',
        viewer: 'مشاهد'
      },
      status: {
        active: 'نشط',
        inactive: 'غير نشط',
        pending: 'في الانتظار',
        suspended: 'معلق'
      },
      permissions: {
        canCreatePosts: 'إنشاء المنشورات',
        canEditPosts: 'تعديل المنشورات',
        canDeletePosts: 'حذف المنشورات',
        canManageTeam: 'إدارة الفريق',
        canViewAnalytics: 'عرض التحليلات',
        canApproveContent: 'الموافقة على المحتوى',
        canManageWorkspaces: 'إدارة مساحات العمل'
      },
      actions: {
        invite: 'دعوة عضو',
        remove: 'إزالة',
        edit: 'تعديل',
        suspend: 'تعليق',
        activate: 'تفعيل',
        createWorkspace: 'إنشاء مساحة عمل',
        createApproval: 'إنشاء سير موافقة',
        search: 'بحث',
        filter: 'تصفية',
        save: 'حفظ',
        cancel: 'إلغاء',
        send: 'إرسال',
        approve: 'موافقة',
        reject: 'رفض'
      },
      messages: {
        memberInvited: 'تم إرسال الدعوة بنجاح',
        memberRemoved: 'تم إزالة العضو',
        memberUpdated: 'تم تحديث العضو',
        workspaceCreated: 'تم إنشاء مساحة العمل',
        approvalCreated: 'تم إنشاء سير الموافقة',
        noPermission: 'ليس لديك صلاحية لهذا الإجراء',
        confirmRemove: 'هل أنت متأكد من إزالة هذا العضو؟'
      }
    },
    en: {
      title: 'Team Collaboration Manager',
      subtitle: 'Manage team members, permissions, and collaborative workflows',
      tabs: {
        members: 'Team Members',
        workspaces: 'Workspaces',
        approvals: 'Approval Workflows',
        permissions: 'Permissions',
        activity: 'Activity'
      },
      roles: {
        owner: 'Owner',
        admin: 'Admin',
        editor: 'Editor',
        reviewer: 'Reviewer',
        viewer: 'Viewer'
      },
      status: {
        active: 'Active',
        inactive: 'Inactive',
        pending: 'Pending',
        suspended: 'Suspended'
      },
      permissions: {
        canCreatePosts: 'Create Posts',
        canEditPosts: 'Edit Posts',
        canDeletePosts: 'Delete Posts',
        canManageTeam: 'Manage Team',
        canViewAnalytics: 'View Analytics',
        canApproveContent: 'Approve Content',
        canManageWorkspaces: 'Manage Workspaces'
      },
      actions: {
        invite: 'Invite Member',
        remove: 'Remove',
        edit: 'Edit',
        suspend: 'Suspend',
        activate: 'Activate',
        createWorkspace: 'Create Workspace',
        createApproval: 'Create Approval Flow',
        search: 'Search',
        filter: 'Filter',
        save: 'Save',
        cancel: 'Cancel',
        send: 'Send',
        approve: 'Approve',
        reject: 'Reject'
      },
      messages: {
        memberInvited: 'Invitation sent successfully',
        memberRemoved: 'Member removed',
        memberUpdated: 'Member updated',
        workspaceCreated: 'Workspace created',
        approvalCreated: 'Approval workflow created',
        noPermission: 'You do not have permission for this action',
        confirmRemove: 'Are you sure you want to remove this member?'
      }
    }
  };

  const text = t[language];

  // Generate demo data
  const generateDemoData = () => {
    const demoMembers: TeamMember[] = [
      {
        id: '1',
        userId: currentUserId,
        name: language === 'ar' ? 'أحمد محمد' : 'Ahmed Mohamed',
        email: '<EMAIL>',
        avatar: 'https://via.placeholder.com/40x40/3b82f6/ffffff?text=AM',
        role: 'owner',
        status: 'active',
        permissions: {
          canCreatePosts: true,
          canEditPosts: true,
          canDeletePosts: true,
          canManageTeam: true,
          canViewAnalytics: true,
          canApproveContent: true,
          canManageWorkspaces: true
        },
        joinedAt: new Date('2024-01-01'),
        lastActive: new Date(),
        workspaces: ['workspace-1', 'workspace-2']
      },
      {
        id: '2',
        userId: 'user-2',
        name: language === 'ar' ? 'فاطمة علي' : 'Fatima Ali',
        email: '<EMAIL>',
        avatar: 'https://via.placeholder.com/40x40/10b981/ffffff?text=FA',
        role: 'admin',
        status: 'active',
        permissions: {
          canCreatePosts: true,
          canEditPosts: true,
          canDeletePosts: true,
          canManageTeam: true,
          canViewAnalytics: true,
          canApproveContent: true,
          canManageWorkspaces: false
        },
        joinedAt: new Date('2024-01-15'),
        lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000),
        invitedBy: currentUserId,
        workspaces: ['workspace-1']
      },
      {
        id: '3',
        userId: 'user-3',
        name: language === 'ar' ? 'محمد سالم' : 'Mohamed Salem',
        email: '<EMAIL>',
        avatar: 'https://via.placeholder.com/40x40/f59e0b/ffffff?text=MS',
        role: 'editor',
        status: 'active',
        permissions: {
          canCreatePosts: true,
          canEditPosts: true,
          canDeletePosts: false,
          canManageTeam: false,
          canViewAnalytics: true,
          canApproveContent: false,
          canManageWorkspaces: false
        },
        joinedAt: new Date('2024-02-01'),
        lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000),
        invitedBy: currentUserId,
        workspaces: ['workspace-2']
      },
      {
        id: '4',
        userId: 'user-4',
        name: language === 'ar' ? 'سارة أحمد' : 'Sara Ahmed',
        email: '<EMAIL>',
        role: 'reviewer',
        status: 'pending',
        permissions: {
          canCreatePosts: false,
          canEditPosts: false,
          canDeletePosts: false,
          canManageTeam: false,
          canViewAnalytics: true,
          canApproveContent: true,
          canManageWorkspaces: false
        },
        joinedAt: new Date('2024-02-15'),
        invitedBy: 'user-2',
        workspaces: []
      }
    ];

    const demoWorkspaces: Workspace[] = [
      {
        id: 'workspace-1',
        name: language === 'ar' ? 'التسويق الرقمي' : 'Digital Marketing',
        description: language === 'ar' ? 'مساحة عمل للحملات التسويقية' : 'Workspace for marketing campaigns',
        color: '#3b82f6',
        memberCount: 3,
        postCount: 45,
        pendingApprovals: 2,
        createdAt: new Date('2024-01-01'),
        isActive: true
      },
      {
        id: 'workspace-2',
        name: language === 'ar' ? 'المحتوى الإبداعي' : 'Creative Content',
        description: language === 'ar' ? 'مساحة عمل للمحتوى الإبداعي' : 'Workspace for creative content',
        color: '#10b981',
        memberCount: 2,
        postCount: 28,
        pendingApprovals: 1,
        createdAt: new Date('2024-01-15'),
        isActive: true
      }
    ];

    const demoApprovalWorkflows: ApprovalWorkflow[] = [
      {
        id: 'approval-1',
        name: language === 'ar' ? 'موافقة المحتوى الأساسية' : 'Basic Content Approval',
        description: language === 'ar' ? 'سير موافقة بسيط للمحتوى العادي' : 'Simple approval flow for regular content',
        steps: [
          {
            id: 'step-1',
            name: language === 'ar' ? 'مراجعة المحرر' : 'Editor Review',
            approvers: ['user-2'],
            requiredApprovals: 1,
            autoApprove: false
          }
        ],
        isActive: true,
        workspaceId: 'workspace-1'
      },
      {
        id: 'approval-2',
        name: language === 'ar' ? 'موافقة المحتوى المتقدمة' : 'Advanced Content Approval',
        description: language === 'ar' ? 'سير موافقة متعدد المراحل للمحتوى الحساس' : 'Multi-stage approval for sensitive content',
        steps: [
          {
            id: 'step-1',
            name: language === 'ar' ? 'مراجعة المحرر' : 'Editor Review',
            approvers: ['user-3'],
            requiredApprovals: 1,
            autoApprove: false
          },
          {
            id: 'step-2',
            name: language === 'ar' ? 'موافقة المدير' : 'Manager Approval',
            approvers: ['user-2'],
            requiredApprovals: 1,
            autoApprove: false
          }
        ],
        isActive: true,
        workspaceId: 'workspace-2'
      }
    ];

    setMembers(demoMembers);
    setWorkspaces(demoWorkspaces);
    setApprovalWorkflows(demoApprovalWorkflows);
  };

  useEffect(() => {
    generateDemoData();
  }, [language, currentUserId]);

  // Filter members
  const filteredMembers = members.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === 'all' || member.role === roleFilter;
    const matchesStatus = statusFilter === 'all' || member.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  // Permission checks
  const canManageTeam = currentUserRole === 'owner' || currentUserRole === 'admin';
  const canManageWorkspaces = currentUserRole === 'owner' || currentUserRole === 'admin';

  // Member management handlers
  const handleInviteMember = useCallback(async (email: string, role: string) => {
    if (!canManageTeam) {
      toast.error(text.messages.noPermission);
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(text.messages.memberInvited);
      setShowInviteDialog(false);
    } catch (error) {
      toast.error('Failed to invite member');
    } finally {
      setIsLoading(false);
    }
  }, [canManageTeam, text.messages]);

  const handleRemoveMember = useCallback(async (memberId: string) => {
    if (!canManageTeam) {
      toast.error(text.messages.noPermission);
      return;
    }

    if (!confirm(text.messages.confirmRemove)) {
      return;
    }

    setMembers(prev => prev.filter(m => m.id !== memberId));
    toast.success(text.messages.memberRemoved);
  }, [canManageTeam, text.messages]);

  const handleUpdateMemberRole = useCallback(async (memberId: string, newRole: string) => {
    if (!canManageTeam) {
      toast.error(text.messages.noPermission);
      return;
    }

    setMembers(prev => prev.map(m => 
      m.id === memberId ? { ...m, role: newRole as any } : m
    ));
    toast.success(text.messages.memberUpdated);
  }, [canManageTeam, text.messages]);

  // Get role icon
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'admin':
        return <Shield className="w-4 h-4 text-blue-500" />;
      case 'editor':
        return <Edit className="w-4 h-4 text-green-500" />;
      case 'reviewer':
        return <Eye className="w-4 h-4 text-purple-500" />;
      case 'viewer':
        return <Eye className="w-4 h-4 text-gray-500" />;
      default:
        return <Users className="w-4 h-4 text-gray-500" />;
    }
  };

  // Get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'suspended':
        return 'destructive';
      case 'inactive':
        return 'outline';
      default:
        return 'outline';
    }
  };

  return (
    <div className={cn(
      "space-y-6",
      language === 'ar' ? "rtl" : "ltr",
      className
    )} dir={rtl.direction}>
      {/* Header */}
      <div className={rtl.cn(
        "flex items-center justify-between",
        rtl.flex()
      )}>
        <div className={rtl.textAlign()}>
          <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
            {text.title}
          </h1>
          <p className="text-gray-600 mt-1" style={{ fontFamily: rtl.getFontFamily('primary') }}>
            {text.subtitle}
          </p>
        </div>
        
        <div className={rtl.cn(
          "flex items-center gap-4",
          rtl.flex()
        )}>
          <Badge variant="outline">
            {members.length} {language === 'ar' ? 'أعضاء' : 'members'}
          </Badge>
          
          <Button
            variant="outline"
            onClick={() => generateDemoData()}
            className={rtl.cn(
              "flex items-center gap-2",
              rtl.flex()
            )}
          >
            <RefreshCw className="w-4 h-4" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="members" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="members">{text.tabs.members}</TabsTrigger>
          <TabsTrigger value="workspaces">{text.tabs.workspaces}</TabsTrigger>
          <TabsTrigger value="approvals">{text.tabs.approvals}</TabsTrigger>
          <TabsTrigger value="permissions">{text.tabs.permissions}</TabsTrigger>
          <TabsTrigger value="activity">{text.tabs.activity}</TabsTrigger>
        </TabsList>

        {/* Team Members Tab */}
        <TabsContent value="members" className="space-y-6">
          {/* Members List */}
          <div className="space-y-4">
            {filteredMembers.map((member) => (
              <Card key={member.id}>
                <CardContent className="p-6">
                  <div className={rtl.cn(
                    "flex items-center justify-between",
                    rtl.flex()
                  )}>
                    <div className={rtl.cn(
                      "flex items-center gap-4",
                      rtl.flex()
                    )}>
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={member.avatar} alt={member.name} />
                        <AvatarFallback>
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>

                      <div className={rtl.textAlign()}>
                        <div className={rtl.cn(
                          "flex items-center gap-2",
                          rtl.flex()
                        )}>
                          <h3 className="font-semibold">{member.name}</h3>
                          {getRoleIcon(member.role)}
                          <Badge variant={getStatusVariant(member.status)}>
                            {text.status[member.status]}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600">{member.email}</p>
                        <div className={rtl.cn(
                          "flex items-center gap-4 mt-1 text-xs text-gray-500",
                          rtl.flex()
                        )}>
                          <span>
                            {language === 'ar' ? 'انضم في' : 'Joined'} {rtl.formatDate(member.joinedAt)}
                          </span>
                          {member.lastActive && (
                            <span>
                              {language === 'ar' ? 'آخر نشاط' : 'Last active'} {rtl.formatRelativeTime(member.lastActive)}
                            </span>
                          )}
                          <span>
                            {member.workspaces.length} {language === 'ar' ? 'مساحات عمل' : 'workspaces'}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className={rtl.cn(
                      "flex items-center gap-2",
                      rtl.flex()
                    )}>
                      <Badge variant="outline">
                        {text.roles[member.role]}
                      </Badge>

                      {canManageTeam && member.userId !== currentUserId && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem onClick={() => handleUpdateMemberRole(member.id, 'admin')}>
                              <Shield className="w-4 h-4 mr-2" />
                              {language === 'ar' ? 'جعل مدير' : 'Make Admin'}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleUpdateMemberRole(member.id, 'editor')}>
                              <Edit className="w-4 h-4 mr-2" />
                              {language === 'ar' ? 'جعل محرر' : 'Make Editor'}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleRemoveMember(member.id)}
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              {text.actions.remove}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>

                  {/* Permissions Preview */}
                  <div className="mt-4 pt-4 border-t">
                    <div className={rtl.cn(
                      "flex flex-wrap gap-2",
                      rtl.flex()
                    )}>
                      {Object.entries(member.permissions).map(([key, value]) => (
                        value && (
                          <Badge key={key} variant="secondary" className="text-xs">
                            {text.permissions[key as keyof typeof text.permissions]}
                          </Badge>
                        )
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {filteredMembers.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>{language === 'ar' ? 'لا توجد أعضاء' : 'No members found'}</p>
              </div>
            )}
          </div>
        </TabsContent>

        {/* Workspaces Tab */}
        <TabsContent value="workspaces" className="space-y-6">
          <div className={rtl.cn(
            "flex items-center justify-between",
            rtl.flex()
          )}>
            <h3 className="text-lg font-semibold">{text.tabs.workspaces}</h3>
            {canManageWorkspaces && (
              <Button className={rtl.cn(
                "flex items-center gap-2",
                rtl.flex()
              )}>
                <Plus className="w-4 h-4" />
                {text.actions.createWorkspace}
              </Button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {workspaces.map((workspace) => (
              <Card key={workspace.id}>
                <CardHeader className="pb-3">
                  <div className={rtl.cn(
                    "flex items-center justify-between",
                    rtl.flex()
                  )}>
                    <div className={rtl.cn(
                      "flex items-center gap-3",
                      rtl.flex()
                    )}>
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: workspace.color }}
                      />
                      <CardTitle className="text-lg">{workspace.name}</CardTitle>
                    </div>
                    <Badge variant={workspace.isActive ? 'default' : 'secondary'}>
                      {workspace.isActive ? (language === 'ar' ? 'نشط' : 'Active') : (language === 'ar' ? 'غير نشط' : 'Inactive')}
                    </Badge>
                  </div>
                  {workspace.description && (
                    <CardDescription className={rtl.textAlign()}>
                      {workspace.description}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className={rtl.cn(
                      "flex items-center justify-between text-sm",
                      rtl.flex()
                    )}>
                      <span className="text-gray-600">
                        {language === 'ar' ? 'الأعضاء' : 'Members'}
                      </span>
                      <span className="font-medium">{workspace.memberCount}</span>
                    </div>
                    <div className={rtl.cn(
                      "flex items-center justify-between text-sm",
                      rtl.flex()
                    )}>
                      <span className="text-gray-600">
                        {language === 'ar' ? 'المنشورات' : 'Posts'}
                      </span>
                      <span className="font-medium">{workspace.postCount}</span>
                    </div>
                    <div className={rtl.cn(
                      "flex items-center justify-between text-sm",
                      rtl.flex()
                    )}>
                      <span className="text-gray-600">
                        {language === 'ar' ? 'في انتظار الموافقة' : 'Pending Approvals'}
                      </span>
                      <Badge variant={workspace.pendingApprovals > 0 ? 'destructive' : 'secondary'}>
                        {workspace.pendingApprovals}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Approval Workflows Tab */}
        <TabsContent value="approvals" className="space-y-6">
          <div className={rtl.cn(
            "flex items-center justify-between",
            rtl.flex()
          )}>
            <h3 className="text-lg font-semibold">{text.tabs.approvals}</h3>
            {canManageWorkspaces && (
              <Button className={rtl.cn(
                "flex items-center gap-2",
                rtl.flex()
              )}>
                <Plus className="w-4 h-4" />
                {text.actions.createApproval}
              </Button>
            )}
          </div>

          <div className="space-y-4">
            {approvalWorkflows.map((workflow) => (
              <Card key={workflow.id}>
                <CardHeader>
                  <div className={rtl.cn(
                    "flex items-center justify-between",
                    rtl.flex()
                  )}>
                    <CardTitle className={rtl.textAlign()}>{workflow.name}</CardTitle>
                    <Badge variant={workflow.isActive ? 'default' : 'secondary'}>
                      {workflow.isActive ? (language === 'ar' ? 'نشط' : 'Active') : (language === 'ar' ? 'غير نشط' : 'Inactive')}
                    </Badge>
                  </div>
                  {workflow.description && (
                    <CardDescription className={rtl.textAlign()}>
                      {workflow.description}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <h4 className={rtl.cn("font-medium", rtl.textAlign())}>
                      {language === 'ar' ? 'خطوات الموافقة' : 'Approval Steps'}
                    </h4>
                    {workflow.steps.map((step, index) => (
                      <div key={step.id} className={rtl.cn(
                        "flex items-center gap-3 p-3 bg-gray-50 rounded-lg",
                        rtl.flex()
                      )}>
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                          {index + 1}
                        </div>
                        <div className={rtl.cn("flex-1", rtl.textAlign())}>
                          <p className="font-medium">{step.name}</p>
                          <p className="text-sm text-gray-600">
                            {step.requiredApprovals} {language === 'ar' ? 'موافقة مطلوبة' : 'approval(s) required'}
                          </p>
                        </div>
                        <Badge variant="outline">
                          {step.approvers.length} {language === 'ar' ? 'مراجعين' : 'reviewers'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {text.tabs.permissions}
              </CardTitle>
              <CardDescription className={rtl.textAlign()}>
                {language === 'ar'
                  ? 'إدارة الأذونات والصلاحيات لكل دور'
                  : 'Manage permissions and access rights for each role'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(text.roles).map(([roleKey, roleName]) => (
                  <div key={roleKey} className="space-y-3">
                    <div className={rtl.cn(
                      "flex items-center gap-2",
                      rtl.flex()
                    )}>
                      {getRoleIcon(roleKey)}
                      <h4 className="font-semibold">{roleName}</h4>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3 pl-6">
                      {Object.entries(text.permissions).map(([permKey, permName]) => {
                        const hasPermission = getDefaultPermissions(roleKey)[permKey as keyof typeof text.permissions];
                        return (
                          <div key={permKey} className={rtl.cn(
                            "flex items-center gap-2",
                            rtl.flex()
                          )}>
                            {hasPermission ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <XCircle className="w-4 h-4 text-gray-300" />
                            )}
                            <span className={cn(
                              "text-sm",
                              hasPermission ? "text-gray-900" : "text-gray-400"
                            )}>
                              {permName}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {text.tabs.activity}
              </CardTitle>
              <CardDescription className={rtl.textAlign()}>
                {language === 'ar'
                  ? 'نشاط الفريق والتعاون الأخير'
                  : 'Recent team activity and collaboration'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {generateActivityData().map((activity, index) => (
                  <div key={index} className={rtl.cn(
                    "flex items-start gap-3 p-3 border rounded-lg",
                    rtl.flex()
                  )}>
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      {activity.icon}
                    </div>
                    <div className={rtl.cn("flex-1", rtl.textAlign())}>
                      <p className="text-sm">{activity.description}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        {rtl.formatRelativeTime(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );

  // Helper function to get default permissions for a role
  function getDefaultPermissions(role: string) {
    const permissions = {
      owner: {
        canCreatePosts: true,
        canEditPosts: true,
        canDeletePosts: true,
        canManageTeam: true,
        canViewAnalytics: true,
        canApproveContent: true,
        canManageWorkspaces: true
      },
      admin: {
        canCreatePosts: true,
        canEditPosts: true,
        canDeletePosts: true,
        canManageTeam: true,
        canViewAnalytics: true,
        canApproveContent: true,
        canManageWorkspaces: false
      },
      editor: {
        canCreatePosts: true,
        canEditPosts: true,
        canDeletePosts: false,
        canManageTeam: false,
        canViewAnalytics: true,
        canApproveContent: false,
        canManageWorkspaces: false
      },
      reviewer: {
        canCreatePosts: false,
        canEditPosts: false,
        canDeletePosts: false,
        canManageTeam: false,
        canViewAnalytics: true,
        canApproveContent: true,
        canManageWorkspaces: false
      },
      viewer: {
        canCreatePosts: false,
        canEditPosts: false,
        canDeletePosts: false,
        canManageTeam: false,
        canViewAnalytics: true,
        canApproveContent: false,
        canManageWorkspaces: false
      }
    };

    return permissions[role as keyof typeof permissions] || permissions.viewer;
  }

  // Helper function to generate activity data
  function generateActivityData() {
    return [
      {
        icon: <UserPlus className="w-4 h-4 text-blue-600" />,
        description: language === 'ar'
          ? 'فاطمة علي انضمت إلى مساحة عمل التسويق الرقمي'
          : 'Fatima Ali joined Digital Marketing workspace',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
      },
      {
        icon: <CheckCircle className="w-4 h-4 text-green-600" />,
        description: language === 'ar'
          ? 'تم الموافقة على منشور "حملة الصيف الجديدة"'
          : 'Post "Summer Campaign" was approved',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)
      },
      {
        icon: <MessageSquare className="w-4 h-4 text-purple-600" />,
        description: language === 'ar'
          ? 'محمد سالم أضاف تعليق على منشور "استراتيجية المحتوى"'
          : 'Mohamed Salem commented on "Content Strategy" post',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000)
      },
      {
        icon: <Edit className="w-4 h-4 text-orange-600" />,
        description: language === 'ar'
          ? 'تم تحديث أذونات سارة أحمد إلى مراجع'
          : 'Sara Ahmed\'s permissions updated to Reviewer',
        timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000)
      }
    ];
  }
}
