/**
 * Manual Token Refresh API Endpoint
 * Allows users to manually refresh tokens for their social accounts
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { TokenRefreshService } from '@/lib/oauth/token-refresh-service'

export async function POST(request: NextRequest) {
  try {
    const { accountId } = await request.json()

    if (!accountId) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      )
    }

    // Get user from session
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get the social account
    const { data: account, error: accountError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('user_id', user.id)
      .single()

    if (accountError || !account) {
      return NextResponse.json(
        { error: 'Social account not found' },
        { status: 404 }
      )
    }

    // Initialize token refresh service
    const tokenRefreshService = new TokenRefreshService()

    // Refresh the token
    const result = await tokenRefreshService.refreshAccountToken(account)

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Token refreshed successfully',
        accountId: accountId,
        expiresAt: result.expiresAt,
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Token refresh failed',
          accountId: accountId,
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('❌ Manual token refresh failed:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
