/**
 * Social Account Connection Validation API
 * Validates social media account connections in real-time
 */

import { NextRequest, NextResponse } from 'next/server'
import { connectionValidator } from '@/lib/oauth/connection-validator'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { accountIds, userId } = body

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // If specific account IDs provided, validate only those
    if (accountIds && Array.isArray(accountIds)) {
      const supabase = createClient()
      
      // Fetch specified accounts
      const { data: accounts, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId)
        .in('id', accountIds)

      if (error) {
        return NextResponse.json(
          { error: 'Failed to fetch accounts' },
          { status: 500 }
        )
      }

      // Convert to validation format
      const accountsToValidate = accounts.map(account => ({
        id: account.id,
        platform: account.platform,
        accessToken: account.access_token,
        refreshToken: account.refresh_token,
        expiresAt: account.expires_at ? new Date(account.expires_at) : undefined,
        lastValidatedAt: account.last_validated_at ? new Date(account.last_validated_at) : undefined
      }))

      // Validate accounts
      const validationResults = await connectionValidator.validateAccounts(accountsToValidate)

      // Update account statuses
      const updatePromises = Array.from(validationResults.entries()).map(
        ([accountId, result]) => connectionValidator.updateAccountStatus(accountId, result)
      )

      await Promise.allSettled(updatePromises)

      // Return validation results
      const results = Array.from(validationResults.entries()).map(([accountId, result]) => ({
        accountId,
        isValid: result.isValid,
        status: result.status,
        error: result.error,
        needsRefresh: result.needsRefresh
      }))

      return NextResponse.json({
        success: true,
        results,
        validatedCount: results.length
      })
    }

    // If no specific accounts, validate all user accounts
    await connectionValidator.validateUserAccounts(userId)

    return NextResponse.json({
      success: true,
      message: 'All accounts validated successfully'
    })

  } catch (error) {
    console.error('Account validation API error:', error)
    return NextResponse.json(
      { 
        error: 'Validation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const accountId = searchParams.get('accountId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    const supabase = createClient()

    if (accountId) {
      // Validate single account
      const { data: account, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('id', accountId)
        .single()

      if (error || !account) {
        return NextResponse.json(
          { error: 'Account not found' },
          { status: 404 }
        )
      }

      const accountToValidate = {
        id: account.id,
        platform: account.platform,
        accessToken: account.access_token,
        refreshToken: account.refresh_token,
        expiresAt: account.expires_at ? new Date(account.expires_at) : undefined,
        lastValidatedAt: account.last_validated_at ? new Date(account.last_validated_at) : undefined
      }

      const validationResult = await connectionValidator.validateAccount(accountToValidate)
      
      // Update account status
      await connectionValidator.updateAccountStatus(accountId, validationResult)

      return NextResponse.json({
        accountId,
        isValid: validationResult.isValid,
        status: validationResult.status,
        error: validationResult.error,
        needsRefresh: validationResult.needsRefresh,
        lastValidated: new Date().toISOString()
      })
    }

    // Get validation status for all user accounts
    const { data: accounts, error } = await supabase
      .from('social_accounts')
      .select('id, platform, connection_status, last_validated_at, expires_at')
      .eq('user_id', userId)

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch accounts' },
        { status: 500 }
      )
    }

    const accountStatuses = accounts.map(account => ({
      accountId: account.id,
      platform: account.platform,
      status: account.connection_status,
      lastValidated: account.last_validated_at,
      expiresAt: account.expires_at,
      needsValidation: !account.last_validated_at || 
        new Date(account.last_validated_at) < new Date(Date.now() - 60 * 60 * 1000) // 1 hour ago
    }))

    return NextResponse.json({
      accounts: accountStatuses,
      totalAccounts: accounts.length,
      needsValidation: accountStatuses.filter(a => a.needsValidation).length
    })

  } catch (error) {
    console.error('Account validation status API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to get validation status',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
