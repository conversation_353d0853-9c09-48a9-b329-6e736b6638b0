'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  RefreshCw, 
  Link, 
  MessageCircle, 
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { OAuthError, RecoveryAction, OAuthErrorType } from '@/lib/error-handling/oauth-error-handler';
import { toast } from 'sonner';

interface OAuthErrorRecoveryProps {
  error: OAuthError;
  onRetry?: () => Promise<void>;
  onReconnect?: () => void;
  onRefresh?: () => Promise<void>;
  onDismiss?: () => void;
  className?: string;
}

export function OAuthErrorRecovery({ 
  error, 
  onRetry, 
  onReconnect, 
  onRefresh, 
  onDismiss,
  className = '' 
}: OAuthErrorRecoveryProps) {
  const [isRecovering, setIsRecovering] = useState(false);
  const [recoveryStatus, setRecoveryStatus] = useState<'idle' | 'success' | 'failed'>('idle');

  const getErrorIcon = () => {
    switch (error.type) {
      case OAuthErrorType.TOKEN_EXPIRED:
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case OAuthErrorType.NETWORK_ERROR:
      case OAuthErrorType.TIMEOUT_ERROR:
        return <RefreshCw className="w-5 h-5 text-blue-500" />;
      case OAuthErrorType.RATE_LIMIT_ERROR:
        return <Clock className="w-5 h-5 text-orange-500" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
    }
  };

  const getErrorSeverity = () => {
    const criticalTypes = [
      OAuthErrorType.DATABASE_ERROR,
      OAuthErrorType.CONFIGURATION_ERROR
    ];
    
    if (criticalTypes.includes(error.type)) {
      return 'critical';
    }
    
    const warningTypes = [
      OAuthErrorType.TOKEN_EXPIRED,
      OAuthErrorType.RATE_LIMIT_ERROR
    ];
    
    if (warningTypes.includes(error.type)) {
      return 'warning';
    }
    
    return 'error';
  };

  const getSeverityColor = () => {
    const severity = getErrorSeverity();
    switch (severity) {
      case 'critical':
        return 'border-red-500 bg-red-50 dark:bg-red-950';
      case 'warning':
        return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950';
      default:
        return 'border-orange-500 bg-orange-50 dark:bg-orange-950';
    }
  };

  const getPlatformName = (platform?: string) => {
    const names: Record<string, string> = {
      'FACEBOOK': 'فيسبوك',
      'INSTAGRAM': 'إنستغرام',
      'TWITTER': 'تويتر',
      'X': 'إكس',
      'LINKEDIN': 'لينكد إن'
    };
    return platform ? names[platform] || platform : '';
  };

  const handleRecoveryAction = async (action: RecoveryAction) => {
    setIsRecovering(true);
    setRecoveryStatus('idle');

    try {
      switch (action.action) {
        case 'retry':
          if (onRetry) {
            await onRetry();
            setRecoveryStatus('success');
            toast.success('تم حل المشكلة بنجاح');
          }
          break;
          
        case 'refresh':
          if (onRefresh) {
            await onRefresh();
            setRecoveryStatus('success');
            toast.success('تم تحديث الرمز بنجاح');
          }
          break;
          
        case 'reconnect':
          if (onReconnect) {
            onReconnect();
          } else if (action.url) {
            window.location.href = action.url;
          }
          break;
          
        case 'contact_support':
          if (action.url) {
            window.open(action.url, '_blank');
          }
          break;
          
        case 'ignore':
          if (onDismiss) {
            onDismiss();
          }
          break;
      }
    } catch (recoveryError) {
      console.error('Recovery action failed:', recoveryError);
      setRecoveryStatus('failed');
      toast.error('فشل في حل المشكلة');
    } finally {
      setIsRecovering(false);
    }
  };

  const getActionIcon = (action: RecoveryAction) => {
    switch (action.action) {
      case 'retry':
        return <RefreshCw className="w-4 h-4" />;
      case 'refresh':
        return <RefreshCw className="w-4 h-4" />;
      case 'reconnect':
        return <Link className="w-4 h-4" />;
      case 'contact_support':
        return <MessageCircle className="w-4 h-4" />;
      default:
        return <CheckCircle className="w-4 h-4" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className={`${className} ${getSeverityColor()} border-2`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            {getErrorIcon()}
            <div>
              <CardTitle className="text-lg">
                {error.platform && (
                  <span className="text-sm font-normal text-muted-foreground">
                    {getPlatformName(error.platform)} • 
                  </span>
                )}
                خطأ في الاتصال
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                {formatTimestamp(error.timestamp)}
              </p>
            </div>
          </div>
          
          {recoveryStatus === 'success' && (
            <CheckCircle className="w-5 h-5 text-green-500" />
          )}
          {recoveryStatus === 'failed' && (
            <XCircle className="w-5 h-5 text-red-500" />
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Error Message */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-right">
            {error.arabicMessage}
          </AlertDescription>
        </Alert>

        {/* Technical Details (for debugging) */}
        {process.env.NODE_ENV === 'development' && (
          <details className="text-xs text-muted-foreground">
            <summary className="cursor-pointer hover:text-foreground">
              تفاصيل تقنية
            </summary>
            <div className="mt-2 p-2 bg-muted rounded text-left" dir="ltr">
              <p><strong>Type:</strong> {error.type}</p>
              <p><strong>Message:</strong> {error.message}</p>
              {error.originalError && (
                <p><strong>Original:</strong> {JSON.stringify(error.originalError, null, 2)}</p>
              )}
            </div>
          </details>
        )}

        {/* Recovery Actions */}
        {error.recoveryActions && error.recoveryActions.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm">الإجراءات المقترحة:</h4>
            <div className="flex flex-wrap gap-2">
              {error.recoveryActions.map((action) => (
                <Button
                  key={action.id}
                  variant={action.action === 'retry' || action.action === 'refresh' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleRecoveryAction(action)}
                  disabled={isRecovering}
                  className="flex items-center gap-2"
                >
                  {isRecovering ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    getActionIcon(action)
                  )}
                  {action.arabicLabel}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Auto-recovery indicator */}
        {error.recoveryActions?.some(action => action.automatic) && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span>جاري المحاولة التلقائية لحل المشكلة...</span>
          </div>
        )}

        {/* Context Information */}
        {error.context?.operation && (
          <div className="text-sm text-muted-foreground">
            <strong>العملية:</strong> {error.context.operation}
          </div>
        )}

        {/* Dismiss Button */}
        {onDismiss && (
          <div className="flex justify-end pt-2 border-t">
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="text-muted-foreground hover:text-foreground"
            >
              إخفاء
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Error Recovery Toast Component
 * Lightweight version for toast notifications
 */
export function OAuthErrorToast({ 
  error, 
  onAction 
}: { 
  error: OAuthError; 
  onAction?: (actionId: string) => void; 
}) {
  const primaryAction = error.recoveryActions?.[0];

  return (
    <div className="flex items-start gap-3 p-3">
      <AlertTriangle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
      <div className="flex-1 min-w-0">
        <p className="font-medium text-sm">{error.arabicMessage}</p>
        {error.platform && (
          <p className="text-xs text-muted-foreground mt-1">
            منصة: {getPlatformName(error.platform)}
          </p>
        )}
      </div>
      {primaryAction && onAction && (
        <Button
          size="sm"
          variant="outline"
          onClick={() => onAction(primaryAction.id)}
          className="flex-shrink-0"
        >
          {primaryAction.arabicLabel}
        </Button>
      )}
    </div>
  );
}

function getPlatformName(platform: string) {
  const names: Record<string, string> = {
    'FACEBOOK': 'فيسبوك',
    'INSTAGRAM': 'إنستغرام',
    'TWITTER': 'تويتر',
    'X': 'إكس',
    'LINKEDIN': 'لينكد إن'
  };
  return names[platform] || platform;
}
