import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';
import { FacebookOAuthService } from '@/lib/oauth/facebook';

/**
 * Enhanced Facebook Pages API endpoint
 * Provides detailed page information with metrics and Instagram Business account detection
 */

export async function GET(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || user.id;

    console.log('🔍 Fetching enhanced Facebook Pages for user:', userId);

    // Get user's Facebook account from database
    const { data: socialAccount, error: accountError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', userId)
      .eq('platform', 'FACEBOOK')
      .eq('is_active', true)
      .single();

    if (accountError || !socialAccount) {
      return NextResponse.json(
        { 
          error: 'لم يتم العثور على حساب فيسبوك متصل',
          code: 'NO_FACEBOOK_ACCOUNT'
        },
        { status: 404 }
      );
    }

    // Initialize Facebook service
    const facebookService = new FacebookOAuthService({
      appId: process.env.FACEBOOK_APP_ID!,
      appSecret: process.env.FACEBOOK_APP_SECRET!,
      businessId: process.env.FACEBOOK_BUSINESS_ID!,
      redirectUri: `${process.env.NEXT_PUBLIC_APP_URL}/api/facebook/callback`
    });

    // Get enhanced user accounts (pages and Instagram)
    const accounts = await facebookService.getUserAccounts(socialAccount.access_token);
    
    // Filter Facebook pages only
    const facebookPages = accounts.filter(account => account.platform === 'FACEBOOK');
    
    // Get Instagram accounts linked to these pages
    const instagramAccounts = accounts.filter(account => account.platform === 'INSTAGRAM');
    
    // Enhance pages with Instagram connection info
    const enhancedPages = facebookPages.map(page => {
      const linkedInstagram = instagramAccounts.find(ig => 
        ig.metadata?.connectedFacebookPageId === page.metadata?.pageId
      );
      
      return {
        id: page.metadata?.pageId,
        name: page.accountName,
        category: page.metadata?.category,
        fanCount: page.metadata?.followerCount || 0,
        about: page.metadata?.about,
        website: page.metadata?.website,
        isVerified: page.metadata?.isVerified || false,
        profilePictureUrl: page.profileImageUrl,
        accessToken: page.accessToken,
        permissions: page.permissions,
        engagementRate: page.metadata?.engagementRate || 0,
        impressions: page.metadata?.impressions || 0,
        lastUpdated: page.metadata?.lastUpdated,
        linkedInstagram: linkedInstagram ? {
          id: linkedInstagram.metadata?.instagramAccountId,
          username: linkedInstagram.metadata?.username,
          displayName: linkedInstagram.metadata?.displayName,
          followerCount: linkedInstagram.metadata?.followerCount || 0,
          mediaCount: linkedInstagram.metadata?.mediaCount || 0,
          profilePictureUrl: linkedInstagram.profileImageUrl
        } : null,
        connectionStatus: page.connectionStatus,
        lastValidatedAt: page.lastValidatedAt
      };
    });

    // Get current user's selected page (if any)
    const { data: userPreferences } = await supabase
      .from('user_business_preferences')
      .select('default_facebook_page_id')
      .eq('user_id', userId)
      .single();

    const selectedPageId = userPreferences?.default_facebook_page_id;

    console.log(`✅ Found ${enhancedPages.length} Facebook Pages with enhanced data`);

    return NextResponse.json({
      success: true,
      pages: enhancedPages,
      selectedPageId,
      totalPages: enhancedPages.length,
      instagramAccountsFound: instagramAccounts.length,
      lastRefreshed: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error fetching Facebook Pages:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في تحميل صفحات فيسبوك',
        details: error instanceof Error ? error.message : 'Unknown error',
        code: 'FETCH_PAGES_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Refresh Facebook Pages data
 */
export async function POST(request: NextRequest) {
  try {
    const { user, supabase } = await getAuthenticatedUser(request);
    const body = await request.json();
    const { userId = user.id, forceRefresh = false } = body;

    console.log('🔄 Refreshing Facebook Pages data for user:', userId);

    // Get user's Facebook account
    const { data: socialAccount, error: accountError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', userId)
      .eq('platform', 'FACEBOOK')
      .eq('is_active', true)
      .single();

    if (accountError || !socialAccount) {
      return NextResponse.json(
        { error: 'لم يتم العثور على حساب فيسبوك متصل' },
        { status: 404 }
      );
    }

    // Initialize Facebook service
    const facebookService = new FacebookOAuthService({
      appId: process.env.FACEBOOK_APP_ID!,
      appSecret: process.env.FACEBOOK_APP_SECRET!,
      businessId: process.env.FACEBOOK_BUSINESS_ID!,
      redirectUri: `${process.env.NEXT_PUBLIC_APP_URL}/api/facebook/callback`
    });

    // Refresh accounts data
    const accounts = await facebookService.getUserAccounts(socialAccount.access_token);
    
    // Update social_accounts table with fresh data
    for (const account of accounts) {
      await supabase
        .from('social_accounts')
        .upsert({
          user_id: userId,
          platform: account.platform,
          account_id: account.id,
          account_name: account.accountName,
          account_handle: account.accountHandle,
          access_token: account.accessToken,
          refresh_token: account.refreshToken,
          expires_at: account.expiresAt?.toISOString(),
          connection_status: account.connectionStatus,
          last_validated_at: account.lastValidatedAt.toISOString(),
          metadata: account.metadata,
          profile_image_url: account.profileImageUrl,
          permissions: account.permissions,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,platform,account_id'
        });
    }

    console.log(`✅ Refreshed ${accounts.length} Facebook/Instagram accounts`);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات صفحات فيسبوك بنجاح',
      accountsUpdated: accounts.length,
      lastRefreshed: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error refreshing Facebook Pages:', error);
    
    return NextResponse.json(
      { 
        error: 'فشل في تحديث صفحات فيسبوك',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
