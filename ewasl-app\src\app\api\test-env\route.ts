import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Test environment variables
    const envVars = {
      FACEBOOK_APP_ID: process.env.FACEBOOK_APP_ID ? 'SET' : 'MISSING',
      FACEBOOK_APP_SECRET: process.env.FACEBOOK_APP_SECRET ? 'SET' : 'MISSING',
      FACEBOOK_BUSINESS_ID: process.env.FACEBOOK_BUSINESS_ID ? 'SET' : 'MISSING',
      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'MISSING',
      NODE_ENV: process.env.NODE_ENV || 'MISSING'
    }

    console.log('Environment Variables Test:', envVars)

    return NextResponse.json({
      success: true,
      environment: envVars,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Environment test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
