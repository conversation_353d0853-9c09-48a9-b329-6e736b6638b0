import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Validation schemas
const bulkOperationSchema = z.object({
  operation: z.enum(['publish', 'schedule', 'delete', 'duplicate', 'update_status', 'update_platforms', 'export']),
  postIds: z.array(z.string()).min(1).max(100), // Limit to 100 posts per operation
  options: z.object({
    scheduledAt: z.string().datetime().optional(),
    newStatus: z.enum(['DRAFT', 'SCHEDULED', 'PUBLISHED']).optional(),
    platforms: z.array(z.string()).optional(),
    timezone: z.string().default('UTC'),
  }).optional().default({})
});

const batchCreateSchema = z.object({
  posts: z.array(z.object({
    content: z.string().min(1).max(2800),
    platforms: z.array(z.string()).min(1),
    scheduledAt: z.string().datetime().optional(),
    mediaUrls: z.array(z.string().url()).default([]),
    tags: z.array(z.string()).default([]),
    priority: z.enum(['low', 'medium', 'high']).default('medium'),
  })).min(1).max(50), // Limit to 50 posts per batch
  globalSettings: z.object({
    autoSchedule: z.boolean().default(false),
    scheduleInterval: z.number().min(1).default(30), // minutes
    timezone: z.string().default('UTC'),
  }).optional().default({})
});

// POST /api/posts/bulk-operations - Handle bulk operations
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type } = body;

    if (type === 'bulk_operation') {
      return handleBulkOperation(supabase, user, body);
    } else if (type === 'batch_create') {
      return handleBatchCreate(supabase, user, body);
    } else {
      return NextResponse.json(
        { error: 'Invalid operation type' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Bulk operations error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle bulk operations on existing posts
async function handleBulkOperation(supabase: any, user: any, body: any) {
  const validation = bulkOperationSchema.safeParse(body);
  
  if (!validation.success) {
    return NextResponse.json(
      { 
        error: 'Invalid request data',
        details: validation.error.errors
      },
      { status: 400 }
    );
  }

  const { operation, postIds, options } = validation.data;
  const results = {
    success: 0,
    failed: 0,
    errors: [] as string[]
  };

  try {
    // Verify user owns all posts
    const { data: posts, error: fetchError } = await supabase
      .from('posts')
      .select('id, status, content')
      .in('id', postIds)
      .eq('user_id', user.id);

    if (fetchError) {
      return NextResponse.json(
        { error: 'Failed to fetch posts' },
        { status: 500 }
      );
    }

    if (posts.length !== postIds.length) {
      return NextResponse.json(
        { error: 'Some posts not found or not owned by user' },
        { status: 403 }
      );
    }

    // Execute operation based on type
    switch (operation) {
      case 'publish':
        await handlePublishOperation(supabase, posts, results);
        break;
      
      case 'schedule':
        if (!options.scheduledAt) {
          return NextResponse.json(
            { error: 'Scheduled date is required for schedule operation' },
            { status: 400 }
          );
        }
        await handleScheduleOperation(supabase, posts, options.scheduledAt, results);
        break;
      
      case 'delete':
        await handleDeleteOperation(supabase, posts, results);
        break;
      
      case 'duplicate':
        await handleDuplicateOperation(supabase, user, posts, results);
        break;
      
      case 'update_status':
        if (!options.newStatus) {
          return NextResponse.json(
            { error: 'New status is required for update_status operation' },
            { status: 400 }
          );
        }
        await handleUpdateStatusOperation(supabase, posts, options.newStatus, results);
        break;
      
      case 'update_platforms':
        if (!options.platforms || options.platforms.length === 0) {
          return NextResponse.json(
            { error: 'Platforms are required for update_platforms operation' },
            { status: 400 }
          );
        }
        await handleUpdatePlatformsOperation(supabase, posts, options.platforms, results);
        break;
      
      case 'export':
        return handleExportOperation(posts);
      
      default:
        return NextResponse.json(
          { error: 'Unsupported operation' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      operation,
      results: {
        total: postIds.length,
        success: results.success,
        failed: results.failed,
        errors: results.errors
      }
    });

  } catch (error) {
    console.error(`Bulk ${operation} operation failed:`, error);
    return NextResponse.json(
      { error: `Bulk ${operation} operation failed` },
      { status: 500 }
    );
  }
}

// Handle batch creation of new posts
async function handleBatchCreate(supabase: any, user: any, body: any) {
  const validation = batchCreateSchema.safeParse(body);
  
  if (!validation.success) {
    return NextResponse.json(
      { 
        error: 'Invalid request data',
        details: validation.error.errors
      },
      { status: 400 }
    );
  }

  const { posts, globalSettings } = validation.data;
  const results = {
    success: 0,
    failed: 0,
    errors: [] as string[],
    createdPosts: [] as any[]
  };

  try {
    for (let i = 0; i < posts.length; i++) {
      const post = posts[i];
      
      try {
        // Calculate scheduled time if auto-scheduling
        let scheduledAt = post.scheduledAt;
        if (globalSettings.autoSchedule && !scheduledAt) {
          const baseDate = new Date();
          scheduledAt = new Date(baseDate.getTime() + i * globalSettings.scheduleInterval * 60 * 1000).toISOString();
        }

        // Create post
        const { data: newPost, error: postError } = await supabase
          .from('posts')
          .insert({
            user_id: user.id,
            content: post.content,
            media_urls: post.mediaUrls,
            hashtags: extractHashtags(post.content),
            status: scheduledAt ? 'SCHEDULED' : 'DRAFT',
            scheduled_at: scheduledAt,
            platform_settings: {},
            timezone: globalSettings.timezone,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (postError) {
          results.failed++;
          results.errors.push(`Post ${i + 1}: ${postError.message}`);
          continue;
        }

        // Get social accounts for platforms
        const { data: socialAccounts } = await supabase
          .from('social_accounts')
          .select('id, platform')
          .eq('user_id', user.id)
          .in('platform', post.platforms);

        // Create post-social account associations
        if (socialAccounts && socialAccounts.length > 0) {
          const associations = socialAccounts
            .filter(account => post.platforms.includes(account.platform))
            .map(account => ({
              post_id: newPost.id,
              social_account_id: account.id,
            }));

          if (associations.length > 0) {
            await supabase
              .from('post_social_accounts')
              .insert(associations);
          }
        }

        results.success++;
        results.createdPosts.push(newPost);

      } catch (error) {
        results.failed++;
        results.errors.push(`Post ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      success: true,
      operation: 'batch_create',
      results: {
        total: posts.length,
        success: results.success,
        failed: results.failed,
        errors: results.errors,
        createdPosts: results.createdPosts
      }
    });

  } catch (error) {
    console.error('Batch create operation failed:', error);
    return NextResponse.json(
      { error: 'Batch create operation failed' },
      { status: 500 }
    );
  }
}

// Helper functions for specific operations
async function handlePublishOperation(supabase: any, posts: any[], results: any) {
  for (const post of posts) {
    try {
      const { error } = await supabase
        .from('posts')
        .update({
          status: 'PUBLISHED',
          published_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', post.id);

      if (error) throw error;
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Post ${post.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

async function handleScheduleOperation(supabase: any, posts: any[], scheduledAt: string, results: any) {
  for (const post of posts) {
    try {
      const { error } = await supabase
        .from('posts')
        .update({
          status: 'SCHEDULED',
          scheduled_at: scheduledAt,
          updated_at: new Date().toISOString()
        })
        .eq('id', post.id);

      if (error) throw error;
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Post ${post.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

async function handleDeleteOperation(supabase: any, posts: any[], results: any) {
  for (const post of posts) {
    try {
      // Delete associated records first
      await supabase.from('post_social_accounts').delete().eq('post_id', post.id);
      
      // Delete the post
      const { error } = await supabase
        .from('posts')
        .delete()
        .eq('id', post.id);

      if (error) throw error;
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Post ${post.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

async function handleDuplicateOperation(supabase: any, user: any, posts: any[], results: any) {
  for (const post of posts) {
    try {
      const { error } = await supabase
        .from('posts')
        .insert({
          user_id: user.id,
          content: `${post.content} (Copy)`,
          status: 'DRAFT',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) throw error;
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Post ${post.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

async function handleUpdateStatusOperation(supabase: any, posts: any[], newStatus: string, results: any) {
  for (const post of posts) {
    try {
      const { error } = await supabase
        .from('posts')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', post.id);

      if (error) throw error;
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Post ${post.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

async function handleUpdatePlatformsOperation(supabase: any, posts: any[], platforms: string[], results: any) {
  for (const post of posts) {
    try {
      // This would require updating post_social_accounts table
      // For now, just mark as success
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Post ${post.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

function handleExportOperation(posts: any[]) {
  const csvData = posts.map(post => ({
    id: post.id,
    content: post.content,
    status: post.status,
    created_at: new Date().toISOString()
  }));

  return NextResponse.json({
    success: true,
    operation: 'export',
    data: csvData,
    format: 'csv'
  });
}

// Helper function to extract hashtags
function extractHashtags(content: string): string[] {
  const hashtagRegex = /#[\w\u0600-\u06FF]+/g;
  const matches = content.match(hashtagRegex);
  return matches ? matches.map(tag => tag.slice(1)) : [];
}
