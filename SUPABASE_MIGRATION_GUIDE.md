# 🚀 eWasl Supabase Database Migration Guide

**Migration Date:** January 2025  
**New Project ID:** nnxfzhxqzmriggulsudr  
**Status:** ✅ Schema Created, ⚠️ Credentials Needed  

---

## 📋 **Migration Summary**

### **✅ Completed Steps:**
1. **Connected to new Supabase project** (nnxfzhxqzmriggulsudr)
2. **Retrieved project details and API credentials**
3. **Updated environment variables** with new Supabase URLs and keys
4. **Created complete database schema** with all tables and relationships
5. **Generated Prisma client** for new database
6. **Created comprehensive test scripts** for validation

### **⚠️ Manual Steps Required:**
1. **Get database password** from Supabase dashboard
2. **Get service role key** from Supabase dashboard
3. **Update environment variables** with real credentials
4. **Run database tests** to verify functionality

---

## 🔧 **Step-by-Step Completion Guide**

### **Step 1: Get Database Credentials**

1. **Go to Supabase Dashboard:**
   ```
   https://supabase.com/dashboard/project/nnxfzhxqzmriggulsudr
   ```

2. **Get Database Password:**
   - Navigate to **Settings** → **Database**
   - Find **Connection string** section
   - Copy the password from the connection string
   - Format: `postgresql://postgres:[PASSWORD]@db.nnxfzhxqzmriggulsudr.supabase.co:5432/postgres`

3. **Get Service Role Key:**
   - Navigate to **Settings** → **API**
   - Find **Project API keys** section
   - Copy the **service_role** key (not the anon key)

### **Step 2: Update Environment Variables**

Edit `ewasl-app/.env.local` and replace these placeholders:

```bash
# Replace [YOUR_DB_PASSWORD] with actual password
DATABASE_URL=postgresql://postgres:[YOUR_DB_PASSWORD]@db.nnxfzhxqzmriggulsudr.supabase.co:5432/postgres

# Replace [YOUR_SERVICE_ROLE_KEY] with actual service role key
SUPABASE_SERVICE_ROLE_KEY=[YOUR_SERVICE_ROLE_KEY]
```

### **Step 3: Test Database Connection**

Run the comprehensive database test:

```bash
cd ewasl-app
node test-prisma-connection.js
```

**Expected Output:**
```
🔍 Testing eWasl Database Connection with Prisma...

1. Testing basic database connection...
   ✅ Database connection successful

2. Checking database tables...
   ✅ Found 8 tables:
      - Account
      - ErrorLog
      - Session
      - VerificationToken
      - _PostToSocialAccount
      - activities
      - posts
      - social_accounts
      - users

3. Testing user operations...
   ✅ Created test user: <EMAIL>
   ✅ Retrieved user: Test User

4. Testing social account operations...
   ✅ Created social account: TWITTER - @testuser

5. Testing post operations...
   ✅ Created test post: "This is a test post for eWasl database integration! 🚀"

6. Testing activity logging...
   ✅ Created activity log: POST_SCHEDULED

7. Testing database relationships...
   ✅ User has 1 posts
   ✅ User has 1 social accounts
   ✅ User has 1 activities

8. Cleaning up test data...
   ✅ Test data cleaned up

🎉 All database tests passed successfully!

📊 Database Integration Summary:
   ✅ Connection: Working
   ✅ Tables: All created
   ✅ CRUD Operations: Working
   ✅ Relationships: Working
   ✅ Prisma Client: Working
```

### **Step 4: Test Application Health**

Restart the development server and test the health endpoint:

```bash
# Kill current dev server (Ctrl+C)
# Then restart:
npm run dev

# In another terminal, test health endpoint:
curl http://localhost:3000/api/health
```

**Expected Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-XX...",
  "database": "connected",
  "version": "1.0.0"
}
```

### **Step 5: Test Authentication Flow**

1. **Open application:** http://localhost:3000
2. **Test user registration/login**
3. **Verify dashboard access**
4. **Test social media connections page**

---

## 📊 **New Database Configuration**

### **Project Details:**
- **Project ID:** nnxfzhxqzmriggulsudr
- **Project Name:** App.eWasl.com
- **Region:** eu-central-1
- **Status:** ACTIVE_HEALTHY
- **Database Version:** PostgreSQL 17.4.1

### **Connection Details:**
- **API URL:** https://nnxfzhxqzmriggulsudr.supabase.co
- **Database Host:** db.nnxfzhxqzmriggulsudr.supabase.co
- **Anon Key:** eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w

### **Database Schema:**
- ✅ **users** - User accounts and profiles
- ✅ **Account** - NextAuth account linking
- ✅ **Session** - NextAuth session management
- ✅ **VerificationToken** - NextAuth email verification
- ✅ **social_accounts** - Connected social media accounts
- ✅ **posts** - Social media posts and content
- ✅ **activities** - User activity logging
- ✅ **ErrorLog** - Application error tracking
- ✅ **_PostToSocialAccount** - Post-to-platform relationships

---

## 🔍 **Troubleshooting**

### **Common Issues:**

1. **"Database connection failed"**
   - Check DATABASE_URL password is correct
   - Verify Supabase project is active
   - Ensure network connectivity

2. **"Prisma client not found"**
   - Run: `npx prisma generate`
   - Restart development server

3. **"Health endpoint returns 500"**
   - Check all environment variables are set
   - Verify database connection
   - Check server logs for specific errors

4. **"Authentication not working"**
   - Verify NEXTAUTH_SECRET is set
   - Check NEXTAUTH_URL matches your domain
   - Ensure Supabase service role key is correct

### **Verification Commands:**
```bash
# Test Supabase connection
node test-db-connection.js

# Test Prisma connection
node test-prisma-connection.js

# Check environment variables
echo $DATABASE_URL
echo $NEXT_PUBLIC_SUPABASE_URL

# Regenerate Prisma client
npx prisma generate

# Check database tables
npx prisma studio
```

---

## ✅ **Success Criteria**

Your migration is complete when:
- [ ] Database test script passes all tests
- [ ] Health API endpoint returns 200 OK
- [ ] Application loads without errors
- [ ] User registration/login works
- [ ] Dashboard displays correctly
- [ ] Social media connections page loads

---

## 📞 **Next Steps**

Once the database migration is complete:
1. **Test all application features** thoroughly
2. **Update production environment** variables
3. **Run comprehensive test suite**
4. **Deploy to production** with new database
5. **Monitor application** for any issues

**The new Supabase database is ready and waiting for your credentials!** 🚀
