import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createClient as createServiceClient } from '@supabase/supabase-js'

// Service role client for storage operations
const supabaseService = createServiceClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // Test 1: List storage buckets
    console.log('🪣 Testing storage buckets...')
    const { data: buckets, error: bucketsError } = await supabaseService.storage.listBuckets()
    
    if (bucketsError) {
      console.error('❌ Failed to list buckets:', bucketsError)
    } else {
      console.log('✅ Available buckets:', buckets?.map(b => b.name))
    }

    // Test 2: Check if 'media' bucket exists
    const mediaBucketExists = buckets?.some(bucket => bucket.name === 'media')
    const mediaFilesBucketExists = buckets?.some(bucket => bucket.name === 'media-files')

    // Test 3: Try to create a test file upload
    let uploadTest = null
    if (mediaBucketExists) {
      try {
        console.log('📤 Testing upload to media bucket...')
        const testContent = 'Test file content for eWasl media upload'
        const testFileName = `test/${user.id}/test-${Date.now()}.txt`
        
        const { data: uploadData, error: uploadError } = await supabaseService.storage
          .from('media')
          .upload(testFileName, testContent, {
            contentType: 'text/plain',
            upsert: false
          })

        if (uploadError) {
          console.error('❌ Upload test failed:', uploadError)
          uploadTest = {
            success: false,
            error: uploadError.message
          }
        } else {
          console.log('✅ Upload test successful:', uploadData.path)
          
          // Get public URL
          const { data: urlData } = supabaseService.storage
            .from('media')
            .getPublicUrl(testFileName)

          // Clean up test file
          await supabaseService.storage
            .from('media')
            .remove([testFileName])

          uploadTest = {
            success: true,
            path: uploadData.path,
            publicUrl: urlData.publicUrl
          }
        }
      } catch (error) {
        console.error('❌ Upload test exception:', error)
        uploadTest = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }

    // Test 4: Check storage policies
    let policiesTest = null
    try {
      console.log('🔒 Testing storage policies...')
      
      // Try to list files in user's folder
      const { data: files, error: listError } = await supabase.storage
        .from('media')
        .list(`uploads/${user.id}`, {
          limit: 5
        })

      if (listError) {
        console.error('❌ List files failed:', listError)
        policiesTest = {
          success: false,
          error: listError.message
        }
      } else {
        console.log('✅ List files successful:', files?.length || 0, 'files found')
        policiesTest = {
          success: true,
          filesCount: files?.length || 0
        }
      }
    } catch (error) {
      console.error('❌ Policies test exception:', error)
      policiesTest = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }

    // Test 5: Check environment variables
    const envTest = {
      supabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      supabaseAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      supabaseServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      urlValue: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + '...',
    }

    return NextResponse.json({
      success: true,
      userId: user.id,
      tests: {
        buckets: {
          available: buckets?.map(b => ({ name: b.name, public: b.public })) || [],
          mediaBucketExists,
          mediaFilesBucketExists,
          error: bucketsError?.message
        },
        upload: uploadTest,
        policies: policiesTest,
        environment: envTest
      }
    })

  } catch (error) {
    console.error('Storage test error:', error)
    return NextResponse.json({
      error: 'Storage test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    // Test actual file upload
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'لم يتم العثور على ملف' },
        { status: 400 }
      )
    }

    console.log('📤 Testing actual file upload:', file.name, file.type, file.size)

    // Generate unique filename
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const fileExtension = file.name.split('.').pop()
    const fileName = `${timestamp}_${randomString}.${fileExtension}`
    const filePath = `test-uploads/${user.id}/${fileName}`

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const buffer = new Uint8Array(arrayBuffer)

    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabaseService.storage
      .from('media')
      .upload(filePath, buffer, {
        contentType: file.type,
        upsert: false
      })

    if (uploadError) {
      console.error('❌ File upload failed:', uploadError)
      return NextResponse.json({
        success: false,
        error: 'فشل في رفع الملف',
        details: uploadError.message
      }, { status: 500 })
    }

    // Get public URL
    const { data: urlData } = supabaseService.storage
      .from('media')
      .getPublicUrl(filePath)

    console.log('✅ File upload successful:', uploadData.path)

    return NextResponse.json({
      success: true,
      fileName,
      originalName: file.name,
      fileType: file.type,
      fileSize: file.size,
      path: uploadData.path,
      publicUrl: urlData.publicUrl
    })

  } catch (error) {
    console.error('File upload test error:', error)
    return NextResponse.json({
      error: 'File upload test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
