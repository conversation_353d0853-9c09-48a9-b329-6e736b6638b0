-- Migration: 003_publishing_results.sql
-- Purpose: Track publishing results for immediate and scheduled posts
-- Date: January 2025

-- Create publishing_results table to track all publishing attempts
CREATE TABLE IF NOT EXISTS publishing_results (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
  social_account_id UUID REFERENCES social_accounts(id) ON DELETE CASCADE,
  platform TEXT NOT NULL,
  success BOOLEAN NOT NULL,
  external_post_id TEXT,
  external_url TEXT,
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(post_id, social_account_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_publishing_results_post_id ON publishing_results(post_id);
CREATE INDEX IF NOT EXISTS idx_publishing_results_success ON publishing_results(success);
CREATE INDEX IF NOT EXISTS idx_publishing_results_platform ON publishing_results(platform);
CREATE INDEX IF NOT EXISTS idx_publishing_results_published_at ON publishing_results(published_at);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_publishing_results_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_publishing_results_updated_at
  BEFORE UPDATE ON publishing_results
  FOR EACH ROW
  EXECUTE FUNCTION update_publishing_results_updated_at();

-- Add comments for documentation
COMMENT ON TABLE publishing_results IS 'Tracks publishing attempts and results for all social media platforms';
COMMENT ON COLUMN publishing_results.post_id IS 'Reference to the post being published';
COMMENT ON COLUMN publishing_results.social_account_id IS 'Reference to the social account used for publishing';
COMMENT ON COLUMN publishing_results.platform IS 'Social media platform (FACEBOOK, TWITTER, LINKEDIN, INSTAGRAM)';
COMMENT ON COLUMN publishing_results.success IS 'Whether the publishing attempt was successful';
COMMENT ON COLUMN publishing_results.external_post_id IS 'Post ID returned by the social media platform';
COMMENT ON COLUMN publishing_results.external_url IS 'Direct URL to the published post';
COMMENT ON COLUMN publishing_results.error_message IS 'Error message if publishing failed';
COMMENT ON COLUMN publishing_results.retry_count IS 'Number of retry attempts made';
COMMENT ON COLUMN publishing_results.published_at IS 'Timestamp when the post was successfully published'; 