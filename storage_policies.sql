-- Storage Bucket Policies for media-files bucket
-- Run these in Supabase SQL Editor after creating the media-files bucket

-- Policy 1: Allow authenticated users to upload files to their own folder
CREATE POLICY "Users can upload to their own folder" ON storage.objects
  FOR INSERT 
  WITH CHECK (
    bucket_id = 'media-files' 
    AND auth.uid()::text = (storage.foldername(name))[1]
    AND auth.role() = 'authenticated'
  );

-- Policy 2: Allow authenticated users to view their own files
CREATE POLICY "Users can view their own files" ON storage.objects
  FOR SELECT 
  USING (
    bucket_id = 'media-files' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Policy 3: Allow authenticated users to update their own files
CREATE POLICY "Users can update their own files" ON storage.objects
  FOR UPDATE 
  USING (
    bucket_id = 'media-files' 
    AND auth.uid()::text = (storage.foldername(name))[1]
    AND auth.role() = 'authenticated'
  );

-- Policy 4: Allow authenticated users to delete their own files
CREATE POLICY "Users can delete their own files" ON storage.objects
  FOR DELETE 
  USING (
    bucket_id = 'media-files' 
    AND auth.uid()::text = (storage.foldername(name))[1]
    AND auth.role() = 'authenticated'
  );

-- Verify policies are created
SELECT 
  policyname, 
  cmd, 
  qual 
FROM pg_policies 
WHERE schemaname = 'storage' 
  AND tablename = 'objects' 
  AND policyname LIKE '%media-files%' 
  OR policyname LIKE '%Users can%';

-- Alternative: If the above policies don't work, try these simpler ones:

-- Simple policy for uploads (fallback)
-- CREATE POLICY "Authenticated users can upload media" ON storage.objects
--   FOR INSERT 
--   WITH CHECK (
--     bucket_id = 'media-files' 
--     AND auth.role() = 'authenticated'
--   );

-- Simple policy for viewing (fallback)
-- CREATE POLICY "Authenticated users can view media" ON storage.objects
--   FOR SELECT 
--   USING (
--     bucket_id = 'media-files'
--   );

-- Simple policy for deleting (fallback)
-- CREATE POLICY "Authenticated users can delete their media" ON storage.objects
--   FOR DELETE 
--   USING (
--     bucket_id = 'media-files' 
--     AND auth.role() = 'authenticated'
--   );
