"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  TrendingUp,
  TrendingDown,
  Eye,
  Heart,
  MessageCircle,
  Share,
  Users,
  Calendar,
  PlusCircle,
  BarChart3,
  Zap,
  Clock,
  Target,
  Globe,
  Smartphone,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileDashboardProps {
  language?: 'ar' | 'en';
  className?: string;
}

interface MetricCard {
  id: string;
  title: string;
  value: string;
  change: number;
  icon: React.ComponentType<any>;
  color: string;
}

export function MobileDashboard({ language = 'ar', className }: MobileDashboardProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState('overview');

  // Translations
  const t = {
    ar: {
      title: 'لوحة التحكم',
      subtitle: 'نظرة سريعة على أداءك',
      tabs: {
        overview: 'نظرة عامة',
        analytics: 'التحليلات',
        quick: 'سريع'
      },
      metrics: {
        totalPosts: 'إجمالي المنشورات',
        engagement: 'التفاعل',
        reach: 'الوصول',
        followers: 'المتابعون',
        scheduledPosts: 'منشورات مجدولة',
        publishedToday: 'نُشر اليوم'
      },
      actions: {
        newPost: 'منشور جديد',
        viewAnalytics: 'عرض التحليلات',
        schedule: 'جدولة',
        refresh: 'تحديث'
      },
      quickActions: {
        title: 'إجراءات سريعة',
        createPost: 'إنشاء منشور',
        uploadMedia: 'رفع وسائط',
        viewSchedule: 'عرض الجدولة',
        checkAnalytics: 'فحص التحليلات'
      },
      recentActivity: {
        title: 'النشاط الأخير',
        noActivity: 'لا يوجد نشاط حديث'
      },
      platforms: {
        title: 'المنصات المتصلة',
        connected: 'متصل',
        disconnected: 'غير متصل'
      }
    },
    en: {
      title: 'Dashboard',
      subtitle: 'Quick overview of your performance',
      tabs: {
        overview: 'Overview',
        analytics: 'Analytics',
        quick: 'Quick'
      },
      metrics: {
        totalPosts: 'Total Posts',
        engagement: 'Engagement',
        reach: 'Reach',
        followers: 'Followers',
        scheduledPosts: 'Scheduled Posts',
        publishedToday: 'Published Today'
      },
      actions: {
        newPost: 'New Post',
        viewAnalytics: 'View Analytics',
        schedule: 'Schedule',
        refresh: 'Refresh'
      },
      quickActions: {
        title: 'Quick Actions',
        createPost: 'Create Post',
        uploadMedia: 'Upload Media',
        viewSchedule: 'View Schedule',
        checkAnalytics: 'Check Analytics'
      },
      recentActivity: {
        title: 'Recent Activity',
        noActivity: 'No recent activity'
      },
      platforms: {
        title: 'Connected Platforms',
        connected: 'Connected',
        disconnected: 'Disconnected'
      }
    }
  };

  const text = t[language];

  // Sample metrics data
  const metrics: MetricCard[] = [
    {
      id: 'posts',
      title: text.metrics.totalPosts,
      value: '156',
      change: 12.5,
      icon: BarChart3,
      color: 'blue'
    },
    {
      id: 'engagement',
      title: text.metrics.engagement,
      value: '12.8K',
      change: 18.3,
      icon: Heart,
      color: 'red'
    },
    {
      id: 'reach',
      title: text.metrics.reach,
      value: '45.6K',
      change: 15.7,
      icon: Users,
      color: 'green'
    },
    {
      id: 'followers',
      title: text.metrics.followers,
      value: '2.3K',
      change: -2.1,
      icon: Users,
      color: 'purple'
    }
  ];

  // Quick actions
  const quickActions = [
    {
      id: 'create',
      title: text.quickActions.createPost,
      icon: PlusCircle,
      color: 'bg-blue-500',
      href: '/posts/create'
    },
    {
      id: 'schedule',
      title: text.quickActions.viewSchedule,
      icon: Calendar,
      color: 'bg-green-500',
      href: '/schedule'
    },
    {
      id: 'analytics',
      title: text.quickActions.checkAnalytics,
      icon: BarChart3,
      color: 'bg-purple-500',
      href: '/analytics'
    },
    {
      id: 'media',
      title: text.quickActions.uploadMedia,
      icon: Globe,
      color: 'bg-orange-500',
      href: '/media'
    }
  ];

  // Platform status
  const platforms = [
    { name: 'Instagram', status: 'connected', icon: '📷', posts: 45 },
    { name: 'Facebook', status: 'connected', icon: '👥', posts: 38 },
    { name: 'Twitter', status: 'connected', icon: '🐦', posts: 42 },
    { name: 'LinkedIn', status: 'disconnected', icon: '💼', posts: 0 }
  ];

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
  };

  // Get trend icon and color
  const getTrendDisplay = (change: number) => {
    const isPositive = change > 0;
    return {
      icon: isPositive ? TrendingUp : TrendingDown,
      color: isPositive ? 'text-green-600' : 'text-red-600',
      bgColor: isPositive ? 'bg-green-50' : 'bg-red-50'
    };
  };

  // Format number
  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        language === 'ar' ? "flex-row-reverse" : ""
      )}>
        <div className={language === 'ar' ? "text-right" : ""}>
          <h1 className="text-2xl font-bold text-gray-900">{text.title}</h1>
          <p className="text-sm text-gray-600">{text.subtitle}</p>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing}
          className={cn(
            "flex items-center gap-2",
            language === 'ar' ? "flex-row-reverse" : ""
          )}
        >
          <RefreshCw className={cn("w-4 h-4", isRefreshing && "animate-spin")} />
          {text.actions.refresh}
        </Button>
      </div>

      {/* Mobile Optimized Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-4">
          <TabsTrigger value="overview" className="text-xs">
            {text.tabs.overview}
          </TabsTrigger>
          <TabsTrigger value="analytics" className="text-xs">
            {text.tabs.analytics}
          </TabsTrigger>
          <TabsTrigger value="quick" className="text-xs">
            {text.tabs.quick}
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {/* Key Metrics Grid */}
          <div className="grid grid-cols-2 gap-3">
            {metrics.map((metric) => {
              const Icon = metric.icon;
              const trend = getTrendDisplay(metric.change);
              const TrendIcon = trend.icon;

              return (
                <Card key={metric.id} className="p-3">
                  <div className="space-y-2">
                    <div className={cn(
                      "flex items-center justify-between",
                      language === 'ar' ? "flex-row-reverse" : ""
                    )}>
                      <div className={`w-8 h-8 bg-${metric.color}-100 rounded-lg flex items-center justify-center`}>
                        <Icon className={`w-4 h-4 text-${metric.color}-600`} />
                      </div>
                      <div className={cn(
                        "flex items-center gap-1",
                        language === 'ar' ? "flex-row-reverse" : ""
                      )}>
                        <TrendIcon className={cn("w-3 h-3", trend.color)} />
                        <span className={cn("text-xs", trend.color)}>
                          {Math.abs(metric.change)}%
                        </span>
                      </div>
                    </div>
                    
                    <div className={language === 'ar' ? "text-right" : ""}>
                      <p className="text-lg font-bold text-gray-900">{metric.value}</p>
                      <p className="text-xs text-gray-600 line-clamp-1">{metric.title}</p>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-3">
            <Card className="p-3">
              <div className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <Clock className="w-4 h-4 text-orange-500" />
                <div className={language === 'ar' ? "text-right" : ""}>
                  <p className="text-sm font-medium">12</p>
                  <p className="text-xs text-gray-600">{text.metrics.scheduledPosts}</p>
                </div>
              </div>
            </Card>

            <Card className="p-3">
              <div className={cn(
                "flex items-center gap-2",
                language === 'ar' ? "flex-row-reverse" : ""
              )}>
                <Zap className="w-4 h-4 text-green-500" />
                <div className={language === 'ar' ? "text-right" : ""}>
                  <p className="text-sm font-medium">3</p>
                  <p className="text-xs text-gray-600">{text.metrics.publishedToday}</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Connected Platforms */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className={cn(
                "text-sm",
                language === 'ar' ? "text-right" : ""
              )}>
                {text.platforms.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                {platforms.map((platform) => (
                  <div
                    key={platform.name}
                    className={cn(
                      "flex items-center justify-between p-2 rounded-lg bg-gray-50",
                      language === 'ar' ? "flex-row-reverse" : ""
                    )}
                  >
                    <div className={cn(
                      "flex items-center gap-2",
                      language === 'ar' ? "flex-row-reverse" : ""
                    )}>
                      <span className="text-lg">{platform.icon}</span>
                      <span className="text-sm font-medium">{platform.name}</span>
                    </div>
                    
                    <div className={cn(
                      "flex items-center gap-2",
                      language === 'ar' ? "flex-row-reverse" : ""
                    )}>
                      {platform.status === 'connected' && (
                        <span className="text-xs text-gray-600">
                          {platform.posts} {language === 'ar' ? 'منشور' : 'posts'}
                        </span>
                      )}
                      <Badge
                        variant={platform.status === 'connected' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {platform.status === 'connected' ? text.platforms.connected : text.platforms.disconnected}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Quick Actions Tab */}
        <TabsContent value="quick" className="space-y-4">
          <div className="grid grid-cols-2 gap-3">
            {quickActions.map((action) => {
              const Icon = action.icon;
              return (
                <Card key={action.id} className="p-4 cursor-pointer hover:shadow-md transition-shadow">
                  <div className={cn(
                    "flex flex-col items-center gap-3 text-center",
                    language === 'ar' ? "text-right" : ""
                  )}>
                    <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center", action.color)}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <span className="text-sm font-medium">{action.title}</span>
                  </div>
                </Card>
              );
            })}
          </div>

          {/* Quick Create Button */}
          <Button className="w-full h-12 text-base" size="lg">
            <PlusCircle className="w-5 h-5 mr-2" />
            {text.actions.newPost}
          </Button>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className={cn(
                "text-sm",
                language === 'ar' ? "text-right" : ""
              )}>
                {language === 'ar' ? 'أداء هذا الأسبوع' : 'This Week Performance'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className={cn(
                    "flex justify-between text-sm mb-1",
                    language === 'ar' ? "flex-row-reverse" : ""
                  )}>
                    <span>{text.metrics.engagement}</span>
                    <span>85%</span>
                  </div>
                  <Progress value={85} className="h-2" />
                </div>
                
                <div>
                  <div className={cn(
                    "flex justify-between text-sm mb-1",
                    language === 'ar' ? "flex-row-reverse" : ""
                  )}>
                    <span>{text.metrics.reach}</span>
                    <span>72%</span>
                  </div>
                  <Progress value={72} className="h-2" />
                </div>
                
                <div>
                  <div className={cn(
                    "flex justify-between text-sm mb-1",
                    language === 'ar' ? "flex-row-reverse" : ""
                  )}>
                    <span>{language === 'ar' ? 'النمو' : 'Growth'}</span>
                    <span>94%</span>
                  </div>
                  <Progress value={94} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Button variant="outline" className="w-full">
            <BarChart3 className="w-4 h-4 mr-2" />
            {text.actions.viewAnalytics}
          </Button>
        </TabsContent>
      </Tabs>
    </div>
  );
}
