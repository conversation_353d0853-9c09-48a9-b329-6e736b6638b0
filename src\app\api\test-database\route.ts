import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Initialize Supabase client
    const supabase = createRouteHandlerClient({ cookies });
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'غير مصرح - يرجى تسجيل الدخول أولاً',
        error_en: 'Unauthorized - Please login first',
        response_time: `${Date.now() - startTime}ms`
      }, { status: 401 });
    }

    // Query social_accounts table for current user
    const { data: socialAccounts, error: queryError } = await supabase
      .from('social_accounts')
      .select(`
        id,
        platform,
        platform_user_id,
        platform_username,
        access_token,
        refresh_token,
        expires_at,
        page_id,
        page_access_token,
        page_name,
        instagram_business_account_id,
        is_active,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (queryError) {
      return NextResponse.json({
        success: false,
        error: 'خطأ في قاعدة البيانات - فشل في استعلام الحسابات الاجتماعية',
        error_en: 'Database error - Failed to query social accounts',
        details: queryError.message,
        response_time: `${Date.now() - startTime}ms`
      }, { status: 500 });
    }

    // Analyze missing fields for each account
    const accountAnalysis = socialAccounts?.map(account => {
      const missingFields = [];
      const criticalFields = {
        page_id: 'معرف الصفحة',
        page_access_token: 'رمز وصول الصفحة',
        page_name: 'اسم الصفحة',
        instagram_business_account_id: 'معرف حساب إنستغرام التجاري'
      };

      // Check for missing critical fields
      Object.entries(criticalFields).forEach(([field, arabicLabel]) => {
        if (!account[field as keyof typeof account] || account[field as keyof typeof account] === null) {
          missingFields.push({
            field,
            arabic_label: arabicLabel,
            status: 'مفقود'
          });
        }
      });

      // Check token expiration
      const isTokenExpired = account.expires_at ? new Date(account.expires_at) < new Date() : false;

      return {
        id: account.id,
        platform: account.platform,
        platform_username: account.platform_username || 'غير محدد',
        is_active: account.is_active,
        token_status: isTokenExpired ? 'منتهي الصلاحية' : 'صالح',
        missing_fields: missingFields,
        missing_count: missingFields.length,
        can_publish: missingFields.length === 0 && !isTokenExpired,
        created_at: account.created_at,
        updated_at: account.updated_at
      };
    }) || [];

    // Generate summary statistics
    const summary = {
      total_accounts: accountAnalysis.length,
      active_accounts: accountAnalysis.filter(acc => acc.is_active).length,
      accounts_ready_to_publish: accountAnalysis.filter(acc => acc.can_publish).length,
      accounts_with_missing_fields: accountAnalysis.filter(acc => acc.missing_count > 0).length,
      platforms: [...new Set(accountAnalysis.map(acc => acc.platform))],
      most_common_missing_fields: getMostCommonMissingFields(accountAnalysis)
    };

    return NextResponse.json({
      success: true,
      message: 'تم استعلام قاعدة البيانات بنجاح',
      message_en: 'Database query successful',
      user_id: user.id,
      user_email: user.email,
      summary,
      accounts: accountAnalysis,
      response_time: `${Date.now() - startTime}ms`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Database test endpoint error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'خطأ داخلي في الخادم أثناء اختبار قاعدة البيانات',
      error_en: 'Internal server error during database test',
      details: error instanceof Error ? error.message : 'Unknown error',
      response_time: `${Date.now() - startTime}ms`
    }, { status: 500 });
  }
}

// Helper function to find most common missing fields
function getMostCommonMissingFields(accounts: any[]) {
  const fieldCounts: Record<string, number> = {};
  
  accounts.forEach(account => {
    account.missing_fields.forEach((field: any) => {
      fieldCounts[field.arabic_label] = (fieldCounts[field.arabic_label] || 0) + 1;
    });
  });

  return Object.entries(fieldCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([field, count]) => ({ field, count }));
}
