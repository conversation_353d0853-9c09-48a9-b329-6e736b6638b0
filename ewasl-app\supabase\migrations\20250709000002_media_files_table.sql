-- Media Files Table for Supabase Storage Integration
-- Stores metadata for uploaded media files

CREATE TABLE IF NOT EXISTS public.media_files (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  file_name TEXT NOT NULL,
  original_name TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  public_url TEXT NOT NULL,
  folder TEXT DEFAULT 'uploads',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_media_files_user_id ON public.media_files(user_id);
CREATE INDEX IF NOT EXISTS idx_media_files_file_type ON public.media_files(file_type);
CREATE INDEX IF NOT EXISTS idx_media_files_folder ON public.media_files(folder);
CREATE INDEX IF NOT EXISTS idx_media_files_created_at ON public.media_files(created_at DESC);

-- Enable RLS
ALTER TABLE public.media_files ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY IF NOT EXISTS "Users can view their own media files" ON public.media_files
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can insert their own media files" ON public.media_files
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own media files" ON public.media_files
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can delete their own media files" ON public.media_files
  FOR DELETE USING (auth.uid() = user_id);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER IF NOT EXISTS update_media_files_updated_at
  BEFORE UPDATE ON public.media_files
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Storage bucket policies (to be run after bucket creation)
-- These policies allow authenticated users to upload and manage their own files

-- Policy for uploads (users can upload to their own folder)
-- INSERT INTO storage.policies (name, bucket_id, policy_definition)
-- VALUES (
--   'Users can upload to their own folder',
--   'media-files',
--   'auth.uid()::text = (storage.foldername(name))[1]'
-- );

-- Policy for viewing (users can view their own files)
-- INSERT INTO storage.policies (name, bucket_id, policy_definition)
-- VALUES (
--   'Users can view their own files',
--   'media-files',
--   'auth.uid()::text = (storage.foldername(name))[1]'
-- );

-- Policy for deleting (users can delete their own files)
-- INSERT INTO storage.policies (name, bucket_id, policy_definition)
-- VALUES (
--   'Users can delete their own files',
--   'media-files',
--   'auth.uid()::text = (storage.foldername(name))[1]'
-- );
