"use client";

import React, { useState } from "react";
import { Plus, Refresh<PERSON><PERSON>, <PERSON>ting<PERSON>, Trash2, ExternalLink, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON>, ActionButton, AsyncButton } from "@/components/ui/enhanced-button";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";

interface SocialAccount {
  id: string;
  platform: 'TWITTER' | 'FACEBOOK' | 'INSTAGRAM' | 'LINKEDIN' | 'SNAPCHAT';
  accountName: string;
  username: string;
  isConnected: boolean;
  followers: number;
  lastSync: Date;
  status: 'active' | 'error' | 'pending';
  avatar?: string;
}

export default function FunctionalSocialPage() {
  const [accounts, setAccounts] = useState<SocialAccount[]>([
    {
      id: "1",
      platform: "TWITTER",
      accountName: "eWasl Official",
      username: "@ewasl_official",
      isConnected: true,
      followers: 15420,
      lastSync: new Date(Date.now() - 1000 * 60 * 30),
      status: "active",
    },
    {
      id: "2",
      platform: "FACEBOOK",
      accountName: "eWasl Page",
      username: "ewasl.page",
      isConnected: true,
      followers: 8750,
      lastSync: new Date(Date.now() - 1000 * 60 * 60 * 2),
      status: "active",
    },
    {
      id: "3",
      platform: "INSTAGRAM",
      accountName: "eWasl",
      username: "@ewasl",
      isConnected: false,
      followers: 0,
      lastSync: new Date(),
      status: "pending",
    },
    {
      id: "4",
      platform: "LINKEDIN",
      accountName: "eWasl Company",
      username: "ewasl-company",
      isConnected: true,
      followers: 2340,
      lastSync: new Date(Date.now() - 1000 * 60 * 60 * 6),
      status: "error",
    },
  ]);

  const [isRefreshing, setIsRefreshing] = useState(false);

  const platformConfig = {
    TWITTER: {
      name: "تويتر",
      icon: "𝕏",
      color: "#000000",
      description: "منصة التواصل الاجتماعي للأخبار والتحديثات",
    },
    FACEBOOK: {
      name: "فيسبوك",
      icon: "f",
      color: "#1877f2",
      description: "أكبر شبكة اجتماعية في العالم",
    },
    INSTAGRAM: {
      name: "إنستغرام",
      icon: "📷",
      color: "#e4405f",
      description: "منصة مشاركة الصور والفيديوهات",
    },
    LINKEDIN: {
      name: "لينكد إن",
      icon: "in",
      color: "#0077b5",
      description: "الشبكة المهنية للأعمال",
    },
    SNAPCHAT: {
      name: "سناب شات",
      icon: "👻",
      color: "#fffc00",
      description: "منصة الرسائل المرئية",
    },
  };

  const handleConnectAccount = async (platform: string) => {
    try {
      toast.loading(`جاري الاتصال بـ ${platformConfig[platform as keyof typeof platformConfig].name}...`);
      
      // Simulate OAuth flow
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update account status
      setAccounts(prev => prev.map(account => 
        account.platform === platform 
          ? { ...account, isConnected: true, status: 'active' as const, lastSync: new Date() }
          : account
      ));
      
      toast.success(`تم ربط حساب ${platformConfig[platform as keyof typeof platformConfig].name} بنجاح!`);
    } catch (error) {
      toast.error(`فشل في ربط حساب ${platformConfig[platform as keyof typeof platformConfig].name}`);
    }
  };

  const handleDisconnectAccount = async (accountId: string, platform: string) => {
    try {
      toast.loading("جاري قطع الاتصال...");
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAccounts(prev => prev.map(account => 
        account.id === accountId 
          ? { ...account, isConnected: false, status: 'pending' as const }
          : account
      ));
      
      toast.success(`تم قطع الاتصال مع ${platformConfig[platform as keyof typeof platformConfig].name}`);
    } catch (error) {
      toast.error("فشل في قطع الاتصال");
    }
  };

  const handleRefreshAccounts = async () => {
    setIsRefreshing(true);
    try {
      toast.loading("جاري تحديث الحسابات...");
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update last sync times
      setAccounts(prev => prev.map(account => ({
        ...account,
        lastSync: new Date(),
        status: account.isConnected ? 'active' as const : account.status
      })));
      
      toast.success("تم تحديث جميع الحسابات بنجاح");
    } catch (error) {
      toast.error("فشل في تحديث الحسابات");
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleSyncAccount = async (accountId: string, platform: string) => {
    try {
      toast.loading(`جاري مزامنة ${platformConfig[platform as keyof typeof platformConfig].name}...`);
      
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setAccounts(prev => prev.map(account => 
        account.id === accountId 
          ? { ...account, lastSync: new Date(), status: 'active' as const }
          : account
      ));
      
      toast.success("تم تحديث الحساب بنجاح");
    } catch (error) {
      toast.error("فشل في تحديث الحساب");
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}م`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}ك`;
    return num.toString();
  };

  const formatLastSync = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffMins < 1) return "الآن";
    if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
    if (diffHours < 24) return `منذ ${diffHours} ساعة`;
    return date.toLocaleDateString('ar-SA');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'pending': return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      default: return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return "نشط";
      case 'error': return "خطأ";
      case 'pending': return "في الانتظار";
      default: return "غير معروف";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50" dir="rtl">
      {/* Header */}
      <DashboardHeader 
        title="الحسابات الاجتماعية" 
        subtitle="إدارة وربط حساباتك على منصات التواصل"
        icon={<Plus className="w-6 h-6" />}
      />

      {/* Main Content */}
      <main className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* Welcome Section */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
                  إدارة الحسابات الاجتماعية 🔗
                </h1>
                <p className="text-gray-600 text-lg mb-4">
                  اربط حساباتك على منصات التواصل الاجتماعي لإدارة المحتوى من مكان واحد
                </p>
                <div className="flex items-center gap-4">
                  <Badge variant="secondary" className="text-sm">
                    {accounts.filter(a => a.isConnected).length} حساب متصل
                  </Badge>
                  <Badge variant="outline" className="text-sm">
                    {accounts.filter(a => a.status === 'active').length} حساب نشط
                  </Badge>
                </div>
              </div>
              <AsyncButton
                asyncAction={handleRefreshAccounts}
                icon={<RefreshCw className="w-5 h-5" />}
                variant="outline"
                className="shadow-lg"
                successMessage="تم التحديث ✓"
                errorMessage="فشل التحديث ✗"
              >
                تحديث الحسابات
              </AsyncButton>
            </div>
          </div>

          {/* Accounts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(platformConfig).map(([platform, config]) => {
              const account = accounts.find(a => a.platform === platform);
              
              return (
                <Card key={platform} className="bg-white/80 backdrop-blur-sm border-white/50 shadow-xl hover:shadow-2xl transition-all duration-300">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div 
                          className="w-12 h-12 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg"
                          style={{ backgroundColor: config.color }}
                        >
                          {config.icon}
                        </div>
                        <div>
                          <CardTitle className="text-lg">{config.name}</CardTitle>
                          <CardDescription className="text-sm">
                            {config.description}
                          </CardDescription>
                        </div>
                      </div>
                      {account && getStatusIcon(account.status)}
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {account?.isConnected ? (
                      <>
                        {/* Account Info */}
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">اسم الحساب:</span>
                            <span className="font-medium">{account.accountName}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">اسم المستخدم:</span>
                            <span className="font-mono text-sm">{account.username}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">المتابعون:</span>
                            <span className="font-bold text-blue-600">{formatNumber(account.followers)}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">آخر مزامنة:</span>
                            <span className="text-sm">{formatLastSync(account.lastSync)}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">الحالة:</span>
                            <div className="flex items-center gap-2">
                              {getStatusIcon(account.status)}
                              <span className="text-sm">{getStatusText(account.status)}</span>
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex gap-2 pt-4 border-t">
                          <AsyncButton
                            asyncAction={() => handleSyncAccount(account.id, platform)}
                            variant="outline"
                            size="sm"
                            className="flex-1"
                            icon={<RefreshCw className="w-4 h-4" />}
                          >
                            مزامنة
                          </AsyncButton>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDisconnectAccount(account.id, platform)}
                            className="text-red-600 hover:text-red-700"
                          >
                            قطع الاتصال
                          </Button>
                        </div>
                      </>
                    ) : (
                      <>
                        {/* Not Connected */}
                        <div className="text-center py-6">
                          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <XCircle className="w-8 h-8 text-gray-400" />
                          </div>
                          <p className="text-gray-600 mb-4">غير متصل</p>
                          <AsyncButton
                            asyncAction={() => handleConnectAccount(platform)}
                            variant="gradient"
                            className="w-full"
                            icon={<Plus className="w-4 h-4" />}
                          >
                            ربط الحساب
                          </AsyncButton>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Help Section */}
          <div className="mt-8 bg-blue-50 rounded-2xl p-6 border border-blue-200">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">💡 نصائح مهمة</h3>
            <ul className="space-y-2 text-blue-800">
              <li className="flex items-start gap-2">
                <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <span>تأكد من أن لديك صلاحيات الإدارة على الحسابات التي تريد ربطها</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <span>يتم تحديث بيانات الحسابات تلقائياً كل ساعة</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <span>يمكنك قطع الاتصال وإعادة ربط الحسابات في أي وقت</span>
              </li>
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
}
