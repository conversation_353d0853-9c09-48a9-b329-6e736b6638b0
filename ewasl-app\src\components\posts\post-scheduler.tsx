'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { toast } from 'sonner'
import { 
  Calendar as CalendarIcon,
  Clock,
  Globe,
  Zap,
  Timer,
  CheckCircle2,
  AlertCircle,
  Info
} from 'lucide-react'
import { format, addMinutes, addHours, addDays, isAfter, isBefore } from 'date-fns'
import { arSA } from 'date-fns/locale'
import { cn } from '@/lib/utils'

interface PostSchedulerProps {
  onScheduleChange: (schedule: ScheduleData | null) => void
  selectedPlatforms: string[]
  className?: string
  initialSchedule?: ScheduleData
}

interface ScheduleData {
  type: 'immediate' | 'scheduled' | 'optimal'
  scheduledAt?: Date
  timezone: string
  optimalTiming?: {
    enabled: boolean
    timeRange: 'morning' | 'afternoon' | 'evening' | 'auto'
  }
}

// Timezone options for Arabic regions
const TIMEZONE_OPTIONS = [
  { value: 'Asia/Riyadh', label: 'الرياض (GMT+3)', offset: '+03:00' },
  { value: 'Asia/Dubai', label: 'دبي (GMT+4)', offset: '+04:00' },
  { value: 'Asia/Kuwait', label: 'الكويت (GMT+3)', offset: '+03:00' },
  { value: 'Asia/Qatar', label: 'قطر (GMT+3)', offset: '+03:00' },
  { value: 'Asia/Bahrain', label: 'البحرين (GMT+3)', offset: '+03:00' },
  { value: 'Africa/Cairo', label: 'القاهرة (GMT+2)', offset: '+02:00' },
  { value: 'Asia/Baghdad', label: 'بغداد (GMT+3)', offset: '+03:00' },
  { value: 'Asia/Beirut', label: 'بيروت (GMT+2)', offset: '+02:00' },
  { value: 'Asia/Damascus', label: 'دمشق (GMT+2)', offset: '+02:00' },
  { value: 'Africa/Casablanca', label: 'الدار البيضاء (GMT+1)', offset: '+01:00' },
]

// Quick schedule options
const QUICK_OPTIONS = [
  { label: 'خلال 5 دقائق', minutes: 5 },
  { label: 'خلال 15 دقيقة', minutes: 15 },
  { label: 'خلال ساعة', minutes: 60 },
  { label: 'خلال 3 ساعات', minutes: 180 },
  { label: 'غداً في نفس الوقت', hours: 24 },
  { label: 'الأسبوع القادم', days: 7 },
]

export function PostScheduler({
  onScheduleChange,
  selectedPlatforms,
  className,
  initialSchedule
}: PostSchedulerProps) {
  const [scheduleType, setScheduleType] = useState<'immediate' | 'scheduled' | 'optimal'>(
    initialSchedule?.type || 'immediate'
  )
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    initialSchedule?.scheduledAt
  )
  const [selectedTime, setSelectedTime] = useState<string>(
    initialSchedule?.scheduledAt 
      ? format(initialSchedule.scheduledAt, 'HH:mm')
      : format(new Date(), 'HH:mm')
  )
  const [timezone, setTimezone] = useState<string>(
    initialSchedule?.timezone || 'Asia/Riyadh'
  )
  const [optimalTiming, setOptimalTiming] = useState({
    enabled: initialSchedule?.optimalTiming?.enabled || false,
    timeRange: initialSchedule?.optimalTiming?.timeRange || 'auto' as const
  })
  const [showCalendar, setShowCalendar] = useState(false)

  // Update parent component when schedule changes
  useEffect(() => {
    let scheduleData: ScheduleData | null = null

    if (scheduleType === 'immediate') {
      scheduleData = {
        type: 'immediate',
        timezone
      }
    } else if (scheduleType === 'scheduled' && selectedDate) {
      const [hours, minutes] = selectedTime.split(':').map(Number)
      const scheduledAt = new Date(selectedDate)
      scheduledAt.setHours(hours, minutes, 0, 0)

      // Validate that the scheduled time is in the future
      if (isAfter(scheduledAt, new Date())) {
        scheduleData = {
          type: 'scheduled',
          scheduledAt,
          timezone
        }
      }
    } else if (scheduleType === 'optimal') {
      scheduleData = {
        type: 'optimal',
        timezone,
        optimalTiming
      }
    }

    onScheduleChange(scheduleData)
  }, [scheduleType, selectedDate, selectedTime, timezone, optimalTiming, onScheduleChange])

  const handleQuickSchedule = (option: typeof QUICK_OPTIONS[0]) => {
    const now = new Date()
    let scheduledDate: Date

    if (option.minutes) {
      scheduledDate = addMinutes(now, option.minutes)
    } else if (option.hours) {
      scheduledDate = addHours(now, option.hours)
    } else if (option.days) {
      scheduledDate = addDays(now, option.days)
    } else {
      return
    }

    setSelectedDate(scheduledDate)
    setSelectedTime(format(scheduledDate, 'HH:mm'))
    setScheduleType('scheduled')
    toast.success(`تم جدولة المنشور لـ ${option.label}`)
  }

  const getScheduleStatus = (): { type: 'success' | 'warning' | 'error', message: string } => {
    if (scheduleType === 'immediate') {
      return { type: 'success', message: 'سيتم نشر المنشور فوراً' }
    }

    if (scheduleType === 'scheduled') {
      if (!selectedDate) {
        return { type: 'error', message: 'يرجى اختيار تاريخ للجدولة' }
      }

      const [hours, minutes] = selectedTime.split(':').map(Number)
      const scheduledAt = new Date(selectedDate)
      scheduledAt.setHours(hours, minutes, 0, 0)

      if (isBefore(scheduledAt, new Date())) {
        return { type: 'error', message: 'لا يمكن جدولة المنشور في الماضي' }
      }

      const timeUntil = scheduledAt.getTime() - new Date().getTime()
      const hoursUntil = Math.floor(timeUntil / (1000 * 60 * 60))
      const minutesUntil = Math.floor((timeUntil % (1000 * 60 * 60)) / (1000 * 60))

      if (hoursUntil < 1) {
        return { 
          type: 'warning', 
          message: `سيتم النشر خلال ${minutesUntil} دقيقة` 
        }
      }

      return { 
        type: 'success', 
        message: `سيتم النشر خلال ${hoursUntil} ساعة و ${minutesUntil} دقيقة` 
      }
    }

    if (scheduleType === 'optimal') {
      return { 
        type: 'success', 
        message: 'سيتم النشر في الوقت الأمثل لأفضل تفاعل' 
      }
    }

    return { type: 'error', message: 'يرجى اختيار نوع الجدولة' }
  }

  const status = getScheduleStatus()
  const StatusIcon = status.type === 'success' ? CheckCircle2 : status.type === 'warning' ? AlertCircle : Info

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-right flex items-center gap-2">
          <Clock className="h-5 w-5" />
          جدولة النشر
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Schedule Type Selection */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">نوع الجدولة</Label>
          <div className="grid grid-cols-1 gap-2">
            <Button
              variant={scheduleType === 'immediate' ? 'default' : 'outline'}
              onClick={() => setScheduleType('immediate')}
              className="justify-start h-auto p-3"
            >
              <div className="flex items-center gap-3">
                <Zap className="h-4 w-4" />
                <div className="text-right">
                  <div className="font-medium">نشر فوري</div>
                  <div className="text-xs text-muted-foreground">نشر المنشور فور الضغط على "نشر"</div>
                </div>
              </div>
            </Button>

            <Button
              variant={scheduleType === 'scheduled' ? 'default' : 'outline'}
              onClick={() => setScheduleType('scheduled')}
              className="justify-start h-auto p-3"
            >
              <div className="flex items-center gap-3">
                <Timer className="h-4 w-4" />
                <div className="text-right">
                  <div className="font-medium">جدولة مخصصة</div>
                  <div className="text-xs text-muted-foreground">اختيار تاريخ ووقت محدد للنشر</div>
                </div>
              </div>
            </Button>

            <Button
              variant={scheduleType === 'optimal' ? 'default' : 'outline'}
              onClick={() => setScheduleType('optimal')}
              className="justify-start h-auto p-3"
              disabled={selectedPlatforms.length === 0}
            >
              <div className="flex items-center gap-3">
                <Globe className="h-4 w-4" />
                <div className="text-right">
                  <div className="font-medium">الوقت الأمثل</div>
                  <div className="text-xs text-muted-foreground">
                    {selectedPlatforms.length === 0 
                      ? 'اختر منصة أولاً' 
                      : 'نشر في أفضل وقت للتفاعل'
                    }
                  </div>
                </div>
              </div>
            </Button>
          </div>
        </div>

        {/* Scheduled Date & Time Selection */}
        {scheduleType === 'scheduled' && (
          <div className="space-y-4">
            {/* Quick Options */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">خيارات سريعة</Label>
              <div className="flex flex-wrap gap-2">
                {QUICK_OPTIONS.map((option, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickSchedule(option)}
                    className="text-xs"
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>

            <Separator />

            {/* Date Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">التاريخ</Label>
              <Popover open={showCalendar} onOpenChange={setShowCalendar}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-right font-normal',
                      !selectedDate && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="ml-2 h-4 w-4" />
                    {selectedDate ? (
                      format(selectedDate, 'PPP', { locale: arSA })
                    ) : (
                      'اختر التاريخ'
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={(date) => {
                      setSelectedDate(date)
                      setShowCalendar(false)
                    }}
                    disabled={(date) => isBefore(date, new Date())}
                    initialFocus
                    locale={arSA}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Time Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">الوقت</Label>
              <Input
                type="time"
                value={selectedTime}
                onChange={(e) => setSelectedTime(e.target.value)}
                className="text-right"
              />
            </div>
          </div>
        )}

        {/* Optimal Timing Settings */}
        {scheduleType === 'optimal' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">تفعيل الوقت الأمثل</Label>
              <Switch
                checked={optimalTiming.enabled}
                onCheckedChange={(enabled) => 
                  setOptimalTiming(prev => ({ ...prev, enabled }))
                }
              />
            </div>

            {optimalTiming.enabled && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">الفترة المفضلة</Label>
                <Select
                  value={optimalTiming.timeRange}
                  onValueChange={(value: any) => 
                    setOptimalTiming(prev => ({ ...prev, timeRange: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">تلقائي (أفضل وقت)</SelectItem>
                    <SelectItem value="morning">الصباح (6-12 ص)</SelectItem>
                    <SelectItem value="afternoon">بعد الظهر (12-6 م)</SelectItem>
                    <SelectItem value="evening">المساء (6-12 م)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        )}

        {/* Timezone Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">المنطقة الزمنية</Label>
          <Select value={timezone} onValueChange={setTimezone}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {TIMEZONE_OPTIONS.map((tz) => (
                <SelectItem key={tz.value} value={tz.value}>
                  {tz.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status Display */}
        <div className={cn(
          'flex items-center gap-2 p-3 rounded-lg border',
          status.type === 'success' && 'bg-green-50 border-green-200 text-green-800',
          status.type === 'warning' && 'bg-yellow-50 border-yellow-200 text-yellow-800',
          status.type === 'error' && 'bg-red-50 border-red-200 text-red-800'
        )}>
          <StatusIcon className="h-4 w-4" />
          <span className="text-sm font-medium">{status.message}</span>
        </div>
      </CardContent>
    </Card>
  )
}
