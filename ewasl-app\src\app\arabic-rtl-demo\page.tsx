"use client";

import React, { useState, useEffect } from 'react';
import { ArabicRTLShowcase } from '@/components/i18n/arabic-rtl-showcase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Globe,
  ArrowLeft,
  Sparkles,
  CheckCircle,
  Info,
  Zap,
  Type,
  Layout,
  Palette,
  Languages,
  Eye,
  MousePointer
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { useRTL } from '@/lib/i18n/arabic-rtl-optimizer';

interface ArabicRTLDemoPageProps {
  searchParams?: {
    lang?: string;
  };
}

export default function ArabicRTLDemoPage({ searchParams }: ArabicRTLDemoPageProps) {
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const router = useRouter();
  const rtl = useRTL(language);

  useEffect(() => {
    // Set language from search params
    if (searchParams?.lang === 'en') {
      setLanguage('en');
    }

    // Apply RTL to document
    document.documentElement.dir = rtl.direction;
    document.documentElement.lang = language;
  }, [searchParams, language, rtl.direction]);

  const handleLanguageChange = (newLanguage: 'ar' | 'en') => {
    setLanguage(newLanguage);
    
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('lang', newLanguage);
    window.history.replaceState({}, '', url.toString());
  };

  // RTL Features
  const rtlFeatures = [
    {
      icon: Type,
      title: language === 'ar' ? 'خطوط محسنة' : 'Optimized Fonts',
      description: language === 'ar' 
        ? 'خطوط عربية محسنة للقراءة والوضوح'
        : 'Arabic fonts optimized for readability and clarity',
      color: 'blue'
    },
    {
      icon: Layout,
      title: language === 'ar' ? 'تخطيط RTL' : 'RTL Layout',
      description: language === 'ar'
        ? 'تخطيط كامل من اليمين إلى اليسار'
        : 'Complete right-to-left layout system',
      color: 'green'
    },
    {
      icon: Languages,
      title: language === 'ar' ? 'ترجمة شاملة' : 'Complete Translation',
      description: language === 'ar'
        ? 'ترجمة كاملة لجميع عناصر الواجهة'
        : 'Complete translation of all UI elements',
      color: 'purple'
    },
    {
      icon: Palette,
      title: language === 'ar' ? 'ألوان متناسقة' : 'Consistent Colors',
      description: language === 'ar'
        ? 'نظام ألوان متناسق مع الثقافة العربية'
        : 'Color system consistent with Arabic culture',
      color: 'orange'
    },
    {
      icon: MousePointer,
      title: language === 'ar' ? 'تفاعل محسن' : 'Enhanced Interaction',
      description: language === 'ar'
        ? 'تفاعلات محسنة للمستخدمين العرب'
        : 'Enhanced interactions for Arabic users',
      color: 'red'
    },
    {
      icon: Eye,
      title: language === 'ar' ? 'تجربة بصرية' : 'Visual Experience',
      description: language === 'ar'
        ? 'تجربة بصرية مثالية للقراءة العربية'
        : 'Perfect visual experience for Arabic reading',
      color: 'indigo'
    }
  ];

  // Performance metrics
  const performanceMetrics = [
    {
      label: language === 'ar' ? 'دعم RTL' : 'RTL Support',
      value: '100%',
      color: 'green'
    },
    {
      label: language === 'ar' ? 'الترجمة' : 'Translation',
      value: '100%',
      color: 'blue'
    },
    {
      label: language === 'ar' ? 'الخطوط' : 'Fonts',
      value: '100%',
      color: 'purple'
    },
    {
      label: language === 'ar' ? 'الأداء' : 'Performance',
      value: '98%',
      color: 'orange'
    }
  ];

  return (
    <div className={cn(
      "min-h-screen bg-gray-50",
      language === 'ar' ? "rtl" : "ltr"
    )} dir={rtl.direction}>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className={rtl.cn(
          "flex items-center justify-between",
          rtl.flex()
        )}>
          <div className={rtl.textAlign()}>
            <div className={rtl.cn(
              "flex items-center gap-3 mb-2",
              rtl.flex()
            )}>
              <h1 className="text-4xl font-bold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                {language === 'ar' ? 'عرض تحسينات اللغة العربية' : 'Arabic RTL Optimization Demo'}
              </h1>
              <Badge variant="secondary" className="text-xs">
                <Sparkles className="w-3 h-3 mr-1" />
                {language === 'ar' ? 'محسن' : 'Optimized'}
              </Badge>
            </div>
            <p className="text-xl text-gray-600" style={{ fontFamily: rtl.getFontFamily('primary') }}>
              {language === 'ar'
                ? 'عرض شامل لتحسينات اللغة العربية ودعم الكتابة من اليمين إلى اليسار'
                : 'Comprehensive showcase of Arabic language optimizations and RTL support'
              }
            </p>
          </div>
          
          <div className={rtl.cn(
            "flex items-center gap-4",
            rtl.flex()
          )}>
            <Button
              variant="outline"
              onClick={() => handleLanguageChange(language === 'ar' ? 'en' : 'ar')}
              className={rtl.cn(
                "flex items-center gap-2",
                rtl.flex()
              )}
            >
              <Globe className="w-4 h-4" />
              {language === 'ar' ? 'English' : 'العربية'}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className={rtl.cn(
                "flex items-center gap-2",
                rtl.flex()
              )}
            >
              <ArrowLeft className={cn("w-4 h-4", language === 'ar' && "rotate-180")} />
              {language === 'ar' ? 'العودة للوحة التحكم' : 'Back to Dashboard'}
            </Button>
          </div>
        </div>

        {/* Language Status Alert */}
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertTitle className={rtl.cn(
            "text-green-900",
            rtl.textAlign()
          )}>
            {language === 'ar' ? 'تحسين RTL نشط' : 'RTL Optimization Active'}
          </AlertTitle>
          <AlertDescription className={rtl.cn(
            "text-green-800",
            rtl.textAlign()
          )}>
            {language === 'ar'
              ? 'جميع العناصر محسنة للكتابة من اليمين إلى اليسار مع دعم كامل للغة العربية والخطوط المحسنة.'
              : 'All elements are optimized for right-to-left writing with full Arabic language support and optimized fonts.'
            }
          </AlertDescription>
        </Alert>

        {/* RTL Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {rtlFeatures.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className={rtl.cn(
                    "flex items-center gap-3",
                    rtl.flex()
                  )}>
                    <div className={`w-10 h-10 bg-${feature.color}-100 rounded-lg flex items-center justify-center`}>
                      <Icon className={`w-5 h-5 text-${feature.color}-600`} />
                    </div>
                    <CardTitle className="text-lg" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                      {feature.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className={rtl.textAlign()} style={{ fontFamily: rtl.getFontFamily('primary') }}>
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className={rtl.cn(
              "flex items-center gap-3",
              rtl.flex(),
              rtl.textAlign()
            )} style={{ fontFamily: rtl.getFontFamily('heading') }}>
              <Zap className="w-6 h-6 text-yellow-500" />
              {language === 'ar' ? 'مقاييس الأداء' : 'Performance Metrics'}
            </CardTitle>
            <CardDescription className={rtl.textAlign()} style={{ fontFamily: rtl.getFontFamily('primary') }}>
              {language === 'ar'
                ? 'مقاييس شاملة لجودة دعم اللغة العربية والأداء'
                : 'Comprehensive metrics for Arabic language support quality and performance'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {performanceMetrics.map((metric, index) => (
                <div key={index} className={rtl.cn("text-center", rtl.textAlign())}>
                  <div className={`text-3xl font-bold text-${metric.color}-600 mb-2`}>
                    {metric.value}
                  </div>
                  <div className="text-sm text-gray-600" style={{ fontFamily: rtl.getFontFamily('primary') }}>
                    {metric.label}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Technical Details */}
        <Card>
          <CardHeader>
            <CardTitle className={rtl.textAlign()} style={{ fontFamily: rtl.getFontFamily('heading') }}>
              {language === 'ar' ? 'التفاصيل التقنية' : 'Technical Details'}
            </CardTitle>
            <CardDescription className={rtl.textAlign()} style={{ fontFamily: rtl.getFontFamily('primary') }}>
              {language === 'ar'
                ? 'تفاصيل تقنية حول تحسينات RTL والدعم العربي'
                : 'Technical details about RTL optimizations and Arabic support'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className={rtl.cn("font-semibold", rtl.textAlign())} style={{ fontFamily: rtl.getFontFamily('heading') }}>
                  {language === 'ar' ? 'الميزات المطبقة' : 'Implemented Features'}
                </h4>
                <ul className={rtl.cn("space-y-2", rtl.textAlign())} style={{ fontFamily: rtl.getFontFamily('primary') }}>
                  <li className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    {language === 'ar' ? 'اتجاه النص من اليمين إلى اليسار' : 'Right-to-left text direction'}
                  </li>
                  <li className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    {language === 'ar' ? 'خطوط عربية محسنة' : 'Optimized Arabic fonts'}
                  </li>
                  <li className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    {language === 'ar' ? 'تنسيق الأرقام العربية' : 'Arabic number formatting'}
                  </li>
                  <li className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    {language === 'ar' ? 'تنسيق التواريخ العربية' : 'Arabic date formatting'}
                  </li>
                  <li className={rtl.cn("flex items-center gap-2", rtl.flex())}>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    {language === 'ar' ? 'تخطيط Flexbox محسن' : 'Optimized Flexbox layouts'}
                  </li>
                </ul>
              </div>

              <div className="space-y-4">
                <h4 className={rtl.cn("font-semibold", rtl.textAlign())} style={{ fontFamily: rtl.getFontFamily('heading') }}>
                  {language === 'ar' ? 'المواصفات التقنية' : 'Technical Specifications'}
                </h4>
                <div className="space-y-3">
                  <div className={rtl.cn("flex justify-between", rtl.flex())}>
                    <span className="text-gray-600">{language === 'ar' ? 'اتجاه النص:' : 'Text Direction:'}</span>
                    <Badge variant="outline">{rtl.direction.toUpperCase()}</Badge>
                  </div>
                  <div className={rtl.cn("flex justify-between", rtl.flex())}>
                    <span className="text-gray-600">{language === 'ar' ? 'اللغة:' : 'Language:'}</span>
                    <Badge variant="outline">{language.toUpperCase()}</Badge>
                  </div>
                  <div className={rtl.cn("flex justify-between", rtl.flex())}>
                    <span className="text-gray-600">{language === 'ar' ? 'الخط الأساسي:' : 'Primary Font:'}</span>
                    <Badge variant="outline">
                      {language === 'ar' ? 'عربي' : 'Arabic'}
                    </Badge>
                  </div>
                  <div className={rtl.cn("flex justify-between", rtl.flex())}>
                    <span className="text-gray-600">{language === 'ar' ? 'نظام الأرقام:' : 'Number System:'}</span>
                    <Badge variant="outline">
                      {language === 'ar' ? 'عربي-هندي' : 'Arabic-Indic'}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* RTL Showcase Component */}
        <ArabicRTLShowcase 
          language={language} 
          onLanguageChange={handleLanguageChange}
        />

        {/* Footer */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-6">
            <div className={rtl.cn(
              "flex items-center justify-center gap-4",
              rtl.flex()
            )}>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-white" />
              </div>
              <div className={rtl.textAlign()}>
                <h4 className="font-semibold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                  {language === 'ar' ? 'تحسين RTL مكتمل بنجاح' : 'RTL Optimization Successfully Completed'}
                </h4>
                <p className="text-sm text-gray-600" style={{ fontFamily: rtl.getFontFamily('primary') }}>
                  {language === 'ar'
                    ? 'منصة eWasl محسنة بالكامل للغة العربية مع دعم شامل للكتابة من اليمين إلى اليسار'
                    : 'eWasl platform is fully optimized for Arabic language with comprehensive RTL support'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
