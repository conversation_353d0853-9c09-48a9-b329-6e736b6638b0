/**
 * OAuth Providers Configuration
 * Centralized configuration for all social media OAuth providers
 */

export interface OAuthProvider {
  id: string;
  name: string;
  clientId: string;
  clientSecret: string;
  scope: string[];
  authUrl: string;
  tokenUrl: string;
  userInfoUrl: string;
  enabled: boolean;
}

export interface OAuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresAt?: Date;
  scope?: string[];
  tokenType?: string;
}

export interface UserProfile {
  id: string;
  name: string;
  username?: string;
  email?: string;
  avatar?: string;
  platform: string;
}

/**
 * Get OAuth provider configuration
 */
export function getOAuthProvider(platform: string): OAuthProvider | null {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  
  const providers: Record<string, OAuthProvider> = {
    facebook: {
      id: 'facebook',
      name: 'Facebook',
      clientId: process.env.FACEBOOK_APP_ID || '',
      clientSecret: process.env.FACEBOOK_APP_SECRET || '',
      scope: [
        'email',
        'public_profile',
        'pages_show_list',
        'pages_manage_posts',
        'pages_read_engagement',
        // REMOVED: 'business_management' - Triggers Business OAuth flow
        'instagram_basic',
        'instagram_content_publish'
      ],
      authUrl: 'https://www.facebook.com/v19.0/dialog/oauth',
      tokenUrl: 'https://graph.facebook.com/v19.0/oauth/access_token',
      userInfoUrl: 'https://graph.facebook.com/v19.0/me',
      enabled: !!(process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET),
    },
    
    twitter: {
      id: 'twitter',
      name: 'Twitter',
      clientId: process.env.TWITTER_CLIENT_ID || '',
      clientSecret: process.env.TWITTER_CLIENT_SECRET || '',
      scope: [
        'tweet.read',
        'tweet.write',
        'users.read',
        'follows.read',
        'follows.write',
        'offline.access'
      ],
      authUrl: 'https://twitter.com/i/oauth2/authorize',
      tokenUrl: 'https://api.twitter.com/2/oauth2/token',
      userInfoUrl: 'https://api.twitter.com/2/users/me',
      enabled: !!(process.env.TWITTER_CLIENT_ID && process.env.TWITTER_CLIENT_SECRET),
    },
    
    linkedin: {
      id: 'linkedin',
      name: 'LinkedIn',
      clientId: process.env.LINKEDIN_CLIENT_ID || '',
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET || '',
      scope: [
        'r_liteprofile',
        'r_emailaddress',
        'w_member_social'
      ],
      authUrl: 'https://www.linkedin.com/oauth/v2/authorization',
      tokenUrl: 'https://www.linkedin.com/oauth/v2/accessToken',
      userInfoUrl: 'https://api.linkedin.com/v2/me',
      enabled: !!(process.env.LINKEDIN_CLIENT_ID && process.env.LINKEDIN_CLIENT_SECRET),
    },
    
    instagram: {
      id: 'instagram',
      name: 'Instagram',
      clientId: process.env.FACEBOOK_APP_ID || '',
      clientSecret: process.env.FACEBOOK_APP_SECRET || '',
      scope: [
        'instagram_basic',
        'instagram_content_publish',
        'pages_show_list',
        'pages_read_engagement'
        // FIXED: Removed invalid scopes instagram_graph_user_profile, instagram_graph_user_media
        // REMOVED: 'business_management' - Triggers Business OAuth flow
      ],
      authUrl: 'https://www.facebook.com/v19.0/dialog/oauth',
      tokenUrl: 'https://graph.facebook.com/v19.0/oauth/access_token',
      userInfoUrl: 'https://graph.facebook.com/v19.0/me',
      enabled: !!(process.env.FACEBOOK_APP_ID && process.env.FACEBOOK_APP_SECRET),
    },
  };

  return providers[platform.toLowerCase()] || null;
}

/**
 * Get all enabled OAuth providers
 */
export function getEnabledProviders(): OAuthProvider[] {
  const platforms = ['facebook', 'twitter', 'linkedin', 'instagram'];
  return platforms
    .map(platform => getOAuthProvider(platform))
    .filter((provider): provider is OAuthProvider => provider !== null && provider.enabled);
}

/**
 * Generate OAuth authorization URL
 */
export function generateAuthUrl(
  provider: OAuthProvider,
  state: string,
  redirectUri: string,
  codeChallenge?: string
): string {
  const params = new URLSearchParams({
    client_id: provider.clientId,
    redirect_uri: redirectUri,
    scope: provider.scope.join(' '),
    response_type: 'code',
    state,
  });

  // Add PKCE for Twitter OAuth 2.0
  if (provider.id === 'twitter' && codeChallenge) {
    params.append('code_challenge', codeChallenge);
    params.append('code_challenge_method', 'S256');
  }

  return `${provider.authUrl}?${params.toString()}`;
}

/**
 * Exchange authorization code for access token
 */
export async function exchangeCodeForToken(
  provider: OAuthProvider,
  code: string,
  redirectUri: string,
  codeVerifier?: string
): Promise<OAuthTokens> {
  const params = new URLSearchParams({
    client_id: provider.clientId,
    client_secret: provider.clientSecret,
    code,
    redirect_uri: redirectUri,
    grant_type: 'authorization_code',
  });

  // Add PKCE verifier for Twitter
  if (provider.id === 'twitter' && codeVerifier) {
    params.append('code_verifier', codeVerifier);
  }

  const response = await fetch(provider.tokenUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
    },
    body: params.toString(),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Token exchange failed: ${error}`);
  }

  const data = await response.json();
  
  return {
    accessToken: data.access_token,
    refreshToken: data.refresh_token,
    expiresAt: data.expires_in ? new Date(Date.now() + data.expires_in * 1000) : undefined,
    scope: data.scope?.split(' '),
    tokenType: data.token_type || 'bearer',
  };
}

/**
 * Get user profile information
 */
export async function getUserProfile(
  provider: OAuthProvider,
  accessToken: string
): Promise<UserProfile> {
  const response = await fetch(provider.userInfoUrl, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Accept': 'application/json',
    },
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to get user profile: ${error}`);
  }

  const data = await response.json();
  
  // Platform-specific profile mapping
  switch (provider.id) {
    case 'facebook':
    case 'instagram':
      return {
        id: data.id,
        name: data.name,
        username: data.username,
        email: data.email,
        avatar: data.picture?.data?.url,
        platform: provider.id,
      };
      
    case 'twitter':
      return {
        id: data.data.id,
        name: data.data.name,
        username: data.data.username,
        avatar: data.data.profile_image_url,
        platform: provider.id,
      };
      
    case 'linkedin':
      return {
        id: data.id,
        name: `${data.localizedFirstName} ${data.localizedLastName}`,
        avatar: data.profilePicture?.['displayImage~']?.elements?.[0]?.identifiers?.[0]?.identifier,
        platform: provider.id,
      };
      
    default:
      throw new Error(`Unsupported platform: ${provider.id}`);
  }
}
