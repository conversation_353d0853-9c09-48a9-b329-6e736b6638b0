#!/usr/bin/env node

/**
 * Initialize Monitoring Script
 * 
 * This script calls the startup API to initialize monitoring systems.
 * It can be run as part of the application startup process.
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// Configuration
const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
const STARTUP_SECRET_KEY = process.env.STARTUP_SECRET_KEY;
const MAX_RETRIES = 5;
const RETRY_DELAY_MS = 3000;

// Validate environment variables
if (!STARTUP_SECRET_KEY) {
  console.error('❌ STARTUP_SECRET_KEY environment variable is not set');
  process.exit(1);
}

/**
 * Call the startup API to initialize monitoring
 */
async function initializeMonitoring(retryCount = 0) {
  try {
    console.log(`🔄 Initializing monitoring systems (attempt ${retryCount + 1}/${MAX_RETRIES})...`);
    
    const url = new URL('/api/startup', APP_URL);
    const options = {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${STARTUP_SECRET_KEY}`,
        'Content-Type': 'application/json'
      }
    };
    
    // Choose http or https module based on URL protocol
    const httpModule = url.protocol === 'https:' ? https : http;
    
    const response = await new Promise((resolve, reject) => {
      const req = httpModule.request(url, options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const parsedData = JSON.parse(data);
            resolve({
              statusCode: res.statusCode,
              data: parsedData
            });
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      });
      
      req.on('error', (error) => {
        reject(error);
      });
      
      req.end();
    });
    
    if (response.statusCode !== 200) {
      throw new Error(`API returned status code ${response.statusCode}: ${JSON.stringify(response.data)}`);
    }
    
    if (response.data.success) {
      console.log('✅ Monitoring systems initialized successfully');
      return true;
    } else {
      throw new Error(`Initialization failed: ${response.data.error || 'Unknown error'}`);
    }
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    
    // Retry if we haven't reached the maximum number of retries
    if (retryCount < MAX_RETRIES - 1) {
      console.log(`⏱️ Retrying in ${RETRY_DELAY_MS / 1000} seconds...`);
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY_MS));
      return initializeMonitoring(retryCount + 1);
    } else {
      console.error('❌ Maximum retry attempts reached. Monitoring initialization failed.');
      return false;
    }
  }
}

// Execute the initialization
initializeMonitoring()
  .then((success) => {
    if (!success) {
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error(`❌ Unhandled error: ${error.message}`);
    process.exit(1);
  }); 