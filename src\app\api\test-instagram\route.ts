import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Initialize Supabase client
    const supabase = createRouteHandlerClient({ cookies });
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'غير مصرح - يرجى تسجيل الدخول أولاً',
        error_en: 'Unauthorized - Please login first',
        response_time: `${Date.now() - startTime}ms`
      }, { status: 401 });
    }

    // Get Facebook accounts (Instagram is accessed through Facebook)
    const { data: facebookAccounts, error: dbError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'facebook')
      .eq('is_active', true);

    if (dbError) {
      return NextResponse.json({
        success: false,
        error: 'خطأ في قاعدة البيانات - فشل في استعلام حسابات فيسبوك',
        error_en: 'Database error - Failed to query Facebook accounts',
        details: dbError.message,
        response_time: `${Date.now() - startTime}ms`
      }, { status: 500 });
    }

    if (!facebookAccounts || facebookAccounts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لا توجد حسابات فيسبوك متصلة - إنستغرام يتطلب حساب فيسبوك مربوط',
        error_en: 'No Facebook accounts connected - Instagram requires a connected Facebook account',
        response_time: `${Date.now() - startTime}ms`
      }, { status: 404 });
    }

    const testResults = [];

    // Test each Facebook account for Instagram integration
    for (const account of facebookAccounts) {
      const accountTest = {
        account_id: account.id,
        platform_username: account.platform_username,
        instagram_business_account_id: account.instagram_business_account_id,
        tests: [] as any[]
      };

      // Test 1: Check if Instagram Business Account ID exists in database
      if (!account.instagram_business_account_id) {
        accountTest.tests.push({
          test: 'فحص معرف حساب إنستغرام في قاعدة البيانات',
          test_en: 'Instagram Business Account ID Database Check',
          status: 'فشل',
          error: 'معرف حساب إنستغرام التجاري مفقود في قاعدة البيانات',
          recommendation: 'يجب إعادة ربط حساب فيسبوك لجلب معرف إنستغرام التجاري'
        });
      } else {
        accountTest.tests.push({
          test: 'فحص معرف حساب إنستغرام في قاعدة البيانات',
          test_en: 'Instagram Business Account ID Database Check',
          status: 'نجح',
          instagram_business_account_id: account.instagram_business_account_id
        });
      }

      // Test 2: Validate Instagram Business Account access
      if (account.instagram_business_account_id) {
        try {
          const instagramResponse = await fetch(
            `https://graph.facebook.com/v18.0/${account.instagram_business_account_id}?fields=id,username,account_type&access_token=${account.access_token}`
          );
          const instagramData = await instagramResponse.json();

          if (instagramResponse.ok) {
            accountTest.tests.push({
              test: 'التحقق من الوصول لحساب إنستغرام التجاري',
              test_en: 'Instagram Business Account Access Validation',
              status: 'نجح',
              instagram_username: instagramData.username,
              account_type: instagramData.account_type,
              api_response_code: instagramResponse.status
            });
          } else {
            accountTest.tests.push({
              test: 'التحقق من الوصول لحساب إنستغرام التجاري',
              test_en: 'Instagram Business Account Access Validation',
              status: 'فشل',
              error: 'فشل في الوصول لحساب إنستغرام التجاري',
              details: instagramData.error?.message || 'Unknown error',
              api_response_code: instagramResponse.status
            });
          }
        } catch (error) {
          accountTest.tests.push({
            test: 'التحقق من الوصول لحساب إنستغرام التجاري',
            test_en: 'Instagram Business Account Access Validation',
            status: 'خطأ',
            error: 'فشل في الاتصال بـ Instagram API',
            details: error instanceof Error ? error.message : 'Network error'
          });
        }
      }

      // Test 3: Check Instagram posting permissions
      try {
        const permissionsResponse = await fetch(
          `https://graph.facebook.com/v18.0/me/permissions?access_token=${account.access_token}`
        );
        const permissionsData = await permissionsResponse.json();

        if (permissionsResponse.ok) {
          const instagramPermissions = [
            'instagram_basic',
            'instagram_content_publish'
          ];

          const grantedPermissions = permissionsData.data
            ?.filter((p: any) => p.status === 'granted')
            ?.map((p: any) => p.permission) || [];

          const missingInstagramPermissions = instagramPermissions.filter(
            perm => !grantedPermissions.includes(perm)
          );

          accountTest.tests.push({
            test: 'فحص صلاحيات النشر على إنستغرام',
            test_en: 'Instagram Publishing Permissions Check',
            status: missingInstagramPermissions.length === 0 ? 'نجح' : 'فشل',
            granted_instagram_permissions: grantedPermissions.filter(p => p.includes('instagram')),
            missing_instagram_permissions: missingInstagramPermissions,
            error: missingInstagramPermissions.length > 0 ? 
              `صلاحيات إنستغرام مفقودة: ${missingInstagramPermissions.join(', ')}` : null,
            api_response_code: permissionsResponse.status
          });
        }
      } catch (error) {
        accountTest.tests.push({
          test: 'فحص صلاحيات النشر على إنستغرام',
          test_en: 'Instagram Publishing Permissions Check',
          status: 'خطأ',
          error: 'فشل في فحص صلاحيات إنستغرام',
          details: error instanceof Error ? error.message : 'Network error'
        });
      }

      // Test 4: Test Instagram media upload capability (simulation)
      if (account.instagram_business_account_id) {
        try {
          // Test if we can access the media endpoint (without actually uploading)
          const mediaEndpointResponse = await fetch(
            `https://graph.facebook.com/v18.0/${account.instagram_business_account_id}/media?access_token=${account.access_token}`,
            { method: 'GET' }
          );

          if (mediaEndpointResponse.ok) {
            accountTest.tests.push({
              test: 'اختبار إمكانية رفع الوسائط لإنستغرام',
              test_en: 'Instagram Media Upload Capability Test',
              status: 'نجح',
              details: 'يمكن الوصول لنقطة نهاية رفع الوسائط',
              api_response_code: mediaEndpointResponse.status
            });
          } else {
            const errorData = await mediaEndpointResponse.json();
            accountTest.tests.push({
              test: 'اختبار إمكانية رفع الوسائط لإنستغرام',
              test_en: 'Instagram Media Upload Capability Test',
              status: 'فشل',
              error: 'لا يمكن الوصول لنقطة نهاية رفع الوسائط',
              details: errorData.error?.message || 'Unknown error',
              api_response_code: mediaEndpointResponse.status
            });
          }
        } catch (error) {
          accountTest.tests.push({
            test: 'اختبار إمكانية رفع الوسائط لإنستغرام',
            test_en: 'Instagram Media Upload Capability Test',
            status: 'خطأ',
            error: 'فشل في اختبار رفع الوسائط',
            details: error instanceof Error ? error.message : 'Network error'
          });
        }
      }

      // Test 5: Check for missing database fields specific to Instagram
      const missingFields = [];
      if (!account.page_id) missingFields.push('page_id - معرف الصفحة');
      if (!account.page_access_token) missingFields.push('page_access_token - رمز وصول الصفحة');
      if (!account.instagram_business_account_id) missingFields.push('instagram_business_account_id - معرف حساب إنستغرام التجاري');

      accountTest.tests.push({
        test: 'فحص الحقول المطلوبة لإنستغرام في قاعدة البيانات',
        test_en: 'Instagram Required Database Fields Check',
        status: missingFields.length === 0 ? 'نجح' : 'فشل',
        missing_fields: missingFields,
        error: missingFields.length > 0 ? 
          `حقول مفقودة: ${missingFields.join(', ')}` : null,
        recommendation: missingFields.length > 0 ? 
          'يجب إعادة ربط حساب فيسبوك لجلب جميع البيانات المطلوبة' : null
      });

      testResults.push(accountTest);
    }

    // Generate summary
    const allTests = testResults.flatMap(result => result.tests);
    const summary = {
      total_accounts_tested: testResults.length,
      accounts_with_instagram: testResults.filter(r => r.instagram_business_account_id).length,
      accounts_missing_instagram: testResults.filter(r => !r.instagram_business_account_id).length,
      total_tests_run: allTests.length,
      successful_tests: allTests.filter(test => test.status === 'نجح').length,
      failed_tests: allTests.filter(test => test.status === 'فشل').length,
      error_tests: allTests.filter(test => test.status === 'خطأ').length,
      ready_for_instagram_publishing: testResults.filter(r => 
        r.instagram_business_account_id && 
        r.tests.filter(t => t.status === 'نجح').length > r.tests.filter(t => t.status === 'فشل').length
      ).length
    };

    return NextResponse.json({
      success: true,
      message: 'تم اختبار Instagram API بنجاح',
      message_en: 'Instagram API test completed successfully',
      summary,
      test_results: testResults,
      response_time: `${Date.now() - startTime}ms`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Instagram test endpoint error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'خطأ داخلي في الخادم أثناء اختبار Instagram API',
      error_en: 'Internal server error during Instagram API test',
      details: error instanceof Error ? error.message : 'Unknown error',
      response_time: `${Date.now() - startTime}ms`
    }, { status: 500 });
  }
}
