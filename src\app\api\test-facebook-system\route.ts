import { NextRequest, NextResponse } from 'next/server';

/**
 * Comprehensive Facebook System Diagnostic Endpoint
 * Runs all diagnostics and provides root cause analysis with recommendations
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const baseUrl = request.nextUrl.origin;
    const results = {
      oauth_tokens: null,
      facebook_permissions: null,
      account_mapping: null,
      system_health: {
        overall_status: 'unknown',
        critical_issues: [],
        warnings: [],
        recommendations: []
      }
    };

    // Run OAuth tokens diagnostic
    try {
      const oauthResponse = await fetch(`${baseUrl}/api/test-oauth-tokens`);
      results.oauth_tokens = await oauthResponse.json();
    } catch (error) {
      results.oauth_tokens = {
        success: false,
        error: 'فشل في تشغيل فحص التوكنات',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      };
    }

    // Run Facebook permissions diagnostic
    try {
      const permissionsResponse = await fetch(`${baseUrl}/api/test-facebook-permissions`);
      results.facebook_permissions = await permissionsResponse.json();
    } catch (error) {
      results.facebook_permissions = {
        success: false,
        error: 'فشل في تشغيل فحص الصلاحيات',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      };
    }

    // Run account mapping diagnostic
    try {
      const mappingResponse = await fetch(`${baseUrl}/api/test-account-mapping`);
      results.account_mapping = await mappingResponse.json();
    } catch (error) {
      results.account_mapping = {
        success: false,
        error: 'فشل في تشغيل فحص ربط الحسابات',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      };
    }

    // Analyze results and determine root causes
    const analysis = analyzeSystemHealth(results);
    results.system_health = analysis;

    const endTime = Date.now();
    const executionTime = endTime - startTime;

    return NextResponse.json({
      success: true,
      message: 'تم تشغيل فحص النظام الشامل بنجاح',
      execution_time_ms: executionTime,
      diagnostics: results,
      root_cause_analysis: generateRootCauseAnalysis(results),
      immediate_actions: generateImmediateActions(results),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Facebook system diagnostic error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم الداخلي أثناء تشغيل فحص النظام',
      details: error instanceof Error ? error.message : 'خطأ غير معروف',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

function analyzeSystemHealth(results: any) {
  const critical_issues = [];
  const warnings = [];
  const recommendations = [];
  let overall_status = 'healthy';

  // Analyze OAuth tokens
  if (results.oauth_tokens) {
    if (!results.oauth_tokens.success) {
      critical_issues.push('فشل في فحص توكنات OAuth - قد يكون هناك مشكلة في قاعدة البيانات');
      overall_status = 'critical';
    } else if (results.oauth_tokens.summary?.errors > 0) {
      critical_issues.push(`${results.oauth_tokens.summary.errors} حساب لديه مشاكل في التوكن`);
      overall_status = 'critical';
    } else if (results.oauth_tokens.summary?.tokens_refreshed > 0) {
      warnings.push(`تم تجديد ${results.oauth_tokens.summary.tokens_refreshed} توكن تلقائياً`);
      if (overall_status === 'healthy') overall_status = 'warning';
    }
  }

  // Analyze Facebook permissions
  if (results.facebook_permissions) {
    if (!results.facebook_permissions.success) {
      critical_issues.push('فشل في فحص صلاحيات فيسبوك');
      overall_status = 'critical';
    } else if (results.facebook_permissions.summary?.accounts_with_issues > 0) {
      critical_issues.push(`${results.facebook_permissions.summary.accounts_with_issues} حساب لديه مشاكل في الصلاحيات`);
      overall_status = 'critical';
    }
  }

  // Analyze account mapping
  if (results.account_mapping) {
    if (!results.account_mapping.success) {
      critical_issues.push('فشل في فحص ربط الحسابات');
      overall_status = 'critical';
    } else if (results.account_mapping.summary?.accounts_with_issues > 0) {
      critical_issues.push(`${results.account_mapping.summary.accounts_with_issues} حساب لديه مشاكل في الربط`);
      overall_status = 'critical';
    }
  }

  // Generate recommendations based on analysis
  if (critical_issues.length > 0) {
    recommendations.push('يوجد مشاكل حرجة تتطلب إصلاح فوري');
    recommendations.push('يرجى إعادة ربط الحسابات المتأثرة');
    recommendations.push('تحقق من إعدادات تطبيق فيسبوك في Developer Console');
  } else if (warnings.length > 0) {
    recommendations.push('النظام يعمل مع بعض التحذيرات');
    recommendations.push('راقب التوكنات المجددة تلقائياً');
  } else {
    recommendations.push('النظام يعمل بشكل طبيعي');
    recommendations.push('جميع الحسابات مربوطة وتعمل بشكل صحيح');
  }

  return {
    overall_status,
    critical_issues,
    warnings,
    recommendations
  };
}

function generateRootCauseAnalysis(results: any) {
  const rootCauses = [];

  // Check for common root causes
  if (results.oauth_tokens?.summary?.errors > 0) {
    rootCauses.push({
      issue: 'مشاكل في توكنات OAuth',
      likely_causes: [
        'انتهاء صلاحية التوكنات',
        'مشكلة في FACEBOOK_APP_SECRET',
        'تغيير في إعدادات تطبيق فيسبوك',
        'إلغاء المستخدم للصلاحيات'
      ],
      impact: 'عدم القدرة على النشر على فيسبوك/إنستغرام',
      priority: 'عالية'
    });
  }

  if (results.facebook_permissions?.summary?.accounts_with_issues > 0) {
    rootCauses.push({
      issue: 'مشاكل في صلاحيات فيسبوك',
      likely_causes: [
        'عدم منح جميع الصلاحيات المطلوبة عند الربط',
        'تغيير في سياسات فيسبوك',
        'مشكلة في إعدادات التطبيق',
        'حساب فيسبوك غير مؤهل للصلاحيات المطلوبة'
      ],
      impact: 'قيود على وظائف النشر والإدارة',
      priority: 'عالية'
    });
  }

  if (results.account_mapping?.summary?.accounts_with_issues > 0) {
    rootCauses.push({
      issue: 'مشاكل في ربط الحسابات',
      likely_causes: [
        'تغيير في معرفات الحسابات',
        'حذف أو إلغاء تفعيل الحسابات',
        'مشكلة في قاعدة البيانات',
        'تغيير في API فيسبوك'
      ],
      impact: 'عدم تطابق البيانات المعروضة مع الحسابات الفعلية',
      priority: 'متوسطة'
    });
  }

  if (rootCauses.length === 0) {
    rootCauses.push({
      issue: 'لا توجد مشاكل محددة',
      likely_causes: ['النظام يعمل بشكل طبيعي'],
      impact: 'لا يوجد تأثير سلبي',
      priority: 'منخفضة'
    });
  }

  return rootCauses;
}

function generateImmediateActions(results: any) {
  const actions = [];

  // Environment variables check
  const envVarsConfigured = results.facebook_permissions?.app_permissions_check;
  if (envVarsConfigured && !envVarsConfigured.app_secret_configured) {
    actions.push({
      priority: 'عاجل',
      action: 'إضافة FACEBOOK_APP_SECRET إلى متغيرات البيئة',
      description: 'مطلوب لتجديد التوكنات تلقائياً',
      estimated_time: '5 دقائق'
    });
  }

  // Token refresh issues
  if (results.oauth_tokens?.summary?.errors > 0) {
    actions.push({
      priority: 'عاجل',
      action: 'إعادة ربط الحسابات المتأثرة',
      description: 'يرجى الذهاب إلى صفحة الحسابات الاجتماعية وإعادة ربط الحسابات',
      estimated_time: '10 دقائق لكل حساب'
    });
  }

  // Permission issues
  if (results.facebook_permissions?.summary?.accounts_with_issues > 0) {
    actions.push({
      priority: 'عالية',
      action: 'مراجعة صلاحيات تطبيق فيسبوك',
      description: 'تحقق من إعدادات التطبيق في Facebook Developer Console',
      estimated_time: '15 دقيقة'
    });
  }

  // Account mapping issues
  if (results.account_mapping?.summary?.accounts_with_issues > 0) {
    actions.push({
      priority: 'متوسطة',
      action: 'تحديث بيانات الحسابات',
      description: 'تحقق من صحة بيانات الحسابات في قاعدة البيانات',
      estimated_time: '20 دقيقة'
    });
  }

  if (actions.length === 0) {
    actions.push({
      priority: 'منخفضة',
      action: 'مراقبة دورية',
      description: 'النظام يعمل بشكل طبيعي - يُنصح بالمراقبة الدورية',
      estimated_time: 'مستمر'
    });
  }

  return actions;
}