/**
 * Enhanced OAuth Error Handling System
 * Provides comprehensive error handling with Arabic messages and recovery workflows
 */

import { createServiceRoleClient } from '@/lib/supabase/service-role';

export enum OAuthErrorType {
  // Authentication Errors
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_REVOKED = 'TOKEN_REVOKED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  
  // Platform Errors
  FACEBOOK_API_ERROR = 'FACEBOOK_API_ERROR',
  INSTAGRAM_API_ERROR = 'INSTAGRAM_API_ERROR',
  LINKEDIN_API_ERROR = 'LINKEDIN_API_ERROR',
  TWITTER_API_ERROR = 'TWITTER_API_ERROR',
  
  // Network Errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  
  // Application Errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  
  // User Errors
  USER_CANCELLED = 'USER_CANCELLED',
  ACCOUNT_NOT_FOUND = 'ACCOUNT_NOT_FOUND',
  DUPLICATE_ACCOUNT = 'DUPLICATE_ACCOUNT'
}

export interface OAuthError {
  type: OAuthErrorType;
  message: string;
  arabicMessage: string;
  platform?: string;
  accountId?: string;
  userId?: string;
  originalError?: any;
  recoveryActions?: RecoveryAction[];
  timestamp: string;
  context?: Record<string, any>;
}

export interface RecoveryAction {
  id: string;
  label: string;
  arabicLabel: string;
  action: 'retry' | 'reconnect' | 'refresh' | 'contact_support' | 'ignore';
  url?: string;
  automatic?: boolean;
}

export class OAuthErrorHandler {
  private supabase = createServiceRoleClient();

  /**
   * Handle OAuth error with comprehensive logging and recovery options
   */
  async handleError(error: any, context: {
    platform?: string;
    accountId?: string;
    userId?: string;
    operation?: string;
    [key: string]: any;
  }): Promise<OAuthError> {
    
    const oauthError = this.classifyError(error, context);
    
    // Log error to database
    await this.logError(oauthError);
    
    // Update account status if needed
    if (oauthError.accountId) {
      await this.updateAccountStatus(oauthError);
    }
    
    // Send notifications if critical
    if (this.isCriticalError(oauthError)) {
      await this.sendErrorNotification(oauthError);
    }
    
    return oauthError;
  }

  /**
   * Classify error and generate appropriate messages
   */
  private classifyError(error: any, context: any): OAuthError {
    const timestamp = new Date().toISOString();
    
    // Facebook/Instagram API Errors
    if (context.platform === 'FACEBOOK' || context.platform === 'INSTAGRAM') {
      return this.handleFacebookError(error, context, timestamp);
    }
    
    // LinkedIn API Errors
    if (context.platform === 'LINKEDIN') {
      return this.handleLinkedInError(error, context, timestamp);
    }
    
    // Twitter/X API Errors
    if (context.platform === 'TWITTER' || context.platform === 'X') {
      return this.handleTwitterError(error, context, timestamp);
    }
    
    // Network Errors
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return {
        type: OAuthErrorType.NETWORK_ERROR,
        message: 'Network connection failed',
        arabicMessage: 'فشل في الاتصال بالشبكة',
        platform: context.platform,
        accountId: context.accountId,
        userId: context.userId,
        originalError: error,
        recoveryActions: [
          {
            id: 'retry',
            label: 'Retry',
            arabicLabel: 'إعادة المحاولة',
            action: 'retry',
            automatic: true
          },
          {
            id: 'check_connection',
            label: 'Check Internet Connection',
            arabicLabel: 'تحقق من اتصال الإنترنت',
            action: 'ignore'
          }
        ],
        timestamp,
        context
      };
    }
    
    // Timeout Errors
    if (error.code === 'ETIMEDOUT' || error.name === 'TimeoutError') {
      return {
        type: OAuthErrorType.TIMEOUT_ERROR,
        message: 'Request timed out',
        arabicMessage: 'انتهت مهلة الطلب',
        platform: context.platform,
        accountId: context.accountId,
        userId: context.userId,
        originalError: error,
        recoveryActions: [
          {
            id: 'retry',
            label: 'Retry',
            arabicLabel: 'إعادة المحاولة',
            action: 'retry',
            automatic: true
          }
        ],
        timestamp,
        context
      };
    }
    
    // Database Errors
    if (error.code?.startsWith('PG') || error.message?.includes('database')) {
      return {
        type: OAuthErrorType.DATABASE_ERROR,
        message: 'Database operation failed',
        arabicMessage: 'فشل في عملية قاعدة البيانات',
        platform: context.platform,
        accountId: context.accountId,
        userId: context.userId,
        originalError: error,
        recoveryActions: [
          {
            id: 'contact_support',
            label: 'Contact Support',
            arabicLabel: 'اتصل بالدعم الفني',
            action: 'contact_support',
            url: '/support'
          }
        ],
        timestamp,
        context
      };
    }
    
    // Generic Error
    return {
      type: OAuthErrorType.CONFIGURATION_ERROR,
      message: error.message || 'Unknown error occurred',
      arabicMessage: 'حدث خطأ غير معروف',
      platform: context.platform,
      accountId: context.accountId,
      userId: context.userId,
      originalError: error,
      recoveryActions: [
        {
          id: 'retry',
          label: 'Retry',
          arabicLabel: 'إعادة المحاولة',
          action: 'retry'
        },
        {
          id: 'contact_support',
          label: 'Contact Support',
          arabicLabel: 'اتصل بالدعم الفني',
          action: 'contact_support',
          url: '/support'
        }
      ],
      timestamp,
      context
    };
  }

  /**
   * Handle Facebook/Instagram specific errors
   */
  private handleFacebookError(error: any, context: any, timestamp: string): OAuthError {
    const errorCode = error.error?.code || error.code;
    const errorMessage = error.error?.message || error.message;
    
    switch (errorCode) {
      case 190: // Invalid access token
        return {
          type: OAuthErrorType.TOKEN_EXPIRED,
          message: 'Facebook access token has expired',
          arabicMessage: 'انتهت صلاحية رمز الوصول لفيسبوك',
          platform: context.platform,
          accountId: context.accountId,
          userId: context.userId,
          originalError: error,
          recoveryActions: [
            {
              id: 'refresh_token',
              label: 'Refresh Token',
              arabicLabel: 'تحديث الرمز',
              action: 'refresh',
              automatic: true
            },
            {
              id: 'reconnect',
              label: 'Reconnect Account',
              arabicLabel: 'إعادة ربط الحساب',
              action: 'reconnect',
              url: '/social'
            }
          ],
          timestamp,
          context
        };
        
      case 100: // Invalid parameter
        return {
          type: OAuthErrorType.VALIDATION_ERROR,
          message: 'Invalid request parameters',
          arabicMessage: 'معاملات الطلب غير صحيحة',
          platform: context.platform,
          accountId: context.accountId,
          userId: context.userId,
          originalError: error,
          recoveryActions: [
            {
              id: 'contact_support',
              label: 'Contact Support',
              arabicLabel: 'اتصل بالدعم الفني',
              action: 'contact_support',
              url: '/support'
            }
          ],
          timestamp,
          context
        };
        
      case 4: // Rate limit exceeded
        return {
          type: OAuthErrorType.RATE_LIMIT_ERROR,
          message: 'Facebook API rate limit exceeded',
          arabicMessage: 'تم تجاوز حد معدل استخدام API فيسبوك',
          platform: context.platform,
          accountId: context.accountId,
          userId: context.userId,
          originalError: error,
          recoveryActions: [
            {
              id: 'wait_and_retry',
              label: 'Wait and Retry',
              arabicLabel: 'انتظر وأعد المحاولة',
              action: 'retry',
              automatic: true
            }
          ],
          timestamp,
          context
        };
        
      default:
        return {
          type: OAuthErrorType.FACEBOOK_API_ERROR,
          message: errorMessage || 'Facebook API error',
          arabicMessage: 'خطأ في API فيسبوك',
          platform: context.platform,
          accountId: context.accountId,
          userId: context.userId,
          originalError: error,
          recoveryActions: [
            {
              id: 'retry',
              label: 'Retry',
              arabicLabel: 'إعادة المحاولة',
              action: 'retry'
            },
            {
              id: 'reconnect',
              label: 'Reconnect Account',
              arabicLabel: 'إعادة ربط الحساب',
              action: 'reconnect',
              url: '/social'
            }
          ],
          timestamp,
          context
        };
    }
  }

  /**
   * Handle LinkedIn specific errors
   */
  private handleLinkedInError(error: any, context: any, timestamp: string): OAuthError {
    const status = error.status || error.response?.status;
    
    switch (status) {
      case 401:
        return {
          type: OAuthErrorType.TOKEN_EXPIRED,
          message: 'LinkedIn access token has expired',
          arabicMessage: 'انتهت صلاحية رمز الوصول للينكد إن',
          platform: context.platform,
          accountId: context.accountId,
          userId: context.userId,
          originalError: error,
          recoveryActions: [
            {
              id: 'refresh_token',
              label: 'Refresh Token',
              arabicLabel: 'تحديث الرمز',
              action: 'refresh',
              automatic: true
            },
            {
              id: 'reconnect',
              label: 'Reconnect Account',
              arabicLabel: 'إعادة ربط الحساب',
              action: 'reconnect',
              url: '/social'
            }
          ],
          timestamp,
          context
        };
        
      case 429:
        return {
          type: OAuthErrorType.RATE_LIMIT_ERROR,
          message: 'LinkedIn API rate limit exceeded',
          arabicMessage: 'تم تجاوز حد معدل استخدام API لينكد إن',
          platform: context.platform,
          accountId: context.accountId,
          userId: context.userId,
          originalError: error,
          recoveryActions: [
            {
              id: 'wait_and_retry',
              label: 'Wait and Retry',
              arabicLabel: 'انتظر وأعد المحاولة',
              action: 'retry',
              automatic: true
            }
          ],
          timestamp,
          context
        };
        
      default:
        return {
          type: OAuthErrorType.LINKEDIN_API_ERROR,
          message: error.message || 'LinkedIn API error',
          arabicMessage: 'خطأ في API لينكد إن',
          platform: context.platform,
          accountId: context.accountId,
          userId: context.userId,
          originalError: error,
          recoveryActions: [
            {
              id: 'retry',
              label: 'Retry',
              arabicLabel: 'إعادة المحاولة',
              action: 'retry'
            }
          ],
          timestamp,
          context
        };
    }
  }

  /**
   * Handle Twitter/X specific errors
   */
  private handleTwitterError(error: any, context: any, timestamp: string): OAuthError {
    const errorCode = error.errors?.[0]?.code || error.code;
    
    switch (errorCode) {
      case 89: // Invalid or expired token
        return {
          type: OAuthErrorType.TOKEN_EXPIRED,
          message: 'Twitter access token has expired',
          arabicMessage: 'انتهت صلاحية رمز الوصول لتويتر',
          platform: context.platform,
          accountId: context.accountId,
          userId: context.userId,
          originalError: error,
          recoveryActions: [
            {
              id: 'reconnect',
              label: 'Reconnect Account',
              arabicLabel: 'إعادة ربط الحساب',
              action: 'reconnect',
              url: '/social'
            }
          ],
          timestamp,
          context
        };
        
      case 88: // Rate limit exceeded
        return {
          type: OAuthErrorType.RATE_LIMIT_ERROR,
          message: 'Twitter API rate limit exceeded',
          arabicMessage: 'تم تجاوز حد معدل استخدام API تويتر',
          platform: context.platform,
          accountId: context.accountId,
          userId: context.userId,
          originalError: error,
          recoveryActions: [
            {
              id: 'wait_and_retry',
              label: 'Wait and Retry',
              arabicLabel: 'انتظر وأعد المحاولة',
              action: 'retry',
              automatic: true
            }
          ],
          timestamp,
          context
        };
        
      default:
        return {
          type: OAuthErrorType.TWITTER_API_ERROR,
          message: error.message || 'Twitter API error',
          arabicMessage: 'خطأ في API تويتر',
          platform: context.platform,
          accountId: context.accountId,
          userId: context.userId,
          originalError: error,
          recoveryActions: [
            {
              id: 'retry',
              label: 'Retry',
              arabicLabel: 'إعادة المحاولة',
              action: 'retry'
            }
          ],
          timestamp,
          context
        };
    }
  }

  /**
   * Log error to database
   */
  private async logError(error: OAuthError): Promise<void> {
    try {
      await this.supabase
        .from('error_logs')
        .insert({
          error_type: error.type,
          message: error.message,
          arabic_message: error.arabicMessage,
          platform: error.platform,
          account_id: error.accountId,
          user_id: error.userId,
          original_error: error.originalError,
          context: error.context,
          recovery_actions: error.recoveryActions,
          created_at: error.timestamp
        });
    } catch (logError) {
      console.error('Failed to log error to database:', logError);
    }
  }

  /**
   * Update account status based on error
   */
  private async updateAccountStatus(error: OAuthError): Promise<void> {
    if (!error.accountId) return;
    
    try {
      let newStatus = 'error';
      
      if (error.type === OAuthErrorType.TOKEN_EXPIRED || error.type === OAuthErrorType.TOKEN_REVOKED) {
        newStatus = 'expired';
      }
      
      await this.supabase
        .from('social_accounts')
        .update({
          connection_status: newStatus,
          last_error: error.message,
          last_error_at: error.timestamp,
          updated_at: error.timestamp
        })
        .eq('id', error.accountId);
        
    } catch (updateError) {
      console.error('Failed to update account status:', updateError);
    }
  }

  /**
   * Check if error is critical and requires immediate attention
   */
  private isCriticalError(error: OAuthError): boolean {
    const criticalTypes = [
      OAuthErrorType.DATABASE_ERROR,
      OAuthErrorType.CONFIGURATION_ERROR
    ];
    
    return criticalTypes.includes(error.type);
  }

  /**
   * Send error notification to administrators
   */
  private async sendErrorNotification(error: OAuthError): Promise<void> {
    try {
      // Create notification record
      await this.supabase
        .from('admin_notifications')
        .insert({
          type: 'critical_error',
          title: `Critical OAuth Error: ${error.type}`,
          message: error.message,
          data: {
            error_type: error.type,
            platform: error.platform,
            user_id: error.userId,
            account_id: error.accountId,
            context: error.context
          },
          created_at: error.timestamp
        });
        
      // TODO: Send email notification to administrators
      // await sendEmailNotification(error);
      
    } catch (notificationError) {
      console.error('Failed to send error notification:', notificationError);
    }
  }

  /**
   * Get error recovery suggestions for user
   */
  getRecoverySuggestions(errorType: OAuthErrorType, platform?: string): RecoveryAction[] {
    const baseActions: RecoveryAction[] = [
      {
        id: 'retry',
        label: 'Try Again',
        arabicLabel: 'حاول مرة أخرى',
        action: 'retry'
      },
      {
        id: 'contact_support',
        label: 'Contact Support',
        arabicLabel: 'اتصل بالدعم الفني',
        action: 'contact_support',
        url: '/support'
      }
    ];

    switch (errorType) {
      case OAuthErrorType.TOKEN_EXPIRED:
        return [
          {
            id: 'refresh_token',
            label: 'Refresh Token',
            arabicLabel: 'تحديث الرمز',
            action: 'refresh',
            automatic: true
          },
          {
            id: 'reconnect',
            label: 'Reconnect Account',
            arabicLabel: 'إعادة ربط الحساب',
            action: 'reconnect',
            url: '/social'
          }
        ];
        
      case OAuthErrorType.RATE_LIMIT_ERROR:
        return [
          {
            id: 'wait_and_retry',
            label: 'Wait and Try Again',
            arabicLabel: 'انتظر وحاول مرة أخرى',
            action: 'retry',
            automatic: true
          }
        ];
        
      default:
        return baseActions;
    }
  }
}
