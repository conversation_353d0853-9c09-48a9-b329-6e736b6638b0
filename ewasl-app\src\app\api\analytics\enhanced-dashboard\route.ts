/**
 * Enhanced Dashboard Analytics API
 * Provides comprehensive real-time analytics for the dashboard
 * Part of Phase 2A: Core UX Enhancements
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { logger } from '@/lib/monitoring/enhanced-logger';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '7d';
    
    const supabase = createClient();
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Calculate date range
    const now = new Date();
    const daysBack = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const startDate = new Date(now);
    startDate.setDate(startDate.getDate() - daysBack);

    // Fetch overview metrics
    const overview = await fetchOverviewMetrics(supabase, user.id, startDate);
    
    // Fetch trends
    const trends = await fetchTrends(supabase, user.id, startDate);
    
    // Fetch platform breakdown
    const platformBreakdown = await fetchPlatformBreakdown(supabase, user.id, startDate);
    
    // Fetch engagement trends
    const engagementTrends = await fetchEngagementTrends(supabase, user.id, startDate, daysBack);
    
    // Fetch top performing posts
    const topPerformingPosts = await fetchTopPerformingPosts(supabase, user.id, startDate);
    
    // Fetch real-time activity
    const realtimeActivity = await fetchRealtimeActivity(supabase, user.id);

    const metrics = {
      overview,
      trends,
      platformBreakdown,
      engagementTrends,
      topPerformingPosts,
      realtimeActivity
    };

    logger.info('Enhanced dashboard metrics fetched', {
      component: 'enhanced-dashboard-api',
      userId: user.id,
      timeRange,
      metricsCount: Object.keys(metrics).length
    });

    return NextResponse.json({
      success: true,
      metrics,
      timestamp: new Date().toISOString(),
      timeRange
    });

  } catch (error) {
    logger.error('Enhanced dashboard API error', {
      component: 'enhanced-dashboard-api',
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return NextResponse.json({
      error: 'Failed to fetch dashboard metrics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Fetch overview metrics
 */
async function fetchOverviewMetrics(supabase: any, userId: string, startDate: Date) {
  try {
    // Get total posts
    const { data: postsData, error: postsError } = await supabase
      .from('posts')
      .select('id, status, created_at')
      .eq('user_id', userId);

    if (postsError) throw postsError;

    // Get social accounts
    const { data: accountsData, error: accountsError } = await supabase
      .from('social_accounts')
      .select('id, platform, status')
      .eq('user_id', userId)
      .eq('status', 'connected');

    if (accountsError) throw accountsError;

    // Get analytics data
    const { data: analyticsData, error: analyticsError } = await supabase
      .from('post_analytics')
      .select('engagement_count, reach_count, impressions_count')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString());

    // Calculate metrics
    const totalPosts = postsData?.length || 0;
    const scheduledPosts = postsData?.filter(p => p.status === 'scheduled').length || 0;
    const publishedToday = postsData?.filter(p => {
      const postDate = new Date(p.created_at);
      const today = new Date();
      return postDate.toDateString() === today.toDateString() && p.status === 'published';
    }).length || 0;

    const totalEngagement = analyticsData?.reduce((sum, a) => sum + (a.engagement_count || 0), 0) || 0;
    const totalReach = analyticsData?.reduce((sum, a) => sum + (a.reach_count || 0), 0) || 0;
    const totalImpressions = analyticsData?.reduce((sum, a) => sum + (a.impressions_count || 0), 0) || 0;
    const engagementRate = totalImpressions > 0 ? (totalEngagement / totalImpressions) * 100 : 0;

    return {
      totalPosts,
      totalEngagement,
      totalReach,
      totalImpressions,
      engagementRate,
      connectedAccounts: accountsData?.length || 0,
      scheduledPosts,
      publishedToday
    };

  } catch (error) {
    logger.error('Failed to fetch overview metrics', { error });
    // Return fallback data
    return {
      totalPosts: 0,
      totalEngagement: 0,
      totalReach: 0,
      totalImpressions: 0,
      engagementRate: 0,
      connectedAccounts: 0,
      scheduledPosts: 0,
      publishedToday: 0
    };
  }
}

/**
 * Fetch growth trends
 */
async function fetchTrends(supabase: any, userId: string, startDate: Date) {
  try {
    // Calculate previous period for comparison
    const periodDays = Math.floor((Date.now() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const previousStartDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - periodDays);

    // Get current period data
    const { data: currentData } = await supabase
      .from('post_analytics')
      .select('engagement_count, reach_count, impressions_count')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString());

    // Get previous period data
    const { data: previousData } = await supabase
      .from('post_analytics')
      .select('engagement_count, reach_count, impressions_count')
      .eq('user_id', userId)
      .gte('created_at', previousStartDate.toISOString())
      .lt('created_at', startDate.toISOString());

    // Calculate growth rates
    const currentEngagement = currentData?.reduce((sum, a) => sum + (a.engagement_count || 0), 0) || 0;
    const previousEngagement = previousData?.reduce((sum, a) => sum + (a.engagement_count || 0), 0) || 0;
    const engagementGrowth = previousEngagement > 0 ? 
      ((currentEngagement - previousEngagement) / previousEngagement) * 100 : 0;

    const currentReach = currentData?.reduce((sum, a) => sum + (a.reach_count || 0), 0) || 0;
    const previousReach = previousData?.reduce((sum, a) => sum + (a.reach_count || 0), 0) || 0;
    const reachGrowth = previousReach > 0 ? 
      ((currentReach - previousReach) / previousReach) * 100 : 0;

    const currentImpressions = currentData?.reduce((sum, a) => sum + (a.impressions_count || 0), 0) || 0;
    const previousImpressions = previousData?.reduce((sum, a) => sum + (a.impressions_count || 0), 0) || 0;
    const impressionsGrowth = previousImpressions > 0 ? 
      ((currentImpressions - previousImpressions) / previousImpressions) * 100 : 0;

    // Get posts growth
    const { data: currentPosts } = await supabase
      .from('posts')
      .select('id')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString());

    const { data: previousPosts } = await supabase
      .from('posts')
      .select('id')
      .eq('user_id', userId)
      .gte('created_at', previousStartDate.toISOString())
      .lt('created_at', startDate.toISOString());

    const postsGrowth = (previousPosts?.length || 0) > 0 ? 
      (((currentPosts?.length || 0) - (previousPosts?.length || 0)) / (previousPosts?.length || 0)) * 100 : 0;

    return {
      postsGrowth,
      engagementGrowth,
      reachGrowth,
      impressionsGrowth
    };

  } catch (error) {
    logger.error('Failed to fetch trends', { error });
    return {
      postsGrowth: 0,
      engagementGrowth: 0,
      reachGrowth: 0,
      impressionsGrowth: 0
    };
  }
}

/**
 * Fetch platform breakdown
 */
async function fetchPlatformBreakdown(supabase: any, userId: string, startDate: Date) {
  try {
    const { data, error } = await supabase
      .from('post_social_accounts')
      .select(`
        social_account_id,
        social_accounts!inner(platform),
        posts!inner(user_id),
        post_analytics(engagement_count, reach_count)
      `)
      .eq('posts.user_id', userId)
      .gte('posts.created_at', startDate.toISOString());

    if (error) throw error;

    // Group by platform
    const platformMap = new Map();
    
    data?.forEach(item => {
      const platform = item.social_accounts.platform;
      if (!platformMap.has(platform)) {
        platformMap.set(platform, {
          platform,
          posts: 0,
          engagement: 0,
          reach: 0,
          color: getPlatformColor(platform)
        });
      }
      
      const platformData = platformMap.get(platform);
      platformData.posts += 1;
      platformData.engagement += item.post_analytics?.engagement_count || 0;
      platformData.reach += item.post_analytics?.reach_count || 0;
    });

    return Array.from(platformMap.values());

  } catch (error) {
    logger.error('Failed to fetch platform breakdown', { error });
    return [];
  }
}

/**
 * Get platform color
 */
function getPlatformColor(platform: string): string {
  const colors: Record<string, string> = {
    'instagram': '#E4405F',
    'facebook': '#1877F2',
    'twitter': '#1DA1F2',
    'linkedin': '#0A66C2',
    'tiktok': '#000000',
    'youtube': '#FF0000'
  };
  return colors[platform.toLowerCase()] || '#6B7280';
}

/**
 * Fetch engagement trends over time
 */
async function fetchEngagementTrends(supabase: any, userId: string, startDate: Date, days: number) {
  try {
    const trends = [];
    
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      const nextDate = new Date(date);
      nextDate.setDate(nextDate.getDate() + 1);

      const { data } = await supabase
        .from('post_analytics')
        .select('engagement_count, reach_count, impressions_count')
        .eq('user_id', userId)
        .gte('created_at', date.toISOString())
        .lt('created_at', nextDate.toISOString());

      const { data: postsData } = await supabase
        .from('posts')
        .select('id')
        .eq('user_id', userId)
        .gte('created_at', date.toISOString())
        .lt('created_at', nextDate.toISOString());

      trends.push({
        date: date.toISOString().split('T')[0],
        engagement: data?.reduce((sum, a) => sum + (a.engagement_count || 0), 0) || 0,
        reach: data?.reduce((sum, a) => sum + (a.reach_count || 0), 0) || 0,
        impressions: data?.reduce((sum, a) => sum + (a.impressions_count || 0), 0) || 0,
        posts: postsData?.length || 0
      });
    }

    return trends;

  } catch (error) {
    logger.error('Failed to fetch engagement trends', { error });
    return [];
  }
}

/**
 * Fetch top performing posts
 */
async function fetchTopPerformingPosts(supabase: any, userId: string, startDate: Date) {
  try {
    const { data, error } = await supabase
      .from('posts')
      .select(`
        id,
        content,
        created_at,
        post_social_accounts!inner(
          social_accounts!inner(platform)
        ),
        post_analytics(engagement_count, reach_count)
      `)
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false })
      .limit(5);

    if (error) throw error;

    return data?.map(post => ({
      id: post.id,
      content: post.content?.substring(0, 100) + '...' || 'No content',
      platform: post.post_social_accounts[0]?.social_accounts?.platform || 'Unknown',
      engagement: post.post_analytics?.engagement_count || 0,
      reach: post.post_analytics?.reach_count || 0,
      publishedAt: post.created_at
    })) || [];

  } catch (error) {
    logger.error('Failed to fetch top performing posts', { error });
    return [];
  }
}

/**
 * Fetch real-time activity
 */
async function fetchRealtimeActivity(supabase: any, userId: string) {
  try {
    const { data, error } = await supabase
      .from('activities')
      .select('id, type, description, created_at, metadata')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) throw error;

    return data?.map(activity => ({
      id: activity.id,
      type: activity.type,
      message: activity.description,
      timestamp: activity.created_at,
      platform: activity.metadata?.platform
    })) || [];

  } catch (error) {
    logger.error('Failed to fetch real-time activity', { error });
    return [];
  }
}
