-- Enhanced Posts System Migration
-- Adds support for comprehensive post creation, scheduling, and publishing

-- First, let's ensure we have the proper enums
DO $$ BEGIN
    CREATE TYPE post_status AS ENUM ('DRAFT', 'SCHEDULED', 'PUBLISHED', 'FAILED', 'CANCELLED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE social_platform AS ENUM ('FACEBOOK', 'INSTAGRAM', 'TWITTER', 'LINKEDIN', 'TIKTOK', 'YOUTUBE');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Enhanced posts table with comprehensive fields
CREATE TABLE IF NOT EXISTS public.posts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Content fields
  content TEXT NOT NULL,
  content_html TEXT, -- Rich text HTML content
  media_urls TEXT[] DEFAULT '{}', -- Array of media URLs
  hashtags TEXT[] DEFAULT '{}', -- Array of hashtags
  mentions TEXT[] DEFAULT '{}', -- Array of mentions
  
  -- Scheduling and status
  status post_status DEFAULT 'DRAFT',
  scheduled_at TIMESTAMPTZ,
  published_at TIMESTAMPTZ,
  
  -- Platform-specific settings
  platform_settings JSONB DEFAULT '{}', -- Platform-specific configurations
  character_counts JSONB DEFAULT '{}', -- Character counts per platform
  
  -- Publishing results
  publishing_results JSONB DEFAULT '{}', -- Results from each platform
  error_messages JSONB DEFAULT '{}', -- Error messages if publishing failed
  
  -- Metadata
  timezone TEXT DEFAULT 'UTC',
  auto_publish BOOLEAN DEFAULT false,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Post social accounts junction table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS public.post_social_accounts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE NOT NULL,
  social_account_id UUID REFERENCES public.social_accounts(id) ON DELETE CASCADE NOT NULL,
  
  -- Platform-specific content variations
  platform_content TEXT, -- Platform-specific content if different from main content
  platform_media_urls TEXT[] DEFAULT '{}', -- Platform-specific media
  
  -- Publishing status per platform
  status post_status DEFAULT 'DRAFT',
  platform_post_id TEXT, -- ID returned by the platform after publishing
  platform_url TEXT, -- URL of the published post
  published_at TIMESTAMPTZ,
  error_message TEXT,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(post_id, social_account_id)
);

-- Media files table for Supabase Storage integration
CREATE TABLE IF NOT EXISTS public.media_files (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- File information
  file_name TEXT NOT NULL,
  original_name TEXT NOT NULL,
  file_type TEXT NOT NULL, -- image/jpeg, video/mp4, etc.
  file_size BIGINT NOT NULL,
  
  -- Storage information
  storage_path TEXT NOT NULL, -- Path in Supabase Storage
  public_url TEXT NOT NULL, -- Public URL for accessing the file
  bucket_name TEXT DEFAULT 'media' NOT NULL,
  
  -- Metadata
  width INTEGER, -- For images/videos
  height INTEGER, -- For images/videos
  duration FLOAT, -- For videos (in seconds)
  alt_text TEXT, -- Accessibility description
  metadata JSONB DEFAULT '{}', -- Additional metadata
  
  -- Organization
  folder TEXT DEFAULT 'uploads',
  tags TEXT[] DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Scheduled posts queue for background processing
CREATE TABLE IF NOT EXISTS public.scheduled_posts_queue (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  post_id UUID REFERENCES public.posts(id) ON DELETE CASCADE NOT NULL,
  
  -- Scheduling information
  scheduled_for TIMESTAMPTZ NOT NULL,
  timezone TEXT DEFAULT 'UTC',
  
  -- Processing status
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  
  -- Error handling
  last_error TEXT,
  next_retry_at TIMESTAMPTZ,
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  processed_at TIMESTAMPTZ
);

-- Hashtag suggestions table
CREATE TABLE IF NOT EXISTS public.hashtag_suggestions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  hashtag TEXT NOT NULL UNIQUE,
  category TEXT,
  popularity_score INTEGER DEFAULT 0,
  language TEXT DEFAULT 'ar',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_posts_user_id ON public.posts(user_id);
CREATE INDEX IF NOT EXISTS idx_posts_status ON public.posts(status);
CREATE INDEX IF NOT EXISTS idx_posts_scheduled_at ON public.posts(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_posts_created_at ON public.posts(created_at);

CREATE INDEX IF NOT EXISTS idx_post_social_accounts_post_id ON public.post_social_accounts(post_id);
CREATE INDEX IF NOT EXISTS idx_post_social_accounts_social_account_id ON public.post_social_accounts(social_account_id);
CREATE INDEX IF NOT EXISTS idx_post_social_accounts_status ON public.post_social_accounts(status);

CREATE INDEX IF NOT EXISTS idx_media_files_user_id ON public.media_files(user_id);
CREATE INDEX IF NOT EXISTS idx_media_files_file_type ON public.media_files(file_type);
CREATE INDEX IF NOT EXISTS idx_media_files_created_at ON public.media_files(created_at);

CREATE INDEX IF NOT EXISTS idx_scheduled_posts_queue_scheduled_for ON public.scheduled_posts_queue(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_scheduled_posts_queue_status ON public.scheduled_posts_queue(status);

CREATE INDEX IF NOT EXISTS idx_hashtag_suggestions_hashtag ON public.hashtag_suggestions(hashtag);
CREATE INDEX IF NOT EXISTS idx_hashtag_suggestions_category ON public.hashtag_suggestions(category);

-- Enable Row Level Security
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.post_social_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.media_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scheduled_posts_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.hashtag_suggestions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for posts
CREATE POLICY "Users can manage their own posts" ON public.posts
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for post_social_accounts
CREATE POLICY "Users can manage their post social accounts" ON public.post_social_accounts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.posts 
            WHERE posts.id = post_social_accounts.post_id 
            AND posts.user_id = auth.uid()
        )
    );

-- RLS Policies for media_files
CREATE POLICY "Users can manage their own media files" ON public.media_files
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for scheduled_posts_queue
CREATE POLICY "Users can view their scheduled posts" ON public.scheduled_posts_queue
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.posts 
            WHERE posts.id = scheduled_posts_queue.post_id 
            AND posts.user_id = auth.uid()
        )
    );

-- RLS Policies for hashtag_suggestions (read-only for all authenticated users)
CREATE POLICY "Authenticated users can read hashtag suggestions" ON public.hashtag_suggestions
    FOR SELECT USING (auth.role() = 'authenticated');

-- Insert some default Arabic hashtag suggestions
INSERT INTO public.hashtag_suggestions (hashtag, category, popularity_score, language) VALUES
('#تسويق', 'marketing', 100, 'ar'),
('#وسائل_التواصل_الاجتماعي', 'social_media', 95, 'ar'),
('#محتوى', 'content', 90, 'ar'),
('#إبداع', 'creativity', 85, 'ar'),
('#تصميم', 'design', 80, 'ar'),
('#تكنولوجيا', 'technology', 85, 'ar'),
('#ريادة_الأعمال', 'entrepreneurship', 75, 'ar'),
('#نجاح', 'success', 70, 'ar'),
('#إلهام', 'inspiration', 75, 'ar'),
('#تطوير', 'development', 70, 'ar'),
('#ابتكار', 'innovation', 80, 'ar'),
('#جودة', 'quality', 65, 'ar'),
('#احترافية', 'professionalism', 70, 'ar'),
('#تميز', 'excellence', 75, 'ar'),
('#رقمي', 'digital', 85, 'ar')
ON CONFLICT (hashtag) DO NOTHING;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_posts_updated_at BEFORE UPDATE ON public.posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_post_social_accounts_updated_at BEFORE UPDATE ON public.post_social_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_media_files_updated_at BEFORE UPDATE ON public.media_files
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scheduled_posts_queue_updated_at BEFORE UPDATE ON public.scheduled_posts_queue
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE public.posts IS 'Enhanced posts table with rich content support and scheduling';
COMMENT ON TABLE public.post_social_accounts IS 'Junction table linking posts to social accounts with platform-specific data';
COMMENT ON TABLE public.media_files IS 'Media files stored in Supabase Storage with metadata';
COMMENT ON TABLE public.scheduled_posts_queue IS 'Queue for processing scheduled posts';
COMMENT ON TABLE public.hashtag_suggestions IS 'Hashtag suggestions for content creation';
