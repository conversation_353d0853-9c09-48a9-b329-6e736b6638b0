# Final Integration Verification for eWasl Production
# Tests critical integrations with production credentials

param(
    [string]$Domain = "app.ewasl.com"
)

$BaseUrl = "https://$Domain"

function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Blue }
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

function Test-CriticalEndpoint {
    param(
        [string]$Endpoint,
        [string]$Description,
        [int]$ExpectedStatus = 200
    )
    
    Write-Info "Testing: $Description"
    
    try {
        $response = Invoke-WebRequest -Uri "$BaseUrl$Endpoint" -Method GET -UseBasicParsing -ErrorAction SilentlyContinue
        $statusCode = $response.StatusCode
        $content = $response.Content
        
        if ($statusCode -eq $ExpectedStatus) {
            Write-Success "$Description - ✅ WORKING (HTTP $statusCode)"
            if ($content -and $content.Length -gt 0) {
                $preview = $content.Substring(0, [Math]::Min(100, $content.Length))
                Write-Host "   Response preview: $preview..." -ForegroundColor Gray
            }
            return $true
        } else {
            Write-Warning "$Description - Unexpected status (HTTP $statusCode)"
            return $false
        }
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode -eq $ExpectedStatus) {
            Write-Success "$Description - ✅ WORKING (HTTP $statusCode)"
            return $true
        } else {
            Write-Error "$Description - ❌ FAILED (HTTP $statusCode)"
            return $false
        }
    }
}

Write-Host ""
Write-Host "🎯 FINAL INTEGRATION VERIFICATION" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host "Domain: $Domain" -ForegroundColor Cyan
Write-Host ""

# Test 1: Core Application
Write-Info "=== CORE APPLICATION ==="
$coreWorking = 0
$coreWorking += if (Test-CriticalEndpoint "/" "Homepage") { 1 } else { 0 }
$coreWorking += if (Test-CriticalEndpoint "/dashboard" "Dashboard") { 1 } else { 0 }
$coreWorking += if (Test-CriticalEndpoint "/auth/signin" "Authentication Page") { 1 } else { 0 }

Write-Host ""
Write-Info "=== SYSTEM HEALTH ==="
$healthWorking = 0
$healthWorking += if (Test-CriticalEndpoint "/api/system/health" "System Health") { 1 } else { 0 }

Write-Host ""
Write-Info "=== PAYMENT SYSTEM (STRIPE) ==="
$paymentWorking = 0
$paymentWorking += if (Test-CriticalEndpoint "/api/billing/health" "Billing Health") { 1 } else { 0 }
$paymentWorking += if (Test-CriticalEndpoint "/api/stripe/webhook" "Stripe Webhook" 405) { 1 } else { 0 }

Write-Host ""
Write-Info "=== AUTHENTICATION & SECURITY ==="
$authWorking = 0
$authWorking += if (Test-CriticalEndpoint "/api/posts" "Protected API (should be 401)" 401) { 1 } else { 0 }

Write-Host ""
Write-Info "=== PERFORMANCE TEST ==="
$startTime = Get-Date
try {
    Invoke-WebRequest -Uri "$BaseUrl/" -UseBasicParsing | Out-Null
    $endTime = Get-Date
    $loadTime = ($endTime - $startTime).TotalMilliseconds
    
    if ($loadTime -lt 2000) {
        Write-Success "Performance: Homepage loads in ${loadTime}ms (< 2s target) ✅"
        $perfWorking = 1
    } else {
        Write-Warning "Performance: Homepage loads in ${loadTime}ms (> 2s target)"
        $perfWorking = 0
    }
} catch {
    Write-Error "Performance: Homepage load test failed"
    $perfWorking = 0
}

Write-Host ""
Write-Host "📊 FINAL VERIFICATION RESULTS" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan

$totalTests = $coreWorking + $healthWorking + $paymentWorking + $authWorking + $perfWorking
$maxTests = 7

Write-Host "Core Application: $coreWorking/3 working" -ForegroundColor $(if ($coreWorking -eq 3) { "Green" } else { "Yellow" })
Write-Host "System Health: $healthWorking/1 working" -ForegroundColor $(if ($healthWorking -eq 1) { "Green" } else { "Yellow" })
Write-Host "Payment System: $paymentWorking/2 working" -ForegroundColor $(if ($paymentWorking -eq 2) { "Green" } else { "Yellow" })
Write-Host "Authentication: $authWorking/1 working" -ForegroundColor $(if ($authWorking -eq 1) { "Green" } else { "Yellow" })
Write-Host "Performance: $perfWorking/1 working" -ForegroundColor $(if ($perfWorking -eq 1) { "Green" } else { "Yellow" })

$successRate = [math]::Round(($totalTests / $maxTests) * 100, 1)

Write-Host ""
Write-Host "🎯 OVERALL SUCCESS RATE: $successRate% ($totalTests/$maxTests)" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

Write-Host ""
if ($successRate -ge 80) {
    Write-Success "🎉 PRODUCTION READY! All critical systems operational"
    Write-Host "✅ eWasl is ready for live production use" -ForegroundColor Green
} elseif ($successRate -ge 60) {
    Write-Warning "⚠️  MOSTLY READY - Some non-critical issues detected"
    Write-Host "🔧 Minor fixes needed but core functionality works" -ForegroundColor Yellow
} else {
    Write-Error "❌ NEEDS ATTENTION - Critical issues detected"
    Write-Host "🚨 Major fixes required before production use" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔗 Production URL: $BaseUrl" -ForegroundColor Cyan
Write-Host "📊 Health Monitor: $BaseUrl/api/system/health" -ForegroundColor Cyan
Write-Host "💳 Payment System: $BaseUrl/api/billing/health" -ForegroundColor Cyan
Write-Host ""

# Generate summary report
$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
$summaryReport = @"
eWasl Production Integration - Final Verification Report
======================================================
Timestamp: $timestamp
Domain: $Domain
Success Rate: $successRate% ($totalTests/$maxTests tests passed)

Critical Systems Status:
- Core Application: $coreWorking/3 ✓
- System Health: $healthWorking/1 ✓
- Payment System: $paymentWorking/2 ✓
- Authentication: $authWorking/1 ✓
- Performance: $perfWorking/1 ✓

Production Readiness: $(if ($successRate -ge 80) { "READY ✅" } elseif ($successRate -ge 60) { "MOSTLY READY ⚠️" } else { "NEEDS WORK ❌" })

Next Steps:
1. Monitor production performance
2. Test end-to-end user workflows
3. Verify payment processing with test transactions
4. Complete social media OAuth testing
5. Set up production monitoring and alerts
"@

$reportFile = "final-verification-report-$(Get-Date -Format 'yyyyMMdd-HHmmss').txt"
$summaryReport | Out-File -FilePath $reportFile -Encoding UTF8
Write-Success "Final verification report saved: $reportFile"
