/**
 * 🔐 SOCIAL MEDIA TOKEN MANAGER
 * 
 * Handles token validation, refresh, and expiry detection
 * for all social media platforms
 */

import { createServiceRoleClient } from '@/lib/supabase/server';

interface TokenInfo {
  platform: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt?: string;
  accountId: string;
  userId: string;
}

interface TokenValidationResult {
  isValid: boolean;
  isExpired: boolean;
  needsRefresh: boolean;
  error?: string;
}

interface RefreshResult {
  success: boolean;
  newToken?: string;
  expiresAt?: string;
  error?: string;
}

export class SocialTokenManager {
  private supabase: any;
  
  constructor() {
    // Lazy initialization to prevent build-time errors
    this.supabase = null;
  }
  
  private getSupabaseClient() {
    if (!this.supabase) {
      this.supabase = createServiceRoleClient();
    }
    return this.supabase;
  }

  /**
   * Validate a token by making a test API call to the platform
   */
  async validateToken(tokenInfo: TokenInfo): Promise<TokenValidationResult> {
    try {
      console.log(`🔍 Validating ${tokenInfo.platform} token...`);
      
      // Check if token is already expired based on timestamp
      if (tokenInfo.expiresAt) {
        const expiryTime = new Date(tokenInfo.expiresAt).getTime();
        const now = Date.now();
        const bufferTime = 5 * 60 * 1000; // 5 minutes buffer
        
        if (now > (expiryTime - bufferTime)) {
          return {
            isValid: false,
            isExpired: true,
            needsRefresh: !!tokenInfo.refreshToken
          };
        }
      }

      // Test the token with platform-specific API calls
      let isValid = false;
      
      switch (tokenInfo.platform.toUpperCase()) {
        case 'TWITTER':
        case 'X':
          isValid = await this.validateTwitterToken(tokenInfo.accessToken);
          break;
        case 'FACEBOOK':
          isValid = await this.validateFacebookToken(tokenInfo.accessToken);
          break;
        case 'INSTAGRAM':
          isValid = await this.validateInstagramToken(tokenInfo.accessToken);
          break;
        case 'LINKEDIN':
          isValid = await this.validateLinkedInToken(tokenInfo.accessToken);
          break;
        default:
          return {
            isValid: false,
            isExpired: false,
            needsRefresh: false,
            error: `Unsupported platform: ${tokenInfo.platform}`
          };
      }

      return {
        isValid,
        isExpired: !isValid,
        needsRefresh: !isValid && !!tokenInfo.refreshToken
      };

    } catch (error) {
      console.error(`Error validating ${tokenInfo.platform} token:`, error);
      return {
        isValid: false,
        isExpired: true,
        needsRefresh: !!tokenInfo.refreshToken,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Refresh an expired token using the refresh token
   */
  async refreshToken(tokenInfo: TokenInfo): Promise<RefreshResult> {
    if (!tokenInfo.refreshToken) {
      return {
        success: false,
        error: 'No refresh token available'
      };
    }

    try {
      console.log(`🔄 Refreshing ${tokenInfo.platform} token...`);
      
      switch (tokenInfo.platform.toUpperCase()) {
        case 'FACEBOOK':
        case 'INSTAGRAM':
          return await this.refreshFacebookToken(tokenInfo);
        case 'LINKEDIN':
          return await this.refreshLinkedInToken(tokenInfo);
        case 'TWITTER':
        case 'X':
          // Twitter doesn't support refresh tokens in OAuth 2.0
          return {
            success: false,
            error: 'Twitter does not support token refresh'
          };
        default:
          return {
            success: false,
            error: `Token refresh not supported for ${tokenInfo.platform}`
          };
      }
    } catch (error) {
      console.error(`Error refreshing ${tokenInfo.platform} token:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check all user's tokens and mark expired ones
   */
  async auditUserTokens(userId: string): Promise<{
    checkedCount: number;
    expiredCount: number;
    refreshedCount: number;
    errors: string[];
  }> {
    const results = {
      checkedCount: 0,
      expiredCount: 0,
      refreshedCount: 0,
      errors: [] as string[]
    };

    try {
      // Get all social accounts for user
      const supabase = this.getSupabaseClient();
      const { data: accounts, error } = await supabase
        .from('social_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) {
        results.errors.push(`Failed to fetch accounts: ${error.message}`);
        return results;
      }

      if (!accounts || accounts.length === 0) {
        return results;
      }

      // Check each account's token
      for (const account of accounts) {
        results.checkedCount++;
        
        const tokenInfo: TokenInfo = {
          platform: account.platform,
          accessToken: account.access_token,
          refreshToken: account.refresh_token,
          expiresAt: account.expires_at,
          accountId: account.id,
          userId: account.user_id
        };

        const validation = await this.validateToken(tokenInfo);
        
        if (!validation.isValid) {
          results.expiredCount++;
          
          // Try to refresh if possible
          if (validation.needsRefresh) {
            const refreshResult = await this.refreshToken(tokenInfo);
            
            if (refreshResult.success) {
              // Update token in database
              await this.updateTokenInDatabase(
                account.id,
                refreshResult.newToken!,
                refreshResult.expiresAt
              );
              results.refreshedCount++;
            } else {
              // Mark account as expired
              await this.markAccountAsExpired(account.id);
              results.errors.push(`Failed to refresh ${account.platform}: ${refreshResult.error}`);
            }
          } else {
            // Mark account as expired
            await this.markAccountAsExpired(account.id);
          }
        }
      }

      return results;
    } catch (error) {
      results.errors.push(`Audit failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return results;
    }
  }

  /**
   * Platform-specific token validation methods
   */
  private async validateTwitterToken(accessToken: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.twitter.com/2/users/me', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  private async validateFacebookToken(accessToken: string): Promise<boolean> {
    try {
      const response = await fetch(`https://graph.facebook.com/me?access_token=${accessToken}`);
      return response.ok;
    } catch {
      return false;
    }
  }

  private async validateInstagramToken(accessToken: string): Promise<boolean> {
    try {
      const response = await fetch(`https://graph.instagram.com/me?fields=id,username&access_token=${accessToken}`);
      return response.ok;
    } catch {
      return false;
    }
  }

  private async validateLinkedInToken(accessToken: string): Promise<boolean> {
    try {
      const response = await fetch('https://api.linkedin.com/v2/people/~', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Platform-specific token refresh methods
   */
  private async refreshFacebookToken(tokenInfo: TokenInfo): Promise<RefreshResult> {
    try {
      const clientId = process.env.FACEBOOK_APP_ID; // Updated to use correct env var
      const clientSecret = process.env.FACEBOOK_APP_SECRET; // Updated to use correct env var

      if (!clientId || !clientSecret) {
        return {
          success: false,
          error: 'Missing Facebook credentials'
        };
      }

      const response = await fetch(
        `https://graph.facebook.com/v18.0/oauth/access_token?` + // Updated to use v18.0
        `grant_type=fb_exchange_token&` +
        `client_id=${clientId}&` +
        `client_secret=${clientSecret}&` +
        `fb_exchange_token=${tokenInfo.accessToken}`
      );

      const data = await response.json();

      if (response.ok && data.access_token) {
        // Facebook long-lived tokens last ~60 days, set expiry to 55 days for buffer
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 55);

        return {
          success: true,
          newToken: data.access_token,
          expiresAt: expiresAt.toISOString()
        };
      }

      return {
        success: false,
        error: data.error?.message || 'Token refresh failed'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async refreshLinkedInToken(tokenInfo: TokenInfo): Promise<RefreshResult> {
    try {
      const clientId = process.env.LINKEDIN_CLIENT_ID;
      const clientSecret = process.env.LINKEDIN_CLIENT_SECRET;
      
      if (!clientId || !clientSecret) {
        return {
          success: false,
          error: 'Missing LinkedIn credentials'
        };
      }

      const response = await fetch('https://www.linkedin.com/oauth/v2/accessToken', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: tokenInfo.refreshToken!,
          client_id: clientId,
          client_secret: clientSecret
        })
      });

      const data = await response.json();

      if (response.ok && data.access_token) {
        const expiresAt = data.expires_in 
          ? new Date(Date.now() + data.expires_in * 1000).toISOString()
          : undefined;

        return {
          success: true,
          newToken: data.access_token,
          expiresAt
        };
      }

      return {
        success: false,
        error: data.error_description || 'Token refresh failed'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Ensure token is valid before API call - refresh if needed
   */
  async ensureValidToken(account: any): Promise<{ token: string; success: boolean; error?: string }> {
    try {
      const tokenInfo: TokenInfo = {
        platform: account.platform,
        accessToken: account.access_token,
        refreshToken: account.refresh_token,
        expiresAt: account.expires_at,
        accountId: account.id,
        userId: account.user_id
      };

      // Check if token is expired or close to expiry (1 day buffer for API calls)
      if (tokenInfo.expiresAt) {
        const expiryTime = new Date(tokenInfo.expiresAt).getTime();
        const now = Date.now();
        const bufferTime = 24 * 60 * 60 * 1000; // 1 day buffer

        if (now > (expiryTime - bufferTime)) {
          console.log(`🔄 Token expired for ${account.platform} account, refreshing...`);

          const refreshResult = await this.refreshToken(tokenInfo);

          if (refreshResult.success && refreshResult.newToken) {
            // Update database with new token
            await this.updateTokenInDatabase(account.id, refreshResult.newToken, refreshResult.expiresAt);

            return {
              token: refreshResult.newToken,
              success: true
            };
          } else {
            return {
              token: account.access_token,
              success: false,
              error: refreshResult.error || 'Token refresh failed'
            };
          }
        }
      }

      // Token is still valid
      return {
        token: account.access_token,
        success: true
      };
    } catch (error) {
      console.error('❌ Error ensuring valid token:', error);
      return {
        token: account.access_token,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Database helper methods
   */
  private async updateTokenInDatabase(
    accountId: string,
    newToken: string,
    expiresAt?: string
  ): Promise<void> {
    const updateData: any = {
      access_token: newToken,
      updated_at: new Date().toISOString(),
      is_active: true,
      connection_status: 'connected',
      last_validated_at: new Date().toISOString()
    };

    if (expiresAt) {
      updateData.expires_at = expiresAt;
    }

    const { error } = await this.getSupabaseClient()
      .from('social_accounts')
      .update(updateData)
      .eq('id', accountId);

    if (error) {
      throw new Error(`Failed to update token: ${error.message}`);
    }
  }

  private async markAccountAsExpired(accountId: string): Promise<void> {
    const { error } = await this.getSupabaseClient()
      .from('social_accounts')
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', accountId);

    if (error) {
      throw new Error(`Failed to mark account as expired: ${error.message}`);
    }
  }
}

// Singleton instance
export const tokenManager = new SocialTokenManager();