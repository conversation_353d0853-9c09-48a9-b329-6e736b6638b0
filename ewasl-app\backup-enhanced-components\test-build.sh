#!/bin/bash

# eWasl Local Build Test Script
echo "🧪 Testing eWasl Build Configuration"
echo "===================================="

# Set error handling
set -e

# Environment setup
export NODE_ENV=production
export NEXT_TELEMETRY_DISABLED=1

echo "📋 Environment Information:"
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"
echo "Working directory: $(pwd)"

# Check package.json for issues
echo "🔍 Checking package.json..."
if [ -f "package.json" ]; then
    echo "✅ package.json found"
    
    # Check for duplicate dependencies
    if grep -q '"sonner".*"sonner"' package.json; then
        echo "❌ Duplicate sonner dependency found!"
        exit 1
    else
        echo "✅ No duplicate dependencies"
    fi
    
    # Check for required dependencies
    if grep -q '"@radix-ui/react-icons"' package.json; then
        echo "✅ @radix-ui/react-icons dependency found"
    else
        echo "❌ Missing @radix-ui/react-icons dependency"
        exit 1
    fi
else
    echo "❌ package.json not found"
    exit 1
fi

# Check for conflicting config files
echo "🔍 Checking configuration files..."
if [ -f "next.config.js" ] && [ -f "next.config.ts" ]; then
    echo "❌ Both next.config.js and next.config.ts found - conflict!"
    exit 1
elif [ -f "next.config.js" ]; then
    echo "✅ next.config.js found"
elif [ -f "next.config.ts" ]; then
    echo "✅ next.config.ts found"
else
    echo "❌ No Next.js config file found"
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf .next
rm -rf node_modules/.cache

# Install dependencies
echo "📦 Installing dependencies..."
npm ci --production=false --prefer-offline --no-audit --no-fund

# Verify critical dependencies
echo "🔍 Verifying critical dependencies..."
npm list next react typescript sonner @radix-ui/react-icons --depth=0

# Check for enhanced components
echo "🔍 Checking enhanced components..."
if [ -f "src/components/ui/enhanced-button.tsx" ]; then
    echo "✅ Enhanced button component found"
else
    echo "❌ Enhanced button component missing"
    exit 1
fi

if [ -f "src/lib/i18n.ts" ]; then
    echo "✅ i18n library found"
else
    echo "❌ i18n library missing"
    exit 1
fi

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Build with memory optimization
echo "🏗️  Building application..."
export NODE_OPTIONS="--max-old-space-size=2048"
npm run build

# Verify build output
echo "✅ Build verification..."
if [ -d ".next" ]; then
    echo "✅ .next directory created"
    if [ -f ".next/BUILD_ID" ]; then
        echo "✅ Build ID: $(cat .next/BUILD_ID)"
    fi
    if [ -d ".next/static" ]; then
        echo "✅ Static assets generated"
    fi
    if [ -f ".next/standalone/server.js" ]; then
        echo "✅ Standalone server generated"
    fi
else
    echo "❌ .next directory not found"
    exit 1
fi

# Test start command
echo "🚀 Testing start command..."
timeout 10s npm start || echo "✅ Start command works (timed out as expected)"

echo ""
echo "🎉 Build test completed successfully!"
echo "✅ All checks passed"
echo "✅ Ready for DigitalOcean deployment"
echo ""
echo "Next steps:"
echo "1. Commit and push changes to GitHub"
echo "2. DigitalOcean will auto-deploy"
echo "3. Monitor build logs in DigitalOcean dashboard"
