#!/usr/bin/env node

/**
 * eWasl Social Media Platform - Vercel Deployment Script
 * 
 * This script handles the deployment of the eWasl application to Vercel production environment
 * to resolve the 500 Internal Server Error related to Facebook/Instagram API localhost restrictions.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 eWasl Social Media Platform - Vercel Deployment');
console.log('=' .repeat(60));

// Environment variables that need to be set in Vercel
const requiredEnvVars = {
  // Application Environment
  'NODE_ENV': 'production',
  'NEXT_PUBLIC_APP_URL': 'https://your-vercel-domain.vercel.app', // Will be updated after deployment
  
  // Database Configuration
  'DATABASE_URL': 'postgresql://postgres:<EMAIL>:5432/postgres',
  
  // Supabase Configuration
  'NEXT_PUBLIC_SUPABASE_URL': 'https://nnxfzhxqzmriggulsudr.supabase.co',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w',
  'SUPABASE_SERVICE_ROLE_KEY': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjA2ODc2OCwiZXhwIjoyMDY3NjQ0NzY4fQ.q4cHohdkLxlB41jp1k-5w4QLWX4R93ugtfEvLnqSJNM',
  
  // NextAuth Configuration
  'NEXTAUTH_SECRET': 'your_nextauth_secret_here_production',
  
  // Facebook/Instagram OAuth
  'FACEBOOK_APP_ID': '1366325774493759',
  'FACEBOOK_APP_SECRET': '********************************',
  'FACEBOOK_BUSINESS_ID': '1479865455689755',
  'INSTAGRAM_APP_ID': '1366325774493759',
  'INSTAGRAM_APP_SECRET': '********************************',
  
  // Security Configuration
  'SECURITY_ENABLE_RATE_LIMIT': 'true',
  'SECURITY_RATE_LIMIT_MAX': '100',
  
  // Cron Secret
  'CRON_SECRET': 'your_cron_secret_here'
};

function runCommand(command, description) {
  console.log(`\n📋 ${description}...`);
  try {
    const output = execSync(command, { 
      stdio: 'pipe', 
      encoding: 'utf8',
      cwd: __dirname 
    });
    console.log(`✅ ${description} completed successfully`);
    return output;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    if (error.stdout) console.log('STDOUT:', error.stdout);
    if (error.stderr) console.log('STDERR:', error.stderr);
    throw error;
  }
}

function checkPrerequisites() {
  console.log('\n🔍 Checking prerequisites...');
  
  // Check if package.json exists
  if (!fs.existsSync('./package.json')) {
    throw new Error('package.json not found. Make sure you\'re in the ewasl-app directory.');
  }
  
  // Check if .env.local exists
  if (!fs.existsSync('./.env.local')) {
    console.log('⚠️  .env.local not found, but environment variables will be set in Vercel');
  }
  
  console.log('✅ Prerequisites check completed');
}

function buildApplication() {
  console.log('\n🔨 Building application...');
  try {
    runCommand('npm run build', 'Application build');
  } catch (error) {
    console.log('⚠️  Build failed, but continuing with deployment (Vercel will build)');
  }
}

function deployToVercel() {
  console.log('\n🚀 Deploying to Vercel...');
  
  try {
    // Try to deploy using npx vercel
    const deployOutput = runCommand('npx vercel --prod --yes', 'Vercel deployment');
    
    // Extract deployment URL from output
    const urlMatch = deployOutput.match(/https:\/\/[^\s]+\.vercel\.app/);
    if (urlMatch) {
      const deploymentUrl = urlMatch[0];
      console.log(`\n🎉 Deployment successful!`);
      console.log(`🌐 Production URL: ${deploymentUrl}`);
      return deploymentUrl;
    }
    
    return null;
  } catch (error) {
    console.log('\n📝 Manual deployment instructions:');
    console.log('1. Install Vercel CLI: npm install -g vercel');
    console.log('2. Login to Vercel: vercel login');
    console.log('3. Deploy: vercel --prod');
    console.log('\nOr use the Vercel Dashboard to deploy from GitHub.');
    throw error;
  }
}

function setEnvironmentVariables(deploymentUrl) {
  console.log('\n⚙️  Setting up environment variables...');
  
  // Update NEXTAUTH_URL and NEXT_PUBLIC_APP_URL with actual deployment URL
  if (deploymentUrl) {
    requiredEnvVars['NEXTAUTH_URL'] = deploymentUrl;
    requiredEnvVars['NEXT_PUBLIC_APP_URL'] = deploymentUrl;
    requiredEnvVars['OAUTH_REDIRECT_URL'] = `${deploymentUrl}/api/auth/callback`;
    requiredEnvVars['SECURITY_ALLOWED_ORIGINS'] = deploymentUrl;
  }
  
  console.log('\n📋 Environment variables to set in Vercel Dashboard:');
  console.log('=' .repeat(60));
  
  Object.entries(requiredEnvVars).forEach(([key, value]) => {
    console.log(`${key}=${value}`);
  });
  
  console.log('\n💡 To set these variables:');
  console.log('1. Go to https://vercel.com/dashboard');
  console.log('2. Select your ewasl-app project');
  console.log('3. Go to Settings > Environment Variables');
  console.log('4. Add each variable above');
  console.log('5. Redeploy the application');
}

function updateFacebookAppSettings(deploymentUrl) {
  if (!deploymentUrl) return;
  
  console.log('\n🔧 Facebook App Configuration Update Required:');
  console.log('=' .repeat(60));
  console.log('1. Go to https://developers.facebook.com/apps/1366325774493759/');
  console.log('2. Update OAuth Redirect URIs:');
  console.log(`   - Add: ${deploymentUrl}/api/auth/callback/facebook`);
  console.log(`   - Add: ${deploymentUrl}/api/auth/callback/instagram`);
  console.log('3. Update App Domains:');
  console.log(`   - Add: ${deploymentUrl.replace('https://', '')}`);
  console.log('4. Update Valid OAuth Redirect URIs in Instagram Basic Display');
}

function displayPostDeploymentInstructions(deploymentUrl) {
  console.log('\n🎯 Post-Deployment Testing Instructions:');
  console.log('=' .repeat(60));
  
  if (deploymentUrl) {
    console.log(`1. Visit: ${deploymentUrl}/posts/new`);
    console.log('2. Test post creation with Facebook/Instagram publishing');
    console.log('3. Verify that the 500 Internal Server Error is resolved');
    console.log(`4. Use debug tools: ${deploymentUrl}/debug-post`);
    console.log('\n🔍 Expected Results:');
    console.log('✅ Facebook/Instagram APIs should work with production domain');
    console.log('✅ OAuth flows should complete successfully');
    console.log('✅ Post publishing should work without 500 errors');
    console.log('✅ Better server-side error logging available');
  }
  
  console.log('\n📊 Monitoring:');
  console.log('- Check Vercel Function logs for any remaining errors');
  console.log('- Monitor API response times and success rates');
  console.log('- Test all social media platform integrations');
}

async function main() {
  try {
    console.log('Starting eWasl deployment process...\n');
    
    // Step 1: Check prerequisites
    checkPrerequisites();
    
    // Step 2: Build application (optional, Vercel will build)
    buildApplication();
    
    // Step 3: Deploy to Vercel
    const deploymentUrl = deployToVercel();
    
    // Step 4: Display environment variables setup
    setEnvironmentVariables(deploymentUrl);
    
    // Step 5: Facebook app configuration update
    updateFacebookAppSettings(deploymentUrl);
    
    // Step 6: Post-deployment instructions
    displayPostDeploymentInstructions(deploymentUrl);
    
    console.log('\n🎉 Deployment process completed!');
    console.log('The 500 Internal Server Error should be resolved in production.');
    
  } catch (error) {
    console.error('\n❌ Deployment failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure you have Vercel CLI installed: npm install -g vercel');
    console.log('2. Login to Vercel: vercel login');
    console.log('3. Try manual deployment: vercel --prod');
    console.log('4. Check Vercel Dashboard for deployment status');
    process.exit(1);
  }
}

// Run the deployment
if (require.main === module) {
  main();
}

module.exports = { main, requiredEnvVars };
