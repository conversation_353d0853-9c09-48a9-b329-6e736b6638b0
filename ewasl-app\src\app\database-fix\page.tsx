'use client';

import { useState } from 'react';

export default function DatabaseFixPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const executeSchemaFix = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/fix-database-schema', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  const testOAuthTokens = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-oauth-tokens');
      const data = await response.json();
      setResult({
        test: 'oauth-tokens',
        ...data
      });
    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            🔧 إصلاح مخطط قاعدة البيانات
          </h1>
          
          <div className="space-y-4 mb-6">
            <button
              onClick={executeSchemaFix}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50"
            >
              {loading ? 'جاري التنفيذ...' : 'تنفيذ إصلاح المخطط'}
            </button>
            
            <button
              onClick={testOAuthTokens}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium disabled:opacity-50 mr-4"
            >
              {loading ? 'جاري الاختبار...' : 'اختبار OAuth Tokens'}
            </button>
          </div>

          {result && (
            <div className="bg-gray-100 rounded-lg p-4">
              <h2 className="text-lg font-semibold mb-3">النتائج:</h2>
              <pre className="text-sm overflow-auto max-h-96 bg-white p-4 rounded border">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
