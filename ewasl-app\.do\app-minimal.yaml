# Minimal DigitalOcean App Platform Specification
# Basic Next.js deployment test

name: ewasl-minimal-test
region: nyc3

services:
  - name: web
    source_dir: /
    github:
      repo: TahaOsa/eWasl.com
      branch: main
      deploy_on_push: true
    
    # Minimal Build Configuration
    build_command: npm install && npm run build
    run_command: npm start
    
    # Minimal Instance Configuration
    instance_count: 1
    instance_size_slug: apps-s-1vcpu-2gb
    
    # HTTP Configuration
    http_port: 3000
    
    # Minimal Environment Variables
    envs:
      # Application Environment
      - key: NODE_ENV
        value: production
        scope: RUN_TIME
      
      - key: NODE_ENV
        value: production
        scope: BUILD_TIME

# No domains, no alerts, minimal configuration
domains: []
