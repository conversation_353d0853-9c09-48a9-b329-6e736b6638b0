import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing database connectivity and schema...');
    
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const results: any = {
      user_info: {
        id: user.id,
        email: user.email
      },
      tables: {},
      errors: []
    };

    // Test 1: Check posts table
    console.log('📝 Testing posts table...');
    try {
      const { data: posts, error: postsError } = await supabase
        .from('posts')
        .select('id, content, status, created_at')
        .eq('user_id', user.id)
        .limit(5);

      if (postsError) {
        results.errors.push({ table: 'posts', error: postsError });
        results.tables.posts = { success: false, error: postsError };
      } else {
        results.tables.posts = { 
          success: true, 
          count: posts?.length || 0,
          sample: posts?.slice(0, 2) || []
        };
      }
    } catch (error) {
      results.errors.push({ table: 'posts', error: error });
      results.tables.posts = { success: false, error: error };
    }

    // Test 2: Check social_accounts table
    console.log('🔗 Testing social_accounts table...');
    try {
      const { data: accounts, error: accountsError } = await supabase
        .from('social_accounts')
        .select('id, platform, account_name, account_id, created_at')
        .eq('user_id', user.id);

      if (accountsError) {
        results.errors.push({ table: 'social_accounts', error: accountsError });
        results.tables.social_accounts = { success: false, error: accountsError };
      } else {
        results.tables.social_accounts = { 
          success: true, 
          count: accounts?.length || 0,
          platforms: accounts?.map(a => ({ platform: a.platform, name: a.account_name })) || []
        };
      }
    } catch (error) {
      results.errors.push({ table: 'social_accounts', error: error });
      results.tables.social_accounts = { success: false, error: error };
    }

    // Test 3: Check publishing_results table (optional)
    console.log('📊 Testing publishing_results table...');
    try {
      const { data: publishingResults, error: publishingError } = await supabase
        .from('publishing_results')
        .select('id, post_id, platform, success, created_at')
        .limit(5);

      if (publishingError) {
        results.tables.publishing_results = { 
          success: false, 
          error: publishingError,
          note: 'Table may not exist - this is optional'
        };
      } else {
        results.tables.publishing_results = { 
          success: true, 
          count: publishingResults?.length || 0,
          sample: publishingResults?.slice(0, 2) || []
        };
      }
    } catch (error) {
      results.tables.publishing_results = { 
        success: false, 
        error: error,
        note: 'Table may not exist - this is optional'
      };
    }

    // Test 4: Check scheduled_posts_queue table (optional)
    console.log('⏰ Testing scheduled_posts_queue table...');
    try {
      const { data: queueData, error: queueError } = await supabase
        .from('scheduled_posts_queue')
        .select('id, post_id, status, scheduled_for, created_at')
        .limit(5);

      if (queueError) {
        results.tables.scheduled_posts_queue = { 
          success: false, 
          error: queueError,
          note: 'Table may not exist - this is optional'
        };
      } else {
        results.tables.scheduled_posts_queue = { 
          success: true, 
          count: queueData?.length || 0,
          sample: queueData?.slice(0, 2) || []
        };
      }
    } catch (error) {
      results.tables.scheduled_posts_queue = { 
        success: false, 
        error: error,
        note: 'Table may not exist - this is optional'
      };
    }

    // Test 5: Check activities table (optional)
    console.log('📋 Testing activities table...');
    try {
      const { data: activities, error: activitiesError } = await supabase
        .from('activities')
        .select('id, action, details, created_at')
        .eq('user_id', user.id)
        .limit(5);

      if (activitiesError) {
        results.tables.activities = { 
          success: false, 
          error: activitiesError,
          note: 'Table may not exist - this is optional'
        };
      } else {
        results.tables.activities = { 
          success: true, 
          count: activities?.length || 0,
          sample: activities?.slice(0, 2) || []
        };
      }
    } catch (error) {
      results.tables.activities = { 
        success: false, 
        error: error,
        note: 'Table may not exist - this is optional'
      };
    }

    // Summary
    const successfulTables = Object.values(results.tables).filter((t: any) => t.success).length;
    const totalTables = Object.keys(results.tables).length;

    return NextResponse.json({
      success: true,
      message: `Database test completed: ${successfulTables}/${totalTables} tables accessible`,
      results,
      summary: {
        total_tables_tested: totalTables,
        successful_tables: successfulTables,
        failed_tables: totalTables - successfulTables,
        critical_tables_status: {
          posts: results.tables.posts?.success || false,
          social_accounts: results.tables.social_accounts?.success || false
        }
      }
    });

  } catch (error: any) {
    console.error('❌ Database test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Database test failed',
      details: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing database write operations...');
    
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { testContent = '🧪 Database test post from eWasl platform' } = body;

    // Test 1: Create a test post
    console.log('📝 Testing post creation...');
    const { data: testPost, error: createError } = await supabase
      .from('posts')
      .insert({
        user_id: user.id,
        content: testContent,
        status: 'DRAFT',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (createError) {
      return NextResponse.json({
        success: false,
        step: 'post_creation',
        error: 'Failed to create test post',
        details: createError
      });
    }

    console.log('✅ Test post created:', testPost.id);

    // Test 2: Update the test post
    console.log('✏️ Testing post update...');
    const { data: updatedPost, error: updateError } = await supabase
      .from('posts')
      .update({
        content: testContent + ' (UPDATED)',
        status: 'PUBLISHED',
        updated_at: new Date().toISOString()
      })
      .eq('id', testPost.id)
      .select()
      .single();

    if (updateError) {
      return NextResponse.json({
        success: false,
        step: 'post_update',
        error: 'Failed to update test post',
        details: updateError
      });
    }

    console.log('✅ Test post updated:', updatedPost.id);

    // Test 3: Create activity log
    console.log('📋 Testing activity logging...');
    const { error: activityError } = await supabase
      .from('activities')
      .insert({
        user_id: user.id,
        post_id: testPost.id,
        action: 'POST_CREATED',
        details: `Database test post created: ${testContent.substring(0, 50)}...`,
        created_at: new Date().toISOString(),
      });

    const activityResult = activityError ? 
      { success: false, error: activityError } : 
      { success: true, message: 'Activity logged successfully' };

    // Test 4: Clean up - delete the test post
    console.log('🗑️ Cleaning up test post...');
    const { error: deleteError } = await supabase
      .from('posts')
      .delete()
      .eq('id', testPost.id);

    const cleanupResult = deleteError ? 
      { success: false, error: deleteError } : 
      { success: true, message: 'Test post deleted successfully' };

    return NextResponse.json({
      success: true,
      message: 'Database write operations test completed',
      results: {
        post_creation: {
          success: true,
          post_id: testPost.id,
          content: testPost.content
        },
        post_update: {
          success: true,
          updated_content: updatedPost.content,
          status_changed: updatedPost.status
        },
        activity_logging: activityResult,
        cleanup: cleanupResult
      }
    });

  } catch (error: any) {
    console.error('❌ Database write test error:', error);
    return NextResponse.json({
      success: false,
      error: 'Database write test failed',
      details: error.message
    }, { status: 500 });
  }
}
