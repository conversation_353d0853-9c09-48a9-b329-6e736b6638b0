import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

export async function GET(request: NextRequest) {
  return getAuthenticatedUser(request).then(({ user, supabase }) => {
    // Return recent activity data for the dashboard
    return NextResponse.json({
      success: true,
      user_id: user.id,
      activities: [
        {
          id: '1',
          type: 'publish_success',
          title: 'تم نشر المنشور بنجاح',
          description: 'تم نشر منشور جديد على Facebook بنجاح',
          platform: 'facebook',
          status: 'success',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          metadata: { 
            post_id: 'post_123',
            engagement: { likes: 45, shares: 12, comments: 8 }
          }
        },
        {
          id: '2',
          type: 'job_scheduled',
          title: 'تم جدولة منشور جديد',
          description: 'تم جدولة منشور للنشر على Instagram غداً الساعة 2:00 م',
          platform: 'instagram',
          status: 'info',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          metadata: { 
            post_id: 'post_124',
            scheduled_for: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          }
        },
        {
          id: '3',
          type: 'connection_added',
          title: 'تم ربط حساب جديد',
          description: 'تم ربط حساب LinkedIn بنجاح',
          platform: 'linkedin',
          status: 'success',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          metadata: { 
            connection_id: 'conn_456',
            account_name: 'شركة eWasl'
          }
        },
        {
          id: '4',
          type: 'publish_failed',
          title: 'فشل في النشر',
          description: 'فشل في نشر المنشور على Twitter - خطأ في الشبكة',
          platform: 'twitter',
          status: 'error',
          timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
          metadata: { 
            post_id: 'post_101',
            error_code: 'NETWORK_ERROR',
            retry_count: 2
          }
        },
        {
          id: '5',
          type: 'system_event',
          title: 'تحديث النظام',
          description: 'تم تحديث خوارزمية الجدولة التلقائية',
          status: 'info',
          timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
          metadata: { 
            version: '2.1.0',
            features: ['auto-scheduling', 'smart-timing']
          }
        },
        {
          id: '6',
          type: 'connection_error',
          title: 'خطأ في الاتصال',
          description: 'انتهت صلاحية رمز Facebook - يرجى إعادة الربط',
          platform: 'facebook',
          status: 'warning',
          timestamp: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
          metadata: { 
            connection_id: 'conn_789',
            error_code: 'TOKEN_EXPIRED',
            expires_at: new Date(Date.now() - 60 * 60 * 1000).toISOString()
          }
        }
      ],
      pagination: {
        total: 6,
        page: 1,
        per_page: 10,
        has_more: false
      },
      generated_at: new Date().toISOString()
    });
  }).catch((error) => {
    return NextResponse.json(
      { error: 'Authentication required for activity data' },
      { status: 401 }
    );
  });
}
