# 🚀 eWasl Social Media Platform - Manual Deployment Guide

## 📋 **Current Status**
✅ **Vercel Project Created**: `ewasl-social-platform`  
✅ **GitHub Repository Connected**: `https://github.com/Mr-Taha-1/ewasl-social-platform`  
✅ **Environment Variables Configured**: All production variables set  
✅ **Deployment URL**: `https://ewasl-social-platform.vercel.app`  

## 🎯 **Step 2: Push eWasl Code to GitHub Repository**

### **Option A: Using Git Command Line (Recommended)**

1. **Open Terminal/Command Prompt** in the `ewasl-app` directory

2. **Initialize Git Repository**:
   ```bash
   git init
   git remote add origin https://github.com/Mr-Taha-1/ewasl-social-platform.git
   ```

3. **Configure Git User** (if not already configured):
   ```bash
   git config user.name "eWasl Deployment"
   git config user.email "<EMAIL>"
   ```

4. **Create .gitignore** (copy content from below):
   ```gitignore
   # Dependencies
   node_modules/
   /.pnp
   .pnp.js
   
   # Testing
   /coverage
   test-results/
   playwright-report/
   
   # Next.js
   /.next/
   /out/
   
   # Production
   /build
   /dist
   
   # Environment variables
   .env
   .env.local
   .env.development.local
   .env.test.local
   .env.production.local
   
   # Vercel
   .vercel
   
   # TypeScript
   *.tsbuildinfo
   next-env.d.ts
   
   # IDE and OS
   .vscode/
   .idea/
   .DS_Store
   Thumbs.db
   
   # Logs
   logs
   *.log
   npm-debug.log*
   
   # Test files and reports
   test-*.png
   test-*.html
   test-*.json
   test-*.txt
   test-*.md
   *test-report*
   *-test.js
   comprehensive-test*
   final-*test*
   browser-test*
   
   # Documentation and reports
   *-REPORT.md
   *-SUMMARY.md
   *-ANALYSIS.md
   *-GUIDE.md
   *-STATUS.md
   DEPLOYMENT*.md
   TESTING*.md
   
   # Scripts and utilities
   fix-*.js
   setup-*.js
   debug-*.js
   test-*.js
   verify-*.js
   
   # Backup files
   *.backup
   *-backup.*
   backup-*/
   
   # Screenshots
   *.png
   *.jpg
   *.jpeg
   
   # Docker and deployment
   Dockerfile*
   docker-compose*
   deploy*.sh
   deploy*.bat
   deploy*.js
   
   # Certificates
   *.crt
   *.key
   *.pem
   ```

5. **Stage and Commit Files**:
   ```bash
   git add .
   git commit -m "Deploy eWasl Social Media Platform

   - Complete Next.js application with Arabic RTL support
   - Multi-platform social media publishing (Facebook, Instagram, LinkedIn, Twitter)
   - TipTap rich text editor with media upload
   - Advanced scheduling and queue system
   - Comprehensive analytics dashboard
   - Supabase integration for database and storage
   - Production-ready deployment"
   ```

6. **Push to GitHub**:
   ```bash
   git push -u origin main
   ```

### **Option B: Using GitHub Desktop**

1. **Download GitHub Desktop**: https://desktop.github.com/
2. **Clone Repository**: `https://github.com/Mr-Taha-1/ewasl-social-platform`
3. **Copy eWasl Files**: Copy all files from `ewasl-app/` to the cloned repository
4. **Commit Changes**: Add commit message and commit
5. **Push to Origin**: Push changes to GitHub

### **Option C: Using VS Code**

1. **Open eWasl Project** in VS Code
2. **Initialize Git**: Use VS Code's Git integration
3. **Add Remote**: Add the GitHub repository as remote
4. **Stage All Files**: Use VS Code's Source Control panel
5. **Commit and Push**: Commit changes and push to GitHub

## 🎯 **Step 3: Update Facebook App Configuration**

### **Facebook Developer Console Updates**

1. **Go to Facebook Developer Console**:
   https://developers.facebook.com/apps/1366325774493759/

2. **Update OAuth Redirect URIs**:
   - Navigate to **Facebook Login** → **Settings**
   - Add these URLs to **Valid OAuth Redirect URIs**:
     ```
     https://ewasl-social-platform.vercel.app/api/auth/callback/facebook
     https://ewasl-social-platform.vercel.app/api/auth/callback
     https://ewasl-social-platform.vercel.app/auth/callback
     ```

3. **Update App Domains**:
   - Go to **App Settings** → **Basic**
   - Add to **App Domains**:
     ```
     ewasl-social-platform.vercel.app
     ```

4. **Update Instagram Basic Display**:
   - Navigate to **Instagram Basic Display** → **Settings**
   - Add to **Valid OAuth Redirect URIs**:
     ```
     https://ewasl-social-platform.vercel.app/api/auth/callback/instagram
     https://ewasl-social-platform.vercel.app/auth/callback
     ```

5. **Update Website URL**:
   - In **App Settings** → **Basic**
   - Set **Website URL**:
     ```
     https://ewasl-social-platform.vercel.app
     ```

## 🎯 **Step 4: Test the Deployment**

### **Automatic Deployment Verification**

1. **Check Vercel Dashboard**:
   - Go to: https://vercel.com/ewasls-projects/ewasl-social-platform
   - Verify deployment is successful
   - Check build logs if there are any issues

2. **Test Application URLs**:
   ```
   Homepage: https://ewasl-social-platform.vercel.app
   Dashboard: https://ewasl-social-platform.vercel.app/dashboard
   Post Creation: https://ewasl-social-platform.vercel.app/posts/new
   Social Accounts: https://ewasl-social-platform.vercel.app/social
   Debug Tools: https://ewasl-social-platform.vercel.app/debug-post
   ```

### **Facebook/Instagram Integration Testing**

1. **Test OAuth Connection**:
   - Visit: https://ewasl-social-platform.vercel.app/social
   - Try connecting Facebook and Instagram accounts
   - Verify OAuth flow completes successfully

2. **Test Post Publishing**:
   - Visit: https://ewasl-social-platform.vercel.app/posts/new
   - Create a test post
   - Select Facebook/Instagram platforms
   - Set to "نشر فوري" (Immediate Publishing)
   - Click "نشر الآن" (Publish Now)
   - **Expected Result**: ✅ No 500 Internal Server Error

3. **Debug Tools Verification**:
   - Visit: https://ewasl-social-platform.vercel.app/debug-post
   - Run the comprehensive debug test
   - **Expected Result**: ✅ All steps pass, including API calls

## 🎯 **Expected Results After Deployment**

### **Issues That Should Be Resolved**
- ✅ **500 Internal Server Error**: Fixed due to production domain
- ✅ **Facebook/Instagram API Restrictions**: Resolved with HTTPS domain
- ✅ **OAuth Callback Issues**: Fixed with proper redirect URIs
- ✅ **Better Error Logging**: Available in Vercel Function logs

### **Performance Improvements**
- ✅ **Faster API Responses**: Production environment optimization
- ✅ **Better Caching**: Vercel's edge network
- ✅ **SSL/HTTPS**: Automatic SSL certificates
- ✅ **CDN**: Global content delivery

## 🚨 **Troubleshooting**

### **If Deployment Fails**
1. Check Vercel build logs in the dashboard
2. Verify all environment variables are set correctly
3. Ensure package.json has correct build scripts
4. Check for any TypeScript or ESLint errors

### **If 500 Errors Persist**
1. Check Vercel Function logs in the dashboard
2. Use debug endpoint: `/debug-post`
3. Verify Facebook/Instagram API credentials
4. Test individual API endpoints

### **If OAuth Fails**
1. Double-check Facebook app redirect URIs
2. Ensure NEXTAUTH_URL matches deployment URL
3. Verify NEXTAUTH_SECRET is set in Vercel
4. Test OAuth flow step by step

## 📞 **Support**

If you encounter issues:
1. **Vercel Logs**: Check Function logs in Vercel Dashboard
2. **Debug Tools**: Use `/debug-post` endpoint
3. **Environment Variables**: Verify in Vercel Settings
4. **Facebook Console**: Check API status and settings

## 🎉 **Success Criteria**

The deployment is successful when:
- ✅ Application loads without errors
- ✅ User authentication works
- ✅ Post creation completes without 500 errors
- ✅ Facebook/Instagram publishing works
- ✅ OAuth flows complete successfully
- ✅ All debug tools show green status

---

**The production deployment should resolve the localhost-related 500 Internal Server Error and enable proper Facebook/Instagram API integration.**
