"use client";

import { useState } from "react";

interface SimplePostFormProps {
  initialData?: any;
}

export function SimplePostForm({ initialData }: SimplePostFormProps) {
  const [content, setContent] = useState("");

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">إنشاء منشور جديد</h1>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">المحتوى</label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            className="w-full p-3 border rounded-lg"
            rows={4}
            placeholder="اكتب محتوى المنشور هنا..."
            dir="rtl"
          />
        </div>
        <button
          type="button"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          نشر
        </button>
      </div>
    </div>
  );
}
