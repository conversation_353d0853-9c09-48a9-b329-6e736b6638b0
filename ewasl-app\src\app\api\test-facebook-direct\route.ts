import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

/**
 * ISOLATED Facebook API Test Endpoint
 * Tests direct Facebook Graph API calls bypassing full publishing workflow
 * Uses existing Facebook account tokens from social_accounts table
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 [ISOLATED TEST] Starting direct Facebook API test...');

    // Get authenticated user
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      content = '🧪 اختبار مباشر لـ Facebook API من منصة إي وصل\n\nTesting direct Facebook API from eWasl platform!\n\n#FacebookAPI #eWasl #DirectTest',
      usePageToken = true 
    } = body;

    console.log('🔍 Test parameters:', { content: content.substring(0, 50) + '...', usePageToken });

    // Get Facebook accounts from database
    const { data: facebookAccounts, error: accountsError } = await supabase
      .from('social_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('platform', 'FACEBOOK')
      .eq('connection_status', 'connected')
      .eq('status', 'ACTIVE');

    if (accountsError) {
      console.error('❌ Error fetching Facebook accounts:', accountsError);
      return NextResponse.json({
        success: false,
        error: 'فشل في جلب حسابات فيسبوك',
        details: accountsError.message
      }, { status: 500 });
    }

    if (!facebookAccounts || facebookAccounts.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لا توجد حسابات فيسبوك متصلة',
        message: 'Please connect a Facebook account first',
        accounts_found: 0
      }, { status: 404 });
    }

    console.log(`✅ Found ${facebookAccounts.length} Facebook accounts`);

    const testResults = [];

    // Test each Facebook account
    for (const account of facebookAccounts) {
      console.log(`🧪 Testing account: ${account.account_name} (${account.account_id})`);
      
      const accountTest = {
        account_id: account.account_id,
        account_name: account.account_name,
        page_id: account.page_id,
        page_name: account.page_name,
        has_access_token: !!account.access_token,
        has_page_access_token: !!account.page_access_token,
        tests: []
      };

      // Test 1: Token validation
      console.log('🔍 Test 1: Token validation...');
      try {
        const tokenToTest = usePageToken && account.page_access_token ? account.page_access_token : account.access_token;
        const targetId = usePageToken && account.page_id ? account.page_id : account.account_id;
        
        const tokenResponse = await fetch(`https://graph.facebook.com/v19.0/${targetId}?access_token=${tokenToTest}`);
        const tokenData = await tokenResponse.json();
        
        accountTest.tests.push({
          test: 'token_validation',
          success: tokenResponse.ok,
          token_type: usePageToken && account.page_access_token ? 'page_token' : 'user_token',
          target_id: targetId,
          response: tokenData,
          error: !tokenResponse.ok ? tokenData.error?.message : null
        });

        console.log(`${tokenResponse.ok ? '✅' : '❌'} Token validation:`, tokenResponse.ok ? 'Valid' : tokenData.error?.message);

      } catch (error) {
        accountTest.tests.push({
          test: 'token_validation',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        console.error('❌ Token validation error:', error);
      }

      // Test 2: Direct Facebook API publishing
      console.log('🔍 Test 2: Direct Facebook API publishing...');
      try {
        const publishingToken = usePageToken && account.page_access_token ? account.page_access_token : account.access_token;
        const targetId = usePageToken && account.page_id ? account.page_id : account.account_id;
        
        // Prepare post data (form-encoded as required by Facebook)
        const postData = new URLSearchParams();
        postData.append('message', content);
        postData.append('access_token', publishingToken);

        console.log('📤 Publishing to Facebook:', {
          url: `https://graph.facebook.com/v19.0/${targetId}/feed`,
          target_id: targetId,
          target_type: usePageToken && account.page_id ? 'PAGE' : 'USER',
          content_length: content.length
        });

        const publishResponse = await fetch(`https://graph.facebook.com/v19.0/${targetId}/feed`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'eWasl-Social-Scheduler/1.0'
          },
          body: postData
        });

        const publishData = await publishResponse.json();
        
        accountTest.tests.push({
          test: 'direct_publishing',
          success: publishResponse.ok,
          token_type: usePageToken && account.page_access_token ? 'page_token' : 'user_token',
          target_id: targetId,
          post_id: publishData.id,
          post_url: publishData.id ? `https://facebook.com/${publishData.id}` : null,
          response: publishData,
          error: !publishResponse.ok ? publishData.error?.message : null
        });

        console.log(`${publishResponse.ok ? '✅' : '❌'} Direct publishing:`, 
          publishResponse.ok ? `Success - Post ID: ${publishData.id}` : publishData.error?.message);

        // Test 3: Create post_social_accounts entry (if publishing succeeded)
        if (publishResponse.ok && publishData.id) {
          console.log('🔍 Test 3: Creating post_social_accounts entry...');
          
          // First create a test post
          const { data: testPost, error: postError } = await supabase
            .from('posts')
            .insert({
              user_id: user.id,
              content: content,
              status: 'PUBLISHED',
              published_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();

          if (postError) {
            accountTest.tests.push({
              test: 'post_creation',
              success: false,
              error: postError.message
            });
          } else {
            // Create post_social_accounts entry
            const { data: socialAccountEntry, error: socialError } = await supabase
              .from('post_social_accounts')
              .insert({
                post_id: testPost.id,
                social_account_id: account.id,
                platform_post_id: publishData.id,
                platform_url: `https://facebook.com/${publishData.id}`,
                status: 'PUBLISHED',
                published_at: new Date().toISOString(),
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .select()
              .single();

            accountTest.tests.push({
              test: 'post_social_accounts_creation',
              success: !socialError,
              post_id: testPost.id,
              social_account_entry_id: socialAccountEntry?.id,
              error: socialError?.message
            });

            console.log(`${!socialError ? '✅' : '❌'} post_social_accounts creation:`, 
              !socialError ? `Success - Entry ID: ${socialAccountEntry?.id}` : socialError?.message);
          }
        }

      } catch (error) {
        accountTest.tests.push({
          test: 'direct_publishing',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        console.error('❌ Direct publishing error:', error);
      }

      testResults.push(accountTest);
    }

    // Summary
    const totalTests = testResults.reduce((sum, account) => sum + account.tests.length, 0);
    const successfulTests = testResults.reduce((sum, account) => 
      sum + account.tests.filter(test => test.success).length, 0);

    console.log(`📊 Test Summary: ${successfulTests}/${totalTests} tests passed`);

    return NextResponse.json({
      success: successfulTests > 0,
      message: successfulTests > 0 ? 
        `نجح ${successfulTests} من ${totalTests} اختبارات Facebook API` :
        'فشلت جميع اختبارات Facebook API',
      accounts_tested: facebookAccounts.length,
      total_tests: totalTests,
      successful_tests: successfulTests,
      test_results: testResults,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [ISOLATED TEST] Critical error:', error);
    return NextResponse.json({
      success: false,
      error: 'فشل حرج في اختبار Facebook API المباشر',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'استخدم POST لتنفيذ اختبار Facebook API المباشر',
    usage: 'POST /api/test-facebook-direct',
    parameters: {
      content: 'نص المنشور (اختياري)',
      usePageToken: 'استخدام رمز الصفحة بدلاً من رمز المستخدم (افتراضي: true)'
    },
    timestamp: new Date().toISOString()
  });
}
