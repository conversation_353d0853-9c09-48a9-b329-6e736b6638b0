{"version": 3, "names": ["_nonIterableSpread", "TypeError"], "sources": ["../../src/helpers/nonIterableSpread.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _nonIterableSpread() {\n  throw new TypeError(\n    \"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\",\n  );\n}\n"], "mappings": ";;;;;;AAEe,SAASA,kBAAkBA,CAAA,EAAG;EAC3C,MAAM,IAAIC,SAAS,CACjB,sIACF,CAAC;AACH", "ignoreList": []}