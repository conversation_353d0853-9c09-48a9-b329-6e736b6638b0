/**
 * Comprehensive OAuth Logging System
 * Provides detailed logging for OAuth operations with Arabic support
 */

import { createServiceRoleClient } from '@/lib/supabase/service-role';

export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL'
}

export enum LogCategory {
  OAUTH_FLOW = 'OAUTH_FLOW',
  TOKEN_MANAGEMENT = 'TOKEN_MANAGEMENT',
  API_REQUEST = 'API_REQUEST',
  USER_ACTION = 'USER_ACTION',
  SYSTEM_EVENT = 'SYSTEM_EVENT',
  SECURITY = 'SECURITY',
  PERFORMANCE = 'PERFORMANCE'
}

export interface LogEntry {
  id?: string;
  level: LogLevel;
  category: LogCategory;
  message: string;
  arabicMessage?: string;
  platform?: string;
  userId?: string;
  accountId?: string;
  sessionId?: string;
  requestId?: string;
  metadata?: Record<string, any>;
  timestamp: string;
  duration?: number;
  ipAddress?: string;
  userAgent?: string;
}

export class OAuthLogger {
  private supabase = createServiceRoleClient();
  private sessionId: string;
  private requestId?: string;

  constructor(sessionId?: string, requestId?: string) {
    this.sessionId = sessionId || this.generateSessionId();
    this.requestId = requestId;
  }

  /**
   * Log OAuth flow events
   */
  async logOAuthFlow(
    level: LogLevel,
    message: string,
    arabicMessage: string,
    platform: string,
    userId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      level,
      category: LogCategory.OAUTH_FLOW,
      message,
      arabicMessage,
      platform,
      userId,
      metadata,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log token management events
   */
  async logTokenEvent(
    level: LogLevel,
    action: 'created' | 'refreshed' | 'expired' | 'revoked' | 'validated',
    platform: string,
    userId: string,
    accountId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const messages = {
      created: {
        en: `Token created for ${platform}`,
        ar: `تم إنشاء رمز لـ ${this.getPlatformNameArabic(platform)}`
      },
      refreshed: {
        en: `Token refreshed for ${platform}`,
        ar: `تم تحديث رمز لـ ${this.getPlatformNameArabic(platform)}`
      },
      expired: {
        en: `Token expired for ${platform}`,
        ar: `انتهت صلاحية رمز لـ ${this.getPlatformNameArabic(platform)}`
      },
      revoked: {
        en: `Token revoked for ${platform}`,
        ar: `تم إلغاء رمز لـ ${this.getPlatformNameArabic(platform)}`
      },
      validated: {
        en: `Token validated for ${platform}`,
        ar: `تم التحقق من رمز لـ ${this.getPlatformNameArabic(platform)}`
      }
    };

    await this.log({
      level,
      category: LogCategory.TOKEN_MANAGEMENT,
      message: messages[action].en,
      arabicMessage: messages[action].ar,
      platform,
      userId,
      accountId,
      metadata: {
        action,
        ...metadata
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log API requests
   */
  async logApiRequest(
    level: LogLevel,
    method: string,
    url: string,
    platform: string,
    userId?: string,
    responseStatus?: number,
    duration?: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    const message = `${method} ${url} - ${responseStatus || 'pending'}`;
    const arabicMessage = `طلب API إلى ${this.getPlatformNameArabic(platform)} - ${responseStatus ? 'مكتمل' : 'قيد المعالجة'}`;

    await this.log({
      level,
      category: LogCategory.API_REQUEST,
      message,
      arabicMessage,
      platform,
      userId,
      duration,
      metadata: {
        method,
        url,
        responseStatus,
        ...metadata
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log user actions
   */
  async logUserAction(
    action: string,
    userId: string,
    platform?: string,
    accountId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const actionMessages: Record<string, { en: string; ar: string }> = {
      'account_connected': {
        en: 'Account connected',
        ar: 'تم ربط الحساب'
      },
      'account_disconnected': {
        en: 'Account disconnected',
        ar: 'تم قطع اتصال الحساب'
      },
      'post_created': {
        en: 'Post created',
        ar: 'تم إنشاء منشور'
      },
      'post_published': {
        en: 'Post published',
        ar: 'تم نشر المنشور'
      },
      'post_scheduled': {
        en: 'Post scheduled',
        ar: 'تم جدولة المنشور'
      }
    };

    const messageData = actionMessages[action] || { en: action, ar: action };

    await this.log({
      level: LogLevel.INFO,
      category: LogCategory.USER_ACTION,
      message: messageData.en,
      arabicMessage: messageData.ar,
      platform,
      userId,
      accountId,
      metadata: {
        action,
        ...metadata
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log security events
   */
  async logSecurityEvent(
    level: LogLevel,
    event: string,
    userId?: string,
    platform?: string,
    ipAddress?: string,
    userAgent?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const securityMessages: Record<string, { en: string; ar: string }> = {
      'suspicious_login': {
        en: 'Suspicious login attempt detected',
        ar: 'تم اكتشاف محاولة تسجيل دخول مشبوهة'
      },
      'token_theft_attempt': {
        en: 'Potential token theft attempt',
        ar: 'محاولة محتملة لسرقة الرمز'
      },
      'rate_limit_exceeded': {
        en: 'Rate limit exceeded',
        ar: 'تم تجاوز حد المعدل'
      },
      'unauthorized_access': {
        en: 'Unauthorized access attempt',
        ar: 'محاولة وصول غير مصرح بها'
      }
    };

    const messageData = securityMessages[event] || { en: event, ar: event };

    await this.log({
      level,
      category: LogCategory.SECURITY,
      message: messageData.en,
      arabicMessage: messageData.ar,
      platform,
      userId,
      ipAddress,
      userAgent,
      metadata: {
        securityEvent: event,
        ...metadata
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log performance metrics
   */
  async logPerformance(
    operation: string,
    duration: number,
    platform?: string,
    userId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const level = duration > 5000 ? LogLevel.WARN : LogLevel.INFO;
    const message = `${operation} completed in ${duration}ms`;
    const arabicMessage = `تمت العملية ${operation} في ${duration} ميلي ثانية`;

    await this.log({
      level,
      category: LogCategory.PERFORMANCE,
      message,
      arabicMessage,
      platform,
      userId,
      duration,
      metadata: {
        operation,
        performanceThreshold: duration > 5000 ? 'exceeded' : 'normal',
        ...metadata
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Generic log method
   */
  async log(entry: LogEntry): Promise<void> {
    try {
      // Add session and request IDs
      const logEntry = {
        ...entry,
        session_id: this.sessionId,
        request_id: this.requestId
      };

      // Console logging for development
      if (process.env.NODE_ENV === 'development') {
        this.consoleLog(logEntry);
      }

      // Database logging
      await this.supabase
        .from('oauth_logs')
        .insert({
          level: logEntry.level,
          category: logEntry.category,
          message: logEntry.message,
          arabic_message: logEntry.arabicMessage,
          platform: logEntry.platform,
          user_id: logEntry.userId,
          account_id: logEntry.accountId,
          session_id: logEntry.session_id,
          request_id: logEntry.request_id,
          metadata: logEntry.metadata,
          duration: logEntry.duration,
          ip_address: logEntry.ipAddress,
          user_agent: logEntry.userAgent,
          created_at: logEntry.timestamp
        });

      // Send to external logging service if configured
      if (process.env.EXTERNAL_LOGGING_ENABLED === 'true') {
        await this.sendToExternalLogger(logEntry);
      }

    } catch (error) {
      console.error('Failed to log entry:', error);
      // Fallback to console logging
      this.consoleLog(entry);
    }
  }

  /**
   * Console logging with formatting
   */
  private consoleLog(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toLocaleString();
    const prefix = `[${timestamp}] [${entry.level}] [${entry.category}]`;
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(`${prefix} ${entry.message}`, entry.metadata);
        break;
      case LogLevel.INFO:
        console.info(`${prefix} ${entry.message}`, entry.metadata);
        break;
      case LogLevel.WARN:
        console.warn(`${prefix} ${entry.message}`, entry.metadata);
        break;
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        console.error(`${prefix} ${entry.message}`, entry.metadata);
        break;
    }
  }

  /**
   * Send to external logging service
   */
  private async sendToExternalLogger(entry: LogEntry): Promise<void> {
    try {
      // Example: Send to external service like DataDog, LogRocket, etc.
      if (process.env.EXTERNAL_LOG_ENDPOINT) {
        await fetch(process.env.EXTERNAL_LOG_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.EXTERNAL_LOG_API_KEY}`
          },
          body: JSON.stringify({
            ...entry,
            service: 'ewasl-oauth',
            environment: process.env.NODE_ENV
          })
        });
      }
    } catch (error) {
      console.warn('Failed to send log to external service:', error);
    }
  }

  /**
   * Get platform name in Arabic
   */
  private getPlatformNameArabic(platform: string): string {
    const names: Record<string, string> = {
      'FACEBOOK': 'فيسبوك',
      'INSTAGRAM': 'إنستغرام',
      'TWITTER': 'تويتر',
      'X': 'إكس',
      'LINKEDIN': 'لينكد إن',
      'TIKTOK': 'تيك توك'
    };
    return names[platform.toUpperCase()] || platform;
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create performance timer
   */
  createTimer(operation: string): {
    end: (platform?: string, userId?: string, metadata?: Record<string, any>) => Promise<void>;
  } {
    const startTime = Date.now();
    
    return {
      end: async (platform?: string, userId?: string, metadata?: Record<string, any>) => {
        const duration = Date.now() - startTime;
        await this.logPerformance(operation, duration, platform, userId, metadata);
      }
    };
  }

  /**
   * Get recent logs for debugging
   */
  async getRecentLogs(
    userId?: string,
    platform?: string,
    level?: LogLevel,
    limit: number = 50
  ): Promise<LogEntry[]> {
    try {
      let query = this.supabase
        .from('oauth_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (userId) {
        query = query.eq('user_id', userId);
      }

      if (platform) {
        query = query.eq('platform', platform);
      }

      if (level) {
        query = query.eq('level', level);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return data.map(log => ({
        id: log.id,
        level: log.level,
        category: log.category,
        message: log.message,
        arabicMessage: log.arabic_message,
        platform: log.platform,
        userId: log.user_id,
        accountId: log.account_id,
        sessionId: log.session_id,
        requestId: log.request_id,
        metadata: log.metadata,
        timestamp: log.created_at,
        duration: log.duration,
        ipAddress: log.ip_address,
        userAgent: log.user_agent
      }));

    } catch (error) {
      console.error('Failed to fetch recent logs:', error);
      return [];
    }
  }
}

// Export singleton instance
export const oauthLogger = new OAuthLogger();

// Export factory function for request-specific loggers
export function createOAuthLogger(sessionId?: string, requestId?: string): OAuthLogger {
  return new OAuthLogger(sessionId, requestId);
}
