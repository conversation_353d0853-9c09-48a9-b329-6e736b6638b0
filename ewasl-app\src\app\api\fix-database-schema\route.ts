import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

/**
 * Database Schema Fix Endpoint
 * Executes the fix-database-schema.sql script to resolve column mismatch issues
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔧 [Schema Fix] Starting database schema fix...');

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const results = {
      timestamp: new Date().toISOString(),
      operations: [] as any[],
      success: true,
      errors: [] as string[]
    };

    // 1. Add connection_status column if it doesn't exist
    try {
      console.log('🔧 Adding connection_status column...');
      const { error: connectionStatusError } = await supabase.rpc('exec_sql', {
        sql: `ALTER TABLE social_accounts 
              ADD COLUMN IF NOT EXISTS connection_status TEXT DEFAULT 'connected' 
              CHECK (connection_status IN ('connected', 'expired', 'error', 'reconnecting', 'disconnected'));`
      });
      
      if (connectionStatusError) {
        // Try alternative approach using direct SQL
        const { error: altError } = await supabase
          .from('social_accounts')
          .select('connection_status')
          .limit(1);
        
        if (altError && altError.message.includes('does not exist')) {
          results.operations.push({
            operation: 'add_connection_status_column',
            status: 'needed',
            message: 'Column does not exist, manual addition required'
          });
        } else {
          results.operations.push({
            operation: 'add_connection_status_column',
            status: 'exists',
            message: 'Column already exists'
          });
        }
      } else {
        results.operations.push({
          operation: 'add_connection_status_column',
          status: 'success',
          message: 'Column added successfully'
        });
      }
    } catch (error) {
      results.errors.push(`Connection status column: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 2. Add status column if it doesn't exist
    try {
      console.log('🔧 Adding status column...');
      const { error: statusError } = await supabase.rpc('exec_sql', {
        sql: `ALTER TABLE social_accounts 
              ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'ACTIVE' 
              CHECK (status IN ('ACTIVE', 'INACTIVE', 'EXPIRED', 'ERROR'));`
      });
      
      if (statusError) {
        const { error: altError } = await supabase
          .from('social_accounts')
          .select('status')
          .limit(1);
        
        if (altError && altError.message.includes('does not exist')) {
          results.operations.push({
            operation: 'add_status_column',
            status: 'needed',
            message: 'Column does not exist, manual addition required'
          });
        } else {
          results.operations.push({
            operation: 'add_status_column',
            status: 'exists',
            message: 'Column already exists'
          });
        }
      } else {
        results.operations.push({
          operation: 'add_status_column',
          status: 'success',
          message: 'Column added successfully'
        });
      }
    } catch (error) {
      results.errors.push(`Status column: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 3. Add other missing columns
    const missingColumns = [
      { name: 'page_id', type: 'TEXT' },
      { name: 'page_access_token', type: 'TEXT' },
      { name: 'page_name', type: 'TEXT' },
      { name: 'instagram_business_account_id', type: 'TEXT' },
      { name: 'last_validated_at', type: 'TIMESTAMPTZ' },
      { name: 'metadata', type: 'JSONB DEFAULT \'{}\'', default: '{}' },
      { name: 'permissions', type: 'JSONB DEFAULT \'[]\'', default: '[]' }
    ];

    for (const column of missingColumns) {
      try {
        console.log(`🔧 Checking column: ${column.name}`);
        const { error } = await supabase
          .from('social_accounts')
          .select(column.name)
          .limit(1);
        
        if (error && error.message.includes('does not exist')) {
          results.operations.push({
            operation: `add_${column.name}_column`,
            status: 'needed',
            message: `Column ${column.name} does not exist, manual addition required`
          });
        } else {
          results.operations.push({
            operation: `check_${column.name}_column`,
            status: 'exists',
            message: `Column ${column.name} already exists`
          });
        }
      } catch (error) {
        results.errors.push(`${column.name} column: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // 4. Check current table schema
    try {
      console.log('🔍 Checking current table schema...');
      const { data: schemaData, error: schemaError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable, column_default')
        .eq('table_name', 'social_accounts')
        .eq('table_schema', 'public');

      if (schemaError) {
        results.errors.push(`Schema check: ${schemaError.message}`);
      } else {
        results.operations.push({
          operation: 'schema_check',
          status: 'success',
          message: 'Schema retrieved successfully',
          columns: schemaData?.map(col => ({
            name: col.column_name,
            type: col.data_type,
            nullable: col.is_nullable,
            default: col.column_default
          })) || []
        });
      }
    } catch (error) {
      results.errors.push(`Schema check: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 5. Update existing records
    try {
      console.log('🔧 Updating existing records...');
      const { error: updateError } = await supabase
        .from('social_accounts')
        .update({
          connection_status: 'connected'
        })
        .is('connection_status', null);

      if (updateError) {
        results.errors.push(`Update records: ${updateError.message}`);
      } else {
        results.operations.push({
          operation: 'update_existing_records',
          status: 'success',
          message: 'Updated existing records with default connection_status'
        });
      }
    } catch (error) {
      results.errors.push(`Update records: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    if (results.errors.length > 0) {
      results.success = false;
    }

    console.log('✅ [Schema Fix] Database schema fix completed');
    
    return NextResponse.json({
      success: results.success,
      message: results.success ? 'تم إصلاح مخطط قاعدة البيانات بنجاح' : 'فشل في إصلاح مخطط قاعدة البيانات',
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ [Schema Fix] Database schema fix failed:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'فشل في إصلاح مخطط قاعدة البيانات',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'استخدم POST لتنفيذ إصلاح مخطط قاعدة البيانات',
    usage: 'POST /api/fix-database-schema',
    timestamp: new Date().toISOString()
  });
}
