import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createClient as createServiceClient } from '@supabase/supabase-js'

// Service role client for storage operations
const supabaseService = createServiceClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const folder = formData.get('folder') as string || 'uploads'

    if (!file) {
      return NextResponse.json(
        { error: 'لم يتم العثور على ملف' },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'video/mp4',
      'video/webm',
      'video/quicktime'
    ]

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'نوع الملف غير مدعوم' },
        { status: 400 }
      )
    }

    // Validate file size (50MB max)
    const maxSize = 50 * 1024 * 1024 // 50MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'حجم الملف كبير جداً (الحد الأقصى 50 ميجابايت)' },
        { status: 400 }
      )
    }

    // Generate unique filename
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const fileExtension = file.name.split('.').pop()
    const fileName = `${timestamp}_${randomString}.${fileExtension}`
    const filePath = `${folder}/${user.id}/${fileName}`

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const buffer = new Uint8Array(arrayBuffer)

    // Check if buckets exist
    console.log('🪣 Checking storage buckets...')
    const { data: buckets, error: bucketsError } = await supabaseService.storage.listBuckets()

    if (bucketsError) {
      console.error('❌ Failed to list buckets:', bucketsError)
      return NextResponse.json(
        { error: 'فشل في الوصول إلى التخزين' },
        { status: 500 }
      )
    }

    // Determine which bucket to use
    const mediaBucketExists = buckets?.some(bucket => bucket.name === 'media')
    const mediaFilesBucketExists = buckets?.some(bucket => bucket.name === 'media-files')

    console.log('📊 Bucket status:', {
      mediaBucketExists,
      mediaFilesBucketExists,
      allBuckets: buckets?.map(b => b.name).join(', ')
    })

    // Try to create media bucket if it doesn't exist
    if (!mediaBucketExists && !mediaFilesBucketExists) {
      console.log('🆕 Creating media bucket...')
      const { error: createError } = await supabaseService.storage.createBucket('media', {
        public: true,
        fileSizeLimit: 50 * 1024 * 1024, // 50MB
      })

      if (createError) {
        console.error('❌ Failed to create media bucket:', createError)
      } else {
        console.log('✅ Media bucket created successfully')
      }
    }

    // Upload to Supabase Storage - try media bucket first
    let uploadData: any = null
    let uploadError: any = null
    let bucketUsed = 'media'

    console.log('📤 Uploading to media bucket...')
    if (mediaBucketExists) {
      const result = await supabaseService.storage
        .from('media')
        .upload(filePath, buffer, {
          contentType: file.type,
          upsert: false
        })

      uploadData = result.data
      uploadError = result.error
    }

    // If media bucket upload failed or doesn't exist, try media-files bucket
    if ((uploadError || !mediaBucketExists) && mediaFilesBucketExists) {
      console.log('🔄 Trying media-files bucket...')
      bucketUsed = 'media-files'
      const result = await supabaseService.storage
        .from('media-files')
        .upload(filePath, buffer, {
          contentType: file.type,
          upsert: false
        })

      uploadData = result.data
      uploadError = result.error
    }

    if (uploadError) {
      console.error('❌ Storage upload error:', uploadError)
      return NextResponse.json(
        {
          error: 'فشل في رفع الملف إلى التخزين',
          details: uploadError.message,
          bucketStatus: { mediaBucketExists, mediaFilesBucketExists, bucketUsed }
        },
        { status: 500 }
      )
    }

    console.log('✅ Upload successful to bucket:', bucketUsed)

    // Get public URL from the bucket that was used
    console.log('🔗 Getting public URL from bucket:', bucketUsed)
    const { data: urlData } = supabaseService.storage
      .from(bucketUsed)
      .getPublicUrl(filePath)

    if (!urlData.publicUrl) {
      return NextResponse.json(
        { error: 'فشل في الحصول على رابط الملف' },
        { status: 500 }
      )
    }

    // Get file metadata
    let width: number | undefined
    let height: number | undefined
    let duration: number | undefined

    if (file.type.startsWith('image/')) {
      // For images, we could use a library like sharp to get dimensions
      // For now, we'll leave them undefined and let the frontend handle it
    }

    // Save file metadata to database
    const { data: mediaRecord, error: dbError } = await supabaseService
      .from('media_files')
      .insert({
        user_id: user.id,
        file_name: fileName,
        original_name: file.name,
        file_type: file.type,
        file_size: file.size,
        storage_path: filePath,
        public_url: urlData.publicUrl,
        bucket_name: bucketUsed,
        folder: folder,
        width,
        height,
        duration,
        metadata: {
          uploaded_at: new Date().toISOString(),
          upload_method: 'api'
        }
      })
      .select()
      .single()

    if (dbError) {
      console.error('Database insert error:', dbError)
      // Don't fail the upload if database insert fails
      // The file is already uploaded to storage
    }

    return NextResponse.json({
      success: true,
      url: urlData.publicUrl,
      path: filePath,
      fileName,
      originalName: file.name,
      fileType: file.type,
      fileSize: file.size,
      mediaRecord: mediaRecord || null
    })

  } catch (error) {
    console.error('Media upload error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ أثناء رفع الملف' },
      { status: 500 }
    )
  }
}

// Handle file deletion
export async function DELETE(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const filePath = searchParams.get('path')
    const mediaId = searchParams.get('id')

    if (!filePath && !mediaId) {
      return NextResponse.json(
        { error: 'مطلوب مسار الملف أو معرف الوسائط' },
        { status: 400 }
      )
    }

    let pathToDelete = filePath

    // If mediaId is provided, get the file path from database
    if (mediaId && !filePath) {
      const { data: mediaRecord, error: fetchError } = await supabaseService
        .from('media_files')
        .select('storage_path')
        .eq('id', mediaId)
        .eq('user_id', user.id)
        .single()

      if (fetchError || !mediaRecord) {
        return NextResponse.json(
          { error: 'لم يتم العثور على الملف' },
          { status: 404 }
        )
      }

      pathToDelete = mediaRecord.storage_path
    }

    // Delete from storage
    const { error: deleteError } = await supabaseService.storage
      .from('media')
      .remove([pathToDelete!])

    if (deleteError) {
      console.error('Storage delete error:', deleteError)
      return NextResponse.json(
        { error: 'فشل في حذف الملف من التخزين' },
        { status: 500 }
      )
    }

    // Delete from database if mediaId is provided
    if (mediaId) {
      const { error: dbDeleteError } = await supabaseService
        .from('media_files')
        .delete()
        .eq('id', mediaId)
        .eq('user_id', user.id)

      if (dbDeleteError) {
        console.error('Database delete error:', dbDeleteError)
        // Don't fail the request if database delete fails
        // The file is already deleted from storage
      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الملف بنجاح'
    })

  } catch (error) {
    console.error('Media delete error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ أثناء حذف الملف' },
      { status: 500 }
    )
  }
}
