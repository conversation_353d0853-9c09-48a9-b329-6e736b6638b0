import { NextRequest, NextResponse } from 'next/server';
import { jobProcessor } from '@/lib/queue/job-processor';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

/**
 * POST - Manually trigger job processing
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Manual job processing triggered');
    
    // Process jobs immediately
    await jobProcessor.processJobs();
    
    // Get current stats
    const stats = await jobProcessor.getStats();
    
    return NextResponse.json({
      success: true,
      message: 'Job processing completed',
      stats,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Manual job processing failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

/**
 * GET - Get job processing status and statistics
 */
export async function GET(request: NextRequest) {
  try {
    const stats = await jobProcessor.getStats();
    
    return NextResponse.json({
      success: true,
      stats,
      processor: {
        running: true, // In production, you'd track this properly
        lastCheck: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Failed to get job stats:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
