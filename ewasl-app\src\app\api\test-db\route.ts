import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

// Test database connection and schema
export async function GET(request: NextRequest) {
  console.log('🧪 Testing database connection...');
  
  try {
    const supabase = createClient();
    console.log('✅ Supabase client created');

    // Test 1: Basic connection
    console.log('🔍 Test 1: Basic connection test...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('posts')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      console.error('❌ Connection test failed:', connectionError);
      return NextResponse.json({
        success: false,
        test: 'connection',
        error: connectionError.message
      }, { status: 500 });
    }
    
    console.log('✅ Connection test passed');

    // Test 2: Check if publishing_results table exists
    console.log('🔍 Test 2: Check publishing_results table...');
    const { data: publishingResults, error: publishingError } = await supabase
      .from('publishing_results')
      .select('*')
      .limit(1);
    
    const publishingTableExists = !publishingError;
    console.log('📊 Publishing results table exists:', publishingTableExists);
    if (publishingError) {
      console.log('❌ Publishing results error:', publishingError.message);
    }

    // Test 3: Check posts table structure
    console.log('🔍 Test 3: Check posts table structure...');
    const { data: postsTest, error: postsError } = await supabase
      .from('posts')
      .select('id, content, status, created_at')
      .limit(1);
    
    if (postsError) {
      console.error('❌ Posts table test failed:', postsError);
      return NextResponse.json({
        success: false,
        test: 'posts_table',
        error: postsError.message
      }, { status: 500 });
    }
    
    console.log('✅ Posts table test passed');

    // Test 4: Check social_accounts table
    console.log('🔍 Test 4: Check social_accounts table...');
    const { data: socialAccounts, error: socialError } = await supabase
      .from('social_accounts')
      .select('id, platform, account_name')
      .limit(1);

    const socialTableExists = !socialError;
    console.log('📊 Social accounts table exists:', socialTableExists);
    if (socialError) {
      console.log('❌ Social accounts error:', socialError.message);
    }

    // Test 5: Check if post_social_accounts table exists (old table name)
    console.log('🔍 Test 5: Check post_social_accounts table...');
    const { data: postSocialAccounts, error: postSocialError } = await supabase
      .from('post_social_accounts')
      .select('*')
      .limit(1);

    const postSocialTableExists = !postSocialError;
    console.log('📊 Post social accounts table exists:', postSocialTableExists);
    if (postSocialError) {
      console.log('❌ Post social accounts error:', postSocialError.message);
    }

    return NextResponse.json({
      success: true,
      tests: {
        connection: true,
        postsTable: true,
        publishingResultsTable: publishingTableExists,
        socialAccountsTable: socialTableExists,
        postSocialAccountsTable: postSocialTableExists
      },
      errors: {
        publishingResults: publishingError?.message || null,
        socialAccounts: socialError?.message || null,
        postSocialAccounts: postSocialError?.message || null
      }
    });

  } catch (error: any) {
    console.error('❌ Database test failed:', error);
    console.error('❌ Error stack:', error.stack);
    
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
