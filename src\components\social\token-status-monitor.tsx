'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Zap,
  Settings,
  Eye,
  Calendar
} from 'lucide-react';
import { toast } from 'sonner';

interface TokenAccount {
  id: string;
  platform: string;
  accountName: string;
  connectionStatus: string;
  expiresAt?: string;
  lastValidatedAt?: string;
  isExpired: boolean;
  isExpiringSoon: boolean;
  needsRefresh: boolean;
  daysUntilExpiry?: number;
}

interface TokenStatusSummary {
  total: number;
  connected: number;
  expired: number;
  expiringSoon: number;
  needsRefresh: number;
}

interface TokenStatusMonitorProps {
  userId: string;
  className?: string;
}

export function TokenStatusMonitor({ userId, className = '' }: TokenStatusMonitorProps) {
  const [accounts, setAccounts] = useState<TokenAccount[]>([]);
  const [summary, setSummary] = useState<TokenStatusSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastChecked, setLastChecked] = useState<string>('');

  useEffect(() => {
    loadTokenStatus();
  }, [userId]);

  const loadTokenStatus = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/oauth/token-refresh?userId=${userId}&action=status`);
      const data = await response.json();

      if (data.success) {
        setAccounts(data.accounts);
        setSummary(data.summary);
        setLastChecked(data.lastChecked);
      } else {
        toast.error('فشل في تحميل حالة الرموز');
      }
    } catch (error) {
      console.error('Error loading token status:', error);
      toast.error('خطأ في تحميل حالة الرموز');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshToken = async (accountId: string, platform: string) => {
    try {
      setIsRefreshing(true);
      toast.loading('جاري تحديث الرمز...', { id: 'refresh-token' });

      const response = await fetch('/api/oauth/token-refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accountId, platform, userId }),
      });

      const data = await response.json();
      toast.dismiss('refresh-token');

      if (data.success) {
        toast.success('تم تحديث الرمز بنجاح');
        await loadTokenStatus(); // Reload status
      } else {
        if (data.shouldReconnect) {
          toast.error('يجب إعادة ربط الحساب', {
            action: {
              label: 'إعادة ربط',
              onClick: () => window.location.href = '/social'
            }
          });
        } else {
          toast.error(data.error || 'فشل في تحديث الرمز');
        }
      }
    } catch (error) {
      toast.dismiss('refresh-token');
      console.error('Error refreshing token:', error);
      toast.error('خطأ في تحديث الرمز');
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusBadge = (account: TokenAccount) => {
    if (account.isExpired) {
      return <Badge variant="destructive">منتهي الصلاحية</Badge>;
    }
    if (account.isExpiringSoon) {
      return <Badge variant="secondary">ينتهي قريباً</Badge>;
    }
    if (account.connectionStatus === 'connected') {
      return <Badge variant="default">متصل</Badge>;
    }
    return <Badge variant="outline">{account.connectionStatus}</Badge>;
  };

  const getStatusIcon = (account: TokenAccount) => {
    if (account.isExpired) {
      return <AlertTriangle className="w-4 h-4 text-red-500" />;
    }
    if (account.isExpiringSoon) {
      return <Clock className="w-4 h-4 text-yellow-500" />;
    }
    if (account.connectionStatus === 'connected') {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    return <RefreshCw className="w-4 h-4 text-gray-500" />;
  };

  const formatExpiryDate = (expiresAt?: string) => {
    if (!expiresAt) return 'غير محدد';
    
    const date = new Date(expiresAt);
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPlatformName = (platform: string) => {
    const names: Record<string, string> = {
      'FACEBOOK': 'فيسبوك',
      'INSTAGRAM': 'إنستغرام',
      'TWITTER': 'تويتر',
      'X': 'إكس',
      'LINKEDIN': 'لينكد إن',
      'TIKTOK': 'تيك توك'
    };
    return names[platform] || platform;
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            حالة رموز المصادقة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin" />
            <span className="mr-2">جاري التحميل...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            حالة رموز المصادقة
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={loadTokenStatus}
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              تحديث
            </Button>
          </div>
        </div>
        
        {lastChecked && (
          <p className="text-sm text-muted-foreground">
            آخر فحص: {new Date(lastChecked).toLocaleString('ar-SA')}
          </p>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Summary */}
        {summary && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{summary.total}</div>
              <div className="text-sm text-muted-foreground">إجمالي الحسابات</div>
            </div>
            <div className="text-center p-3 bg-green-50 dark:bg-green-950 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{summary.connected}</div>
              <div className="text-sm text-muted-foreground">متصلة</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-950 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{summary.expiringSoon}</div>
              <div className="text-sm text-muted-foreground">تنتهي قريباً</div>
            </div>
            <div className="text-center p-3 bg-red-50 dark:bg-red-950 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{summary.expired}</div>
              <div className="text-sm text-muted-foreground">منتهية</div>
            </div>
          </div>
        )}

        {/* Alerts */}
        {summary && summary.needsRefresh > 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              يوجد {summary.needsRefresh} حساب يحتاج إلى تحديث رمز المصادقة أو إعادة ربط.
            </AlertDescription>
          </Alert>
        )}

        {/* Accounts List */}
        <div className="space-y-3">
          <h4 className="font-medium">تفاصيل الحسابات</h4>
          
          {accounts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              لا توجد حسابات متصلة
            </div>
          ) : (
            accounts.map((account) => (
              <div
                key={account.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
              >
                <div className="flex items-center gap-3">
                  {getStatusIcon(account)}
                  <div>
                    <div className="font-medium">{account.accountName}</div>
                    <div className="text-sm text-muted-foreground">
                      {getPlatformName(account.platform)}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="text-right text-sm">
                    {account.expiresAt && (
                      <div className="text-muted-foreground">
                        ينتهي: {formatExpiryDate(account.expiresAt)}
                      </div>
                    )}
                    {account.daysUntilExpiry !== null && account.daysUntilExpiry !== undefined && (
                      <div className={`text-xs ${
                        account.daysUntilExpiry <= 7 ? 'text-red-600' :
                        account.daysUntilExpiry <= 30 ? 'text-yellow-600' :
                        'text-green-600'
                      }`}>
                        {account.daysUntilExpiry > 0 ? 
                          `${account.daysUntilExpiry} يوم متبقي` : 
                          'منتهي الصلاحية'
                        }
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    {getStatusBadge(account)}
                    
                    {account.needsRefresh && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => refreshToken(account.id, account.platform)}
                        disabled={isRefreshing}
                      >
                        <RefreshCw className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
                        تحديث
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
