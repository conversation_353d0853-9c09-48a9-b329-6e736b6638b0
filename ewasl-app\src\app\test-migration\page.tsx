"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';

export default function TestMigrationPage() {
  const [isRunning, setIsRunning] = useState(false);
  const [result, setResult] = useState<any>(null);

  const runMigration = async () => {
    setIsRunning(true);
    setResult(null);
    
    try {
      const response = await fetch('/api/social/migrate-enhanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      setResult(data);
      
      if (response.ok) {
        toast.success('Migration completed successfully!');
      } else {
        toast.error('Migration failed: ' + data.error);
      }
    } catch (error) {
      console.error('Migration error:', error);
      toast.error('Migration failed: ' + error);
      setResult({ error: 'Network error', details: error });
    } finally {
      setIsRunning(false);
    }
  };

  const testEnhancedAPI = async () => {
    try {
      const response = await fetch('/api/social/accounts/enhanced');
      const data = await response.json();
      
      if (response.ok) {
        toast.success('Enhanced API working!');
        console.log('Enhanced API response:', data);
      } else {
        toast.error('Enhanced API failed: ' + data.error);
        console.error('Enhanced API error:', data);
      }
    } catch (error) {
      console.error('Enhanced API error:', error);
      toast.error('Enhanced API failed: ' + error);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Social Accounts Migration Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button 
              onClick={runMigration} 
              disabled={isRunning}
              variant="default"
            >
              {isRunning ? 'Running Migration...' : 'Run Migration'}
            </Button>
            
            <Button 
              onClick={testEnhancedAPI} 
              variant="outline"
            >
              Test Enhanced API
            </Button>
          </div>
          
          {result && (
            <div className="mt-4">
              <h3 className="font-semibold mb-2">Migration Result:</h3>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
