"use client";

import React, { useState } from "react";
import { PlusCircle, MoreHorizontal, Search, BarChart3, Calendar, Users, Settings, LogOut, Bell, User, Edit, Trash2, Eye, Copy, Share } from "lucide-react";
import Link from "next/link";
import { Button, ActionButton, LogoutButton } from "@/components/ui/enhanced-button";
import { DashboardHeader } from "@/components/layout/dashboard-header";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export default function EnhancedPostsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const router = useRouter();

  // Mock data for demonstration
  const mockPosts = [
    {
      id: "1",
      content: "أهلاً بكم في منصة eWasl لإدارة وسائل التواصل الاجتماعي!",
      status: "PUBLISHED",
      publishedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2),
      platforms: ["TWITTER", "FACEBOOK"],
    },
    {
      id: "2",
      content: "نحن نعمل على تحسين المنصة لتوفير تجربة أفضل للمستخدمين.",
      status: "SCHEDULED",
      scheduledAt: new Date(Date.now() + 1000 * 60 * 60 * 24),
      platforms: ["TWITTER", "INSTAGRAM"],
    },
    {
      id: "3",
      content: "هذا منشور تجريبي لاختبار واجهة المستخدم.",
      status: "DRAFT",
      createdAt: new Date(Date.now() - 1000 * 60 * 30),
      platforms: ["FACEBOOK"],
    },
    {
      id: "4",
      content: "استخدم الذكاء الاصطناعي لإنشاء محتوى جذاب لوسائل التواصل الاجتماعي.",
      status: "FAILED",
      publishedAt: new Date(Date.now() - 1000 * 60 * 60 * 5),
      platforms: ["LINKEDIN"],
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "DRAFT": return "#6b7280";
      case "SCHEDULED": return "#3b82f6";
      case "PUBLISHED": return "#10b981";
      case "FAILED": return "#ef4444";
      default: return "#6b7280";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "DRAFT": return "مسودة";
      case "SCHEDULED": return "مجدول";
      case "PUBLISHED": return "منشور";
      case "FAILED": return "فشل";
      default: return "غير معروف";
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "TWITTER": return "𝕏";
      case "FACEBOOK": return "f";
      case "INSTAGRAM": return "📷";
      case "LINKEDIN": return "in";
      default: return "?";
    }
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return 'غير محدد';
    try {
      return date.toLocaleDateString('ar-SA');
    } catch (error) {
      return 'غير محدد';
    }
  };

  const filteredPosts = mockPosts.filter((post) => {
    const matchesSearch = post.content.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "ALL" || post.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Post action handlers
  const handleViewPost = (postId: string) => {
    router.push(`/posts/${postId}`);
  };

  const handleEditPost = (postId: string) => {
    router.push(`/posts/${postId}/edit`);
  };

  const handleDeletePost = async (postId: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("تم حذف المنشور بنجاح");
      // In real app, update the posts list
    } catch (error) {
      toast.error("فشل في حذف المنشور");
    }
  };

  const handleDuplicatePost = async (postId: string) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      toast.success("تم نسخ المنشور بنجاح");
    } catch (error) {
      toast.error("فشل في نسخ المنشور");
    }
  };

  const handleSharePost = (postId: string) => {
    const shareUrl = `${window.location.origin}/posts/${postId}`;
    navigator.clipboard.writeText(shareUrl);
    toast.success("تم نسخ رابط المنشور");
  };

  const handleRetryPost = async (postId: string) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast.success("تم إعادة نشر المنشور بنجاح");
    } catch (error) {
      toast.error("فشل في إعادة النشر");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50" dir="rtl">
      {/* Sidebar */}
      <div className="fixed top-0 right-0 bottom-0 w-72 bg-white/95 backdrop-blur-md border-l border-gray-200/50 shadow-xl z-50 flex flex-col">
        {/* Logo Section */}
        <div className="p-6 border-b border-gray-200/50">
          <Link href="/dashboard" className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
              eW
            </div>
            <div>
              <h2 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                eWasl
              </h2>
              <p className="text-xs text-gray-500">منصة إدارة المحتوى</p>
            </div>
          </Link>
        </div>

        {/* Navigation */}
        <div className="flex-1 p-4">
          <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3 px-3">
            القائمة الرئيسية
          </p>
          <div className="space-y-1">
            {[
              { icon: BarChart3, label: 'لوحة التحكم', href: '/dashboard' },
              { icon: PlusCircle, label: 'المنشورات', href: '/posts', active: true },
              { icon: Calendar, label: 'الجدولة', href: '/schedule' },
              { icon: Users, label: 'الحسابات', href: '/social' },
              { icon: BarChart3, label: 'التحليلات', href: '/analytics' },
              { icon: Settings, label: 'الإعدادات', href: '/settings' }
            ].map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className={`flex items-center h-11 px-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                  item.active 
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg' 
                    : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                }`}
              >
                <item.icon className="w-5 h-5 ml-3" />
                {item.label}
              </Link>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200/50">
          <LogoutButton className="w-full justify-start" />
        </div>
      </div>

      {/* Main Content */}
      <div className="mr-72">
        {/* Header */}
        <DashboardHeader 
          title="المنشورات" 
          subtitle="إدارة وتنظيم منشوراتك"
          icon={<PlusCircle className="w-6 h-6" />}
        />

        {/* Main Content */}
        <main className="min-h-[calc(100vh-4rem)] bg-gradient-to-br from-gray-50/30 via-white/50 to-blue-50/20 p-6">
          <div className="max-width-7xl mx-auto">
            {/* Welcome Section */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 mb-8">
              <div className="flex flex-col gap-6">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-purple-700 rounded-xl flex items-center justify-center shadow-lg">
                    <PlusCircle className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      إدارة المنشورات 📝
                    </h1>
                    <p className="text-gray-600 text-lg mt-2">
                      إدارة وتنظيم منشوراتك على جميع منصات التواصل الاجتماعي
                    </p>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-500">
                    إنشاء وجدولة ومتابعة أداء المحتوى الخاص بك
                  </p>
                  <Link href="/posts/new">
                    <ActionButton 
                      action="create"
                      icon={<PlusCircle className="w-5 h-5" />}
                      className="shadow-lg hover:shadow-xl"
                    >
                      إنشاء منشور جديد
                    </ActionButton>
                  </Link>
                </div>
              </div>
            </div>

            {/* Posts List */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/50">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-3 h-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full"></div>
                <h2 className="text-xl font-bold text-gray-900">قائمة المنشورات</h2>
              </div>
              <p className="text-sm text-gray-600 mb-6">
                جميع منشوراتك على وسائل التواصل الاجتماعي مع إمكانية البحث والتصفية
              </p>

              {/* Search and Filter */}
              <div className="flex flex-col gap-4 mb-6">
                <div className="relative max-w-96">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="بحث في المنشورات..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full h-12 pr-10 pl-4 border border-gray-200 rounded-xl text-sm focus:border-blue-500 focus:ring-0 transition-colors"
                  />
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-48 h-12 px-4 border border-gray-200 rounded-xl text-sm focus:border-blue-500 focus:ring-0 bg-white"
                >
                  <option value="ALL">جميع الحالات</option>
                  <option value="DRAFT">مسودة</option>
                  <option value="SCHEDULED">مجدول</option>
                  <option value="PUBLISHED">منشور</option>
                  <option value="FAILED">فشل</option>
                </select>
              </div>

              {/* Posts List */}
              <div className="space-y-4">
                {filteredPosts.length === 0 ? (
                  <div className="bg-gray-50 rounded-xl p-12 text-center border border-gray-200">
                    <PlusCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد منشورات</h3>
                    <p className="text-sm text-gray-600">ابدأ بإنشاء منشورك الأول</p>
                  </div>
                ) : (
                  filteredPosts.map((post) => (
                    <div key={post.id} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex-1">
                          <Link href={`/posts/${post.id}`} className="block">
                            <h3 className="text-base font-semibold text-blue-600 hover:text-blue-700 mb-2 cursor-pointer">
                              {post.content.length > 60 ? post.content.substring(0, 60) + "..." : post.content}
                            </h3>
                          </Link>
                          <p className="text-xs text-gray-500">ID: {post.id}</p>
                        </div>
                        <div className="flex items-center gap-3">
                          <span 
                            className="px-3 py-1 text-white rounded-lg text-xs font-medium"
                            style={{ backgroundColor: getStatusColor(post.status) }}
                          >
                            {getStatusLabel(post.status)}
                          </span>
                          
                          {/* More Options Dropdown */}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuItem onClick={() => handleViewPost(post.id)}>
                                <Eye className="w-4 h-4 mr-2" />
                                عرض المنشور
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditPost(post.id)}>
                                <Edit className="w-4 h-4 mr-2" />
                                تعديل
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDuplicatePost(post.id)}>
                                <Copy className="w-4 h-4 mr-2" />
                                نسخ
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleSharePost(post.id)}>
                                <Share className="w-4 h-4 mr-2" />
                                مشاركة
                              </DropdownMenuItem>
                              {post.status === "FAILED" && (
                                <DropdownMenuItem onClick={() => handleRetryPost(post.id)}>
                                  <BarChart3 className="w-4 h-4 mr-2" />
                                  إعادة المحاولة
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleDeletePost(post.id)}
                                className="text-red-600 focus:text-red-600"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                حذف
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <div className="flex gap-2">
                          {post.platforms.map((platform) => (
                            <div
                              key={platform}
                              className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-bold"
                              style={{
                                backgroundColor: platform === "TWITTER" ? "#000000" :
                                              platform === "FACEBOOK" ? "#1877f2" :
                                              platform === "INSTAGRAM" ? "#e4405f" :
                                              platform === "LINKEDIN" ? "#0077b5" : "#6b7280"
                              }}
                            >
                              {getPlatformIcon(platform)}
                            </div>
                          ))}
                        </div>
                        <div className="text-sm font-medium text-gray-900">
                          {post.status === "SCHEDULED"
                            ? formatDate(post.scheduledAt!)
                            : post.status === "PUBLISHED"
                            ? formatDate(post.publishedAt!)
                            : formatDate(post.createdAt!)}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
