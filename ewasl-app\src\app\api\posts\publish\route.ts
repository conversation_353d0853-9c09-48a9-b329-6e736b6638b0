import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// Facebook Graph API publishing
async function publishToFacebook(accessToken: string, pageId: string, content: string, mediaUrls?: string[]) {
  try {
    let postData: any = {
      message: content.replace(/<[^>]*>/g, ''), // Strip HTML tags
      access_token: accessToken
    }

    // If there are media URLs, handle them
    if (mediaUrls && mediaUrls.length > 0) {
      // For single image
      if (mediaUrls.length === 1 && mediaUrls[0].match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
        postData.url = mediaUrls[0]
        
        const response = await fetch(`https://graph.facebook.com/v18.0/${pageId}/photos`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(postData)
        })

        const result = await response.json()
        
        if (!response.ok) {
          throw new Error(result.error?.message || 'Failed to publish to Facebook')
        }

        return {
          platform_post_id: result.id,
          platform_url: `https://facebook.com/${result.id}`,
          success: true
        }
      } else {
        // For multiple media or videos, use feed endpoint
        postData.link = mediaUrls[0] // Use first media as link
      }
    }

    // Publish to Facebook Page feed
    const response = await fetch(`https://graph.facebook.com/v18.0/${pageId}/feed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(postData)
    })

    const result = await response.json()
    
    if (!response.ok) {
      throw new Error(result.error?.message || 'Failed to publish to Facebook')
    }

    return {
      platform_post_id: result.id,
      platform_url: `https://facebook.com/${result.id}`,
      success: true
    }

  } catch (error) {
    console.error('Facebook publishing error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Instagram publishing
async function publishToInstagram(accessToken: string, instagramAccountId: string, content: string, mediaUrls?: string[]) {
  try {
    if (!mediaUrls || mediaUrls.length === 0) {
      throw new Error('Instagram posts require at least one image or video')
    }

    const mediaUrl = mediaUrls[0]
    const caption = content.replace(/<[^>]*>/g, '') // Strip HTML tags

    // Step 1: Create media container
    const containerData = {
      image_url: mediaUrl,
      caption: caption,
      access_token: accessToken
    }

    const containerResponse = await fetch(
      `https://graph.facebook.com/v18.0/${instagramAccountId}/media`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(containerData)
      }
    )

    const containerResult = await containerResponse.json()
    
    if (!containerResponse.ok) {
      throw new Error(containerResult.error?.message || 'Failed to create Instagram media container')
    }

    const creationId = containerResult.id

    // Step 2: Publish the media
    const publishData = {
      creation_id: creationId,
      access_token: accessToken
    }

    const publishResponse = await fetch(
      `https://graph.facebook.com/v18.0/${instagramAccountId}/media_publish`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(publishData)
      }
    )

    const publishResult = await publishResponse.json()
    
    if (!publishResponse.ok) {
      throw new Error(publishResult.error?.message || 'Failed to publish to Instagram')
    }

    return {
      platform_post_id: publishResult.id,
      platform_url: `https://instagram.com/p/${publishResult.id}`,
      success: true
    }

  } catch (error) {
    console.error('Instagram publishing error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { postId, publishNow = false } = body

    if (!postId) {
      return NextResponse.json(
        { error: 'معرف المنشور مطلوب' },
        { status: 400 }
      )
    }

    // Get post details with associated social accounts
    const { data: post, error: postError } = await supabase
      .from('posts')
      .select(`
        *,
        post_social_accounts (
          id,
          social_account_id,
          platform_content,
          social_accounts (
            id,
            platform,
            account_id,
            account_name,
            access_token,
            metadata
          )
        )
      `)
      .eq('id', postId)
      .eq('user_id', user.id)
      .single()

    if (postError || !post) {
      return NextResponse.json(
        { error: 'لم يتم العثور على المنشور' },
        { status: 404 }
      )
    }

    // Check if post is ready for publishing
    if (!publishNow && post.status !== 'SCHEDULED') {
      return NextResponse.json(
        { error: 'المنشور غير مجدول للنشر' },
        { status: 400 }
      )
    }

    const publishingResults = []
    let successCount = 0
    let failureCount = 0

    // Publish to each connected platform
    for (const association of post.post_social_accounts) {
      const account = association.social_accounts
      const content = association.platform_content || post.content
      
      try {
        let result = { success: false, error: 'Platform not supported' }

        if (account.platform === 'FACEBOOK') {
          result = await publishToFacebook(
            account.access_token,
            account.account_id,
            content,
            post.media_urls
          )
        } else if (account.platform === 'INSTAGRAM') {
          // Get Instagram account ID from metadata
          const instagramAccountId = account.metadata?.instagramAccountId || account.account_id
          result = await publishToInstagram(
            account.access_token,
            instagramAccountId,
            content,
            post.media_urls
          )
        }

        // Update post-social-account association with result
        const updateData: any = {
          updated_at: new Date().toISOString()
        }

        if (result.success) {
          updateData.status = 'PUBLISHED'
          updateData.platform_post_id = result.platform_post_id
          updateData.platform_url = result.platform_url
          updateData.published_at = new Date().toISOString()
          successCount++
        } else {
          updateData.status = 'FAILED'
          updateData.error_message = result.error
          failureCount++
        }

        await supabase
          .from('post_social_accounts')
          .update(updateData)
          .eq('id', association.id)

        publishingResults.push({
          platform: account.platform,
          account_name: account.account_name,
          success: result.success,
          error: result.error,
          platform_post_id: result.platform_post_id,
          platform_url: result.platform_url
        })

      } catch (error) {
        console.error(`Error publishing to ${account.platform}:`, error)
        
        await supabase
          .from('post_social_accounts')
          .update({
            status: 'FAILED',
            error_message: error instanceof Error ? error.message : 'Unknown error',
            updated_at: new Date().toISOString()
          })
          .eq('id', association.id)

        publishingResults.push({
          platform: account.platform,
          account_name: account.account_name,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })

        failureCount++
      }
    }

    // Update main post status
    const finalStatus = successCount > 0 ? 'PUBLISHED' : 'FAILED'
    await supabase
      .from('posts')
      .update({
        status: finalStatus,
        published_at: successCount > 0 ? new Date().toISOString() : null,
        publishing_results: publishingResults,
        updated_at: new Date().toISOString()
      })
      .eq('id', postId)

    // Remove from scheduled queue if it was scheduled
    if (post.status === 'SCHEDULED') {
      await supabase
        .from('scheduled_posts_queue')
        .update({
          status: 'completed',
          processed_at: new Date().toISOString()
        })
        .eq('post_id', postId)
    }

    return NextResponse.json({
      success: true,
      results: publishingResults,
      summary: {
        total: publishingResults.length,
        successful: successCount,
        failed: failureCount
      },
      message: successCount > 0 
        ? `تم نشر المنشور بنجاح على ${successCount} من ${publishingResults.length} منصات`
        : 'فشل في نشر المنشور على جميع المنصات'
    })

  } catch (error) {
    console.error('Publishing error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ أثناء نشر المنشور' },
      { status: 500 }
    )
  }
}
