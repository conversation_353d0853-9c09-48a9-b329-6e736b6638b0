"use client";

import React, { useState, useEffect } from 'react';
import { BulkOperationsManager } from '@/components/bulk/bulk-operations-manager';
import { BatchPostCreator } from '@/components/bulk/batch-post-creator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Zap,
  ArrowLeft,
  Sparkles,
  CheckSquare,
  Plus,
  BarChart3,
  Clock,
  Users,
  Target,
  Globe,
  Info,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { useRTL } from '@/lib/i18n/arabic-rtl-optimizer';
import { toast } from 'sonner';

interface BulkOperationsPageProps {
  searchParams?: {
    lang?: string;
  };
}

export default function BulkOperationsPage({ searchParams }: BulkOperationsPageProps) {
  const [language, setLanguage] = useState<'ar' | 'en'>('ar');
  const [posts, setPosts] = useState<any[]>([]);
  const router = useRouter();
  const rtl = useRTL(language);

  useEffect(() => {
    // Set language from search params
    if (searchParams?.lang === 'en') {
      setLanguage('en');
    }

    // Apply RTL to document
    document.documentElement.dir = rtl.direction;
    document.documentElement.lang = language;

    // Generate demo posts
    setPosts(generateDemoPosts());
  }, [searchParams, language, rtl.direction]);

  const handleLanguageChange = (newLanguage: 'ar' | 'en') => {
    setLanguage(newLanguage);
    
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('lang', newLanguage);
    window.history.replaceState({}, '', url.toString());
  };

  // Bulk operation features
  const bulkFeatures = [
    {
      icon: CheckSquare,
      title: language === 'ar' ? 'تحديد متعدد' : 'Multi-Selection',
      description: language === 'ar' 
        ? 'حدد عدة منشورات وطبق العمليات عليها دفعة واحدة'
        : 'Select multiple posts and apply operations to them at once',
      color: 'blue'
    },
    {
      icon: Zap,
      title: language === 'ar' ? 'عمليات مجمعة' : 'Bulk Operations',
      description: language === 'ar'
        ? 'نشر، جدولة، حذف، أو تحديث عدة منشورات بنقرة واحدة'
        : 'Publish, schedule, delete, or update multiple posts with one click',
      color: 'green'
    },
    {
      icon: Plus,
      title: language === 'ar' ? 'إنشاء مجمع' : 'Batch Creation',
      description: language === 'ar'
        ? 'أنشئ عدة منشورات في وقت واحد مع إعدادات مشتركة'
        : 'Create multiple posts at once with shared settings',
      color: 'purple'
    },
    {
      icon: Clock,
      title: language === 'ar' ? 'جدولة ذكية' : 'Smart Scheduling',
      description: language === 'ar'
        ? 'جدولة تلقائية مع فترات زمنية محددة'
        : 'Automatic scheduling with specified time intervals',
      color: 'orange'
    },
    {
      icon: BarChart3,
      title: language === 'ar' ? 'تتبع التقدم' : 'Progress Tracking',
      description: language === 'ar'
        ? 'راقب تقدم العمليات المجمعة في الوقت الفعلي'
        : 'Monitor bulk operation progress in real-time',
      color: 'red'
    },
    {
      icon: Target,
      title: language === 'ar' ? 'تصفية متقدمة' : 'Advanced Filtering',
      description: language === 'ar'
        ? 'صفي المنشورات حسب الحالة والمنصة والمحتوى'
        : 'Filter posts by status, platform, and content',
      color: 'indigo'
    }
  ];

  // Key benefits
  const keyBenefits = [
    {
      metric: language === 'ar' ? 'توفير الوقت' : 'Time Saved',
      value: '80%',
      improvement: 'vs manual'
    },
    {
      metric: language === 'ar' ? 'كفاءة العمليات' : 'Operation Efficiency',
      value: '10x',
      improvement: 'faster'
    },
    {
      metric: language === 'ar' ? 'دقة التنفيذ' : 'Execution Accuracy',
      value: '99%',
      improvement: 'success rate'
    },
    {
      metric: language === 'ar' ? 'إدارة المحتوى' : 'Content Management',
      value: '500+',
      improvement: 'posts/hour'
    }
  ];

  // Generate demo posts
  const generateDemoPosts = () => {
    const statuses = ['draft', 'scheduled', 'published', 'failed'];
    const platforms = ['instagram', 'facebook', 'twitter', 'linkedin'];
    const priorities = ['low', 'medium', 'high'];

    return Array.from({ length: 25 }, (_, i) => ({
      id: `post-${i}`,
      content: language === 'ar' 
        ? `منشور تجريبي رقم ${i + 1} - محتوى تجريبي للاختبار والعرض`
        : `Sample post ${i + 1} - Demo content for testing and demonstration`,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      platforms: platforms.slice(0, Math.floor(Math.random() * 3) + 1),
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      author: {
        name: language === 'ar' ? `المؤلف ${i + 1}` : `Author ${i + 1}`,
        avatar: `https://via.placeholder.com/32x32/3b82f6/ffffff?text=A${i + 1}`
      },
      tags: [`tag${i}`, `category${Math.floor(i / 5)}`],
      analytics: Math.random() > 0.5 ? {
        views: Math.floor(Math.random() * 10000),
        likes: Math.floor(Math.random() * 1000),
        comments: Math.floor(Math.random() * 100),
        shares: Math.floor(Math.random() * 50),
        engagement: Math.random() * 10
      } : undefined
    }));
  };

  // Handle bulk operations
  const handleBulkOperation = async (operation: any, selectedPosts: any[]) => {
    console.log('Bulk operation:', operation.type, 'on', selectedPosts.length, 'posts');
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    toast.success(
      language === 'ar' 
        ? `تم تطبيق العملية على ${selectedPosts.length} منشور`
        : `Operation applied to ${selectedPosts.length} posts`
    );
  };

  // Handle batch creation
  const handleBatchCreate = async (batchPosts: any[]) => {
    console.log('Creating batch of', batchPosts.length, 'posts');
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Add to posts list
    setPosts(prev => [...prev, ...batchPosts.map(post => ({
      ...post,
      id: `batch-${Date.now()}-${Math.random()}`,
      createdAt: new Date(),
      author: {
        name: language === 'ar' ? 'المستخدم الحالي' : 'Current User',
        avatar: 'https://via.placeholder.com/32x32/10b981/ffffff?text=U'
      }
    }))]);
    
    toast.success(
      language === 'ar' 
        ? `تم إنشاء ${batchPosts.length} منشور بنجاح`
        : `Successfully created ${batchPosts.length} posts`
    );
  };

  return (
    <div className={cn(
      "min-h-screen bg-gray-50",
      language === 'ar' ? "rtl" : "ltr"
    )} dir={rtl.direction}>
      <div className="container mx-auto px-4 py-8 space-y-8">
        {/* Header */}
        <div className={rtl.cn(
          "flex items-center justify-between",
          rtl.flex()
        )}>
          <div className={rtl.textAlign()}>
            <div className={rtl.cn(
              "flex items-center gap-3 mb-2",
              rtl.flex()
            )}>
              <h1 className="text-4xl font-bold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                {language === 'ar' ? 'العمليات المجمعة والنشر المتعدد' : 'Bulk Operations & Batch Posting'}
              </h1>
              <Badge variant="secondary" className="text-xs">
                <Sparkles className="w-3 h-3 mr-1" />
                {language === 'ar' ? 'متقدم' : 'Advanced'}
              </Badge>
            </div>
            <p className="text-xl text-gray-600" style={{ fontFamily: rtl.getFontFamily('primary') }}>
              {language === 'ar'
                ? 'إدارة متقدمة للمنشورات المتعددة مع عمليات مجمعة وإنشاء متعدد'
                : 'Advanced management for multiple posts with bulk operations and batch creation'
              }
            </p>
          </div>
          
          <div className={rtl.cn(
            "flex items-center gap-4",
            rtl.flex()
          )}>
            <Button
              variant="outline"
              onClick={() => handleLanguageChange(language === 'ar' ? 'en' : 'ar')}
              className={rtl.cn(
                "flex items-center gap-2",
                rtl.flex()
              )}
            >
              <Globe className="w-4 h-4" />
              {language === 'ar' ? 'English' : 'العربية'}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className={rtl.cn(
                "flex items-center gap-2",
                rtl.flex()
              )}
            >
              <ArrowLeft className={cn("w-4 h-4", language === 'ar' && "rotate-180")} />
              {language === 'ar' ? 'العودة للوحة التحكم' : 'Back to Dashboard'}
            </Button>
          </div>
        </div>

        {/* Bulk Features Alert */}
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertTitle className={rtl.cn(
            "text-green-900",
            rtl.textAlign()
          )}>
            {language === 'ar' ? 'ميزات العمليات المجمعة نشطة' : 'Bulk Operations Features Active'}
          </AlertTitle>
          <AlertDescription className={rtl.cn(
            "text-green-800",
            rtl.textAlign()
          )}>
            {language === 'ar'
              ? 'استخدم التحديد المتعدد والعمليات المجمعة لإدارة المنشورات بكفاءة، أو أنشئ عدة منشورات دفعة واحدة.'
              : 'Use multi-selection and bulk operations to manage posts efficiently, or create multiple posts at once.'
            }
          </AlertDescription>
        </Alert>

        {/* Key Benefits */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {keyBenefits.map((benefit, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6 text-center">
                <div className="space-y-2">
                  <p className="text-3xl font-bold text-blue-600">{benefit.value}</p>
                  <p className="text-sm font-medium text-gray-900">{benefit.metric}</p>
                  <p className="text-xs text-green-600">{benefit.improvement}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bulk Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {bulkFeatures.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className={rtl.cn(
                    "flex items-center gap-3",
                    rtl.flex()
                  )}>
                    <div className={`w-10 h-10 bg-${feature.color}-100 rounded-lg flex items-center justify-center`}>
                      <Icon className={`w-5 h-5 text-${feature.color}-600`} />
                    </div>
                    <CardTitle className="text-lg" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                      {feature.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className={rtl.textAlign()} style={{ fontFamily: rtl.getFontFamily('primary') }}>
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Main Content */}
        <Tabs defaultValue="manage" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="manage">
              {language === 'ar' ? 'إدارة المنشورات' : 'Manage Posts'}
            </TabsTrigger>
            <TabsTrigger value="create">
              {language === 'ar' ? 'إنشاء مجمع' : 'Batch Create'}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="manage">
            <BulkOperationsManager
              language={language}
              posts={posts}
              onPostsUpdate={setPosts}
              onBulkOperation={handleBulkOperation}
            />
          </TabsContent>

          <TabsContent value="create">
            <BatchPostCreator
              language={language}
              onBatchCreate={handleBatchCreate}
              onSaveDraft={(posts) => {
                console.log('Saving draft:', posts);
                toast.success(language === 'ar' ? 'تم حفظ المسودة' : 'Draft saved');
              }}
            />
          </TabsContent>
        </Tabs>

        {/* Footer */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
          <CardContent className="p-6">
            <div className={rtl.cn(
              "flex items-center justify-center gap-4",
              rtl.flex()
            )}>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <div className={rtl.textAlign()}>
                <h4 className="font-semibold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
                  {language === 'ar' ? 'العمليات المجمعة جاهزة' : 'Bulk Operations Ready'}
                </h4>
                <p className="text-sm text-gray-600" style={{ fontFamily: rtl.getFontFamily('primary') }}>
                  {language === 'ar'
                    ? 'إدارة فعالة للمحتوى مع عمليات مجمعة وإنشاء متعدد للمنشورات'
                    : 'Efficient content management with bulk operations and batch post creation'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
