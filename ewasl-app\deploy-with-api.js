#!/usr/bin/env node

/**
 * eWasl Social Media Platform - Vercel API Deployment Script
 * 
 * This script uses the Vercel API to deploy the eWasl application to production
 * to resolve the 500 Internal Server Error related to Facebook/Instagram API localhost restrictions.
 */

const https = require('https');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Vercel API Configuration
const VERCEL_TOKEN = '************************';
const TEAM_ID = 'ewasls-projects';
const PROJECT_NAME = 'ewasl-social-platform';

console.log('🚀 eWasl Social Media Platform - Vercel API Deployment');
console.log('=' .repeat(60));

// Environment variables for production
const environmentVariables = [
  { key: 'NODE_ENV', value: 'production', type: 'plain' },
  { key: 'DATABASE_URL', value: 'postgresql://postgres:<EMAIL>:5432/postgres', type: 'secret' },
  { key: 'NEXT_PUBLIC_SUPABASE_URL', value: 'https://nnxfzhxqzmriggulsudr.supabase.co', type: 'plain' },
  { key: 'NEXT_PUBLIC_SUPABASE_ANON_KEY', value: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjg3NjgsImV4cCI6MjA2NzY0NDc2OH0.1qBlXLOqGtJX_LFRC7XihOpwCtD1U1I2_jdyJ8sq_8w', type: 'secret' },
  { key: 'SUPABASE_SERVICE_ROLE_KEY', value: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ueGZ6aHhxem1yaWdndWxzdWRyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjA2ODc2OCwiZXhwIjoyMDY3NjQ0NzY4fQ.q4cHohdkLxlB41jp1k-5w4QLWX4R93ugtfEvLnqSJNM', type: 'secret' },
  { key: 'NEXTAUTH_SECRET', value: 'ewasl_production_secret_2024_secure_key_for_nextauth', type: 'secret' },
  { key: 'FACEBOOK_APP_ID', value: '1366325774493759', type: 'secret' },
  { key: 'FACEBOOK_APP_SECRET', value: '********************************', type: 'secret' },
  { key: 'FACEBOOK_BUSINESS_ID', value: '1479865455689755', type: 'secret' },
  { key: 'INSTAGRAM_APP_ID', value: '1366325774493759', type: 'secret' },
  { key: 'INSTAGRAM_APP_SECRET', value: '********************************', type: 'secret' },
  { key: 'SECURITY_ENABLE_RATE_LIMIT', value: 'true', type: 'plain' },
  { key: 'SECURITY_RATE_LIMIT_MAX', value: '100', type: 'plain' },
  { key: 'CRON_SECRET', value: 'ewasl_cron_secret_2024_secure', type: 'secret' }
];

function makeAPIRequest(method, endpoint, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.vercel.com',
      path: endpoint,
      method: method,
      headers: {
        'Authorization': `Bearer ${VERCEL_TOKEN}`,
        'Content-Type': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(parsedData);
          } else {
            reject(new Error(`API Error ${res.statusCode}: ${parsedData.error?.message || responseData}`));
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${responseData}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function checkExistingProject() {
  console.log('\n🔍 Checking for existing project...');
  
  try {
    const projects = await makeAPIRequest('GET', `/v9/projects?teamId=${TEAM_ID}`);
    const existingProject = projects.projects?.find(p => p.name === PROJECT_NAME);
    
    if (existingProject) {
      console.log(`✅ Found existing project: ${existingProject.name}`);
      return existingProject;
    } else {
      console.log('📝 No existing project found, will create new one');
      return null;
    }
  } catch (error) {
    console.log('⚠️  Could not check existing projects:', error.message);
    return null;
  }
}

async function createProject() {
  console.log('\n📝 Creating new Vercel project...');
  
  const projectData = {
    name: PROJECT_NAME,
    framework: 'nextjs',
    buildCommand: 'npm run build',
    outputDirectory: '.next',
    installCommand: 'npm ci',
    devCommand: 'npm run dev'
  };

  try {
    const project = await makeAPIRequest('POST', `/v10/projects?teamId=${TEAM_ID}`, projectData);
    console.log(`✅ Project created successfully: ${project.name}`);
    return project;
  } catch (error) {
    console.error('❌ Failed to create project:', error.message);
    throw error;
  }
}

async function setEnvironmentVariables(projectId) {
  console.log('\n⚙️  Setting environment variables...');
  
  for (const envVar of environmentVariables) {
    try {
      const envData = {
        key: envVar.key,
        value: envVar.value,
        type: envVar.type,
        target: ['production', 'preview']
      };
      
      await makeAPIRequest('POST', `/v10/projects/${projectId}/env?teamId=${TEAM_ID}`, envData);
      console.log(`✅ Set ${envVar.key}`);
    } catch (error) {
      console.log(`⚠️  Failed to set ${envVar.key}: ${error.message}`);
    }
  }
}

async function createDeployment(projectId) {
  console.log('\n🚀 Creating deployment...');
  
  // Create a simple deployment using Git
  const deploymentData = {
    name: PROJECT_NAME,
    project: projectId,
    target: 'production',
    gitSource: {
      type: 'github',
      repo: 'ewasl-app', // This should be updated to match your actual GitHub repo
      ref: 'main'
    }
  };

  try {
    const deployment = await makeAPIRequest('POST', `/v13/deployments?teamId=${TEAM_ID}`, deploymentData);
    console.log(`✅ Deployment created: ${deployment.url}`);
    return deployment;
  } catch (error) {
    console.error('❌ Failed to create deployment:', error.message);
    throw error;
  }
}

async function updateEnvironmentVariablesWithURL(projectId, deploymentUrl) {
  console.log('\n🔧 Updating environment variables with deployment URL...');
  
  const urlBasedEnvVars = [
    { key: 'NEXT_PUBLIC_APP_URL', value: deploymentUrl },
    { key: 'NEXTAUTH_URL', value: deploymentUrl },
    { key: 'OAUTH_REDIRECT_URL', value: `${deploymentUrl}/api/auth/callback` },
    { key: 'SECURITY_ALLOWED_ORIGINS', value: deploymentUrl }
  ];

  for (const envVar of urlBasedEnvVars) {
    try {
      const envData = {
        key: envVar.key,
        value: envVar.value,
        type: 'plain',
        target: ['production', 'preview']
      };
      
      await makeAPIRequest('POST', `/v10/projects/${projectId}/env?teamId=${TEAM_ID}`, envData);
      console.log(`✅ Updated ${envVar.key} = ${envVar.value}`);
    } catch (error) {
      console.log(`⚠️  Failed to update ${envVar.key}: ${error.message}`);
    }
  }
}

function displayPostDeploymentInstructions(deploymentUrl) {
  console.log('\n🎯 Post-Deployment Instructions:');
  console.log('=' .repeat(60));
  
  console.log('\n🔧 Facebook App Configuration Update:');
  console.log('1. Go to https://developers.facebook.com/apps/1366325774493759/');
  console.log('2. Update OAuth Redirect URIs:');
  console.log(`   - Add: ${deploymentUrl}/api/auth/callback/facebook`);
  console.log(`   - Add: ${deploymentUrl}/api/auth/callback`);
  console.log('3. Update App Domains:');
  console.log(`   - Add: ${deploymentUrl.replace('https://', '')}`);
  
  console.log('\n🧪 Testing Instructions:');
  console.log(`1. Visit: ${deploymentUrl}`);
  console.log(`2. Test post creation: ${deploymentUrl}/posts/new`);
  console.log(`3. Use debug tools: ${deploymentUrl}/debug-post`);
  console.log('4. Verify Facebook/Instagram publishing works');
  
  console.log('\n✅ Expected Results:');
  console.log('- No more 500 Internal Server Errors');
  console.log('- Facebook/Instagram APIs work with production domain');
  console.log('- OAuth flows complete successfully');
  console.log('- Better server-side error logging');
}

async function main() {
  try {
    console.log('Starting eWasl deployment to Vercel...\n');
    
    // Step 1: Check for existing project
    let project = await checkExistingProject();
    
    // Step 2: Create project if it doesn't exist
    if (!project) {
      project = await createProject();
    }
    
    // Step 3: Set environment variables
    await setEnvironmentVariables(project.id);
    
    // Step 4: Create deployment (this might fail if no Git repo is connected)
    let deploymentUrl = null;
    try {
      const deployment = await createDeployment(project.id);
      deploymentUrl = `https://${deployment.url}`;
    } catch (error) {
      console.log('\n⚠️  Automatic deployment failed. Manual deployment required.');
      console.log('This is normal if the GitHub repository is not connected yet.');
      
      // Use a placeholder URL for now
      deploymentUrl = `https://${PROJECT_NAME}.vercel.app`;
    }
    
    // Step 5: Update environment variables with deployment URL
    await updateEnvironmentVariablesWithURL(project.id, deploymentUrl);
    
    // Step 6: Display instructions
    displayPostDeploymentInstructions(deploymentUrl);
    
    console.log('\n🎉 Deployment setup completed!');
    console.log('\n📋 Next Steps:');
    console.log('1. Connect your GitHub repository in Vercel Dashboard');
    console.log('2. Trigger a deployment');
    console.log('3. Update Facebook app settings');
    console.log('4. Test the application');
    
  } catch (error) {
    console.error('\n❌ Deployment failed:', error.message);
    console.log('\n🔧 Manual Setup Required:');
    console.log('1. Go to https://vercel.com/ewasls-projects');
    console.log('2. Click "Add New..." → "Project"');
    console.log('3. Import your GitHub repository');
    console.log('4. Set environment variables from PRODUCTION_DEPLOYMENT_GUIDE.md');
    console.log('5. Deploy the project');
    process.exit(1);
  }
}

// Run the deployment
if (require.main === module) {
  main();
}

module.exports = { main };
