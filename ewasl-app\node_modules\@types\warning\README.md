# Installation
> `npm install --save @types/warning`

# Summary
This package contains type definitions for warning (https://github.com/BerkeleyTrue/warning).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/warning.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/warning/index.d.ts)
````ts
declare const warning: (condition: any, format?: string, ...extra: any[]) => void;
export = warning;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 15:11:36 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON>](https://github.com/cvle).
