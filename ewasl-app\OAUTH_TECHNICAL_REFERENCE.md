# OAuth Technical Reference - eWasl Platform
**Last Updated**: January 14, 2025  
**Status**: Production Ready ✅

## 📋 Quick Reference

### Facebook App Configuration
- **App ID**: `****************`
- **App Secret**: `[ENVIRONMENT_VARIABLE]`
- **Business ID**: `****************`
- **App Type**: Business
- **Status**: Live (Production)

### Production URLs
- **App Domain**: `app.ewasl.com`
- **Facebook Callback**: `https://app.ewasl.com/api/oauth/facebook/callback`
- **Instagram Callback**: `https://app.ewasl.com/api/oauth/instagram/callback`
- **Social Accounts Page**: `https://app.ewasl.com/social`

## 🔧 OAuth URL Construction

### Facebook OAuth URL
```
https://www.facebook.com/v18.0/dialog/oauth
?client_id=****************
&redirect_uri=https://app.ewasl.com/api/oauth/facebook/callback
&scope=pages_show_list,pages_read_engagement,pages_manage_posts,pages_read_user_content,instagram_basic,instagram_content_publish,business_management
&response_type=code
&state={secure_random_state}
```

### Instagram OAuth URL
```
https://www.facebook.com/v18.0/dialog/oauth
?client_id=****************
&redirect_uri=https://app.ewasl.com/api/oauth/instagram/callback
&scope=instagram_basic,instagram_content_publish,pages_show_list,pages_read_engagement,business_management
&response_type=code
&state={secure_random_state}
```

## 🔐 Valid OAuth Scopes

### Facebook Scopes
| Scope | Purpose | Status |
|-------|---------|--------|
| `pages_show_list` | List user's Facebook pages | ✅ Valid |
| `pages_read_engagement` | Read page engagement metrics | ✅ Valid |
| `pages_manage_posts` | Create/edit/delete page posts | ✅ Valid |
| `pages_read_user_content` | Read user-generated content | ✅ Valid |
| `business_management` | Manage business assets | ✅ Valid |

### Instagram Scopes
| Scope | Purpose | Status |
|-------|---------|--------|
| `instagram_basic` | Basic Instagram account access | ✅ Valid |
| `instagram_content_publish` | Publish content to Instagram | ✅ Valid |
| `instagram_graph_user_profile` | Access user profile | ❌ Invalid for OAuth |
| `instagram_graph_user_media` | Access user media | ❌ Invalid for OAuth |

## 🔄 OAuth Flow Sequence

### 1. Initiation
```javascript
// Frontend initiates OAuth
const response = await fetch('/api/oauth/facebook/auth');
const { authUrl, state } = await response.json();
window.location.href = authUrl;
```

### 2. User Authorization
- User redirected to Facebook OAuth dialog
- User grants permissions
- Facebook redirects back with authorization code

### 3. Callback Processing
```
GET /api/oauth/facebook/callback?code={auth_code}&state={state_token}
```

### 4. Token Exchange
```javascript
// Backend exchanges code for access token
const tokenResponse = await fetch('https://graph.facebook.com/v18.0/oauth/access_token', {
  method: 'POST',
  body: new URLSearchParams({
    client_id: process.env.FACEBOOK_APP_ID,
    client_secret: process.env.FACEBOOK_APP_SECRET,
    redirect_uri: callbackUrl,
    code: authorizationCode
  })
});
```

## 🗄️ Database Schema

### oauth_states Table
```sql
CREATE TABLE oauth_states (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  platform TEXT NOT NULL,
  state_token TEXT NOT NULL UNIQUE,
  redirect_uri TEXT NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### social_accounts Table
```sql
CREATE TABLE social_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  platform TEXT NOT NULL,
  platform_user_id TEXT NOT NULL,
  username TEXT,
  display_name TEXT,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  token_expires_at TIMESTAMP WITH TIME ZONE,
  account_type TEXT DEFAULT 'personal',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🚨 Error Handling

### Common Error Responses
| Error | Cause | Solution |
|-------|-------|----------|
| `invalid_state` | State token mismatch/expired | Regenerate OAuth flow |
| `access_denied` | User denied permissions | User must re-authorize |
| `invalid_client` | Wrong App ID/Secret | Check environment variables |
| `invalid_redirect_uri` | URI not in app settings | Add URI to Facebook app |

### Error Response Format
```json
{
  "error": "invalid_state",
  "error_description": "The state parameter is invalid or expired",
  "platform": "facebook"
}
```

## 🔧 Environment Variables

```bash
# Facebook OAuth Configuration
FACEBOOK_APP_ID=****************
FACEBOOK_APP_SECRET=[SECRET_VALUE]
FACEBOOK_BUSINESS_ID=****************

# Application URLs
NEXT_PUBLIC_APP_URL=https://app.ewasl.com
FACEBOOK_REDIRECT_URI=https://app.ewasl.com/api/oauth/facebook/callback
INSTAGRAM_REDIRECT_URI=https://app.ewasl.com/api/oauth/instagram/callback
```

## 📊 Testing Checklist

### Pre-Production Testing
- [ ] OAuth URL generation
- [ ] State parameter validation
- [ ] Token exchange process
- [ ] Database storage
- [ ] Error handling
- [ ] Security validation

### Production Verification
- [x] Facebook OAuth flow end-to-end
- [x] Instagram OAuth flow end-to-end
- [x] Callback URL processing
- [x] State validation security
- [x] Error handling
- [x] Platform detection

## 🔗 Useful Links

- **Facebook App Dashboard**: https://developers.facebook.com/apps/****************/
- **Business Manager**: https://business.facebook.com/settings/accounts/****************
- **Graph API Explorer**: https://developers.facebook.com/tools/explorer/
- **OAuth Documentation**: https://developers.facebook.com/docs/facebook-login/guides/advanced/manual-flow

---
*This reference covers the complete OAuth implementation for Facebook and Instagram integration in the eWasl platform.*
