#!/usr/bin/env node

/**
 * eWasl Platform - Performance Validation Suite
 * 
 * Tests performance metrics, loading times, and optimization
 * to ensure production-ready performance.
 */

const https = require('https');
const http = require('http');
const { performance } = require('perf_hooks');

// Configuration
const config = {
  baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://app.ewasl.com',
  timeout: 30000,
  samples: 3,
  thresholds: {
    pageLoad: 3000,      // 3 seconds
    apiResponse: 1000,   // 1 second
    ttfb: 800,          // Time to first byte
    largeAsset: 5000,   // Large assets (5MB)
  }
};

// Color codes
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Performance results
const performanceResults = {
  tests: [],
  metrics: {},
  recommendations: []
};

/**
 * Make timed HTTP request
 */
function makeTimedRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const startTime = performance.now();
    const isHttps = url.startsWith('https:');
    const requestModule = isHttps ? https : http;
    
    const req = requestModule.get(url, options, (res) => {
      const ttfb = performance.now() - startTime;
      let data = '';
      let contentLength = 0;
      
      res.on('data', (chunk) => {
        data += chunk;
        contentLength += chunk.length;
      });
      
      res.on('end', () => {
        const totalTime = performance.now() - startTime;
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data,
          contentLength,
          ttfb,
          totalTime,
          url
        });
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`Request timeout after ${config.timeout}ms`));
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(config.timeout);
  });
}

/**
 * Log performance test result
 */
function logPerformanceTest(name, value, threshold, unit = 'ms', higherIsBetter = false) {
  const passed = higherIsBetter ? value >= threshold : value <= threshold;
  const status = passed ? '✅ PASS' : '❌ FAIL';
  const color = passed ? colors.green : colors.red;
  
  console.log(`${color}${status}${colors.reset} ${name}: ${value}${unit} (threshold: ${threshold}${unit})`);
  
  performanceResults.tests.push({
    name,
    value,
    threshold,
    unit,
    passed
  });
  
  if (!passed) {
    performanceResults.recommendations.push(`Optimize ${name} - current: ${value}${unit}, target: <${threshold}${unit}`);
  }
}

/**
 * Test 1: Page Load Performance
 */
async function testPageLoadPerformance() {
  console.log(`\n${colors.bold}⚡ Testing Page Load Performance${colors.reset}`);
  
  const pages = [
    { path: '/', name: 'Homepage' },
    { path: '/auth/signin', name: 'Sign In' },
    { path: '/dashboard', name: 'Dashboard' },
    { path: '/social', name: 'Social Media' },
    { path: '/posts', name: 'Posts' }
  ];
  
  for (const page of pages) {
    try {
      const samples = [];
      
      // Take multiple samples
      for (let i = 0; i < config.samples; i++) {
        const response = await makeTimedRequest(`${config.baseUrl}${page.path}`);
        samples.push({
          ttfb: response.ttfb,
          totalTime: response.totalTime,
          contentLength: response.contentLength
        });
        
        // Wait between samples
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      // Calculate averages
      const avgTtfb = samples.reduce((sum, s) => sum + s.ttfb, 0) / samples.length;
      const avgTotalTime = samples.reduce((sum, s) => sum + s.totalTime, 0) / samples.length;
      const avgContentLength = samples.reduce((sum, s) => sum + s.contentLength, 0) / samples.length;
      
      logPerformanceTest(`${page.name} - TTFB`, Math.round(avgTtfb), config.thresholds.ttfb);
      logPerformanceTest(`${page.name} - Total Load`, Math.round(avgTotalTime), config.thresholds.pageLoad);
      
      // Store metrics
      performanceResults.metrics[`${page.name.toLowerCase()}_ttfb`] = avgTtfb;
      performanceResults.metrics[`${page.name.toLowerCase()}_load`] = avgTotalTime;
      performanceResults.metrics[`${page.name.toLowerCase()}_size`] = avgContentLength;
      
    } catch (error) {
      console.log(`${colors.red}❌ FAIL${colors.reset} ${page.name} - Error: ${error.message}`);
    }
  }
}

/**
 * Test 2: API Response Performance
 */
async function testAPIPerformance() {
  console.log(`\n${colors.bold}🔌 Testing API Performance${colors.reset}`);
  
  const apis = [
    { path: '/api/health', name: 'Health Check' },
    { path: '/api/system/health', name: 'System Health' },
    { path: '/api/social/accounts', name: 'Social Accounts' },
    { path: '/api/posts', name: 'Posts API' },
    { path: '/api/analytics/overview', name: 'Analytics' }
  ];
  
  for (const api of apis) {
    try {
      const samples = [];
      
      for (let i = 0; i < config.samples; i++) {
        const response = await makeTimedRequest(`${config.baseUrl}${api.path}`);
        samples.push(response.totalTime);
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      const avgResponseTime = samples.reduce((sum, time) => sum + time, 0) / samples.length;
      logPerformanceTest(`${api.name} API`, Math.round(avgResponseTime), config.thresholds.apiResponse);
      
      performanceResults.metrics[`${api.name.toLowerCase().replace(' ', '_')}_api`] = avgResponseTime;
      
    } catch (error) {
      console.log(`${colors.red}❌ FAIL${colors.reset} ${api.name} API - Error: ${error.message}`);
    }
  }
}

/**
 * Test 3: Static Asset Performance
 */
async function testStaticAssetPerformance() {
  console.log(`\n${colors.bold}📁 Testing Static Asset Performance${colors.reset}`);
  
  try {
    // Test main page to get asset references
    const mainPage = await makeTimedRequest(config.baseUrl);
    const htmlContent = mainPage.body;
    
    // Extract CSS and JS references
    const cssMatches = htmlContent.match(/href="([^"]*\.css[^"]*)"/g) || [];
    const jsMatches = htmlContent.match(/src="([^"]*\.js[^"]*)"/g) || [];
    
    const assets = [
      ...cssMatches.map(m => m.match(/href="([^"]*)"/)[1]),
      ...jsMatches.map(m => m.match(/src="([^"]*)"/)[1])
    ].filter(url => url.startsWith('/') || url.startsWith(config.baseUrl));
    
    console.log(`Found ${assets.length} static assets to test`);
    
    for (const asset of assets.slice(0, 5)) { // Test first 5 assets
      try {
        const assetUrl = asset.startsWith('/') ? `${config.baseUrl}${asset}` : asset;
        const response = await makeTimedRequest(assetUrl);
        
        const sizeKB = Math.round(response.contentLength / 1024);
        const loadTime = Math.round(response.totalTime);
        
        console.log(`📄 Asset: ${asset.split('/').pop()} - ${sizeKB}KB in ${loadTime}ms`);
        
        // Check if asset is reasonably sized and fast
        if (response.contentLength > 1024 * 1024) { // > 1MB
          performanceResults.recommendations.push(`Large asset detected: ${asset} (${sizeKB}KB)`);
        }
        
        if (response.totalTime > 2000) { // > 2 seconds
          performanceResults.recommendations.push(`Slow loading asset: ${asset} (${loadTime}ms)`);
        }
        
      } catch (error) {
        console.log(`${colors.yellow}⚠️  WARN${colors.reset} Asset ${asset} - ${error.message}`);
      }
    }
    
  } catch (error) {
    console.log(`${colors.red}❌ FAIL${colors.reset} Static asset analysis - ${error.message}`);
  }
}

/**
 * Test 4: Compression and Optimization
 */
async function testCompression() {
  console.log(`\n${colors.bold}🗜️ Testing Compression and Optimization${colors.reset}`);
  
  try {
    // Test with and without compression
    const responseWithGzip = await makeTimedRequest(config.baseUrl, {
      headers: { 'Accept-Encoding': 'gzip, deflate, br' }
    });
    
    const responseWithoutGzip = await makeTimedRequest(config.baseUrl);
    
    const compressionRatio = responseWithoutGzip.contentLength / responseWithGzip.contentLength;
    const hasCompression = responseWithGzip.headers['content-encoding'];
    
    if (hasCompression) {
      console.log(`${colors.green}✅ PASS${colors.reset} Compression enabled: ${hasCompression}`);
      console.log(`${colors.blue}📊 INFO${colors.reset} Compression ratio: ${compressionRatio.toFixed(2)}x`);
    } else {
      console.log(`${colors.yellow}⚠️  WARN${colors.reset} Compression not detected`);
      performanceResults.recommendations.push('Enable gzip/brotli compression');
    }
    
    // Test caching headers
    const cacheControl = responseWithGzip.headers['cache-control'];
    if (cacheControl) {
      console.log(`${colors.green}✅ PASS${colors.reset} Cache headers present: ${cacheControl}`);
    } else {
      console.log(`${colors.yellow}⚠️  WARN${colors.reset} Cache headers missing`);
      performanceResults.recommendations.push('Add appropriate cache headers');
    }
    
  } catch (error) {
    console.log(`${colors.red}❌ FAIL${colors.reset} Compression test - ${error.message}`);
  }
}

/**
 * Test 5: Concurrent Load Handling
 */
async function testConcurrentLoad() {
  console.log(`\n${colors.bold}👥 Testing Concurrent Load Handling${colors.reset}`);
  
  try {
    const concurrentRequests = 10;
    const startTime = performance.now();
    
    // Make concurrent requests
    const requests = [];
    for (let i = 0; i < concurrentRequests; i++) {
      requests.push(makeTimedRequest(`${config.baseUrl}/api/health`));
    }
    
    const responses = await Promise.all(requests);
    const totalTime = performance.now() - startTime;
    
    const successfulRequests = responses.filter(r => r.statusCode === 200).length;
    const avgResponseTime = responses.reduce((sum, r) => sum + r.totalTime, 0) / responses.length;
    
    logPerformanceTest('Concurrent requests success rate', successfulRequests, concurrentRequests, ' requests', true);
    logPerformanceTest('Concurrent avg response time', Math.round(avgResponseTime), config.thresholds.apiResponse);
    
    console.log(`${colors.blue}📊 INFO${colors.reset} ${concurrentRequests} concurrent requests completed in ${Math.round(totalTime)}ms`);
    
  } catch (error) {
    console.log(`${colors.red}❌ FAIL${colors.reset} Concurrent load test - ${error.message}`);
  }
}

/**
 * Test 6: Memory and Resource Usage
 */
async function testResourceUsage() {
  console.log(`\n${colors.bold}💾 Testing Resource Usage${colors.reset}`);
  
  try {
    // Monitor memory usage during test
    const initialMemory = process.memoryUsage();
    
    // Make several requests to test memory leaks
    for (let i = 0; i < 20; i++) {
      await makeTimedRequest(`${config.baseUrl}/api/health`);
    }
    
    const finalMemory = process.memoryUsage();
    const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
    
    console.log(`${colors.blue}📊 INFO${colors.reset} Memory usage increase: ${Math.round(memoryIncrease / 1024)}KB`);
    
    if (memoryIncrease > 10 * 1024 * 1024) { // > 10MB increase
      performanceResults.recommendations.push('Investigate potential memory leaks');
    }
    
  } catch (error) {
    console.log(`${colors.red}❌ FAIL${colors.reset} Resource usage test - ${error.message}`);
  }
}

/**
 * Generate Performance Report
 */
function generatePerformanceReport() {
  console.log(`\n${colors.bold}📊 PERFORMANCE REPORT${colors.reset}`);
  console.log(`${colors.bold}${'='.repeat(60)}${colors.reset}`);
  
  const passedTests = performanceResults.tests.filter(t => t.passed).length;
  const totalTests = performanceResults.tests.length;
  const performanceScore = Math.round((passedTests / totalTests) * 100);
  
  console.log(`${colors.bold}Performance Score: ${performanceScore}%${colors.reset} (${passedTests}/${totalTests} tests passed)`);
  
  // Performance grade
  let grade, gradeColor;
  if (performanceScore >= 90) {
    grade = 'A+'; gradeColor = colors.green;
  } else if (performanceScore >= 80) {
    grade = 'A'; gradeColor = colors.green;
  } else if (performanceScore >= 70) {
    grade = 'B'; gradeColor = colors.yellow;
  } else if (performanceScore >= 60) {
    grade = 'C'; gradeColor = colors.yellow;
  } else {
    grade = 'F'; gradeColor = colors.red;
  }
  
  console.log(`${colors.bold}Performance Grade: ${gradeColor}${grade}${colors.reset}`);
  
  // Key metrics summary
  console.log(`\n${colors.bold}Key Metrics:${colors.reset}`);
  Object.entries(performanceResults.metrics).forEach(([key, value]) => {
    console.log(`  ${key}: ${Math.round(value)}ms`);
  });
  
  // Recommendations
  if (performanceResults.recommendations.length > 0) {
    console.log(`\n${colors.bold}${colors.yellow}Recommendations:${colors.reset}`);
    performanceResults.recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`);
    });
  }
  
  console.log(`\n${colors.bold}Production Readiness:${colors.reset}`);
  if (performanceScore >= 85) {
    console.log(`${colors.green}🚀 EXCELLENT - Performance optimized for production!${colors.reset}`);
  } else if (performanceScore >= 70) {
    console.log(`${colors.yellow}⚠️  GOOD - Minor optimizations recommended${colors.reset}`);
  } else {
    console.log(`${colors.red}🚫 NEEDS IMPROVEMENT - Performance optimizations required${colors.reset}`);
  }
  
  console.log(`${colors.bold}${'='.repeat(60)}${colors.reset}\n`);
}

/**
 * Main performance test runner
 */
async function runPerformanceTests() {
  console.log(`${colors.bold}${colors.blue}⚡ eWasl Platform - Performance Validation Suite${colors.reset}`);
  console.log(`${colors.bold}Testing URL: ${config.baseUrl}${colors.reset}`);
  console.log(`${colors.bold}Samples per test: ${config.samples}${colors.reset}\n`);
  
  const startTime = Date.now();
  
  await testPageLoadPerformance();
  await testAPIPerformance();
  await testStaticAssetPerformance();
  await testCompression();
  await testConcurrentLoad();
  await testResourceUsage();
  
  const endTime = Date.now();
  const duration = Math.round((endTime - startTime) / 1000);
  
  console.log(`\n${colors.bold}Performance testing completed in ${duration} seconds${colors.reset}`);
  generatePerformanceReport();
  
  const passedTests = performanceResults.tests.filter(t => t.passed).length;
  const performanceScore = Math.round((passedTests / performanceResults.tests.length) * 100);
  
  process.exit(performanceScore >= 70 ? 0 : 1);
}

// Handle command line arguments
if (process.argv.length > 2) {
  const customUrl = process.argv[2];
  if (customUrl.startsWith('http')) {
    config.baseUrl = customUrl;
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runPerformanceTests().catch(error => {
    console.error(`${colors.red}Performance test runner error: ${error.message}${colors.reset}`);
    process.exit(1);
  });
}

module.exports = { runPerformanceTests }; 