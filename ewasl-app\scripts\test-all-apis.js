#!/usr/bin/env node

/**
 * Combined API Test Suite
 * Tests both DigitalOcean and Stripe APIs for connectivity and functionality
 */

const { testDigitalOceanAPI } = require('./test-digitalocean-api');
const { testStripeAPI } = require('./test-stripe-api');

/**
 * Run all API tests
 */
async function runAllAPITests() {
  console.log('🚀 eWasl API Connection Test Suite');
  console.log('Testing DigitalOcean and Stripe API connections');
  console.log('=' * 60);
  
  const startTime = Date.now();
  let allResults = {
    digitalocean: null,
    stripe: null,
    summary: {
      totalTests: 0,
      totalPassed: 0,
      totalFailed: 0,
      totalErrors: 0
    }
  };

  try {
    // Test DigitalOcean API
    console.log('\n🌊 PHASE 1: DIGITALOCEAN API TESTING');
    console.log('=' * 60);
    allResults.digitalocean = await testDigitalOceanAPI();
    
    // Test Stripe API
    console.log('\n\n💳 PHASE 2: STRIPE API TESTING');
    console.log('=' * 60);
    allResults.stripe = await testStripeAPI();
    
    // Calculate summary
    const doResults = allResults.digitalocean || [];
    const stripeResults = allResults.stripe || [];
    
    allResults.summary.totalTests = doResults.length + stripeResults.length;
    allResults.summary.totalPassed = 
      doResults.filter(r => r.status === 'PASSED').length +
      stripeResults.filter(r => r.status === 'PASSED').length;
    allResults.summary.totalFailed = 
      doResults.filter(r => r.status === 'FAILED').length +
      stripeResults.filter(r => r.status === 'FAILED').length;
    allResults.summary.totalErrors = 
      doResults.filter(r => r.status === 'ERROR').length +
      stripeResults.filter(r => r.status === 'ERROR').length;

    // Final Summary
    console.log('\n\n' + '=' * 60);
    console.log('🎯 FINAL API TEST SUMMARY');
    console.log('=' * 60);
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log(`⏱️  Total Test Duration: ${duration} seconds`);
    console.log(`📊 Total Tests Run: ${allResults.summary.totalTests}`);
    console.log(`✅ Total Passed: ${allResults.summary.totalPassed}`);
    console.log(`❌ Total Failed: ${allResults.summary.totalFailed}`);
    console.log(`💥 Total Errors: ${allResults.summary.totalErrors}`);
    
    // Success Rate
    const successRate = allResults.summary.totalTests > 0 
      ? ((allResults.summary.totalPassed / allResults.summary.totalTests) * 100).toFixed(1)
      : 0;
    console.log(`📈 Success Rate: ${successRate}%`);
    
    // Individual API Status
    console.log('\n📋 API STATUS BREAKDOWN:');
    
    const doPassed = doResults.filter(r => r.status === 'PASSED').length;
    const doTotal = doResults.length;
    const doStatus = doPassed === doTotal ? '✅ FULLY OPERATIONAL' : 
                     doPassed > 0 ? '⚠️  PARTIALLY WORKING' : '❌ NOT WORKING';
    console.log(`🌊 DigitalOcean API: ${doStatus} (${doPassed}/${doTotal})`);
    
    const stripePassed = stripeResults.filter(r => r.status === 'PASSED').length;
    const stripeTotal = stripeResults.length;
    const stripeStatus = stripePassed === stripeTotal ? '✅ FULLY OPERATIONAL' : 
                         stripePassed > 0 ? '⚠️  PARTIALLY WORKING' : '❌ NOT WORKING';
    console.log(`💳 Stripe API: ${stripeStatus} (${stripePassed}/${stripeTotal})`);
    
    // Overall Status
    console.log('\n🎯 OVERALL STATUS:');
    if (allResults.summary.totalPassed === allResults.summary.totalTests) {
      console.log('🎉 ALL APIS WORKING PERFECTLY!');
      console.log('✅ Ready to proceed with implementation');
    } else if (allResults.summary.totalPassed > 0) {
      console.log('⚠️  SOME APIS WORKING');
      console.log('🔧 Check failed tests and fix issues before proceeding');
    } else {
      console.log('🚨 NO APIS WORKING');
      console.log('❌ Check API keys and network connectivity');
    }
    
    // Next Steps
    console.log('\n📝 NEXT STEPS:');
    if (allResults.summary.totalFailed > 0 || allResults.summary.totalErrors > 0) {
      console.log('1. Review failed tests above');
      console.log('2. Verify API keys are correct and active');
      console.log('3. Check network connectivity and firewall settings');
      console.log('4. Contact API providers if issues persist');
    } else {
      console.log('1. ✅ APIs are ready for integration');
      console.log('2. ✅ Proceed with updating environment variables');
      console.log('3. ✅ Deploy updated configuration');
      console.log('4. ✅ Test real functionality in application');
    }
    
    return allResults;
    
  } catch (error) {
    console.error('\n💥 FATAL ERROR during API testing:', error);
    console.error('Stack trace:', error.stack);
    return null;
  }
}

// Export for use in other scripts
module.exports = { runAllAPITests };

// Run if called directly
if (require.main === module) {
  runAllAPITests()
    .then((results) => {
      if (results && results.summary.totalPassed === results.summary.totalTests) {
        console.log('\n🎉 Exiting with success code');
        process.exit(0);
      } else {
        console.log('\n⚠️  Exiting with warning code');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 Fatal error in test suite:', error);
      process.exit(2);
    });
}
