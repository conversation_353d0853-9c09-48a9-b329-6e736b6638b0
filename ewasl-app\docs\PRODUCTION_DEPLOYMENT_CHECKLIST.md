# 🚀 eWasl Production Deployment Checklist

## Overview
This checklist covers the configuration steps needed to deploy eWasl to production. The application is 90%+ complete with all core features implemented - this focuses on production configuration only.

---

## ✅ PHASE 1: PRODUCTION API CONFIGURATION

### 1.1 Stripe Live Environment
- [ ] **Stripe Live API Keys**
  - [x] Publishable Key: `pk_live_51NlyLHEpEYvJL85M...` (configured)
  - [x] Secret Key: `sk_live_51NlyLHEpEYvJL85M...` (configured)
  - [ ] **Webhook Configuration**:
    - [x] Endpoint URL: `https://app.ewasl.com/api/stripe/webhook`
    - [ ] Select Events:
      - `customer.subscription.created`
      - `customer.subscription.updated` 
      - `customer.subscription.deleted`
      - `invoice.payment_succeeded`
      - `invoice.payment_failed`
      - `payment_intent.succeeded`
    - [ ] Get Webhook Secret and update `STRIPE_WEBHOOK_SECRET`

### 1.2 LinkedIn Production App
- [x] **App Created**: `eWasl Social Scheduler`
- [x] **Client ID**: `787coegnsdocvq` (configured)
- [x] **Client Secret**: `WPL_AP1.F6yN8tH55DHmqsqP.ep1SGQ==` (configured)
- [ ] **OAuth Settings**:
  - [ ] Redirect URL: `https://app.ewasl.com/api/linkedin/callback`
  - [ ] Scopes: `openid`, `profile`, `w_member_social`, `email`

### 1.3 Facebook/Instagram Production App
- [x] **App ID**: `YOUR_FACEBOOK_APP_ID` (configured)
- [ ] **App Secret**: Get from Facebook Developer Console
- [ ] **OAuth Settings**:
  - [ ] Valid OAuth Redirect URIs:
    - `https://app.ewasl.com/api/facebook/callback`
    - `https://app.ewasl.com/api/instagram/callback`
  - [ ] App Domain: `app.ewasl.com`

### 1.4 Twitter/X Production App
- [ ] **Create Twitter App** for production
- [ ] **Get API Credentials**:
  - [ ] API Key
  - [ ] API Secret
  - [ ] Client ID
  - [ ] Client Secret
- [ ] **OAuth Settings**:
  - [ ] Callback URL: `https://app.ewasl.com/api/x/callback`
  - [ ] Website URL: `https://app.ewasl.com`

### 1.5 OpenAI API Configuration
- [ ] **Get OpenAI API Key** for production
- [ ] **Configure Model Settings**:
  - Model: `gpt-4`
  - Max Tokens: `2000`

---

## ✅ PHASE 2: INFRASTRUCTURE CONFIGURATION

### 2.1 Redis Instance for Background Jobs
- [ ] **Create DigitalOcean Managed Redis**:
  - Name: `ewasl-redis-production`
  - Size: `db-s-1vcpu-1gb`
  - Region: `nyc3`
- [ ] **Update Environment Variables**:
  - `REDIS_URL`
  - `REDIS_HOST`
  - `REDIS_PORT`
  - `REDIS_PASSWORD`

### 2.2 CDN Configuration for Media
- [ ] **Create DigitalOcean Spaces**:
  - Bucket: `ewasl-media-production`
  - Region: `nyc3`
- [ ] **Configure CDN Domain**: `cdn.ewasl.com`
- [ ] **Get Access Keys**:
  - `CDN_ACCESS_KEY`
  - `CDN_SECRET_KEY`

### 2.3 Environment Variables Update
- [ ] **Run deployment script**: `./scripts/production-deployment.sh`
- [ ] **Verify all environment variables** are set correctly
- [ ] **Deploy updated configuration** to DigitalOcean

---

## ✅ PHASE 3: TESTING AND VERIFICATION

### 3.1 End-to-End Social Media Publishing
- [ ] **Test LinkedIn Publishing**:
  - [ ] OAuth connection
  - [ ] Text post publishing
  - [ ] Media upload and posting
- [ ] **Test Facebook Publishing**:
  - [ ] OAuth connection
  - [ ] Text post publishing
  - [ ] Image posting
- [ ] **Test Instagram Publishing**:
  - [ ] OAuth connection
  - [ ] Image posting with caption
- [ ] **Test Twitter/X Publishing**:
  - [ ] OAuth connection
  - [ ] Tweet publishing
  - [ ] Media tweet publishing

### 3.2 Payment Processing Verification
- [ ] **Test Subscription Creation**:
  - [ ] Pro Plan ($9/month)
  - [ ] Business Plan ($25/month)
  - [ ] Enterprise Plan (custom)
- [ ] **Test Webhook Processing**:
  - [ ] Subscription created event
  - [ ] Payment succeeded event
  - [ ] Payment failed event
- [ ] **Test Billing Dashboard**:
  - [ ] Current subscription display
  - [ ] Usage tracking
  - [ ] Plan upgrade/downgrade

### 3.3 Background Job Processing
- [ ] **Test Scheduled Posts**:
  - [ ] Create scheduled post
  - [ ] Verify background processing
  - [ ] Confirm post publication
- [ ] **Test Recurring Posts**:
  - [ ] Create recurring schedule
  - [ ] Verify pattern generation
  - [ ] Confirm automated posting

### 3.4 Media Processing Pipeline
- [ ] **Test Image Upload**:
  - [ ] Upload various formats (JPG, PNG, GIF)
  - [ ] Verify optimization
  - [ ] Test platform-specific sizing
- [ ] **Test Video Upload**:
  - [ ] Upload video files
  - [ ] Verify processing
  - [ ] Test format conversion

---

## ✅ PHASE 4: PRODUCTION READINESS

### 4.1 Performance Optimization
- [ ] **Enable Production Optimizations**:
  - [ ] Next.js production build
  - [ ] Image optimization
  - [ ] CDN caching
- [ ] **Database Optimization**:
  - [ ] Connection pooling
  - [ ] Query optimization
  - [ ] Index verification

### 4.2 Monitoring and Logging
- [ ] **Set up Application Monitoring**:
  - [ ] Health check endpoints
  - [ ] Error tracking
  - [ ] Performance monitoring
- [ ] **Configure Logging**:
  - [ ] Application logs
  - [ ] Error logs
  - [ ] Audit logs

### 4.3 Security Configuration
- [ ] **SSL/TLS Configuration**:
  - [x] Custom domain SSL (app.ewasl.com)
  - [ ] Security headers
  - [ ] CORS configuration
- [ ] **API Security**:
  - [ ] Rate limiting
  - [ ] Input validation
  - [ ] Authentication verification

---

## 🎯 DEPLOYMENT COMMANDS

### Quick Deployment
```bash
# Make script executable
chmod +x scripts/production-deployment.sh

# Run production deployment
./scripts/production-deployment.sh
```

### Manual Steps
```bash
# Update app configuration
doctl apps update 92d1f7b6-85f2-47e2-8a69-d823b1586159 --spec .do/app-simple.yaml

# Monitor deployment
doctl apps get 92d1f7b6-85f2-47e2-8a69-d823b1586159

# Test health
curl https://app.ewasl.com/api/system/health
```

---

## 📊 SUCCESS CRITERIA

### Application is Production Ready When:
- [ ] All OAuth flows work with production credentials
- [ ] Payment processing works with live Stripe
- [ ] Background jobs process scheduled posts automatically
- [ ] Media uploads and optimization work correctly
- [ ] All API endpoints respond correctly
- [ ] Performance meets requirements (< 2s page load)
- [ ] Security measures are in place
- [ ] Monitoring and logging are active

---

## 🚨 ROLLBACK PROCEDURES

### If Issues Occur:
1. **Revert to Previous Deployment**:
   ```bash
   doctl apps get-deployment 92d1f7b6-85f2-47e2-8a69-d823b1586159 PREVIOUS_DEPLOYMENT_ID
   ```

2. **Switch Back to Test Mode**:
   - Update Stripe to test keys
   - Disable background jobs
   - Use development OAuth apps

3. **Emergency Contacts**:
   - Technical Lead: [Contact Info]
   - DevOps Team: [Contact Info]
   - Stripe Support: [Contact Info]

---

**Estimated Total Time**: 4-6 hours for complete production configuration
**Prerequisites**: All development work is complete (90%+ implemented)
**Focus**: Configuration and integration, not development
