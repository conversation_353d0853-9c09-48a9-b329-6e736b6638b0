"use client";

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import {
  Plus,
  Trash2,
  Copy,
  Upload,
  Download,
  Calendar,
  Clock,
  Send,
  Save,
  FileText,
  Image,
  Video,
  Globe,
  Zap,
  CheckCircle,
  AlertCircle,
  Info,
  Loader2,
  RotateCcw,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRTL } from '@/lib/i18n/arabic-rtl-optimizer';
import { toast } from 'sonner';

interface BatchPost {
  id: string;
  content: string;
  platforms: string[];
  scheduledAt?: Date;
  mediaUrls: string[];
  tags: string[];
  priority: 'low' | 'medium' | 'high';
  status: 'draft' | 'ready' | 'processing' | 'completed' | 'failed';
  error?: string;
}

interface BatchPostCreatorProps {
  language?: 'ar' | 'en';
  className?: string;
  onBatchCreate?: (posts: BatchPost[]) => Promise<void>;
  onSaveDraft?: (posts: BatchPost[]) => void;
}

export function BatchPostCreator({
  language = 'ar',
  className,
  onBatchCreate,
  onSaveDraft
}: BatchPostCreatorProps) {
  const [posts, setPosts] = useState<BatchPost[]>([createEmptyPost()]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [globalSettings, setGlobalSettings] = useState({
    platforms: [] as string[],
    scheduledAt: '',
    autoSchedule: false,
    scheduleInterval: 30, // minutes
    priority: 'medium' as 'low' | 'medium' | 'high'
  });

  const rtl = useRTL(language);

  // Translations
  const t = {
    ar: {
      title: 'منشئ المنشورات المجمعة',
      subtitle: 'إنشاء ونشر عدة منشورات في وقت واحد',
      tabs: {
        posts: 'المنشورات',
        settings: 'الإعدادات العامة',
        preview: 'المعاينة',
        import: 'الاستيراد'
      },
      actions: {
        addPost: 'إضافة منشور',
        removePost: 'حذف منشور',
        duplicatePost: 'نسخ منشور',
        createBatch: 'إنشاء المجموعة',
        saveDraft: 'حفظ كمسودة',
        importCSV: 'استيراد CSV',
        exportTemplate: 'تصدير قالب',
        reset: 'إعادة تعيين',
        preview: 'معاينة'
      },
      fields: {
        content: 'المحتوى',
        platforms: 'المنصات',
        scheduledAt: 'موعد النشر',
        mediaUrls: 'روابط الوسائط',
        tags: 'العلامات',
        priority: 'الأولوية'
      },
      status: {
        draft: 'مسودة',
        ready: 'جاهز',
        processing: 'قيد المعالجة',
        completed: 'مكتمل',
        failed: 'فشل'
      },
      priority: {
        low: 'منخفضة',
        medium: 'متوسطة',
        high: 'عالية'
      },
      platforms: {
        instagram: 'إنستغرام',
        facebook: 'فيسبوك',
        twitter: 'تويتر',
        linkedin: 'لينكد إن'
      },
      settings: {
        globalPlatforms: 'المنصات الافتراضية',
        autoSchedule: 'جدولة تلقائية',
        scheduleInterval: 'فترة الجدولة (دقائق)',
        defaultPriority: 'الأولوية الافتراضية'
      },
      messages: {
        postAdded: 'تم إضافة منشور جديد',
        postRemoved: 'تم حذف المنشور',
        postDuplicated: 'تم نسخ المنشور',
        batchCreated: 'تم إنشاء المجموعة بنجاح',
        draftSaved: 'تم حفظ المسودة',
        invalidContent: 'يرجى إدخال محتوى صحيح لجميع المنشورات',
        noPlatforms: 'يرجى اختيار منصة واحدة على الأقل',
        processing: 'جاري معالجة المنشورات...'
      }
    },
    en: {
      title: 'Batch Post Creator',
      subtitle: 'Create and publish multiple posts at once',
      tabs: {
        posts: 'Posts',
        settings: 'Global Settings',
        preview: 'Preview',
        import: 'Import'
      },
      actions: {
        addPost: 'Add Post',
        removePost: 'Remove Post',
        duplicatePost: 'Duplicate Post',
        createBatch: 'Create Batch',
        saveDraft: 'Save Draft',
        importCSV: 'Import CSV',
        exportTemplate: 'Export Template',
        reset: 'Reset',
        preview: 'Preview'
      },
      fields: {
        content: 'Content',
        platforms: 'Platforms',
        scheduledAt: 'Scheduled At',
        mediaUrls: 'Media URLs',
        tags: 'Tags',
        priority: 'Priority'
      },
      status: {
        draft: 'Draft',
        ready: 'Ready',
        processing: 'Processing',
        completed: 'Completed',
        failed: 'Failed'
      },
      priority: {
        low: 'Low',
        medium: 'Medium',
        high: 'High'
      },
      platforms: {
        instagram: 'Instagram',
        facebook: 'Facebook',
        twitter: 'Twitter',
        linkedin: 'LinkedIn'
      },
      settings: {
        globalPlatforms: 'Default Platforms',
        autoSchedule: 'Auto Schedule',
        scheduleInterval: 'Schedule Interval (minutes)',
        defaultPriority: 'Default Priority'
      },
      messages: {
        postAdded: 'New post added',
        postRemoved: 'Post removed',
        postDuplicated: 'Post duplicated',
        batchCreated: 'Batch created successfully',
        draftSaved: 'Draft saved',
        invalidContent: 'Please enter valid content for all posts',
        noPlatforms: 'Please select at least one platform',
        processing: 'Processing posts...'
      }
    }
  };

  const text = t[language];

  // Create empty post
  function createEmptyPost(): BatchPost {
    return {
      id: `post-${Date.now()}-${Math.random()}`,
      content: '',
      platforms: globalSettings.platforms,
      mediaUrls: [],
      tags: [],
      priority: globalSettings.priority,
      status: 'draft'
    };
  }

  // Post management
  const addPost = useCallback(() => {
    setPosts(prev => [...prev, createEmptyPost()]);
    toast.success(text.messages.postAdded);
  }, [text.messages.postAdded, globalSettings]);

  const removePost = useCallback((postId: string) => {
    setPosts(prev => prev.filter(post => post.id !== postId));
    toast.success(text.messages.postRemoved);
  }, [text.messages.postRemoved]);

  const duplicatePost = useCallback((postId: string) => {
    const postToDuplicate = posts.find(post => post.id === postId);
    if (postToDuplicate) {
      const duplicatedPost = {
        ...postToDuplicate,
        id: `post-${Date.now()}-${Math.random()}`,
        status: 'draft' as const
      };
      setPosts(prev => [...prev, duplicatedPost]);
      toast.success(text.messages.postDuplicated);
    }
  }, [posts, text.messages.postDuplicated]);

  const updatePost = useCallback((postId: string, updates: Partial<BatchPost>) => {
    setPosts(prev => prev.map(post => 
      post.id === postId ? { ...post, ...updates } : post
    ));
  }, []);

  // Validation
  const validatePosts = useCallback(() => {
    const errors: string[] = [];

    posts.forEach((post, index) => {
      if (!post.content.trim()) {
        errors.push(`${language === 'ar' ? 'المنشور' : 'Post'} ${index + 1}: ${text.messages.invalidContent}`);
      }
      if (post.platforms.length === 0) {
        errors.push(`${language === 'ar' ? 'المنشور' : 'Post'} ${index + 1}: ${text.messages.noPlatforms}`);
      }
    });

    return errors;
  }, [posts, text.messages, language]);

  // Batch creation
  const handleCreateBatch = useCallback(async () => {
    const errors = validatePosts();
    if (errors.length > 0) {
      errors.forEach(error => toast.error(error));
      return;
    }

    setIsProcessing(true);
    setProcessingProgress(0);

    try {
      // Update posts status to ready
      const readyPosts = posts.map(post => ({ ...post, status: 'ready' as const }));
      setPosts(readyPosts);

      // Simulate processing with progress
      for (let i = 0; i <= 100; i += 10) {
        setProcessingProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      if (onBatchCreate) {
        await onBatchCreate(readyPosts);
      }

      // Update posts status to completed
      setPosts(prev => prev.map(post => ({ ...post, status: 'completed' as const })));
      
      toast.success(text.messages.batchCreated);
    } catch (error) {
      console.error('Batch creation failed:', error);
      setPosts(prev => prev.map(post => ({ 
        ...post, 
        status: 'failed' as const,
        error: error instanceof Error ? error.message : 'Unknown error'
      })));
      toast.error(language === 'ar' ? 'فشل في إنشاء المجموعة' : 'Failed to create batch');
    } finally {
      setIsProcessing(false);
      setProcessingProgress(0);
    }
  }, [posts, validatePosts, onBatchCreate, text.messages, language]);

  // Save draft
  const handleSaveDraft = useCallback(() => {
    if (onSaveDraft) {
      onSaveDraft(posts);
    }
    toast.success(text.messages.draftSaved);
  }, [posts, onSaveDraft, text.messages.draftSaved]);

  // Apply global settings to all posts
  const applyGlobalSettings = useCallback(() => {
    setPosts(prev => prev.map(post => ({
      ...post,
      platforms: globalSettings.platforms.length > 0 ? globalSettings.platforms : post.platforms,
      priority: globalSettings.priority,
      scheduledAt: globalSettings.scheduledAt ? new Date(globalSettings.scheduledAt) : post.scheduledAt
    })));
  }, [globalSettings]);

  // Auto-schedule posts
  const autoSchedulePosts = useCallback(() => {
    if (!globalSettings.autoSchedule || !globalSettings.scheduledAt) return;

    const baseDate = new Date(globalSettings.scheduledAt);
    setPosts(prev => prev.map((post, index) => ({
      ...post,
      scheduledAt: new Date(baseDate.getTime() + index * globalSettings.scheduleInterval * 60 * 1000)
    })));
  }, [globalSettings]);

  // Get status icon
  const getStatusIcon = (status: BatchPost['status']) => {
    switch (status) {
      case 'draft':
        return <FileText className="w-4 h-4 text-gray-500" />;
      case 'ready':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'processing':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className={cn(
      "space-y-6",
      language === 'ar' ? "rtl" : "ltr",
      className
    )} dir={rtl.direction}>
      {/* Header */}
      <div className={rtl.cn(
        "flex items-center justify-between",
        rtl.flex()
      )}>
        <div className={rtl.textAlign()}>
          <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: rtl.getFontFamily('heading') }}>
            {text.title}
          </h1>
          <p className="text-gray-600 mt-1" style={{ fontFamily: rtl.getFontFamily('primary') }}>
            {text.subtitle}
          </p>
        </div>
        
        <div className={rtl.cn(
          "flex items-center gap-4",
          rtl.flex()
        )}>
          <Badge variant="outline">
            {posts.length} {language === 'ar' ? 'منشورات' : 'posts'}
          </Badge>
          
          <Button
            variant="outline"
            onClick={() => setPosts([createEmptyPost()])}
            className={rtl.cn(
              "flex items-center gap-2",
              rtl.flex()
            )}
          >
            <RotateCcw className="w-4 h-4" />
            {text.actions.reset}
          </Button>
        </div>
      </div>

      {/* Processing Progress */}
      {isProcessing && (
        <Alert className="border-blue-200 bg-blue-50">
          <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />
          <AlertTitle className={rtl.cn(
            "text-blue-900",
            rtl.textAlign()
          )}>
            {text.messages.processing}
          </AlertTitle>
          <AlertDescription className={rtl.cn(
            "text-blue-800",
            rtl.textAlign()
          )}>
            <Progress value={processingProgress} className="mt-2" />
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs defaultValue="posts" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="posts">{text.tabs.posts}</TabsTrigger>
          <TabsTrigger value="settings">{text.tabs.settings}</TabsTrigger>
          <TabsTrigger value="preview">{text.tabs.preview}</TabsTrigger>
          <TabsTrigger value="import">{text.tabs.import}</TabsTrigger>
        </TabsList>

        {/* Posts Tab */}
        <TabsContent value="posts" className="space-y-6">
          <div className={rtl.cn(
            "flex items-center justify-between",
            rtl.flex()
          )}>
            <h3 className="text-lg font-semibold">{text.tabs.posts}</h3>
            <Button
              onClick={addPost}
              className={rtl.cn(
                "flex items-center gap-2",
                rtl.flex()
              )}
            >
              <Plus className="w-4 h-4" />
              {text.actions.addPost}
            </Button>
          </div>

          <div className="space-y-4">
            {posts.map((post, index) => (
              <Card key={post.id}>
                <CardHeader className="pb-3">
                  <div className={rtl.cn(
                    "flex items-center justify-between",
                    rtl.flex()
                  )}>
                    <div className={rtl.cn(
                      "flex items-center gap-2",
                      rtl.flex()
                    )}>
                      {getStatusIcon(post.status)}
                      <span className="font-medium">
                        {language === 'ar' ? 'منشور' : 'Post'} {index + 1}
                      </span>
                      <Badge variant={
                        post.priority === 'high' ? 'destructive' :
                        post.priority === 'medium' ? 'default' : 'secondary'
                      }>
                        {text.priority[post.priority]}
                      </Badge>
                    </div>

                    <div className={rtl.cn(
                      "flex items-center gap-2",
                      rtl.flex()
                    )}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => duplicatePost(post.id)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removePost(post.id)}
                        disabled={posts.length === 1}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Content */}
                  <div>
                    <Label className={rtl.textAlign()}>
                      {text.fields.content}
                    </Label>
                    <Textarea
                      placeholder={language === 'ar' ? 'اكتب محتوى المنشور...' : 'Write your post content...'}
                      value={post.content}
                      onChange={(e) => updatePost(post.id, { content: e.target.value })}
                      className={rtl.textAlign()}
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Platforms */}
                    <div>
                      <Label className={rtl.textAlign()}>
                        {text.fields.platforms}
                      </Label>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        {['instagram', 'facebook', 'twitter', 'linkedin'].map((platform) => (
                          <div key={platform} className={rtl.cn(
                            "flex items-center space-x-2",
                            rtl.flex()
                          )}>
                            <Checkbox
                              id={`${post.id}-${platform}`}
                              checked={post.platforms.includes(platform)}
                              onCheckedChange={(checked) => {
                                const newPlatforms = checked
                                  ? [...post.platforms, platform]
                                  : post.platforms.filter(p => p !== platform);
                                updatePost(post.id, { platforms: newPlatforms });
                              }}
                            />
                            <Label htmlFor={`${post.id}-${platform}`} className="text-sm">
                              {text.platforms[platform as keyof typeof text.platforms]}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Priority */}
                    <div>
                      <Label className={rtl.textAlign()}>
                        {text.fields.priority}
                      </Label>
                      <Select
                        value={post.priority}
                        onValueChange={(value: 'low' | 'medium' | 'high') =>
                          updatePost(post.id, { priority: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">{text.priority.low}</SelectItem>
                          <SelectItem value="medium">{text.priority.medium}</SelectItem>
                          <SelectItem value="high">{text.priority.high}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Scheduled At */}
                  <div>
                    <Label className={rtl.textAlign()}>
                      {text.fields.scheduledAt}
                    </Label>
                    <Input
                      type="datetime-local"
                      value={post.scheduledAt ? post.scheduledAt.toISOString().slice(0, 16) : ''}
                      onChange={(e) => updatePost(post.id, {
                        scheduledAt: e.target.value ? new Date(e.target.value) : undefined
                      })}
                      className={rtl.textAlign()}
                    />
                  </div>

                  {/* Media URLs */}
                  <div>
                    <Label className={rtl.textAlign()}>
                      {text.fields.mediaUrls}
                    </Label>
                    <Input
                      placeholder={language === 'ar' ? 'روابط الصور أو الفيديوهات (مفصولة بفواصل)' : 'Image or video URLs (comma separated)'}
                      value={post.mediaUrls.join(', ')}
                      onChange={(e) => updatePost(post.id, {
                        mediaUrls: e.target.value.split(',').map(url => url.trim()).filter(Boolean)
                      })}
                      className={rtl.textAlign()}
                    />
                  </div>

                  {/* Tags */}
                  <div>
                    <Label className={rtl.textAlign()}>
                      {text.fields.tags}
                    </Label>
                    <Input
                      placeholder={language === 'ar' ? 'العلامات (مفصولة بفواصل)' : 'Tags (comma separated)'}
                      value={post.tags.join(', ')}
                      onChange={(e) => updatePost(post.id, {
                        tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                      })}
                      className={rtl.textAlign()}
                    />
                  </div>

                  {/* Error Display */}
                  {post.error && (
                    <Alert className="border-red-200 bg-red-50">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <AlertDescription className={rtl.textAlign()}>
                        {post.error}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {text.tabs.settings}
              </CardTitle>
              <CardDescription className={rtl.textAlign()}>
                {language === 'ar'
                  ? 'إعدادات عامة تطبق على جميع المنشورات'
                  : 'Global settings that apply to all posts'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Global Platforms */}
              <div>
                <Label className={rtl.textAlign()}>
                  {text.settings.globalPlatforms}
                </Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {['instagram', 'facebook', 'twitter', 'linkedin'].map((platform) => (
                    <div key={platform} className={rtl.cn(
                      "flex items-center space-x-2",
                      rtl.flex()
                    )}>
                      <Checkbox
                        id={`global-${platform}`}
                        checked={globalSettings.platforms.includes(platform)}
                        onCheckedChange={(checked) => {
                          const newPlatforms = checked
                            ? [...globalSettings.platforms, platform]
                            : globalSettings.platforms.filter(p => p !== platform);
                          setGlobalSettings(prev => ({ ...prev, platforms: newPlatforms }));
                        }}
                      />
                      <Label htmlFor={`global-${platform}`} className="text-sm">
                        {text.platforms[platform as keyof typeof text.platforms]}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Default Priority */}
              <div>
                <Label className={rtl.textAlign()}>
                  {text.settings.defaultPriority}
                </Label>
                <Select
                  value={globalSettings.priority}
                  onValueChange={(value: 'low' | 'medium' | 'high') =>
                    setGlobalSettings(prev => ({ ...prev, priority: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">{text.priority.low}</SelectItem>
                    <SelectItem value="medium">{text.priority.medium}</SelectItem>
                    <SelectItem value="high">{text.priority.high}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Auto Schedule */}
              <div className="space-y-4">
                <div className={rtl.cn(
                  "flex items-center space-x-2",
                  rtl.flex()
                )}>
                  <Checkbox
                    id="auto-schedule"
                    checked={globalSettings.autoSchedule}
                    onCheckedChange={(checked) =>
                      setGlobalSettings(prev => ({ ...prev, autoSchedule: !!checked }))
                    }
                  />
                  <Label htmlFor="auto-schedule">
                    {text.settings.autoSchedule}
                  </Label>
                </div>

                {globalSettings.autoSchedule && (
                  <>
                    <div>
                      <Label className={rtl.textAlign()}>
                        {language === 'ar' ? 'تاريخ البداية' : 'Start Date'}
                      </Label>
                      <Input
                        type="datetime-local"
                        value={globalSettings.scheduledAt}
                        onChange={(e) => setGlobalSettings(prev => ({
                          ...prev,
                          scheduledAt: e.target.value
                        }))}
                        className={rtl.textAlign()}
                      />
                    </div>

                    <div>
                      <Label className={rtl.textAlign()}>
                        {text.settings.scheduleInterval}
                      </Label>
                      <Input
                        type="number"
                        min="1"
                        value={globalSettings.scheduleInterval}
                        onChange={(e) => setGlobalSettings(prev => ({
                          ...prev,
                          scheduleInterval: parseInt(e.target.value) || 30
                        }))}
                        className={rtl.textAlign()}
                      />
                    </div>
                  </>
                )}
              </div>

              <Separator />

              <div className={rtl.cn(
                "flex gap-2",
                rtl.flex()
              )}>
                <Button onClick={applyGlobalSettings} variant="outline">
                  <Settings className="w-4 h-4 mr-2" />
                  {language === 'ar' ? 'تطبيق على الكل' : 'Apply to All'}
                </Button>

                {globalSettings.autoSchedule && (
                  <Button onClick={autoSchedulePosts} variant="outline">
                    <Clock className="w-4 h-4 mr-2" />
                    {language === 'ar' ? 'جدولة تلقائية' : 'Auto Schedule'}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Preview Tab */}
        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {text.tabs.preview}
              </CardTitle>
              <CardDescription className={rtl.textAlign()}>
                {language === 'ar'
                  ? 'معاينة جميع المنشورات قبل النشر'
                  : 'Preview all posts before publishing'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {posts.map((post, index) => (
                  <div key={post.id} className="p-4 border rounded-lg">
                    <div className={rtl.cn(
                      "flex items-center justify-between mb-3",
                      rtl.flex()
                    )}>
                      <div className={rtl.cn(
                        "flex items-center gap-2",
                        rtl.flex()
                      )}>
                        {getStatusIcon(post.status)}
                        <span className="font-medium">
                          {language === 'ar' ? 'منشور' : 'Post'} {index + 1}
                        </span>
                        <Badge variant="outline">
                          {text.priority[post.priority]}
                        </Badge>
                      </div>

                      <div className={rtl.cn(
                        "flex items-center gap-2",
                        rtl.flex()
                      )}>
                        {post.platforms.map((platform, idx) => (
                          <span key={idx} className="text-lg">
                            {platform === 'instagram' && '📷'}
                            {platform === 'facebook' && '👥'}
                            {platform === 'twitter' && '🐦'}
                            {platform === 'linkedin' && '💼'}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className={rtl.textAlign()}>
                      <p className="text-gray-900 mb-2">{post.content}</p>

                      {post.tags.length > 0 && (
                        <div className={rtl.cn(
                          "flex flex-wrap gap-1 mb-2",
                          rtl.flex()
                        )}>
                          {post.tags.map((tag, idx) => (
                            <Badge key={idx} variant="secondary" className="text-xs">
                              #{tag}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {post.scheduledAt && (
                        <p className="text-sm text-gray-600">
                          📅 {rtl.formatDateTime(post.scheduledAt)}
                        </p>
                      )}

                      {post.mediaUrls.length > 0 && (
                        <div className={rtl.cn(
                          "flex items-center gap-2 mt-2 text-sm text-gray-600",
                          rtl.flex()
                        )}>
                          <Image className="w-4 h-4" />
                          <span>{post.mediaUrls.length} {language === 'ar' ? 'وسائط' : 'media files'}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Import Tab */}
        <TabsContent value="import" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className={rtl.textAlign()}>
                {text.tabs.import}
              </CardTitle>
              <CardDescription className={rtl.textAlign()}>
                {language === 'ar'
                  ? 'استيراد المنشورات من ملف CSV أو تصدير قالب'
                  : 'Import posts from CSV file or export template'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Import Section */}
                <div className="space-y-4">
                  <h4 className={rtl.cn("font-semibold", rtl.textAlign())}>
                    {language === 'ar' ? 'استيراد من CSV' : 'Import from CSV'}
                  </h4>

                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="w-8 h-8 mx-auto mb-4 text-gray-400" />
                    <p className="text-sm text-gray-600 mb-4">
                      {language === 'ar'
                        ? 'اسحب ملف CSV هنا أو انقر للتحديد'
                        : 'Drag CSV file here or click to select'
                      }
                    </p>
                    <Button variant="outline">
                      <Upload className="w-4 h-4 mr-2" />
                      {text.actions.importCSV}
                    </Button>
                  </div>

                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription className={rtl.textAlign()}>
                      {language === 'ar'
                        ? 'يجب أن يحتوي ملف CSV على الأعمدة: content, platforms, scheduledAt, tags, priority'
                        : 'CSV file should contain columns: content, platforms, scheduledAt, tags, priority'
                      }
                    </AlertDescription>
                  </Alert>
                </div>

                {/* Export Section */}
                <div className="space-y-4">
                  <h4 className={rtl.cn("font-semibold", rtl.textAlign())}>
                    {language === 'ar' ? 'تصدير القالب' : 'Export Template'}
                  </h4>

                  <div className="p-6 bg-gray-50 rounded-lg">
                    <Download className="w-8 h-8 mx-auto mb-4 text-gray-400" />
                    <p className="text-sm text-gray-600 mb-4 text-center">
                      {language === 'ar'
                        ? 'قم بتنزيل قالب CSV لتعبئة المنشورات'
                        : 'Download CSV template to fill in your posts'
                      }
                    </p>
                    <Button variant="outline" className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      {text.actions.exportTemplate}
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <h5 className={rtl.cn("text-sm font-medium", rtl.textAlign())}>
                      {language === 'ar' ? 'أعمدة القالب:' : 'Template Columns:'}
                    </h5>
                    <ul className={rtl.cn("text-xs text-gray-600 space-y-1", rtl.textAlign())}>
                      <li>• content - {language === 'ar' ? 'محتوى المنشور' : 'Post content'}</li>
                      <li>• platforms - {language === 'ar' ? 'المنصات (مفصولة بـ ;)' : 'Platforms (separated by ;)'}</li>
                      <li>• scheduledAt - {language === 'ar' ? 'موعد النشر' : 'Schedule date'}</li>
                      <li>• tags - {language === 'ar' ? 'العلامات (مفصولة بـ ;)' : 'Tags (separated by ;)'}</li>
                      <li>• priority - {language === 'ar' ? 'الأولوية (low/medium/high)' : 'Priority (low/medium/high)'}</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <Card>
        <CardContent className="p-6">
          <div className={rtl.cn(
            "flex items-center justify-between",
            rtl.flex()
          )}>
            <div className={rtl.cn(
              "flex items-center gap-4",
              rtl.flex()
            )}>
              <div className="text-sm text-gray-600">
                <span className="font-medium">{posts.length}</span> {language === 'ar' ? 'منشورات' : 'posts'}
                <span className="mx-2">•</span>
                <span className="font-medium">
                  {posts.filter(p => p.platforms.length > 0).length}
                </span> {language === 'ar' ? 'جاهزة' : 'ready'}
              </div>
            </div>

            <div className={rtl.cn(
              "flex items-center gap-3",
              rtl.flex()
            )}>
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                className={rtl.cn(
                  "flex items-center gap-2",
                  rtl.flex()
                )}
              >
                <Save className="w-4 h-4" />
                {text.actions.saveDraft}
              </Button>

              <Button
                onClick={handleCreateBatch}
                disabled={isProcessing || posts.length === 0}
                className={rtl.cn(
                  "flex items-center gap-2",
                  rtl.flex()
                )}
              >
                {isProcessing ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Zap className="w-4 h-4" />
                )}
                {text.actions.createBatch}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
