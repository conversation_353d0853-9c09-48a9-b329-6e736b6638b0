import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth/api-auth';

export async function GET(request: NextRequest) {
  return getAuthenticatedUser(request).then(({ user, supabase }) => {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '30d';
    
    // Generate chart data based on time range
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    
    // Generate engagement trends data
    const engagement_trends = Array.from({ length: days }, (_, i) => {
      const date = new Date(Date.now() - (days - i - 1) * 24 * 60 * 60 * 1000);
      const baseEngagement = 200 + (i * 8);
      const baseReach = 1000 + (i * 40);
      
      return {
        date: date.toISOString().split('T')[0],
        engagement: Math.floor(baseEngagement + Math.random() * 300),
        reach: Math.floor(baseReach + Math.random() * 1500),
        posts: Math.floor(Math.random() * 8) + 3
      };
    });

    // Platform statistics
    const platform_stats = [
      {
        platform: 'Facebook',
        engagement: Math.floor(Math.random() * 500) + 1000,
        posts: Math.floor(Math.random() * 20) + 35,
        color: '#1877F2'
      },
      {
        platform: 'Instagram', 
        engagement: Math.floor(Math.random() * 400) + 800,
        posts: Math.floor(Math.random() * 15) + 30,
        color: '#E4405F'
      },
      {
        platform: 'Twitter',
        engagement: Math.floor(Math.random() * 300) + 600,
        posts: Math.floor(Math.random() * 25) + 40,
        color: '#1DA1F2'
      },
      {
        platform: 'LinkedIn',
        engagement: Math.floor(Math.random() * 200) + 300,
        posts: Math.floor(Math.random() * 10) + 15,
        color: '#0A66C2'
      }
    ];

    // Hourly performance analysis
    const hourly_performance = Array.from({ length: 24 }, (_, hour) => {
      // Higher engagement during business hours (9-21)
      const isBusinessHour = hour >= 9 && hour <= 21;
      const baseRate = isBusinessHour ? 5.5 : 3.2;
      const variation = Math.random() * 2.5;
      
      return {
        hour,
        engagement_rate: Math.round((baseRate + variation) * 10) / 10
      };
    });

    // Content type performance
    const content_performance = [
      {
        type: 'صور',
        avg_engagement: 7.2 + Math.random() * 2,
        post_count: Math.floor(Math.random() * 20) + 35,
        color: '#10B981'
      },
      {
        type: 'فيديو',
        avg_engagement: 9.1 + Math.random() * 2,
        post_count: Math.floor(Math.random() * 15) + 20,
        color: '#F59E0B'
      },
      {
        type: 'نص',
        avg_engagement: 4.8 + Math.random() * 1.5,
        post_count: Math.floor(Math.random() * 30) + 50,
        color: '#6366F1'
      },
      {
        type: 'رابط',
        avg_engagement: 3.2 + Math.random() * 1,
        post_count: Math.floor(Math.random() * 15) + 25,
        color: '#EF4444'
      }
    ];

    // Growth metrics
    const growth_metrics = {
      engagement_growth: (Math.random() * 20) - 5, // -5% to +15%
      reach_growth: (Math.random() * 25) - 3, // -3% to +22%
      follower_growth: (Math.random() * 15) + 2, // +2% to +17%
      post_frequency_change: (Math.random() * 30) - 10 // -10% to +20%
    };

    return NextResponse.json({
      success: true,
      user_id: user.id,
      time_range: timeRange,
      charts: {
        engagement_trends,
        platform_stats,
        hourly_performance,
        content_performance,
        growth_metrics,
        summary: {
          total_engagement: engagement_trends.reduce((sum, day) => sum + day.engagement, 0),
          total_reach: engagement_trends.reduce((sum, day) => sum + day.reach, 0),
          total_posts: engagement_trends.reduce((sum, day) => sum + day.posts, 0),
          avg_engagement_rate: hourly_performance.reduce((sum, hour) => sum + hour.engagement_rate, 0) / 24,
          best_performing_platform: platform_stats.reduce((best, current) => 
            current.engagement > best.engagement ? current : best
          ),
          peak_engagement_hour: hourly_performance.reduce((peak, current) => 
            current.engagement_rate > peak.engagement_rate ? current : peak
          ).hour
        }
      },
      generated_at: new Date().toISOString(),
      cache_duration: 300 // 5 minutes
    });
  }).catch((error) => {
    return NextResponse.json(
      { error: 'Authentication required for charts data' },
      { status: 401 }
    );
  });
}
