/**
 * Global Test Setup for eWasl OAuth Integration Tests
 * Prepares test environment and validates prerequisites
 */

import { chromium, FullConfig } from '@playwright/test';
import { createServiceRoleClient } from '@/lib/supabase/service-role';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting eWasl OAuth Integration Test Setup...');

  // Validate environment variables
  const requiredEnvVars = [
    'NEXT_PUBLIC_APP_URL',
    'TEST_USER_EMAIL',
    'TEST_USER_PASSWORD',
    'SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars);
    throw new Error(`Missing environment variables: ${missingVars.join(', ')}`);
  }

  // Test database connection
  try {
    console.log('🔍 Testing database connection...');
    const supabase = createServiceRoleClient();
    
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (error) {
      throw new Error(`Database connection failed: ${error.message}`);
    }

    console.log('✅ Database connection successful');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }

  // Test application availability
  try {
    console.log('🌐 Testing application availability...');
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    const response = await page.goto(process.env.NEXT_PUBLIC_APP_URL!);
    
    if (!response || response.status() >= 400) {
      throw new Error(`Application not available. Status: ${response?.status()}`);
    }

    await browser.close();
    console.log('✅ Application is available');
  } catch (error) {
    console.error('❌ Application availability check failed:', error);
    throw error;
  }

  // Prepare test data
  try {
    console.log('📝 Preparing test data...');
    await prepareTestData();
    console.log('✅ Test data prepared');
  } catch (error) {
    console.error('❌ Test data preparation failed:', error);
    throw error;
  }

  // Create test results directory
  const fs = require('fs');
  const path = require('path');
  
  const testResultsDir = path.join(process.cwd(), 'test-results');
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
  }

  console.log('✅ Global setup completed successfully');
}

/**
 * Prepare test data in database
 */
async function prepareTestData() {
  const supabase = createServiceRoleClient();

  // Ensure test user exists
  const testUserEmail = process.env.TEST_USER_EMAIL!;
  
  const { data: existingUser, error: userError } = await supabase
    .from('users')
    .select('id, email')
    .eq('email', testUserEmail)
    .single();

  if (userError && userError.code !== 'PGRST116') { // Not found error
    throw new Error(`Error checking test user: ${userError.message}`);
  }

  if (!existingUser) {
    console.log('👤 Creating test user...');
    
    // Create test user (this would typically be done through auth signup)
    const { data: newUser, error: createError } = await supabase
      .from('users')
      .insert({
        email: testUserEmail,
        name: 'Test User',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      throw new Error(`Error creating test user: ${createError.message}`);
    }

    console.log('✅ Test user created:', newUser.id);
  } else {
    console.log('✅ Test user exists:', existingUser.id);
  }

  // Clean up any existing test data
  await cleanupTestData();
}

/**
 * Clean up test data
 */
async function cleanupTestData() {
  const supabase = createServiceRoleClient();
  const testUserEmail = process.env.TEST_USER_EMAIL!;

  // Get test user ID
  const { data: testUser } = await supabase
    .from('users')
    .select('id')
    .eq('email', testUserEmail)
    .single();

  if (!testUser) return;

  console.log('🧹 Cleaning up existing test data...');

  // Clean up test posts
  await supabase
    .from('posts')
    .delete()
    .eq('user_id', testUser.id)
    .like('content', '%TEST%');

  // Clean up test social accounts (keep real ones)
  await supabase
    .from('social_accounts')
    .delete()
    .eq('user_id', testUser.id)
    .like('account_name', '%TEST%');

  // Clean up test activities
  await supabase
    .from('activities')
    .delete()
    .eq('user_id', testUser.id)
    .like('details', '%TEST%');

  console.log('✅ Test data cleanup completed');
}

export default globalSetup;
