/**
 * Enhanced OAuth Token Refresh Service
 * Handles automatic token refresh, expiration monitoring, and graceful error handling
 */

import { createServiceRoleClient } from '@/lib/supabase/service-role';
import { FacebookOAuthService } from './facebook';

export interface TokenInfo {
  id: string;
  userId: string;
  platform: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt?: string;
  lastValidatedAt?: string;
  connectionStatus: string;
}

export interface TokenRefreshResult {
  success: boolean;
  error?: string;
  newAccessToken?: string;
  newRefreshToken?: string;
  expiresAt?: string;
  shouldReconnect?: boolean;
}

export class TokenRefreshService {
  private supabase = createServiceRoleClient();

  /**
   * Check if token is expired or will expire soon (within 1 hour)
   */
  isTokenExpiringSoon(expiresAt?: string): boolean {
    if (!expiresAt) return false;
    
    const expirationTime = new Date(expiresAt).getTime();
    const currentTime = Date.now();
    const oneHourFromNow = currentTime + (60 * 60 * 1000); // 1 hour in milliseconds
    
    return expirationTime <= oneHourFromNow;
  }

  /**
   * Check if token is already expired
   */
  isTokenExpired(expiresAt?: string): boolean {
    if (!expiresAt) return false;
    
    const expirationTime = new Date(expiresAt).getTime();
    const currentTime = Date.now();
    
    return expirationTime <= currentTime;
  }

  /**
   * Get all tokens that need refresh
   */
  async getTokensNeedingRefresh(): Promise<TokenInfo[]> {
    try {
      const { data: accounts, error } = await this.supabase
        .from('social_accounts')
        .select('*')
        .eq('is_active', true)
        .not('expires_at', 'is', null);

      if (error) {
        console.error('Error fetching accounts for token refresh:', error);
        return [];
      }

      return accounts
        .filter(account => 
          this.isTokenExpiringSoon(account.expires_at) || 
          account.connection_status === 'expired'
        )
        .map(account => ({
          id: account.id,
          userId: account.user_id,
          platform: account.platform,
          accessToken: account.access_token,
          refreshToken: account.refresh_token,
          expiresAt: account.expires_at,
          lastValidatedAt: account.last_validated_at,
          connectionStatus: account.connection_status
        }));
    } catch (error) {
      console.error('Error in getTokensNeedingRefresh:', error);
      return [];
    }
  }

  /**
   * Refresh token for a specific platform
   */
  async refreshToken(tokenInfo: TokenInfo): Promise<TokenRefreshResult> {
    try {
      console.log(`🔄 Refreshing ${tokenInfo.platform} token for user ${tokenInfo.userId}`);

      switch (tokenInfo.platform.toUpperCase()) {
        case 'FACEBOOK':
        case 'INSTAGRAM':
          return await this.refreshFacebookToken(tokenInfo);
        
        case 'LINKEDIN':
          return await this.refreshLinkedInToken(tokenInfo);
        
        case 'TWITTER':
        case 'X':
          // Twitter OAuth 2.0 doesn't support refresh tokens
          return {
            success: false,
            error: 'Twitter does not support token refresh',
            shouldReconnect: true
          };
        
        default:
          return {
            success: false,
            error: `Token refresh not supported for ${tokenInfo.platform}`,
            shouldReconnect: true
          };
      }
    } catch (error) {
      console.error(`Error refreshing ${tokenInfo.platform} token:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        shouldReconnect: true
      };
    }
  }

  /**
   * Refresh Facebook/Instagram token
   */
  private async refreshFacebookToken(tokenInfo: TokenInfo): Promise<TokenRefreshResult> {
    try {
      // Facebook uses long-lived tokens that don't need traditional refresh
      // Instead, we extend the token or validate it
      const facebookService = new FacebookOAuthService({
        appId: process.env.FACEBOOK_APP_ID!,
        appSecret: process.env.FACEBOOK_APP_SECRET!,
        businessId: process.env.FACEBOOK_BUSINESS_ID!,
        redirectUri: `${process.env.NEXT_PUBLIC_APP_URL}/api/facebook/callback`
      });

      // Try to extend the access token
      const extendedToken = await facebookService.extendAccessToken(tokenInfo.accessToken);
      
      if (extendedToken) {
        return {
          success: true,
          newAccessToken: extendedToken.access_token,
          expiresAt: new Date(Date.now() + (extendedToken.expires_in || 5183944) * 1000).toISOString()
        };
      }

      // If extension fails, validate current token
      const isValid = await facebookService.validateToken(tokenInfo.accessToken);
      
      if (isValid) {
        return {
          success: true,
          newAccessToken: tokenInfo.accessToken, // Keep current token
          expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString() // 60 days
        };
      }

      return {
        success: false,
        error: 'Facebook token is invalid',
        shouldReconnect: true
      };
    } catch (error) {
      console.error('Error refreshing Facebook token:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Facebook token refresh failed',
        shouldReconnect: true
      };
    }
  }

  /**
   * Refresh LinkedIn token
   */
  private async refreshLinkedInToken(tokenInfo: TokenInfo): Promise<TokenRefreshResult> {
    try {
      if (!tokenInfo.refreshToken) {
        return {
          success: false,
          error: 'No refresh token available for LinkedIn',
          shouldReconnect: true
        };
      }

      const response = await fetch('https://www.linkedin.com/oauth/v2/accessToken', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: tokenInfo.refreshToken,
          client_id: process.env.LINKEDIN_CLIENT_ID!,
          client_secret: process.env.LINKEDIN_CLIENT_SECRET!,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: `LinkedIn token refresh failed: ${errorData.error_description || errorData.error}`,
          shouldReconnect: true
        };
      }

      const tokenData = await response.json();
      
      return {
        success: true,
        newAccessToken: tokenData.access_token,
        newRefreshToken: tokenData.refresh_token || tokenInfo.refreshToken,
        expiresAt: new Date(Date.now() + tokenData.expires_in * 1000).toISOString()
      };
    } catch (error) {
      console.error('Error refreshing LinkedIn token:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'LinkedIn token refresh failed',
        shouldReconnect: true
      };
    }
  }

  /**
   * Update token in database after successful refresh
   */
  async updateTokenInDatabase(tokenInfo: TokenInfo, refreshResult: TokenRefreshResult): Promise<boolean> {
    try {
      const updateData: any = {
        connection_status: refreshResult.success ? 'connected' : 'expired',
        last_validated_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      if (refreshResult.success) {
        if (refreshResult.newAccessToken) {
          updateData.access_token = refreshResult.newAccessToken;
        }
        if (refreshResult.newRefreshToken) {
          updateData.refresh_token = refreshResult.newRefreshToken;
        }
        if (refreshResult.expiresAt) {
          updateData.expires_at = refreshResult.expiresAt;
        }
      }

      const { error } = await this.supabase
        .from('social_accounts')
        .update(updateData)
        .eq('id', tokenInfo.id);

      if (error) {
        console.error('Error updating token in database:', error);
        return false;
      }

      console.log(`✅ Updated ${tokenInfo.platform} token for user ${tokenInfo.userId}`);
      return true;
    } catch (error) {
      console.error('Error in updateTokenInDatabase:', error);
      return false;
    }
  }

  /**
   * Run token refresh for all accounts that need it
   */
  async refreshAllExpiredTokens(): Promise<{
    processed: number;
    successful: number;
    failed: number;
    needReconnection: string[];
  }> {
    const tokensToRefresh = await this.getTokensNeedingRefresh();
    
    console.log(`🔄 Found ${tokensToRefresh.length} tokens needing refresh`);
    
    let successful = 0;
    let failed = 0;
    const needReconnection: string[] = [];

    for (const tokenInfo of tokensToRefresh) {
      const refreshResult = await this.refreshToken(tokenInfo);
      
      if (refreshResult.success) {
        const updated = await this.updateTokenInDatabase(tokenInfo, refreshResult);
        if (updated) {
          successful++;
        } else {
          failed++;
        }
      } else {
        failed++;
        if (refreshResult.shouldReconnect) {
          needReconnection.push(`${tokenInfo.platform}:${tokenInfo.userId}`);
        }
      }
    }

    console.log(`✅ Token refresh complete: ${successful} successful, ${failed} failed`);
    
    return {
      processed: tokensToRefresh.length,
      successful,
      failed,
      needReconnection
    };
  }
}
