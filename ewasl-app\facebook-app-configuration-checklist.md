# Facebook App Configuration Checklist for eWasl

## App Information
- **App ID**: ****************
- **Production Domain**: https://app.ewasl.com
- **Developer Console**: https://developers.facebook.com/apps/****************/

## ✅ Configuration Checklist

### 1. Basic App Settings
- [ ] **App Domains**: Add `app.ewasl.com`
- [ ] **Site URL**: Set to `https://app.ewasl.com`
- [ ] **App Mode**: Ensure app is in "Live" mode (not Development)

### 2. Facebook Login Configuration
Navigate to: Products → Facebook Login → Settings

**Valid OAuth Redirect URIs** (add all three):
```
https://app.ewasl.com/api/auth/callback/facebook
https://app.ewasl.com/api/oauth/facebook/callback
https://app.ewasl.com/api/oauth/instagram/callback
```

**Settings to Enable**:
- [ ] Client OAuth Login: ✅ Enable
- [ ] Web OAuth Login: ✅ Enable
- [ ] Enforce HTTPS: ✅ Enable
- [ ] Use Strict Mode for Redirect URIs: ✅ Enable

### 3. Instagram Basic Display API
Navigate to: Products → Instagram Basic Display → Basic Display

**Valid OAuth Redirect URIs**:
```
https://app.ewasl.com/api/oauth/instagram/callback
```

### 4. Required Permissions
Ensure these permissions are available in App Review:
- [ ] `pages_show_list` - To list user's Facebook pages
- [ ] `pages_manage_posts` - To publish posts to pages
- [ ] `pages_read_engagement` - To read page engagement data
- [ ] `instagram_basic` - Basic Instagram access
- [ ] `instagram_content_publish` - To publish Instagram content
- [ ] `business_management` - For business account access

### 5. Instagram Graph API (for Business Accounts)
If available, configure:
- [ ] Instagram Graph API product added
- [ ] Business account permissions configured

## 🧪 Testing Checklist

After configuration, test these flows:

### Facebook OAuth Flow
1. [ ] Navigate to https://app.ewasl.com/social
2. [ ] Click "ربط حساب جديد" (Connect New Account)
3. [ ] Select "Facebook"
4. [ ] Should redirect to Facebook OAuth (no domain error)
5. [ ] Complete Facebook login and permissions
6. [ ] Should redirect back to eWasl with success message
7. [ ] Verify Facebook accounts appear in social accounts list

### Instagram OAuth Flow
1. [ ] Click "ربط حساب جديد" (Connect New Account)
2. [ ] Select "Instagram"
3. [ ] Should redirect to Facebook OAuth with Instagram scopes
4. [ ] Complete login and permissions
5. [ ] Should redirect back with Instagram Business accounts
6. [ ] Verify Instagram accounts appear in social accounts list

### Database Verification
1. [ ] Check `social_accounts` table has new records
2. [ ] Verify `access_token` is stored and valid
3. [ ] Confirm `platform` field shows 'FACEBOOK' or 'INSTAGRAM'
4. [ ] Check `connection_status` is 'connected'

## 🚨 Common Issues and Solutions

### "Domain not included in app's domains"
- **Solution**: Add `app.ewasl.com` to App Domains in Basic Settings

### "Invalid OAuth redirect URI"
- **Solution**: Ensure exact URLs are added to Valid OAuth Redirect URIs

### "App not approved for Instagram permissions"
- **Solution**: Submit app for review or use test accounts during development

### "Invalid scopes"
- **Solution**: Verify all required permissions are available in App Review

## 📞 Support
If you encounter issues:
1. Check Facebook Developer Console error logs
2. Review eWasl application logs at `/api/oauth/logs`
3. Verify environment variables in `.env.local`
4. Test with Facebook's Graph API Explorer

## 🔗 Useful Links
- [Facebook App Dashboard](https://developers.facebook.com/apps/****************/)
- [Facebook Login Documentation](https://developers.facebook.com/docs/facebook-login/)
- [Instagram Basic Display API](https://developers.facebook.com/docs/instagram-basic-display-api/)
- [Instagram Graph API](https://developers.facebook.com/docs/instagram-api/)
