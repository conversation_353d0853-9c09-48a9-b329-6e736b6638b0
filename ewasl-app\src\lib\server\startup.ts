/**
 * Server Startup Configuration
 * Initializes background services and job processors
 */

import { jobProcessor } from '@/lib/queue/job-processor';

let isInitialized = false;

/**
 * Initialize server-side services
 */
export function initializeServer() {
  if (isInitialized) {
    console.log('⚠️ Server already initialized, skipping...');
    return;
  }

  console.log('🚀 Initializing eWasl server services...');

  try {
    // Start job processor for scheduled posts
    jobProcessor.start(60000); // Check every minute
    
    console.log('✅ Job processor started');
    
    // Handle graceful shutdown
    process.on('SIGTERM', () => {
      console.log('📴 Shutting down server services...');
      jobProcessor.stop();
      process.exit(0);
    });

    process.on('SIGINT', () => {
      console.log('📴 Shutting down server services...');
      jobProcessor.stop();
      process.exit(0);
    });

    isInitialized = true;
    console.log('✅ eWasl server services initialized successfully');

  } catch (error) {
    console.error('❌ Failed to initialize server services:', error);
  }
}

/**
 * Check if server is initialized
 */
export function isServerInitialized() {
  return isInitialized;
}
